import { isEmpty, isNil } from 'lodash-es';
import { openDisableMonitorModal } from '@components/monitoring';
import { activeMonitoringController } from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { snackbarController } from '@controllers/snackbar';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
    type GlobalFilterState,
} from '@cosmos/components/datatable';
import {
    autopilotControllerBulkRetestV2Mutation,
    autopilotControllerRetestMutation,
    monitorsControllerStopControlTestInstanceMutation,
    monitorsV2ControllerDownloadMonitorTestsOptions,
    monitorsV2ControllerListCodeMonitorsOptions,
    monitorsV2ControllerUpdateMonitorStatusMutation,
} from '@globals/api-sdk/queries';
import type {
    ControlTestInstanceAutopilotBulkRequestDto,
    MonitorTestInstanceResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getCurrentDateString } from '@helpers/date-time';
import { downloadBlob } from '@helpers/download-file';
import { getFilters } from '../helpers/get-filters.helper';
import type { CodeFilterType } from '../types/monitoring-code-controller.types';
import type { ProductionFilterType } from '../types/monitoring-controller.types';

export const CODE_COLUMN_NAMES_TO_SORT_IDS_MAP = [
    'checkResultStatus',
    'findingsCount',
    'checkStatus',
] as const;

export const DEFAULT_CODE_SORT_COLUMN = 'checkStatus';
export const DEFAULT_CODE_SORT_ORDER = 'DESC';

type CodeSortIds = (typeof CODE_COLUMN_NAMES_TO_SORT_IDS_MAP)[number];

class MonitoringCodeController {
    constructor() {
        makeAutoObservable(this);
    }

    monitoringCodeList = new ObservedQuery(
        monitorsV2ControllerListCodeMonitorsOptions,
    );

    monitoringV2DownloadQuery = new ObservedQuery(
        monitorsV2ControllerDownloadMonitorTestsOptions,
    );

    updateStatusMutation = new ObservedMutation(
        monitorsV2ControllerUpdateMonitorStatusMutation,
    );

    bulkStatusUpdateMutation = new ObservedMutation(
        monitorsV2ControllerUpdateMonitorStatusMutation,
    );

    bulkRetestMutation = new ObservedMutation(
        autopilotControllerBulkRetestV2Mutation,
    );

    singleRetestMutation = new ObservedMutation(
        autopilotControllerRetestMutation,
    );

    stopTestMutation = new ObservedMutation(
        monitorsControllerStopControlTestInstanceMutation,
    );

    /**
     * Store the current query parameters for downloads.
     */
    currentQueryParams?: FetchDataResponseParams = undefined;

    get monitoringCodeListData(): MonitorTestInstanceResponseDto[] {
        return this.monitoringCodeList.data?.data ?? [];
    }

    get isLoading(): boolean {
        return this.monitoringCodeList.isLoading;
    }

    get monitoringCodeTotal(): number {
        return this.monitoringCodeList.data?.total ?? 0;
    }

    getFilters = (
        params: GlobalFilterState['filters'],
    ): ProductionFilterType => {
        return Object.fromEntries(
            Object.entries(params)
                .filter(
                    ([, filter]) =>
                        !isEmpty(filter.value) && !isNil(filter.value),
                )
                .map(([key, filter]) => [key, filter.value]),
        );
    };

    monitoringCodeListLoad = (params?: FetchDataResponseParams): void => {
        const { pagination, sorting, globalFilter } = params ?? {
            pagination: {},
            sorting: [],
            globalFilter: { search: '', filters: {} },
        };

        const { search, filters } = globalFilter;
        const values = getFilters(filters);

        const query: CodeFilterType = {
            page: pagination.page ?? 1,
            limit: pagination.pageSize ?? DEFAULT_PAGE_SIZE,
            q: search ?? '',
            ...values,
        };

        if (!isEmpty(sorting)) {
            const sortId = sorting[0].id;

            if (
                CODE_COLUMN_NAMES_TO_SORT_IDS_MAP.includes(
                    sortId as CodeSortIds,
                )
            ) {
                query.sort = sortId as CodeSortIds;
                query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
            }
        }

        // Store the current query parameters for future reloads
        this.currentQueryParams = {
            pagination: {
                pageSize: query.limit ?? DEFAULT_PAGE_SIZE,
                pageIndex: pagination.pageIndex ?? 0,
                pageSizeOptions:
                    params?.pagination.pageSizeOptions ??
                    DEFAULT_PAGE_SIZE_OPTIONS,
                page: query.page ?? 1,
            },
            sorting,
            globalFilter,
        };

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const workspaceId = sharedWorkspacesController.currentWorkspace
                    ?.id as number;

                this.monitoringCodeList.load({
                    query,
                    path: {
                        workspaceId,
                        xWorkspaceId: workspaceId,
                    },
                });
            },
        );
    };

    /**
     * Downloads monitoring code tests as CSV based on current filters.
     * Note: The API downloads all tests matching current filters, not specific selections.
     */
    downloadTests = (testIds: number[]): void => {
        const { currentWorkspace } = sharedWorkspacesController;

        this.monitoringV2DownloadQuery.load({
            query: { 'testIds[]': testIds },
            path: {
                workspaceId: currentWorkspace?.id as number,
            },
        });

        when(
            () => !this.monitoringV2DownloadQuery.isLoading,
            () => {
                if (this.monitoringV2DownloadQuery.hasError) {
                    snackbarController.addSnackbar({
                        id: 'monitoring-code-download-error',
                        props: {
                            title: t`Download failed`,
                            description: t`Unable to download tests. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (this.monitoringV2DownloadQuery.data) {
                    const blob = new Blob(
                        [
                            this.monitoringV2DownloadQuery
                                .data as unknown as string,
                        ],
                        {
                            type: 'text/csv',
                        },
                    );

                    downloadBlob(
                        blob,
                        `Codebase-Tests-${getCurrentDateString()}.csv`,
                    );

                    snackbarController.addSnackbar({
                        id: 'monitoring-code-download-success',
                        props: {
                            title: t`Download successful`,
                            description: t`Codebase tests downloaded successfully.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };

    /**
     * Updates the status of a monitor test instance.
     * For DISABLED status, this should be called after getting the reason from the modal.
     */
    updateMonitorStatus = (
        testId: number,
        newStatus: 'ENABLED' | 'DISABLED',
        disabledMessage?: string,
    ): void => {
        when(
            () =>
                !sharedWorkspacesController.isLoading &&
                !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                const workspaceId = sharedWorkspacesController.currentWorkspace
                    ?.id as number;

                const requestBody: ControlTestInstanceAutopilotBulkRequestDto =
                    {
                        checkStatus: newStatus,
                        controlTestInstanceTestIds: [testId],
                        disabledMessage: disabledMessage || '',
                    };

                this.updateStatusMutation
                    .mutateAsync({
                        path: { workspaceId },
                        body: requestBody,
                    })
                    .then(() => {
                        snackbarController.addSnackbar({
                            id: 'monitor-status-updated',
                            props: {
                                title: t`Monitor status updated successfully`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        // Reload the monitoring list to reflect the changes
                        this.monitoringCodeListLoad(this.currentQueryParams);

                        // Invalidate monitor details to update the details view
                        activeMonitoringController.monitorDetails.invalidate();
                        sharedMonitoringTestDetailsController.testDetailsQuery.invalidate();
                    })
                    .catch(() => {
                        snackbarController.addSnackbar({
                            id: 'monitor-status-update-error',
                            props: {
                                title: t`Update Failed`,
                                description: t`An error occurred while updating the test status. Please try again.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
        );
    };

    /**
     * Handles monitor status toggle from the UI.
     * Opens a modal for disable actions to collect the reason.
     */
    handleMonitorStatusToggle = (
        testId: number,
        newStatus: 'ENABLED' | 'DISABLED',
    ): void => {
        if (newStatus === 'ENABLED') {
            // For enabling, no modal needed
            this.updateMonitorStatus(testId, newStatus);
        } else {
            openDisableMonitorModal((reason: string) => {
                this.updateMonitorStatus(testId, newStatus, reason);
            });
        }
    };

    /**
     * Handles bulk status update action for multiple monitor tests.
     * Uses the v2/monitors/status endpoint for bulk status updates.
     * For DISABLED status, this should be called after getting the reason from the modal.
     */
    bulkUpdateTestStatus = (
        testIds: number[],
        newStatus: 'ENABLED' | 'DISABLED',
        disabledMessage?: string,
    ): void => {
        if (isEmpty(testIds)) {
            return;
        }

        when(
            () =>
                !sharedWorkspacesController.isLoading &&
                !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                const requestBody: ControlTestInstanceAutopilotBulkRequestDto =
                    {
                        checkStatus: newStatus,
                        disabledMessage: disabledMessage || '',
                        controlTestInstanceTestIds: testIds,
                    };

                this.bulkStatusUpdateMutation
                    .mutateAsync({
                        path: {
                            workspaceId: sharedWorkspacesController
                                .currentWorkspace?.id as number,
                        },
                        body: requestBody,
                    })
                    .then(() => {
                        snackbarController.addSnackbar({
                            id: 'bulk-monitor-status-updated',
                            props: {
                                title: t`Test status updated successfully`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        // Reload the monitoring list to reflect the changes
                        this.monitoringCodeListLoad(this.currentQueryParams);
                    })
                    .catch(() => {
                        const actionDescription =
                            newStatus === 'ENABLED'
                                ? t`An error occurred while enabling the tests. Please try again.`
                                : t`An error occurred while disabling the tests. Please try again.`;
                        const actionTitle =
                            newStatus === 'ENABLED'
                                ? t`Enable tests failed`
                                : t`Disable tests failed`;

                        snackbarController.addSnackbar({
                            id: 'bulk-monitor-status-update-error',
                            props: {
                                title: actionTitle,
                                description: actionDescription,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
        );
    };

    /**
     * Handles bulk enable action for multiple monitor tests.
     */
    bulkEnableTests = (testIds: number[]): void => {
        this.bulkUpdateTestStatus(testIds, 'ENABLED');
    };

    /**
     * Handles bulk disable action for multiple monitor tests.
     * Opens a modal to collect the reason for disabling.
     */
    bulkDisableTests = (testIds: number[]): void => {
        openDisableMonitorModal((reason: string) => {
            this.bulkUpdateTestStatus(testIds, 'DISABLED', reason);
        });
    };

    /**
     * Handles bulk "Test now" action for multiple monitor tests.
     * Executes tests for the provided test IDs using the bulk retest API.
     */
    bulkTestNow = (testIds: number[]): void => {
        if (isEmpty(testIds)) {
            return;
        }

        when(
            () =>
                !sharedWorkspacesController.isLoading &&
                !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                const workspaceId = sharedWorkspacesController.currentWorkspace
                    ?.id as number;

                this.bulkRetestMutation
                    .mutateAsync({
                        path: {
                            xProductId: workspaceId,
                        },
                        body: {
                            testIds,
                        },
                    })
                    .then(() => {
                        snackbarController.addSnackbar({
                            id: 'bulk-test-now-success',
                            props: {
                                title: t`Tests started`,
                                description: t`Tests started successfully`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        // Reload the monitoring list to reflect the changes
                        this.monitoringCodeListLoad(this.currentQueryParams);
                    })
                    .catch(() => {
                        snackbarController.addSnackbar({
                            id: 'bulk-test-now-error',
                            props: {
                                title: t`Test execution failed`,
                                description: t`An error occurred while starting the tests. Please try again.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
        );
    };

    /**
     * Handles the "Test now" action for a monitor test.
     */
    handleTestNow = (testId: number): void => {
        when(
            () =>
                !sharedWorkspacesController.isLoading &&
                !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                const workspaceId = sharedWorkspacesController.currentWorkspace
                    ?.id as number;

                this.singleRetestMutation
                    .mutateAsync({
                        path: {
                            testId,
                            xProductId: workspaceId,
                        },
                    })
                    .then(() => {
                        snackbarController.addSnackbar({
                            id: 'test-now-success',
                            props: {
                                title: t`Test started`,
                                description: t`Test has been started successfully.`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        // Reload the monitoring list to reflect the changes
                        this.monitoringCodeListLoad(this.currentQueryParams);
                    })
                    .catch(() => {
                        snackbarController.addSnackbar({
                            id: 'test-now-error',
                            props: {
                                title: t`Test execution failed`,
                                description: t`An error occurred while starting the test. Please try again.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
        );
    };

    /**
     * Handles the "Stop testing" action for a monitor test.
     */
    handleStopTesting = (testId: number): void => {
        when(
            () =>
                !sharedWorkspacesController.isLoading &&
                !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                const workspaceId = sharedWorkspacesController.currentWorkspace
                    ?.id as number;

                this.stopTestMutation
                    .mutateAsync({
                        path: {
                            testId,
                            xProductId: workspaceId,
                        },
                    })
                    .then(() => {
                        snackbarController.addSnackbar({
                            id: 'stop-testing-success',
                            props: {
                                title: t`Test stopped`,
                                description: t`Test has been stopped successfully.`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        // Reload the monitoring list to reflect the changes
                        this.monitoringCodeListLoad(this.currentQueryParams);
                    })
                    .catch(() => {
                        snackbarController.addSnackbar({
                            id: 'stop-testing-error',
                            props: {
                                title: t`Stop test failed`,
                                description: t`An error occurred while stopping the test. Please try again.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
        );
    };

    /**
     * Handles the "View help article" action for a monitor test.
     */
    handleViewHelpArticle = (): void => {
        // Open help article in a new tab
        window.open(
            'https://help.drata.com/en/articles/6188453-monitoring-overview',
            '_blank',
        );
    };
}

export const sharedMonitoringCodeController = new MonitoringCodeController();
