import { isEmpty } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { monitorExclusionsControllerListMonitorCodebaseExclusionsOptions } from '@globals/api-sdk/queries';
import type { MonitorCodebaseExclusionResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class MonitoringCodeExclusionsController {
    constructor() {
        makeAutoObservable(this);
    }

    testId: number | null = null;

    monitoringCodeExclusions = new ObservedQuery(
        monitorExclusionsControllerListMonitorCodebaseExclusionsOptions,
    );

    setTestId(testId: number): void {
        this.testId = testId;
    }

    get isLoading(): boolean {
        return this.monitoringCodeExclusions.isLoading;
    }

    get isFetching(): boolean {
        return this.monitoringCodeExclusions.isFetching;
    }

    get monitoringCodeExclusionsData(): MonitorCodebaseExclusionResponseDto[] {
        return this.monitoringCodeExclusions.data?.data ?? [];
    }

    get monitoringCodeExclusionsTotal(): number {
        return this.monitoringCodeExclusions.data?.total ?? 0;
    }

    monitoringCodeExclusionLoad = (params?: FetchDataResponseParams): void => {
        const testId = Number(this.testId);

        if (isNaN(testId)) {
            throw new TypeError('testId must be numbers');
        }

        const { pagination, sorting, globalFilter } = params ?? {
            pagination: { page: 1, pageSize: 10 },
            sorting: [],
            globalFilter: { search: '', filters: {} },
        };

        const { search } = globalFilter;

        type Query = Required<
            Parameters<
                typeof monitorExclusionsControllerListMonitorCodebaseExclusionsOptions
            >
        >[0]['query'];

        const query: Query = {
            page: pagination.page,
            limit: pagination.pageSize,
            ...(!isEmpty(search) && { q: search }),
        };

        if (!isEmpty(sorting)) {
            query.sort = sorting[0].id as
                | 'CODEBASE_REPOSITORY'
                | 'CODEBASE_RESOURCE'
                | 'CODEBASE_PROPERTY'
                | 'EXCLUSION_REASON'
                | 'START_DATE'
                | 'CREATED_BY'
                | undefined;
            query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
        }

        this.monitoringCodeExclusions.load({
            query,
            path: {
                testId,
                workspaceId:
                    sharedWorkspacesController.currentWorkspace?.id ?? 1,
            },
        });
    };
}

export const sharedMonitoringCodeExclusionsController =
    new MonitoringCodeExclusionsController();
