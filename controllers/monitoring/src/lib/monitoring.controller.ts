import { isEmpty } from 'lodash-es';
import { openDisableMonitorModal } from '@components/monitoring';
import { snackbarController } from '@controllers/snackbar';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import {
    autopilotControllerBulkRetestV2Mutation,
    autopilotControllerRetestMutation,
    monitorsControllerControlTestInstanceAutopilotUpdateMutation,
    monitorsControllerStopControlTestInstanceMutation,
    monitorsV2ControllerBulkActionPublishDraftTestsMutation,
    monitorsV2ControllerDownloadMonitorTestsOptions,
    monitorsV2ControllerGetTestIdsOptions,
    monitorsV2ControllerListProdMonitorsOptions,
    monitorsV2ControllerUpdateMonitorStatusMutation,
} from '@globals/api-sdk/queries';
import type {
    ControlTestInstanceAutopilotBulkRequestDto,
    ControlTestInstanceAutopilotRequestDto,
    MonitorTestInstanceResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getCurrentDateString } from '@helpers/date-time';
import { downloadBlob } from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { getFilters } from '../helpers/get-filters.helper';
import type { ProductionFilterType } from '../types/monitoring-controller.types';

export const COLUMN_NAMES_TO_SORT_IDS_MAP = [
    'checkResultStatus',
    'findingsCount',
    'checkStatus',
    'category',
] as const;

export const DEFAULT_SORT_COLUMN = 'checkStatus';
export const DEFAULT_SORT_ORDER = 'DESC';

type SortIds = (typeof COLUMN_NAMES_TO_SORT_IDS_MAP)[number];

class MonitoringController {
    constructor() {
        makeAutoObservable(this);
    }

    monitoringProductionList = new ObservedQuery(
        monitorsV2ControllerListProdMonitorsOptions,
    );

    monitoringV2DownloadQuery = new ObservedQuery(
        monitorsV2ControllerDownloadMonitorTestsOptions,
    );

    updateStatusMutation = new ObservedMutation(
        monitorsControllerControlTestInstanceAutopilotUpdateMutation,
    );

    bulkRetestMutation = new ObservedMutation(
        autopilotControllerBulkRetestV2Mutation,
    );

    singleRetestMutation = new ObservedMutation(
        autopilotControllerRetestMutation,
    );

    stopTestMutation = new ObservedMutation(
        monitorsControllerStopControlTestInstanceMutation,
    );

    bulkStatusUpdateMutation = new ObservedMutation(
        monitorsV2ControllerUpdateMonitorStatusMutation,
    );

    bulkPublishMutation = new ObservedMutation(
        monitorsV2ControllerBulkActionPublishDraftTestsMutation,
    );

    monitoringTestIdsQuery = new ObservedQuery(
        monitorsV2ControllerGetTestIdsOptions,
    );

    /**
     * Store the current query parameters for downloads.
     */
    currentQueryParams?: FetchDataResponseParams = undefined;

    get monitoringListData(): MonitorTestInstanceResponseDto[] {
        return this.monitoringProductionList.data?.data ?? [];
    }

    get isLoading(): boolean {
        return this.monitoringProductionList.isLoading;
    }

    get isSingleTesting(): boolean {
        return this.singleRetestMutation.isPending;
    }

    get monitoringListTotal(): number {
        return this.monitoringProductionList.data?.total ?? 0;
    }

    monitoringListLoad = (params?: FetchDataResponseParams): void => {
        const { pagination, sorting, globalFilter } = params ?? {
            pagination: {},
            sorting: [],
            globalFilter: { search: '', filters: {} },
        };

        const { search, filters } = globalFilter;
        const values = getFilters(filters);

        const query: ProductionFilterType = {
            page: pagination.page ?? 1,
            limit: pagination.pageSize ?? DEFAULT_PAGE_SIZE,
            q: search ?? '',
            ...values,
        };

        if (!isEmpty(sorting)) {
            const sortId = sorting[0].id;

            if (COLUMN_NAMES_TO_SORT_IDS_MAP.includes(sortId as SortIds)) {
                query.sort = sortId as SortIds;
                query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
            }
        }

        // Store the current query parameters for future reloads
        this.currentQueryParams = {
            pagination: {
                pageSize: query.limit ?? DEFAULT_PAGE_SIZE,
                pageIndex: pagination.pageIndex ?? 0,
                pageSizeOptions:
                    params?.pagination.pageSizeOptions ??
                    DEFAULT_PAGE_SIZE_OPTIONS,
                page: query.page ?? 1,
            },
            sorting,
            globalFilter,
        };

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const workspaceId = sharedWorkspacesController.currentWorkspace
                    ?.id as number;

                this.monitoringProductionList.load({
                    query,
                    path: {
                        workspaceId,
                        xWorkspaceId: workspaceId,
                    },
                });
            },
        );
    };

    /**
     * Downloads monitoring production tests as CSV based on selected test IDs.
     *
     * @param testIds - Array of test IDs to download. If not provided, downloads all tests matching current filters.
     */
    downloadTests = (testIds: number[]): void => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!isEmpty(testIds)) {
            // Use the v2 endpoint that accepts testIds
            this.monitoringV2DownloadQuery.load({
                query: { 'testIds[]': testIds },
                path: {
                    workspaceId: currentWorkspace?.id as number,
                },
            });

            when(
                () => !this.monitoringV2DownloadQuery.isLoading,
                () => {
                    if (this.monitoringV2DownloadQuery.hasError) {
                        snackbarController.addSnackbar({
                            id: 'monitoring-download-error',
                            props: {
                                title: t`Download failed`,
                                description: t`Unable to download tests. Please try again.`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        return;
                    }

                    if (this.monitoringV2DownloadQuery.data) {
                        const blob = new Blob(
                            [
                                this.monitoringV2DownloadQuery
                                    .data as unknown as string,
                            ],
                            {
                                type: 'text/csv',
                            },
                        );

                        downloadBlob(
                            blob,
                            `Production-Tests-${getCurrentDateString()}.csv`,
                        );

                        snackbarController.addSnackbar({
                            id: 'monitoring-download-success',
                            props: {
                                title: t`Download successful`,
                                description: t`Production tests downloaded successfully.`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    }
                },
            );
        }
    };

    /**
     * Updates the status of a monitor test instance.
     * For DISABLED status, this should be called after getting the reason from the modal.
     */
    updateMonitorStatus = (
        testId: number,
        newStatus: 'ENABLED' | 'DISABLED',
        disabledMessage?: string,
    ): void => {
        this.executeWithWorkspace((workspaceId) => {
            const requestBody: ControlTestInstanceAutopilotRequestDto = {
                checkStatus: newStatus,
                disabledMessage: disabledMessage || '',
            };

            this.updateStatusMutation
                .mutateAsync({
                    path: { xProductId: workspaceId, testId },
                    body: requestBody,
                })
                .then(() => {
                    snackbarController.addSnackbar({
                        id: 'monitor-status-updated',
                        props: {
                            title: t`Test status updated successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    this.monitoringListLoad(this.currentQueryParams);
                })
                .catch(() => {
                    snackbarController.addSnackbar({
                        id: 'monitor-status-update-error',
                        props: {
                            title: t`Test status update Failed`,
                            description: t`An error occurred while updating the monitor status. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                });
        });
    };

    /**
     * Handles monitor status toggle from the UI.
     * Opens a modal for disable actions to collect the reason.
     */
    handleMonitorStatusToggle = (
        testId: number,
        newStatus: 'ENABLED' | 'DISABLED',
    ): void => {
        if (newStatus === 'ENABLED') {
            // For enabling, no modal needed
            this.updateMonitorStatus(testId, newStatus);
        } else {
            openDisableMonitorModal((reason: string) => {
                this.updateMonitorStatus(testId, newStatus, reason);
            });
        }
    };

    /**
     * Handles the "Test now" action for a monitor test.
     * Executes the test using the autopilot retest API.
     */
    handleTestNow = (testId: number): void => {
        this.executeWithWorkspace((workspaceId) => {
            this.singleRetestMutation
                .mutateAsync({
                    path: {
                        testId,
                        xProductId: workspaceId,
                    },
                })
                .then(() => {
                    snackbarController.addSnackbar({
                        id: 'test-now-success',
                        props: {
                            title: t`Test started`,
                            description: t`Test has been started successfully.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    this.monitoringListLoad(this.currentQueryParams);
                })
                .catch(() => {
                    snackbarController.addSnackbar({
                        id: 'test-now-error',
                        props: {
                            title: t`Test execution failed`,
                            description: t`An error occurred while starting the test. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                });
        });
    };

    /**
     * Handles bulk "Test now" action for multiple monitor tests.
     * Executes tests for the provided test IDs using the bulk retest API.
     */
    bulkTestNow = (testIds: number[]): void => {
        this.executeWithWorkspace((workspaceId) => {
            this.bulkRetestMutation
                .mutateAsync({
                    path: {
                        xProductId: workspaceId,
                    },
                    body: {
                        testIds,
                    },
                })
                .then(() => {
                    snackbarController.addSnackbar({
                        id: 'bulk-test-now-success',
                        props: {
                            title: t`Tests started`,
                            description: t`Tests started successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    this.monitoringListLoad(this.currentQueryParams);
                })
                .catch(() => {
                    snackbarController.addSnackbar({
                        id: 'bulk-test-now-error',
                        props: {
                            title: t`Test execution failed`,
                            description: t`An error occurred while starting the tests. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                });
        });
    };

    /**
     * Handles bulk status update action for multiple monitor tests.
     * Uses the v2/monitors/status endpoint for bulk status updates.
     * For DISABLED status, this should be called after getting the reason from the modal.
     */
    bulkUpdateTestStatus = (
        testIds: number[],
        newStatus: 'ENABLED' | 'DISABLED',
        disabledMessage?: string,
    ): void => {
        if (isEmpty(testIds)) {
            return;
        }

        this.executeWithWorkspace((workspaceId) => {
            const requestBody: ControlTestInstanceAutopilotBulkRequestDto = {
                checkStatus: newStatus,
                disabledMessage: disabledMessage || '',
                controlTestInstanceTestIds: testIds,
            };

            this.bulkStatusUpdateMutation
                .mutateAsync({
                    path: { workspaceId },
                    body: requestBody,
                })
                .then(() => {
                    snackbarController.addSnackbar({
                        id: `bulk-update-test-status-success`,
                        props: {
                            title: t`Test status updated successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    this.monitoringListLoad(this.currentQueryParams);
                })
                .catch(() => {
                    const actionDescription =
                        newStatus === 'ENABLED'
                            ? t`An error occurred while enabling the tests. Please try again.`
                            : t`An error occurred while disabling the tests. Please try again.`;
                    const actionTitle =
                        newStatus === 'ENABLED'
                            ? t`Enable tests failed`
                            : t`Disable tests failed`;

                    snackbarController.addSnackbar({
                        id: `bulk-update-status-error`,
                        props: {
                            title: actionTitle,
                            description: actionDescription,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                });
        });
    };

    /**
     * Handles bulk enable action for multiple monitor tests.
     */
    bulkEnableTests = (testIds: number[]): void => {
        this.bulkUpdateTestStatus(testIds, 'ENABLED');
    };

    /**
     * Handles bulk disable action for multiple monitor tests.
     * Opens a modal to collect the reason for disabling.
     */
    bulkDisableTests = (testIds: number[]): void => {
        openDisableMonitorModal((reason: string) => {
            this.bulkUpdateTestStatus(testIds, 'DISABLED', reason);
        });
    };

    /**
     * Handles bulk publish action for multiple monitor tests.
     * Opens a confirmation modal and publishes tests on confirmation.
     */
    bulkPublishTests = (testIds: number[]): void => {
        const selectedCount = testIds.length;

        openConfirmationModal({
            title: t`Publish ${selectedCount} Tests`,
            body: t`This action is permanent. Once published test results will contribute to control readiness.`,
            confirmText: t`Publish ${selectedCount} tests`,
            cancelText: t`Cancel`,
            onConfirm: () => {
                this.performBulkPublish(testIds);
                closeConfirmationModal();
            },
            onCancel: () => {
                closeConfirmationModal();
            },
            type: 'primary',
        });
    };

    /**
     * Performs the actual bulk publish operation.
     */
    performBulkPublish = (testIds: number[]): void => {
        if (isEmpty(testIds)) {
            return;
        }

        this.executeWithWorkspace((workspaceId) => {
            this.bulkPublishMutation
                .mutateAsync({
                    path: { workspaceId },
                    body: { testIds },
                })
                .then(() => {
                    snackbarController.addSnackbar({
                        id: 'bulk-publish-success',
                        props: {
                            title: t`Tests published successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    this.monitoringListLoad(this.currentQueryParams);
                })
                .catch(() => {
                    snackbarController.addSnackbar({
                        id: 'bulk-publish-error',
                        props: {
                            title: t`Publish tests failed`,
                            description: t`An error occurred while publishing the tests. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                });
        });
    };

    /**
     * Handles the "Stop testing" action for a monitor test.
     */
    handleStopTesting = (testId: number): void => {
        this.executeWithWorkspace((workspaceId) => {
            this.stopTestMutation
                .mutateAsync({
                    path: {
                        testId,
                        xProductId: workspaceId,
                    },
                })
                .then(() => {
                    snackbarController.addSnackbar({
                        id: 'stop-testing-success',
                        props: {
                            title: t`Test stopped`,
                            description: t`Test has been stopped successfully.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    this.monitoringListLoad(this.currentQueryParams);
                })
                .catch(() => {
                    snackbarController.addSnackbar({
                        id: 'stop-testing-error',
                        props: {
                            title: t`Stop test failed`,
                            description: t`An error occurred while stopping the test. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                });
        });
    };

    /**
     * Handles the "View help article" action for a Drata test.
     * Opens the help article in a new tab.
     */
    handleViewHelpArticle = (): void => {
        const helpUrl = 'https://help.drata.com/en/articles/4790546-monitoring';

        window.open(helpUrl, '_blank', 'noopener,noreferrer');
    };

    /**
     * Gets all test IDs based on current filters.
     */
    getTestIds = async (): Promise<number[]> => {
        const { pagination, sorting, globalFilter } = this
            .currentQueryParams ?? {
            pagination: {},
            sorting: [],
            globalFilter: { search: '', filters: {} },
        };

        const { search, filters } = globalFilter;
        const values = getFilters(filters);

        const query: ProductionFilterType = {
            page: pagination.page ?? 1,
            limit: pagination.pageSize ?? DEFAULT_PAGE_SIZE,
            q: search ?? '',
            sortDir: DEFAULT_SORT_ORDER as 'ASC' | 'DESC',
            sort: DEFAULT_SORT_COLUMN as SortIds,
            ...values,
        };

        if (!isEmpty(sorting)) {
            const sortId = sorting[0].id;

            if (COLUMN_NAMES_TO_SORT_IDS_MAP.includes(sortId as SortIds)) {
                query.sort = sortId as SortIds;
                query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
            }
        }

        return new Promise((resolve) => {
            this.executeWithWorkspace((workspaceId) => {
                this.monitoringTestIdsQuery.load({
                    query,
                    path: { workspaceId },
                });

                when(
                    () => !this.monitoringTestIdsQuery.isLoading,
                    () => {
                        resolve(
                            this.monitoringTestIdsQuery.data?.testIds ?? [],
                        );
                    },
                );
            });
        });
    };

    executeWithWorkspace = (action: (workspaceId: number) => void): void => {
        when(
            () => sharedWorkspacesController.isLoaded,
            () => {
                const workspaceId =
                    sharedWorkspacesController.currentWorkspace?.id;

                if (!workspaceId) {
                    logger.error('Workspace ID not available');

                    return;
                }

                action(workspaceId);
            },
        );
    };
}

export const sharedMonitoringController = new MonitoringController();
