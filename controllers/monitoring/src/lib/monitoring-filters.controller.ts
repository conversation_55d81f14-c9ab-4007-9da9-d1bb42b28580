import { isEmpty, isNil } from 'lodash-es';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { monitorsV2ControllerListFiltersOptions } from '@globals/api-sdk/queries';
import type { FilterItemDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

// Temporary interface until the API response type is properly defined
interface ProductionFiltersResponse {
    connectionFilters: FilterItemDto[];
    controlFilters: FilterItemDto[];
    frameworkFilters: FilterItemDto[];
}

class MonitoringFiltersController {
    connectionSearchTerm = '';
    controlSearchTerm = '';
    frameworkSearchTerm = '';

    constructor() {
        makeAutoObservable(this);
    }

    filtersQuery = new ObservedQuery(monitorsV2ControllerListFiltersOptions);

    get connectionOptions(): ListBoxItemData[] {
        // Type assertion needed due to incorrect API response type in generated SDK
        const data = this.filtersQuery
            .data as unknown as ProductionFiltersResponse;

        if (isNil(data) || isEmpty(data.connectionFilters)) {
            return [];
        }

        const connections = data.connectionFilters;

        // Filter connections based on search term
        const filteredConnections = this.connectionSearchTerm
            ? connections.filter((connection: FilterItemDto) =>
                  connection.label
                      .toLowerCase()
                      .includes(this.connectionSearchTerm.toLowerCase()),
              )
            : connections;

        return filteredConnections.map((connection: FilterItemDto) => ({
            id: connection.value,
            label: connection.label,
            value: connection.value,
        }));
    }

    get controlOptions(): ListBoxItemData[] {
        // Type assertion needed due to incorrect API response type in generated SDK
        const data = this.filtersQuery
            .data as unknown as ProductionFiltersResponse;

        if (isNil(data) || isEmpty(data.controlFilters)) {
            return [];
        }

        const controls = data.controlFilters;

        // Filter controls based on search term
        const filteredControls = this.controlSearchTerm
            ? controls.filter((control: FilterItemDto) =>
                  control.label
                      .toLowerCase()
                      .includes(this.controlSearchTerm.toLowerCase()),
              )
            : controls;

        return filteredControls.map((control: FilterItemDto) => ({
            id: control.value,
            label: control.label,
            value: control.value,
        }));
    }

    get frameworkOptions(): ListBoxItemData[] {
        // Type assertion needed due to incorrect API response type in generated SDK
        const data = this.filtersQuery
            .data as unknown as ProductionFiltersResponse;

        if (isNil(data) || isEmpty(data.frameworkFilters)) {
            return [];
        }

        const frameworks = data.frameworkFilters;

        // Filter frameworks based on search term
        const filteredFrameworks = this.frameworkSearchTerm
            ? frameworks.filter((framework: FilterItemDto) =>
                  framework.label
                      .toLowerCase()
                      .includes(this.frameworkSearchTerm.toLowerCase()),
              )
            : frameworks;

        return filteredFrameworks.map((framework: FilterItemDto) => ({
            id: framework.value,
            label: framework.label,
            value: framework.value,
        }));
    }

    get isLoading(): boolean {
        return this.filtersQuery.isLoading;
    }

    loadFilters = (): void => {
        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const workspaceId =
                    sharedWorkspacesController.currentWorkspace?.id;

                if (!workspaceId) {
                    return;
                }

                this.filtersQuery.load({
                    path: {
                        workspaceId,
                    },
                });
            },
        );
    };

    searchConnectionOptions = ({ search }: { search?: string }): void => {
        this.connectionSearchTerm = search?.trim() ?? '';
    };

    searchControlOptions = ({ search }: { search?: string }): void => {
        this.controlSearchTerm = search?.trim() ?? '';
    };

    searchFrameworkOptions = ({ search }: { search?: string }): void => {
        this.frameworkSearchTerm = search?.trim() ?? '';
    };
}

export const sharedMonitoringFiltersController =
    new MonitoringFiltersController();
