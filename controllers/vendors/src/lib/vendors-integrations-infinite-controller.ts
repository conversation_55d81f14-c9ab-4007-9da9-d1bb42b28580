import { isEmpty } from 'lodash-es';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { vendorsControllerListVendorsInfiniteOptions } from '@globals/api-sdk/queries';
import type {
    VendorReduceDirectoryResponseDto,
    VendorsControllerListVendorsData,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';

class VendorsIntegrationsInfiniteController {
    lastSearchQuery = '';
    excludeIds: number[] = [];

    constructor() {
        makeAutoObservable(this);
    }

    integrationsInfiniteQuery = new ObservedInfiniteQuery(
        vendorsControllerListVendorsInfiniteOptions,
    );

    get hasNextPage(): boolean {
        return this.integrationsInfiniteQuery.hasNextPage;
    }

    get integrationsList(): VendorReduceDirectoryResponseDto[] {
        return (
            this.integrationsInfiniteQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get hasError(): boolean {
        return this.integrationsInfiniteQuery.hasError;
    }

    get isLoading(): boolean {
        return this.integrationsInfiniteQuery.isLoading;
    }

    get isFetching(): boolean {
        return this.integrationsInfiniteQuery.isFetching;
    }

    get options(): ListBoxItemData[] {
        return this.integrationsList.map((vendor) => ({
            id: String(vendor.id),
            label: vendor.name,
            value: String(vendor.id),
        }));
    }

    loadNextPage = ({ search }: { search?: string }): void => {
        if (search !== this.lastSearchQuery) {
            this.integrationsInfiniteQuery.unload();
            this.lastSearchQuery = search ?? '';
            this.loadIntegrations({ q: search });

            return;
        }

        this.integrationsInfiniteQuery.nextPage();
    };

    loadIntegrations = ({
        q,
        excludeIds,
    }: {
        q?: string;
        excludeIds?: number[];
    }): void => {
        const query: VendorsControllerListVendorsData['query'] = {
            page: 1,
            isArchived: false,
        };

        if (excludeIds) {
            this.excludeIds = excludeIds;
        }

        if (!isEmpty(this.excludeIds)) {
            query.excludeIds = this.excludeIds;
        }

        if (q) {
            query.q = q;
        }

        this.integrationsInfiniteQuery.load({
            query,
        });
    };

    load = (search?: string, excludeIds?: number[]): void => {
        this.loadIntegrations({ q: search, excludeIds });
    };

    onFetchIntegrations = ({
        search,
        increasePage,
        excludeIds,
    }: {
        search?: string;
        increasePage?: boolean;
        excludeIds?: number[];
    } = {}): void => {
        if (increasePage) {
            this.loadNextPage({ search });

            return;
        }
        this.loadIntegrations({ q: search?.trim(), excludeIds });
    };

    onFetchRisks = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    } = {}): void => {
        if (increasePage) {
            this.loadNextPage({ search });

            return;
        }
        this.integrationsInfiniteQuery.load({
            query: {
                q: search?.trim(),
                withLastQuestionnaires: false,
            },
        });
    };
}

export const sharedVendorsIntegrationsInfiniteController =
    new VendorsIntegrationsInfiniteController();
