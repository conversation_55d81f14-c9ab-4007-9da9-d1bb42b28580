import { isArray, isEmpty, isNil, isString } from 'lodash-es';
import {
    vendorTrustCenterControllerGetVendorTrustCenterAccessRequestOptions,
    vendorTrustCenterControllerGetVendorTrustCenterAccessRequestRequirementsOptions,
    vendorTrustCenterControllerGetVendorTrustCenterCertificationsOptions,
    vendorTrustCenterControllerGetVendorTrustCenterDocumentsOptions,
    vendorTrustCenterControllerGetVendorTrustCenterItemsByCategoryOptions,
    vendorTrustCenterControllerGetVendorTrustCenterOptions,
    vendorTrustCenterControllerGetVendorTrustCenterOverviewOptions,
    vendorTrustCenterControllerGetVendorTrustCenterSubProcessorsOptions,
} from '@globals/api-sdk/queries';
import type {
    VendorTrustCenterAccessRequestRequirementsResponseDto,
    VendorTrustCenterAccessRequestResponseDto,
    VendorTrustCenterCertificationResponseDto,
    VendorTrustCenterDocumentResponseDto,
    VendorTrustCenterItemsByCategoryResponseDto,
    VendorTrustCenterOverviewResponseDto,
    VendorTrustCenterResponseDto,
    VendorTrustCenterSubProcessorItemResponseDto,
} from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import {
    makeAutoObservable,
    ObservedQuery,
    reaction,
    when,
} from '@globals/mobx';
import { getTimeDiff } from '@helpers/date-time';
import { generateLogoDevUrl } from '@helpers/formatters';
import {
    normalizeSecurityGrade,
    normalizeWebsiteUrl,
} from './helpers/vendors-trust-center.helper';
import { sharedVendorsDetailsController } from './vendors-details-controller';

class VendorTrustCenterController {
    #currentVendorId: number | null = null;

    constructor() {
        makeAutoObservable(this);

        reaction(
            () => sharedVendorsDetailsController.vendorDetails?.url,
            () => {
                this.#vendorTrustCenterQuery.invalidate();
            },
        );
    }

    readonly #vendorTrustCenterQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterOptions,
    );

    readonly #vendorTrustCenterOverviewQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterOverviewOptions,
    );

    readonly #vendorTrustCenterCertificationsQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterCertificationsOptions,
    );

    readonly #vendorTrustCenterDocumentsQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterDocumentsOptions,
    );

    readonly #vendorTrustCenterSubprocessorsQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterSubProcessorsOptions,
    );

    readonly #vendorTrustCenterItemsQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterItemsByCategoryOptions,
    );

    readonly #vendorTrustCenterAccessRequestQuery = new ObservedQuery(
        vendorTrustCenterControllerGetVendorTrustCenterAccessRequestOptions,
    );

    readonly #vendorTrustCenterAccessRequestRequirementsQuery =
        new ObservedQuery(
            vendorTrustCenterControllerGetVendorTrustCenterAccessRequestRequirementsOptions,
        );

    get info(): VendorTrustCenterResponseDto | null {
        return this.#vendorTrustCenterQuery.data ?? null;
    }

    get overview(): VendorTrustCenterOverviewResponseDto['overview'] | null {
        return this.#vendorTrustCenterOverviewQuery.data?.overview ?? null;
    }

    get certifications(): VendorTrustCenterCertificationResponseDto[] {
        return (
            this.#vendorTrustCenterCertificationsQuery.data?.compliances ?? []
        );
    }

    get documents(): VendorTrustCenterDocumentResponseDto[] {
        const responseData = this.#vendorTrustCenterDocumentsQuery.data;

        if (!responseData?.documents) {
            return [];
        }

        return responseData.documents.map((document) => ({
            id: document.id,
            name: document.name,
            isViewOnly: document.isViewOnly,
            isAccessRequired: false,
        }));
    }

    get subprocessors(): VendorTrustCenterSubProcessorItemResponseDto[] {
        const responseData = this.#vendorTrustCenterSubprocessorsQuery.data;

        if (!responseData?.subProcessors) {
            return [];
        }

        return responseData.subProcessors.map((subprocessor) => {
            const { company } = subprocessor;

            if (!company) {
                return subprocessor;
            }

            const normalizedWebsite = normalizeWebsiteUrl(company.website);
            const logoUrl = company.website
                ? generateLogoDevUrl(company.website)
                : '';

            return {
                ...subprocessor,
                company: {
                    ...company,
                    logo: logoUrl,
                    website: normalizedWebsite,
                },
            };
        });
    }

    get itemsByCategory(): VendorTrustCenterItemsByCategoryResponseDto[] {
        if (!this.#vendorTrustCenterItemsQuery.data?.categories) {
            return [];
        }

        return this.#vendorTrustCenterItemsQuery.data.categories.map(
            (category) => {
                return {
                    ...category,
                    items: category.items.map((item) => {
                        if (item.type === 'security_grades') {
                            return {
                                ...item,
                                additionalFields: normalizeSecurityGrade(item),
                            };
                        }

                        return {
                            ...item,
                            additionalFields: {
                                ...item.additionalFields,
                                ...(isString(item.additionalFields?.href) && {
                                    href: normalizeWebsiteUrl(
                                        item.additionalFields.href,
                                    ),
                                }),
                            },
                        };
                    }),
                };
            },
        );
    }

    get accessRequest(): VendorTrustCenterAccessRequestResponseDto | null {
        return this.#vendorTrustCenterAccessRequestQuery.data ?? null;
    }

    get accessRequestRequirements(): VendorTrustCenterAccessRequestRequirementsResponseDto | null {
        return (
            this.#vendorTrustCenterAccessRequestRequirementsQuery.data ?? null
        );
    }

    get accessRequestRequirementsUrls(): string[] {
        const fields = isArray(this.accessRequestRequirements?.fields)
            ? this.accessRequestRequirements.fields
            : [];

        const urls: string[] = [];

        for (const field of fields) {
            if (isArray(field.urls)) {
                // Filter out invalid URLs and ensure they're strings
                const validUrls = field.urls.filter((url): url is string => {
                    return isString(url) && !isEmpty(url.trim());
                });

                urls.push(...validUrls);
            }
        }

        // Remove duplicates and return unique URLs
        return [...new Set(urls)];
    }

    get isInfoLoading(): boolean {
        return this.#vendorTrustCenterQuery.isLoading;
    }

    get isOverviewLoading(): boolean {
        return this.#vendorTrustCenterOverviewQuery.isLoading;
    }

    get isCertificationsLoading(): boolean {
        return this.#vendorTrustCenterCertificationsQuery.isLoading;
    }

    get isDocumentsLoading(): boolean {
        return this.#vendorTrustCenterDocumentsQuery.isLoading;
    }

    get isSubprocessorsLoading(): boolean {
        return this.#vendorTrustCenterSubprocessorsQuery.isLoading;
    }

    get isItemsLoading(): boolean {
        return this.#vendorTrustCenterItemsQuery.isLoading;
    }

    get isAccessRequestLoading(): boolean {
        return this.#vendorTrustCenterAccessRequestQuery.isLoading;
    }

    get isAccessRequestRequirementsLoading(): boolean {
        return this.#vendorTrustCenterAccessRequestRequirementsQuery.isLoading;
    }

    get hasAccessRequestRequirementsError(): boolean {
        return this.#vendorTrustCenterAccessRequestRequirementsQuery.hasError;
    }

    get hasVendorTrustCenterAccess(): boolean {
        return sharedFeatureAccessModel.hasVendorProfileTrustCenterAccess;
    }

    get isVendorTrustCenterEnabled(): boolean {
        return !isNil(this.info?.organizationId);
    }

    get isAccessNotRequested(): boolean {
        return isNil(this.accessRequest?.status);
    }

    get isAccessPending(): boolean {
        return this.accessRequest?.status === 'pending';
    }

    get isAccessGranted(): boolean {
        return (
            this.accessRequest?.status === 'granted' && !this.isAccessExpired
        );
    }

    get isAccessDeclined(): boolean {
        return (
            this.accessRequest?.status === 'declined' ||
            this.accessRequest?.status === 'revoked'
        );
    }

    get isAccessExpired(): boolean {
        if (this.accessRequest?.status === 'expired') {
            return true;
        }

        if (
            this.accessRequest?.status === 'granted' &&
            this.accessRequest.expiresAt
        ) {
            return (
                getTimeDiff(
                    new Date(),
                    this.accessRequest.expiresAt,
                    'hours',
                ) <= 0
            );
        }

        return false;
    }

    loadInfo = (vendorId: number) => {
        this.#currentVendorId = vendorId;
        when(
            () => this.hasVendorTrustCenterAccess,
            () => {
                this.#vendorTrustCenterQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadOverview = (vendorId: number) => {
        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterOverviewQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadCertifications = (vendorId: number) => {
        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterCertificationsQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadDocuments = (vendorId: number) => {
        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterDocumentsQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadSubprocessors = (vendorId: number) => {
        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterSubprocessorsQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadItems = (vendorId: number) => {
        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterItemsQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadAccessRequest = (vendorId: number) => {
        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterAccessRequestQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };

    loadAccessRequestRequirements = () => {
        const vendorId = this.#currentVendorId;

        if (isNil(vendorId)) {
            return;
        }

        when(
            () => this.isVendorTrustCenterEnabled,
            () => {
                this.#vendorTrustCenterAccessRequestRequirementsQuery.load({
                    path: { id: vendorId },
                });
            },
        );
    };
}

export const sharedVendorTrustCenterController =
    new VendorTrustCenterController();
