import type { VRM_AGENT_ACTION_MAPPING } from '../constants/vrm-agent-actions-mapping.constants';
import type { VRM_AGENT_REFERENCE_MAPPING } from '../constants/vrm-agent-reference-mapping.constants';

export type VrmAgentReferenceId = keyof typeof VRM_AGENT_REFERENCE_MAPPING;

export type VrmAgentActionId = keyof typeof VRM_AGENT_ACTION_MAPPING;

export type VrmAgentAuditTrailStepStatus = 'IN_PROGRESS' | 'COMPLETED';

/**
 * Individual step in the VRM Agent audit trail.
 */
export interface VrmAgentAuditTrailStep {
    id: string;
    displayName: string;
    status: VrmAgentAuditTrailStepStatus;
    createdAt?: string;
    href?: string;
}
