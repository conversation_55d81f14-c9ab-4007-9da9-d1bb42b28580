/* eslint-disable @typescript-eslint/unbound-method -- Accessing mocked controller methods in tests */
import { beforeEach, describe, expect, test, vi } from 'vitest';
// Import the mocked module to access the mock functions
import { sharedBannerServiceController } from '@controllers/banner-service';
import { VendorsQuestionnaireAddController } from './vendors-questionnaire-add-controller';

// Mock the banner service
vi.mock('@controllers/banner-service', () => ({
    BannerLocation: {
        PAGE_HEADER: 'PAGE_HEADER',
    },
    BannerPersistenceType: {
        TIMER_BASED: 'TIMER_BASED',
    },
    generateBannerId: vi.fn(() => 'test-banner-id'),
    sharedBannerServiceController: {
        addBanner: vi.fn(),
        dismissBanner: vi.fn(),
    },
}));

// Mock other dependencies
vi.mock('@controllers/snackbar', () => ({
    snackbarController: {
        addSnackbar: vi.fn(),
    },
}));

vi.mock('@globals/api-sdk/queries', () => ({
    questionnaireVendorSecurityTypeformControllerCreateVendorSecurityTypeformMutation:
        vi.fn(),
    questionnaireVendorSecurityTypeformControllerQuestionnaireTitleCheckOptions:
        vi.fn(),
    questionnaireVendorSecurityTypeformControllerUpdateVendorSecurityTypeformMutation:
        vi.fn(),
}));

vi.mock('@globals/i18n/macro', () => ({
    t: (strings: TemplateStringsArray) => strings[0],
}));

vi.mock('@models/vendors-questionnaire-add', () => ({
    VendorsQuestionnaireAddModel: class {
        canSubmit = true;
        resetForm = vi.fn();
        formData = { questions: [] };
    },
}));

vi.mock('./vendors-typeform-questionnaires-controller', () => ({
    sharedVendorsTypeformQuestionnairesController: {},
}));

describe('vendorsQuestionnaireAddController - Banner Service Integration', () => {
    let controller: VendorsQuestionnaireAddController;

    beforeEach(() => {
        vi.clearAllMocks();
        controller = new VendorsQuestionnaireAddController();
    });

    describe('showImportBanner', () => {
        test('should call addBanner with correct parameters for success', () => {
            controller.showImportBanner('Success!', {
                severity: 'success',
                body: 'Import completed',
            });

            expect(
                sharedBannerServiceController.addBanner,
            ).toHaveBeenCalledWith({
                id: 'test-banner-id',
                title: 'Success!',
                location: 'PAGE_HEADER',
                severity: 'success',
                body: 'Import completed',
            });

            expect(controller.currentImportBannerId).toBe('test-banner-id');
        });

        test('should call addBanner with correct parameters for warning', () => {
            controller.showImportBanner('Warning!', {
                severity: 'warning',
                body: 'Some issues found',
            });

            expect(
                sharedBannerServiceController.addBanner,
            ).toHaveBeenCalledWith({
                id: 'test-banner-id',
                title: 'Warning!',
                location: 'PAGE_HEADER',
                severity: 'warning',
                body: 'Some issues found',
            });

            expect(controller.currentImportBannerId).toBe('test-banner-id');
        });

        test('should clear existing banner before showing new one', () => {
            // Show first banner
            controller.showImportBanner('First', {
                severity: 'success',
                body: 'First message',
            });
            expect(controller.currentImportBannerId).toBe('test-banner-id');

            // Show second banner - should dismiss first
            controller.showImportBanner('Second', {
                severity: 'warning',
                body: 'Second message',
            });

            expect(
                sharedBannerServiceController.dismissBanner,
            ).toHaveBeenCalledWith('test-banner-id');
            expect(controller.currentImportBannerId).toBe('test-banner-id');
        });
    });

    describe('clearImportBanner', () => {
        test('should dismiss banner and clear ID when banner exists', () => {
            const mockBannerId = 'test-banner-id';

            controller.currentImportBannerId = mockBannerId;

            controller.clearImportBanner();

            expect(
                sharedBannerServiceController.dismissBanner,
            ).toHaveBeenCalledWith(mockBannerId);
            expect(controller.currentImportBannerId).toBeNull();
        });

        test('should do nothing when no banner exists', () => {
            controller.currentImportBannerId = null;

            controller.clearImportBanner();

            expect(
                sharedBannerServiceController.dismissBanner,
            ).not.toHaveBeenCalled();
            expect(controller.currentImportBannerId).toBeNull();
        });
    });

    describe('resetForNewQuestionnaire', () => {
        test('should clear import banner when resetting', () => {
            const mockBannerId = 'test-banner-id';

            controller.currentImportBannerId = mockBannerId;

            controller.resetForNewQuestionnaire();

            expect(
                sharedBannerServiceController.dismissBanner,
            ).toHaveBeenCalledWith(mockBannerId);
            expect(controller.currentImportBannerId).toBeNull();
        });
    });
});
