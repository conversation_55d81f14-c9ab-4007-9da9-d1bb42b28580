import { isEmpty, isNil } from 'lodash-es';
import {
    calculateReviewDeadlineDate,
    EDIT_IMPACT_LEVEL_MODAL_ID,
} from '@components/vendors-security-reviews';
import { modalController } from '@controllers/modal';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    vendorsControllerDeleteVendorMutation,
    vendorsControllerGetVendorOptions,
    vendorsControllerUpdateVendorMutation,
    vendorsControllerUpdateVendorStatusMutation,
} from '@globals/api-sdk/queries';
import type {
    VendorModifyRequestDto,
    VendorRelationshipContactRequestDto,
    VendorResponseDto,
    VendorSecurityReviewResponseDto,
} from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { addDaysToDate, formatDate } from '@helpers/date-time';
import { convertToMinorUnits, extractFieldValue } from '@helpers/formatters';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import type { FormValues } from '@ui/forms';
import type { VendorProfileHeader } from './types/vendor-profile.type';
import { sharedVendorCustomFieldsController } from './vendor-custom-fields.controller';
import { sharedVendorsCurrentController } from './vendors-current-controller';
import { sharedVendorsProspectiveController } from './vendors-prospective-controller';
import { sharedVendorsSecurityReviewDetailsController } from './vendors-security-review-details-controller';
import { sharedVendorsSettingsController } from './vendors-settings-controller';

class VendorsDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorDetailsTemporalValues: VendorResponseDto | null = null;

    vendorDetailsQuery = new ObservedQuery(vendorsControllerGetVendorOptions);

    updateDetailsMutation = new ObservedMutation(
        vendorsControllerUpdateVendorMutation,
        {
            onSuccess: () => {
                this.vendorDetailsQuery.invalidate();
            },
        },
    );

    updateVendorStatusMutation = new ObservedMutation(
        vendorsControllerUpdateVendorStatusMutation,
    );

    deleteVendorMutation = new ObservedMutation(
        vendorsControllerDeleteVendorMutation,
    );

    get vendorDetails(): VendorResponseDto | null {
        return this.vendorDetailsQuery.data;
    }

    get isProspectiveVendor(): boolean {
        return this.vendorDetails?.status === 'PROSPECTIVE';
    }

    get isLoading(): boolean {
        return this.vendorDetailsQuery.isLoading;
    }

    get isSaving(): boolean {
        return this.updateDetailsMutation.isPending;
    }

    get isUpdatingVendor(): boolean {
        return this.isLoading || this.isSaving;
    }

    get hasErrorVendorUpdate(): boolean {
        return this.updateDetailsMutation.hasError;
    }

    get profileHeaderData(): VendorProfileHeader | null {
        const { data } = this.vendorDetailsQuery;

        if (!data) {
            return null;
        }

        const { vendorSettings } = sharedVendorsSettingsController;

        const {
            name,
            logoUrl,
            category,
            impactLevel,
            risk,
            riskCount,
            latestSecurityReviews,
            status,
            renewalDateStatus,
            renewalDate,
        } = data;
        const showSecurityReviewsInfoBanner =
            latestSecurityReviews?.some(
                (item: VendorSecurityReviewResponseDto) =>
                    item.status === 'IN_PROGRESS',
            ) ?? false;

        const buildSecurityReviewWindow = (): string | undefined => {
            const { renewalScheduleType } = data;

            if (renewalScheduleType === 'NONE') {
                return undefined;
            }

            if (
                !isNil(renewalScheduleType) &&
                renewalScheduleType !== 'CUSTOM' &&
                vendorSettings?.defaultReviewPeriod
            ) {
                const renewalDeadlineDate =
                    calculateReviewDeadlineDate(renewalScheduleType);

                const reviewStartDate = addDaysToDate(
                    renewalDeadlineDate,
                    -vendorSettings.defaultReviewPeriod,
                );

                const reviewStartFormatted = formatDate(
                    'field',
                    reviewStartDate,
                );
                const renewalDeadlineFormatted = formatDate(
                    'field',
                    renewalDeadlineDate,
                );

                return `${reviewStartFormatted} to ${renewalDeadlineFormatted}`;
            }

            if (
                renewalDateStatus !== 'NO_RENEWAL' &&
                renewalDate &&
                vendorSettings
            ) {
                const renewalFormattedDate = formatDate(
                    'field',
                    addDaysToDate(
                        renewalDate,
                        -vendorSettings.defaultReviewPeriod,
                    ),
                );

                const formattedDate = formatDate('field', renewalDate);

                return `${renewalFormattedDate} to ${formattedDate}`;
            }

            return undefined;
        };

        const currentRiskCount =
            status === 'PROSPECTIVE' ? undefined : (riskCount ?? 0);

        return {
            name,
            logoUrl: logoUrl ?? '',
            showSecurityReviewsInfoBanner,
            businessUnit: category,
            impactLevel,
            risk,
            associatedRisks: currentRiskCount,
            securityReviewWindow: buildSecurityReviewWindow(),
        };
    }

    loadVendorDetails = (vendorId: number) => {
        this.vendorDetailsQuery.load({
            path: { id: vendorId },
        });

        sharedVendorCustomFieldsController.loadList();
        sharedVendorCustomFieldsController.loadByVendorId(vendorId);

        when(
            () => !this.isLoading,
            () => {
                this.vendorDetailsTemporalValues = this.vendorDetailsQuery.data;
            },
        );
    };

    updateVendorDetails = (
        vendorId: number,
        baseValues: FormValues,
        options?: {
            isFromSecurityReviewModal?: boolean;
            allFormValues?: FormValues;
        },
    ) => {
        const currentVendor = this.vendorDetails;

        if (!currentVendor) {
            return;
        }

        const baseVendorData = {
            ...currentVendor,
            status: currentVendor.status,
            location: currentVendor.location,
            operationalImpact: currentVendor.operationalImpact,
            environmentAccess: currentVendor.environmentAccess,
            impactLevel: currentVendor.impactLevel,
            renewalScheduleType: currentVendor.renewalScheduleType,
            renewalDate: currentVendor.renewalDate ?? undefined,
            passwordPolicy: currentVendor.passwordPolicy,
            type: currentVendor.type,
            category: currentVendor.category,
            url: currentVendor.url === '' ? null : currentVendor.url,
            integrations:
                currentVendor.integrations?.map(
                    (integration) => integration.id,
                ) ?? [],
            userId: currentVendor.user?.id ?? null,
            contact: currentVendor.contact
                ? { id: currentVendor.contact.id }
                : null,
            notes: currentVendor.notes,
        };

        const passwordPolicyGroup = baseValues.passwordPolicyGroup as
            | FormValues
            | undefined;
        const passwordPolicyValue = passwordPolicyGroup?.passwordPolicy as
            | ListBoxItemData
            | undefined;
        const passwordMinLengthValue = passwordPolicyGroup?.passwordMinLength as
            | ListBoxItemData
            | undefined;

        const integrationsValue = baseValues.integrations as
            | ListBoxItemData[]
            | undefined;
        let transformedIntegrations: number[];

        if ('integrations' in baseValues) {
            // Field provided: allow clearing (empty array) or setting new values
            transformedIntegrations =
                integrationsValue?.map((integration) =>
                    Number(integration.value),
                ) ?? [];
        } else {
            // Field not provided: preserve existing integrations
            transformedIntegrations = baseVendorData.integrations;
        }

        const userValue = baseValues.user as ListBoxItemData | undefined;
        let transformedUserId: number | null | undefined;

        if ('user' in baseValues) {
            // Field provided: allow clearing (null) or setting new value
            transformedUserId = userValue ? Number(userValue.value) : null;
        } else {
            // Field not provided: preserve existing value
            transformedUserId = baseVendorData.userId;
        }

        const contactValue = baseValues.contact as ListBoxItemData | undefined;
        let transformedContact:
            | VendorRelationshipContactRequestDto
            | null
            | undefined;

        if ('contact' in baseValues) {
            // Field provided: allow clearing (null) or setting new value
            transformedContact = contactValue
                ? { id: Number(contactValue.value) }
                : null;
        } else {
            // Field not provided: preserve existing value
            transformedContact = baseVendorData.contact;
        }

        // Extract and transform ListBoxItemData values for fields that might contain React elements
        const categoryValue = extractFieldValue(baseValues.category);
        const riskValue = extractFieldValue(baseValues.risk);
        const statusValue = extractFieldValue(baseValues.status);
        const typeValue = extractFieldValue(baseValues.type);
        const impactLevelValue = extractFieldValue(baseValues.impactLevel);

        const baseData = {
            ...baseVendorData,
            ...baseValues,
            ...passwordPolicyGroup,
            category:
                'category' in baseValues
                    ? (categoryValue ?? baseVendorData.category)
                    : baseVendorData.category,
            risk:
                'risk' in baseValues
                    ? (riskValue ?? baseVendorData.risk)
                    : baseVendorData.risk,
            status:
                'status' in baseValues
                    ? (statusValue ?? baseVendorData.status)
                    : baseVendorData.status,
            type:
                'type' in baseValues
                    ? (typeValue ?? null)
                    : baseVendorData.type,
            impactLevel:
                'impactLevel' in baseValues
                    ? (impactLevelValue ?? baseVendorData.impactLevel)
                    : baseVendorData.impactLevel,
            passwordPolicy:
                passwordPolicyValue?.value ?? baseVendorData.passwordPolicy,
            passwordMinLength: passwordMinLengthValue
                ? Number(passwordMinLengthValue.value)
                : baseVendorData.passwordMinLength,
            integrations: transformedIntegrations,
            userId: transformedUserId,
            contact: transformedContact,
        };

        const apiPayload = {
            ...baseData,
            notes: baseData.notes,
        } as VendorModifyRequestDto;

        this.updateDetailsMutation.mutate({
            path: { id: vendorId },
            body: apiPayload,
        });

        when(
            () => !this.updateDetailsMutation.isPending,
            () => {
                if (this.updateDetailsMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'vendor-data-update-error',
                        props: {
                            title: t`An error occurred while saving the vendor data.`,
                            description: t`Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (sharedFeatureAccessModel.isCustomFieldsEnabled) {
                    const vendorDetailsCustomFields =
                        sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                            options?.allFormValues ?? baseValues,
                            sharedVendorCustomFieldsController.vendorDetailsCustomFieldsList,
                        );

                    const internalDetailsCustomFields =
                        sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                            options?.allFormValues ?? baseValues,
                            sharedVendorCustomFieldsController.vendorInternalDetailsCustomFieldsList,
                        );

                    sharedVendorCustomFieldsController.vendorCustomFieldsSubmission(
                        vendorId,
                        vendorDetailsCustomFields,
                        internalDetailsCustomFields,
                    );
                }

                if (options?.isFromSecurityReviewModal) {
                    // Close Impact Level modal in Security Review completed view in prospective vendors
                    modalController.closeModal(EDIT_IMPACT_LEVEL_MODAL_ID);
                    sharedVendorsSecurityReviewDetailsController.securityReviewDetailsQuery.invalidate();
                }

                snackbarController.addSnackbar({
                    id: 'vendor-data-updated-successfully',
                    props: {
                        title: t`Vendor Updated.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    updateBaseDetails = (vendorId: number, formValues: FormValues) => {
        const passwordPolicyGroup = formValues.passwordPolicyGroup as
            | FormValues
            | undefined;
        const passwordPolicyValue = passwordPolicyGroup?.passwordPolicy as
            | ListBoxItemData
            | undefined;

        const contactsEmailValue = this.getContactsEmailValue(formValues);

        const newValues: FormValues = {
            ...formValues,
            passwordPolicy: passwordPolicyValue?.value ?? 'NONE',
            passwordPolicyGroup: {
                passwordPolicy: passwordPolicyValue,
                passwordRequiresMinLength:
                    passwordPolicyGroup?.passwordRequiresMinLength ?? false,
                passwordMinLength: passwordPolicyGroup?.passwordMinLength,
                passwordRequiresNumber:
                    passwordPolicyGroup?.passwordRequiresNumber ?? false,
                passwordRequiresSymbol:
                    passwordPolicyGroup?.passwordRequiresSymbol ?? false,
                passwordMfaEnabled:
                    passwordPolicyGroup?.passwordMfaEnabled ?? false,
            },
            url: this.getUrlValue(formValues),
            servicesProvided: this.getServicesProvidedValue(formValues),
            privacyUrl: this.getPrivacyUrlValue(formValues),
            trustCenterUrl: this.getTrustCenterUrlValue(formValues),
            termsUrl: this.getTermsUrlValue(formValues),
            contactAtVendor: this.getContactAtVendorValue(formValues),
            contactsEmail: isNil(contactsEmailValue)
                ? undefined
                : contactsEmailValue.trim(),
        };

        this.updateVendorDetails(vendorId, newValues, {
            allFormValues: formValues,
        });
    };

    updateImpactAssessmentDetails = (
        vendorId: number,
        currentState: VendorResponseDto,
        formValues: FormValues,
        options?: {
            isFromSecurityReviewModal?: boolean;
        },
    ) => {
        const impactLevelValue = formValues.impactLevel as
            | ListBoxItemData
            | undefined;

        const newValues: FormValues = {
            ...formValues,
            impactLevel: impactLevelValue?.value ?? undefined,
            dataAccessedOrProcessedList: isEmpty(
                formValues.dataAccessedOrProcessedList,
            )
                ? undefined
                : formValues.dataAccessedOrProcessedList,
            operationalImpact: isEmpty(formValues.operationalImpact)
                ? undefined
                : formValues.operationalImpact,
            environmentAccess: isEmpty(formValues.environmentAccess)
                ? undefined
                : formValues.environmentAccess,
            passwordMinLength: currentState.passwordMinLength,
        };

        this.updateVendorDetails(vendorId, newValues, options);
    };

    updateInternalDetails = (
        vendorId: number,
        currentState: VendorResponseDto,
        formValues: FormValues,
    ) => {
        const categoryValue = extractFieldValue(formValues.category);
        const riskValue = extractFieldValue(formValues.risk);
        const statusValue = extractFieldValue(formValues.status);
        const typeValue = extractFieldValue(formValues.type);
        const impactLevelValue = extractFieldValue(formValues.impactLevel);

        const userValue = formValues.user as ListBoxItemData | undefined;
        const contactValue = formValues.contact as ListBoxItemData | undefined;

        const newValues: FormValues = {
            category:
                'category' in formValues
                    ? (categoryValue ?? undefined)
                    : undefined,
            risk: 'risk' in formValues ? (riskValue ?? undefined) : undefined,
            status:
                'status' in formValues ? (statusValue ?? undefined) : undefined,
            type:
                'type' in formValues ? (typeValue ?? null) : currentState.type,
            impactLevel:
                'impactLevel' in formValues
                    ? (impactLevelValue ?? undefined)
                    : currentState.impactLevel,
            dataStored: this.getDataStoredValue(formValues),
            notes: this.getNotesValue(formValues),
            cost: this.getCostValue(formValues),
            hasPii: formValues.hasPii,
            isSubProcessor: formValues.isSubProcessor,
            location: formValues.location,
            ...('integrations' in formValues && {
                integrations: formValues.integrations,
            }),
            ...('user' in formValues && {
                user: userValue
                    ? { value: userValue.value, label: userValue.label }
                    : null,
            }),
            ...('contact' in formValues && {
                contact: contactValue
                    ? { value: contactValue.value, label: contactValue.label }
                    : null,
            }),
        };

        this.updateVendorDetails(vendorId, newValues, {
            allFormValues: formValues,
        });
    };

    deleteVendor = (vendorId: number, isProspective?: boolean): void => {
        if (!vendorId) {
            return;
        }

        this.deleteVendorMutation.mutate({ path: { id: vendorId } });
        when(
            () => !this.deleteVendorMutation.isPending,
            () => {
                if (this.deleteVendorMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'vendor-deletion-error',
                        props: {
                            title: t`An error occurred while deleting the vendor.`,
                            description: t`Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (isProspective) {
                    sharedVendorsProspectiveController.allVendorsProspectiveQuery.invalidate();
                } else {
                    sharedVendorsCurrentController.allVendorsCurrentQuery.invalidate();
                }
                modalController.closeModal('delete-vendor-modal');

                snackbarController.addSnackbar({
                    id: 'vendor-deleted-successfully',
                    hasTimeout: true,
                    props: {
                        title: t`Vendor deleted successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    archiveVendor = (vendorId: number, isProspective?: boolean): void => {
        if (!vendorId) {
            return;
        }

        this.updateVendorStatusMutation.mutate({
            path: { id: vendorId },
            body: {
                vendorStatus: 'ARCHIVED',
            },
        });
        when(
            () => !this.updateVendorStatusMutation.isPending,
            () => {
                if (this.updateVendorStatusMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'vendor-archive-error',
                        props: {
                            title: t`An error occurred while archiving the vendor.`,
                            description: t`Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Invalidate queries to refresh data
                this.vendorDetailsQuery.invalidate();

                // Invalidate the appropriate vendor list based on vendor type
                const isVendorProspective =
                    isProspective ?? this.isProspectiveVendor;

                if (isVendorProspective) {
                    sharedVendorsProspectiveController.allVendorsProspectiveQuery.invalidate();
                } else {
                    sharedVendorsCurrentController.allVendorsCurrentQuery.invalidate();
                }

                // Close the modal
                modalController.closeModal('archive-vendor-modal');

                // Show success message
                snackbarController.addSnackbar({
                    id: 'vendor-archived-successfully',
                    hasTimeout: true,
                    props: {
                        title: t`Vendor archived successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    archiveVendorAndRedirect = (
        vendorId: number,
        redirectPath: string,
    ): void => {
        if (!vendorId) {
            return;
        }

        this.archiveVendor(vendorId, false);

        when(
            () => !this.updateVendorStatusMutation.isPending,
            () => {
                if (!this.updateVendorStatusMutation.hasError) {
                    sharedProgrammaticNavigationController.navigateTo(
                        redirectPath,
                    );
                }
            },
        );
    };

    private getDataStoredValue(formValues: FormValues): string | null {
        if (!('dataStored' in formValues)) {
            return this.vendorDetails?.dataStored ?? null;
        }
        if (formValues.dataStored === '' || formValues.dataStored === null) {
            return null;
        }

        return formValues.dataStored as string;
    }

    private getNotesValue(formValues: FormValues): string | null {
        if (!('notes' in formValues)) {
            return this.vendorDetails?.notes ?? null;
        }
        if (formValues.notes === '' || formValues.notes === null) {
            return null;
        }

        return formValues.notes as string;
    }

    private getCostValue(formValues: FormValues): string | null {
        if (!('cost' in formValues)) {
            return this.vendorDetails?.cost ?? null;
        }
        const cost = formValues.cost as string;

        if (cost === '' || isNaN(Number(cost))) {
            return null;
        }

        return convertToMinorUnits(formValues.cost as string);
    }

    getUrlValue(formValues: FormValues): string | null {
        if (!('url' in formValues)) {
            return this.vendorDetails?.url ?? null;
        }
        if (formValues.url === '' || formValues.url === null) {
            return null;
        }

        return formValues.url as string;
    }

    private getServicesProvidedValue(formValues: FormValues): string | null {
        if (!('servicesProvided' in formValues)) {
            return this.vendorDetails?.servicesProvided ?? null;
        }
        if (
            formValues.servicesProvided === '' ||
            formValues.servicesProvided === null
        ) {
            return null;
        }

        return formValues.servicesProvided as string;
    }

    private getPrivacyUrlValue(formValues: FormValues): string | null {
        if (!('privacyUrl' in formValues)) {
            return this.vendorDetails?.privacyUrl ?? null;
        }
        if (formValues.privacyUrl === '' || formValues.privacyUrl === null) {
            return null;
        }

        return formValues.privacyUrl as string;
    }

    private getTermsUrlValue(formValues: FormValues): string | null {
        if (!('termsUrl' in formValues)) {
            return this.vendorDetails?.termsUrl ?? null;
        }
        if (formValues.termsUrl === '' || formValues.termsUrl === null) {
            return null;
        }

        return formValues.termsUrl as string;
    }

    private getTrustCenterUrlValue(formValues: FormValues): string | null {
        if (!('trustCenterUrl' in formValues)) {
            return this.vendorDetails?.trustCenterUrl ?? null;
        }
        if (
            formValues.trustCenterUrl === '' ||
            formValues.trustCenterUrl === null
        ) {
            return null;
        }

        return formValues.trustCenterUrl as string;
    }

    private getContactAtVendorValue(formValues: FormValues): string | null {
        if (!('contactAtVendor' in formValues)) {
            return this.vendorDetails?.contactAtVendor ?? null;
        }
        if (
            formValues.contactAtVendor === '' ||
            formValues.contactAtVendor === null
        ) {
            return null;
        }

        return formValues.contactAtVendor as string;
    }

    private getContactsEmailValue(formValues: FormValues): string | null {
        if (!('contactsEmail' in formValues)) {
            return this.vendorDetails?.contactsEmail ?? null;
        }
        if (
            formValues.contactsEmail === '' ||
            formValues.contactsEmail === null
        ) {
            return null;
        }

        return formValues.contactsEmail as string;
    }

    restoreVendor = (vendorId: number, isProspective?: boolean): void => {
        if (!vendorId) {
            return;
        }

        this.updateVendorStatusMutation.mutate({
            path: { id: vendorId },
            body: {
                vendorStatus: 'ACTIVE',
            },
        });
        when(
            () => !this.updateVendorStatusMutation.isPending,
            () => {
                if (this.updateVendorStatusMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'vendor-restore-error',
                        props: {
                            title: t`An error occurred while restoring the vendor.`,
                            description: t`Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Invalidate queries to refresh data
                this.vendorDetailsQuery.invalidate();

                // Invalidate the appropriate vendor list based on vendor type
                const isVendorProspective =
                    isProspective ?? this.isProspectiveVendor;

                if (isVendorProspective) {
                    sharedVendorsProspectiveController.allVendorsProspectiveQuery.invalidate();
                } else {
                    sharedVendorsCurrentController.allVendorsCurrentQuery.invalidate();
                }

                // Show success message
                snackbarController.addSnackbar({
                    id: 'vendor-restored-successfully',
                    hasTimeout: true,
                    props: {
                        title: t`Vendor restored successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedVendorsDetailsController = new VendorsDetailsController();
