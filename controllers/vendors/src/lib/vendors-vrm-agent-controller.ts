import { isEmpty, isError } from 'lodash-es';
import {
    type UtilitiesVrmAgentMessageAction,
    type UtilitiesVrmAgentMessageData,
    VRM_AGENT_MOCK_MESSAGES,
} from '@components/utilities';
import { routeController } from '@controllers/route';
import { makeAutoObservable } from '@globals/mobx';
import { VRM_AGENT_REFERENCE_MAPPING } from './constants/vrm-agent-reference-mapping.constants';
import type {
    VrmAgentAuditTrailStep,
    VrmAgentReferenceId,
} from './types/vendor-vrm-agent.types';
import { sharedVendorVrmAgentActionsController } from './vendor-vrm-agent-actions.controller';
import { sharedVendorsSecurityReviewDocumentsController } from './vendors-security-review-documents-controller';

class VendorsVrmAgentController {
    currentVendorId: string | null = null;

    #actionsController = sharedVendorVrmAgentActionsController;

    constructor() {
        makeAutoObservable(this);
    }

    assessment: {
        id: string;
        createdAt: string;
        messages: UtilitiesVrmAgentMessageData[];
        canFinalize: boolean;
    } | null = null;
    isLoadingMock = false;
    isExecutingActionState = false;
    error: string | null = null;

    auditTrailSteps: VrmAgentAuditTrailStep[] = [];

    get assessmentMessages(): UtilitiesVrmAgentMessageData[] {
        // If there are no assessment messages (empty state), return the empty state message
        if (!this.assessment?.messages || isEmpty(this.assessment.messages)) {
            return [this.emptyStateMessage];
        }

        return this.assessment.messages;
    }

    get emptyStateMessage(): UtilitiesVrmAgentMessageData {
        return {
            id: 'vrm-empty-state-001',
            caller: 'AGENT',
            title: null,
            body: [
                {
                    id: 'vrm-empty-body-001',
                    text: `I can help you conduct a detailed assessment and provide a report of their security posture.`,
                },
            ],
            actions: [
                {
                    id: 'vrm-empty-action-001',
                    type: 'action',
                    text: 'Start review',
                    action: 'VendorAssessmentStart',
                },
            ],
        };
    }

    get isLoading(): boolean {
        return this.isLoadingMock;
    }

    get hasAgentWorkflow(): boolean {
        // Remove mock once backend logic is ready
        return true;
    }

    get hasAssessmentMessages(): boolean {
        // Always return true since we now show empty state when no messages exist
        return true;
    }

    get isExecutingAction(): boolean {
        return this.isExecutingActionState;
    }

    /**
     * Checks if there are security review documents that were created or updated after the assessment creation date.
     * This determines if the re-run assessment button should be available.
     */
    hasNewerSecurityReviewDocuments = (): boolean => {
        if (!this.assessment?.createdAt) {
            return false;
        }

        const { allSecurityReviewDocuments } =
            sharedVendorsSecurityReviewDocumentsController;

        if (isEmpty(allSecurityReviewDocuments)) {
            return false;
        }

        const reviewCreationTime = new Date(
            this.assessment.createdAt,
        ).getTime();

        return allSecurityReviewDocuments.some((securityReviewDocument) => {
            if (securityReviewDocument.vendorDocument) {
                const document = securityReviewDocument.vendorDocument;
                const documentCreatedTime = new Date(
                    document.createdAt,
                ).getTime();
                const documentUpdatedTime = new Date(
                    document.updatedAt,
                ).getTime();

                return (
                    documentCreatedTime > reviewCreationTime ||
                    documentUpdatedTime > reviewCreationTime
                );
            }

            return false;
        });
    };

    handleActionClick = (action: UtilitiesVrmAgentMessageAction): void => {
        switch (action.type) {
            case 'button':
            case 'action': {
                this.executeAction(action.action);
                break;
            }
            case 'link': {
                break;
            }
            default: {
                console.warn(`Unknown action type: ${action.type}`);
                break;
            }
        }
    };

    executeAction = (actionId: string): void => {
        if (!this.currentVendorId) {
            console.warn('Cannot execute action: no vendor ID set');

            return;
        }

        this.isExecutingActionState = true;

        try {
            this.#actionsController.executeAction(
                actionId,
                this.currentVendorId,
            );
        } catch (error) {
            console.error('Failed to execute action:', error);
        } finally {
            this.isExecutingActionState = false;
        }
    };

    resolveReferenceToUrl = (refId: string): string | null => {
        if (!this.currentVendorId) {
            return null;
        }

        if (!(refId in VRM_AGENT_REFERENCE_MAPPING)) {
            return null;
        }

        const routePattern =
            VRM_AGENT_REFERENCE_MAPPING[refId as VrmAgentReferenceId];

        const route = routePattern.replace(':vendorId', this.currentVendorId);

        return `${routeController.userPartOfUrl}/${route}`;
    };

    processItemsWithReferences = <
        T extends { ref?: string } | { action: string; type: string },
    >(
        items: T[],
    ): T[] => {
        return items.map((item) => {
            // Handle actions with type 'link'
            if ('action' in item && 'type' in item && item.type === 'link') {
                const resolvedUrl = this.resolveReferenceToUrl(item.action);

                return {
                    ...item,
                    action: resolvedUrl || item.action,
                };
            }

            // Handle items with ref property
            if ('ref' in item && item.ref) {
                const resolvedUrl = this.resolveReferenceToUrl(item.ref);

                return {
                    ...item,
                    ref: resolvedUrl || item.ref,
                };
            }

            return item;
        });
    };

    loadAssessment = (vendorId: string): void => {
        if (!vendorId) {
            this.error = 'Vendor ID is required';

            return;
        }

        this.currentVendorId = vendorId;
        this.isLoadingMock = true;
        this.error = null;
        this.assessment = null;

        try {
            // For now, use mock data
            this.loadMockData();
        } catch (error) {
            this.error = isError(error) ? error.message : 'Unknown error';
            this.assessment = null;
        } finally {
            this.isLoadingMock = false;
        }
    };

    loadMockData = (): void => {
        this.isLoadingMock = true;
        const rawMessages = VRM_AGENT_MOCK_MESSAGES;

        const processedMessages = rawMessages.map((message) => ({
            ...message,
            title: message.title
                ? this.processItemsWithReferences(message.title)
                : message.title,
            body: message.body
                ? this.processItemsWithReferences(message.body)
                : message.body,
            actions: this.processItemsWithReferences(message.actions ?? []),
        }));

        this.assessment = {
            id: 'mock-assessment-123',
            createdAt: '2025-01-01T10:00:00Z',
            messages: processedMessages,
            canFinalize: true,
        };

        this.isLoadingMock = false;

        this.loadMockAuditTrail();
    };

    loadMockAuditTrail = (): void => {
        this.auditTrailSteps = [
            {
                id: 'security-review-questionnaire',
                displayName: 'Security review questionnaire',
                status: 'COMPLETED',
                createdAt: 'August 10, 2024 at 10:23',
                href:
                    this.resolveReferenceToUrl('VendorsDocuments') || undefined,
            },
            {
                id: 'documents-collected',
                displayName: 'Documents collected from SB Trust Center',
                status: 'COMPLETED',
                createdAt: 'August 10, 2024 at 10:23',
                href:
                    this.resolveReferenceToUrl('VendorsDocuments') || undefined,
            },
            {
                id: 'criteria-assessment-results',
                displayName: 'Criteria assessment results',
                status: 'COMPLETED',
                createdAt: 'August 10, 2024 at 10:23',
                href: undefined,
            },
            {
                id: 'follow-up-questionnaire',
                displayName: 'Follow-up questionnaire',
                status: 'IN_PROGRESS',
                createdAt: undefined,
            },
        ];
    };
}

export const sharedVendorsVrmAgentController = new VendorsVrmAgentController();
