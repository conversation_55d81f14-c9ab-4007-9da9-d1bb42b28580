import { describe, expect, test } from 'vitest';
import type { FormValues } from '@ui/forms';
import { mergeCustomFieldsIntoValues } from './form-custom-fields.helper';

describe('mergeCustomFieldsIntoValues', () => {
    test('merges only keys starting with customField_', () => {
        const formValues: FormValues = {
            name: 'Acme',
            customField_1: 'one',
            customField_2: 2,
            url: 'https://acme.test',
        } as unknown as FormValues;

        const target: FormValues = {
            name: 'Acme',
        } as unknown as FormValues;

        const result = mergeCustomFieldsIntoValues(formValues, target);

        // Mutation expected
        expect(result).toStrictEqual(target);

        // Only custom fields were added
        expect((target as Record<string, unknown>).customField_1).toBe('one');
        expect((target as Record<string, unknown>).customField_2).toBe(2);
        // Non-custom keys not touched
        expect((target as Record<string, unknown>).url).toBeUndefined();
        expect((target as Record<string, unknown>).name).toBe('Acme');
    });

    test('overwrites existing custom field values on target', () => {
        const formValues: FormValues = {
            customField_1: 'new',
        } as unknown as FormValues;

        const target: FormValues = {
            customField_1: 'old',
        } as unknown as FormValues;

        mergeCustomFieldsIntoValues(formValues, target);

        expect((target as Record<string, unknown>).customField_1).toBe('new');
    });

    test('supports object values (e.g. list-box items)', () => {
        const formValues: FormValues = {
            customField_select: { id: '1', label: 'One', value: '1' },
        } as unknown as FormValues;

        const target: FormValues = {} as unknown as FormValues;

        mergeCustomFieldsIntoValues(formValues, target);

        expect(
            (target as Record<string, unknown>).customField_select,
        ).toStrictEqual({
            id: '1',
            label: 'One',
            value: '1',
        });
    });
});
