import type { FormValues } from '@ui/forms';

/**
 * Merge all customField_* entries from formValues into the provided target object.
 * Mutates and also returns the target for convenience.
 */
export function mergeCustomFieldsIntoValues(
    formValues: FormValues,
    target: FormValues,
): FormValues {
    Object.entries(formValues)
        .filter(([key]) => key.startsWith('customField_'))
        .forEach(([key, value]) => {
            (target as Record<string, unknown>)[key] = value;
        });

    return target;
}
