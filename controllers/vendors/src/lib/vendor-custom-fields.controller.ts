import { isEmpty } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    vendorsCustomFieldsControllerCreateCustomFieldMutation,
    vendorsCustomFieldsControllerGetCustomFieldsListOptions,
    vendorsCustomFieldsControllerGetVendorsSubmissionsByVendorIdOptions,
} from '@globals/api-sdk/queries';
import type {
    CustomFieldsSubmissionResponseDto,
    CustomFieldSubmissionRequestDto,
    CustomFieldSubmissionsRequestDto,
} from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';

export class VendorCustomFieldsController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorCustomFieldsSubmissionsQuery = new ObservedQuery(
        vendorsCustomFieldsControllerGetVendorsSubmissionsByVendorIdOptions,
    );

    vendorCustomFieldsListQuery = new ObservedQuery(
        vendorsCustomFieldsControllerGetCustomFieldsListOptions,
    );

    createCustomFieldMutation = new ObservedMutation(
        vendorsCustomFieldsControllerCreateCustomFieldMutation,
    );

    loadByVendorId = (vendorId: number): void => {
        when(
            () => !sharedFeatureAccessModel.isLoading,
            () => {
                if (sharedFeatureAccessModel.isCustomFieldsEnabled) {
                    this.vendorCustomFieldsSubmissionsQuery.load({
                        path: { vendorId },
                    });
                }
            },
        );
    };

    loadList = (): void => {
        when(
            () => !sharedFeatureAccessModel.isLoading,
            () => {
                if (sharedFeatureAccessModel.isCustomFieldsEnabled) {
                    this.vendorCustomFieldsListQuery.load();
                }
            },
        );
    };

    get vendorCustomFieldsByVendorId(): CustomFieldsSubmissionResponseDto[] {
        return (
            this.vendorCustomFieldsSubmissionsQuery.data?.data[0]
                ?.customFields ?? []
        );
    }

    get vendorCustomFieldsList(): CustomFieldsSubmissionResponseDto[] {
        return (
            this.vendorCustomFieldsListQuery.data?.data[0]?.customFields ?? []
        );
    }

    get vendorDetailsCustomFieldsByVendorId(): CustomFieldsSubmissionResponseDto[] {
        const vendorDetailsSection =
            this.vendorCustomFieldsSubmissionsQuery.data?.data.find(
                (section) => section.section === 'VENDOR_DETAILS',
            );

        return vendorDetailsSection?.customFields ?? [];
    }

    get vendorInternalDetailsCustomFieldsByVendorId(): CustomFieldsSubmissionResponseDto[] {
        const internalDetailsSection =
            this.vendorCustomFieldsSubmissionsQuery.data?.data.find(
                (section) => section.section === 'VENDOR_INTERNAL_DETAILS',
            );

        return internalDetailsSection?.customFields ?? [];
    }

    get vendorDetailsCustomFieldsList(): CustomFieldsSubmissionResponseDto[] {
        const vendorDetailsSection =
            this.vendorCustomFieldsListQuery.data?.data.find(
                (section) => section.section === 'VENDOR_DETAILS',
            );

        return vendorDetailsSection?.customFields ?? [];
    }

    get vendorInternalDetailsCustomFieldsList(): CustomFieldsSubmissionResponseDto[] {
        const internalDetailsSection =
            this.vendorCustomFieldsListQuery.data?.data.find(
                (section) => section.section === 'VENDOR_INTERNAL_DETAILS',
            );

        return internalDetailsSection?.customFields ?? [];
    }

    get isLoadingByVendorId(): boolean {
        return this.vendorCustomFieldsSubmissionsQuery.isLoading;
    }

    get isLoadingList(): boolean {
        return this.vendorCustomFieldsListQuery.isLoading;
    }

    get isSubmittingCustomFields(): boolean {
        return this.createCustomFieldMutation.isPending;
    }

    get hasSubmissionError(): boolean {
        return this.createCustomFieldMutation.hasError;
    }

    vendorCustomFieldsSubmission = (
        vendorId: number,
        vendorDetailsCustomFields: Record<string, unknown> | null,
        internalDetailsCustomFields: Record<string, unknown> | null,
    ): void => {
        const vendorDetailsSubmissions = Array.isArray(
            vendorDetailsCustomFields?.customFieldSubmissions,
        )
            ? vendorDetailsCustomFields.customFieldSubmissions
            : [];
        const internalDetailsSubmissions = Array.isArray(
            internalDetailsCustomFields?.customFieldSubmissions,
        )
            ? internalDetailsCustomFields.customFieldSubmissions
            : [];

        const allCustomFields = [
            ...vendorDetailsSubmissions,
            ...internalDetailsSubmissions,
        ].filter(
            (field) =>
                !isEmpty((field as CustomFieldSubmissionRequestDto).value),
        );

        if (!isEmpty(allCustomFields)) {
            this.submitCustomFields(vendorId, {
                customFieldSubmissions: allCustomFields,
            });
        }
    };

    submitCustomFields = (
        vendorId: number,
        customFieldSubmissions: CustomFieldSubmissionsRequestDto,
    ): void => {
        if (!sharedFeatureAccessModel.isCustomFieldsEnabled) {
            return;
        }

        this.createCustomFieldMutation.mutate({
            path: { vendorId },
            body: customFieldSubmissions,
        });

        when(
            () => !this.isSubmittingCustomFields,
            () => {
                if (this.hasSubmissionError) {
                    snackbarController.addSnackbar({
                        id: `vendor-custom-fields-submission-error-${vendorId}`,
                        props: {
                            title: t`Failed to submit custom fields`,
                            description: t`An error occurred while submitting the custom fields. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    this.vendorCustomFieldsSubmissionsQuery.invalidate();
                }
            },
        );
    };
}

export const sharedVendorCustomFieldsController =
    new VendorCustomFieldsController();
