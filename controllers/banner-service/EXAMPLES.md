# Banner Service Examples

## Programmatic banner

```ts
import { sharedBannerServiceController, BannerPersistenceType, BannerLocation } from '@controllers/banner-service';

sharedBannerServiceController.addBanner({
  title: 'Welcome to your workspace',
  location: BannerLocation.APP_HEADER,
  persistenceType: BannerPersistenceType.PERSISTENT,
});
```

## Route banner function

```ts
export const clientLoader = () => {
  return {
    banners: () => [
      { title: 'Compliance tools available', location: 'PAGE_HEADER' },
    ],
  };
};
```

## ROUTE_SCOPED programmatic banner (auto-clears on navigation)

```ts
sharedBannerServiceController.addBanner({
  title: 'Only for this route',
  location: BannerLocation.PAGE_HEADER,
  persistenceType: BannerPersistenceType.ROUTE_SCOPED,
  routePattern: '/workspaces/*/compliance*',
});
```

## SESSION_SCOPED banner (clears when workspace changes)

```ts
sharedBannerServiceController.addBanner({
  title: 'Session banner for this workspace',
  location: BannerLocation.PAGE_HEADER,
  persistenceType: BannerPersistenceType.SESSION_SCOPED,
});
```

