# Banner Service

Centralized banner management for displaying contextual messages across the Multiverse application. Replaces one-off banner components with a shared, reactive service that handles positioning, timing, and cleanup automatically.


> Quick-start first. Historical notes are removed or moved to the bottom. Follow these steps to add banners today.


## ✅ Do / ❌ Don’t (Current Rules)
- ✅ Prefer `sharedBannerServiceController.addBanner({ title })` for quick wins
- ✅ Use `banners: () => [...]` in routes for reactive, route-scoped banners
- ✅ Use ROUTE_SCOPED for banners that must clear on nav
- ✅ Use SESSION_SCOPED for banners that should clear on workspace change
- ✅ Use TIMER_BASED only when you explicitly want auto-dismiss
- ❌ Don’t hardcode window.*; rely on routeController and service APIs
- ❌ Don’t create custom banner components; use LocationBanners + service
- ❌ Don’t rely on old manual reaction patterns in loaders/controllers

## 🎯 Common Actions

### Add Conditional Banner
Show banners that react to MobX state changes:

```typescript
// In route clientLoader
import { BannerLocation, BannerPersistenceType, createBannerMessage } from '@controllers/banner-service';

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    return {
        banners: () => {
            // React to any MobX state - feature flags, user settings, navigation state, etc.
            const { isNewFeatureEnabled } = sharedFeatureAccessModel;

            if (isNewFeatureEnabled) {
                return [createBannerMessage({
                    id: 'new-feature-tip',
                    title: t`Try our new feature!`,
                    severity: 'primary',
                    location: BannerLocation.PAGE_HEADER,
                    persistenceType: BannerPersistenceType.ROUTE_SCOPED,
                })];
            }

            return [];
        },
    };
});
```

### Add Route-Based Banner
Static banners for specific routes:

```typescript
// In route clientLoader
import { BannerLocation, BannerPersistenceType } from '@controllers/banner-service';

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    return {
        banners: [{
            id: 'maintenance-notice',
            title: t`Scheduled maintenance tonight 11 PM - 1 AM EST`,
            severity: 'warning',
            location: BannerLocation.APP_HEADER,
            persistenceType: BannerPersistenceType.PERSISTENT,
        }],
    };
});
```

### 🚀 Incredibly Simple API!
With smart defaults, most banners need just a title:

```typescript
import { sharedBannerServiceController } from '@controllers/banner-service';

// 🎯 Most common case - just title!
sharedBannerServiceController.addBanner({ title: 'Data saved successfully!' });

// 🎨 Add severity for different styles
sharedBannerServiceController.addBanner({
    title: 'Something went wrong',
    severity: 'critical'
});

// ⚙️ Full control when needed
sharedBannerServiceController.addBanner({
    title: 'Maintenance scheduled',
    severity: 'warning',
    location: BannerLocation.PAGE_HEADER,
    body: 'System will be down tonight 11 PM - 1 AM EST',
});
```

**🎁 Smart Defaults (Perfect for Real-World Use):**
- `severity`: `'primary'` (clean, neutral styling)
- `location`: `BannerLocation.APP_HEADER` (most visible location)
- `persistenceType`: `BannerPersistenceType.PERSISTENT` (stays until dismissed)
- `autoHideDuration`: `5000` (5 seconds, only for TIMER_BASED banners)
- `id`: Auto-generated from title (unique & predictable)

### Real-World React Component Examples
See how simple banner integration becomes:

```typescript
import { sharedBannerServiceController, BannerPersistenceType } from '@controllers/banner-service';

function SaveButton() {
    const handleSave = async () => {
        try {
            await saveData();
            // ✨ Just title + severity - that's it!
            sharedBannerServiceController.addBanner({
                title: 'Data saved successfully!',
                severity: 'success',
            });
        } catch (error) {
            // 🚨 Critical errors stay visible until dismissed
            sharedBannerServiceController.addBanner({
                title: 'Failed to save data',
                severity: 'critical',
            });
        }
    };

    const showTempNotification = () => {
        // 💨 Rare case: auto-dismiss after 3 seconds
        sharedBannerServiceController.addBanner({
            title: 'Copying to clipboard...',
            severity: 'primary',
            persistenceType: BannerPersistenceType.TIMER_BASED,
            autoHideDuration: 3000,
        });
    };

    return (
        <div>
            <button onClick={handleSave}>Save Data</button>
            <button onClick={showTempNotification}>Copy Link</button>
        </div>
    );
}
```

### MobX Controller Integration
Perfect for API calls and business logic:

```typescript
import { sharedBannerServiceController } from '@controllers/banner-service';

class DataController {
    async saveUserProfile() {
        try {
            await this.apiCall();
            // 🎉 Success stays visible until user dismisses
            sharedBannerServiceController.addBanner({
                title: 'Profile updated successfully!',
                severity: 'success',
            });
        } catch (error) {
            // 🚨 Errors stay visible - users won't miss them
            sharedBannerServiceController.addBanner({
                title: 'Failed to update profile',
                severity: 'critical',
                body: 'Please try again or contact support',
            });
        }
    }

    async importData() {
        // 📊 Show progress with custom location
        sharedBannerServiceController.addBanner({
            title: 'Import in progress...',
            severity: 'primary',
            location: BannerLocation.PAGE_HEADER,
            body: 'This may take a few minutes',
        });
    }
}
```

### Configure Auto-Dismiss Timing
Control how long banners stay visible:

```typescript
// Auto-dismiss after 10 seconds
sharedBannerServiceController.addBanner({
    title: 'Loading complete',
    severity: 'success',
    autoHideDuration: 10000,
    persistenceType: BannerPersistenceType.TIMER_BASED
});

// Never auto-dismiss (manual only) - this is the default!
sharedBannerServiceController.addBanner({
    title: 'Important notice',
    severity: 'warning',
    // persistenceType: PERSISTENT is the default
});
```

### Ordering
Banners in the same location are presented in FIFO/time-based order.

```typescript
sharedBannerServiceController.addBanner({ title: 'First banner', severity: 'primary' });
sharedBannerServiceController.addBanner({ title: 'Second banner', severity: 'education' });
// Will render in the order they were added
```

### Target Specific Routes with Route Patterns
Show banners only on matching routes:

```typescript
sharedBannerServiceController.addBanner({
    title: 'Compliance tools available',
    severity: 'education',
    routePattern: '/workspaces/*/compliance*',
    location: BannerLocation.PAGE_HEADER
});
```

### Use Different Severities
Each severity has different styling (all auto-dismiss by default):

```typescript
sharedBannerServiceController.addBanner({ title: 'Operation completed!', severity: 'success' });     // Green styling
sharedBannerServiceController.addBanner({ title: 'Error occurred!', severity: 'critical' });         // Red styling
sharedBannerServiceController.addBanner({ title: 'Please review', severity: 'warning' });            // Orange styling
sharedBannerServiceController.addBanner({ title: 'Information' });                                   // Blue styling (default: 'primary')
sharedBannerServiceController.addBanner({ title: 'AI suggestion available', severity: 'ai' });       // Purple styling
sharedBannerServiceController.addBanner({ title: 'Pro tip', severity: 'education' });                // Teal styling
```

### Show Multiple Banners Simultaneously
Banners stack vertically in each location:

```typescript
// These will both appear, stacked in the order they were added
sharedBannerServiceController.addBanner({ title: 'First banner', severity: 'primary' });
sharedBannerServiceController.addBanner({ title: 'Second banner', severity: 'education' });
```

### Dismiss Banners Manually
Remove banners programmatically:

```typescript
// Add banner with specific ID for later dismissal
sharedBannerServiceController.addBanner({
    id: 'temp-message',
    title: 'Temporary message',
    severity: 'primary'
});

// Dismiss after some condition
setTimeout(() => {
    sharedBannerServiceController.dismissBanner('temp-message');
}, 5000);
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Route Loaders   │    │ React           │    │ MobX            │
│                 │    │ Components      │    │ Controllers     │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │  Banner Service         │
                    │  Controller             │
                    │  • Manages state        │
                    │  • Auto-dismiss timers  │
                    │  • Route filtering      │
                    └────────────┬────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │  <LocationBanners/>     │
                    │  Component              │
                    └─┬─────────┬─────────┬───┘
                      │         │         │
        ┌─────────────▼─┐ ┌─────▼─────┐ ┌─▼─────────────┐
        │ APP_HEADER    │ │PAGE_HEADER│ │DOMAIN_CONTENT │
        │ Full width    │ │Above page │ │In main content│
        │ at top        │ │content    │ │area           │
        └───────────────┘ └───────────┘ └───────────────┘
```

## ⚡ Why Use Banner Service?

**Instead of creating custom banner components:**

- ✅ **Consistent UX** - Standardized animations, positioning, and styling across the app
- ✅ **Less Code** - 2-line banner creation vs 20+ lines of custom components
- ✅ **Automatic Cleanup** - Route changes and timers handled automatically
- ✅ **Type Safety** - Full TypeScript integration prevents runtime errors
- ✅ **Reactive Updates** - Banners automatically update when MobX state changes
- ✅ **No Duplication** - Single service eliminates duplicate banner logic across teams
- ✅ **Easy Integration** - Drop-in `<LocationBanners />` component works anywhere

## 📋 API Reference

### Core API

```typescript
// 🎯 Main method - only title required!
sharedBannerServiceController.addBanner(banner: Partial<BannerMessage>): void

// 🗑️ Dismiss banners programmatically
sharedBannerServiceController.dismissBanner(bannerId: string): void

// 📍 Get banners for specific locations (used by components)
sharedBannerServiceController.getBannersForLocation(location: BannerLocation): BannerMessage[]

// 🔧 Utility functions (rarely needed)
generateBannerId(title: string): string
createBannerMessage(banner: Partial<BannerMessage>): BannerMessage
```

### Essential Configuration

```typescript
interface BannerOptions {
    severity?: 'success' | 'critical' | 'warning' | 'primary' | 'ai' | 'education'; // Styling (default: 'primary')
    location?: BannerLocation;           // APP_HEADER | PAGE_HEADER | DOMAIN_CONTENT
    persistenceType?: BannerPersistenceType; // PERSISTENT | ROUTE_SCOPED | TIMER_BASED | SESSION_SCOPED
    autoHideDuration?: number;           // Auto-dismiss time (ms, default: 5000)
    routePattern?: string;               // Route targeting (e.g., "/workspaces/*/compliance")
    body?: string;                       // Additional content below title
}
```

### Severities & Locations

**Severities:** `'success'` (green) | `'critical'` (red) | `'warning'` (orange) | `'primary'` (blue) | `'ai'` (purple) | `'education'` (teal)


## ⚡ Route-Provided Banners (Current Behavior)

- Banners returned from route data can be static arrays or reactive functions.
- TIMER_BASED route banners now auto-dismiss using their `autoHideDuration`.
- Manual dismiss on route banners animates out and hides them until navigation.
- ROUTE_SCOPED banners auto-clear on navigation; SESSION_SCOPED clear on workspace change.

```ts
// Example – route with reactive banners
export const clientLoader = action((): ClientLoader => ({
  banners: () => someFlag ? [createBannerMessage({
    id: 'tip',
    title: t`Tip`,
    severity: 'education',
    location: BannerLocation.PAGE_HEADER,
    persistenceType: BannerPersistenceType.ROUTE_SCOPED,
  })] : []
}));
```

> Prefer reactive functions for conditional banners; use programmatic `addBanner` for app-wide or non-route-scoped cases.

**Locations:** `APP_HEADER` (full width top) | `PAGE_HEADER` (above content) | `DOMAIN_CONTENT` (main area)

## 🚀 Integration

Add banners to any location in your app:

```typescript
import { LocationBanners } from '@components/location-banners';
import { BannerLocation } from '@controllers/banner-service';

// If you want the container id to be "app-header-banners", pass a prefix ending with "-banner"
<LocationBanners
    location={BannerLocation.APP_HEADER}
    dataIdPrefix="app-header-banner"
/>

// If you pass a prefix without "-banner", the container will pluralize with "s" (e.g., "app-headers")
<LocationBanners
    location={BannerLocation.APP_HEADER}
    dataIdPrefix="app-header"
/>
```

**Current Integration Points:** APP_HEADER, PAGE_HEADER, and DOMAIN_CONTENT are already integrated.

## 🧪 Testing

```bash
pnpm test controllers/banner-service    # 47/47 tests passing ✅
pnpm test components/location-banners
```
