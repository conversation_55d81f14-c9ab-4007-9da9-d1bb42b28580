import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest';
import { routeController } from '@controllers/route';
import type { UIMatch } from '@remix-run/react';
import {
    BannerLocation,
    BannerPersistenceType,
} from './banner-message.constants';
import { sharedBannerServiceController } from './banner-service.controller';

// Mock the logger
vi.mock('@globals/logger', () => ({
    logger: {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
    },
}));

// Mock the route controller
vi.mock('@controllers/route', () => ({
    routeController: {
        matches: [],
    },
}));

describe('bannerServiceController', () => {
    beforeEach(() => {
        // Clear all banners before each test
        sharedBannerServiceController.clearAllBanners();
        vi.clearAllMocks();
    });

    afterEach(() => {
        // Clean up after each test
        sharedBannerServiceController.clearAllBanners();
    });

    describe('addBanner', () => {
        test('should add a valid banner successfully', () => {
            const banner = {
                id: 'test-banner',
                title: 'Test Banner',
                location: BannerLocation.APP_HEADER,
            };

            sharedBannerServiceController.addBanner(banner);

            const { banners } = sharedBannerServiceController;

            expect(banners).toHaveLength(1);
            expect(banners[0].id).toBe('test-banner');
            expect(banners[0].title).toBe('Test Banner');
            expect(banners[0].location).toBe(BannerLocation.APP_HEADER);
        });

        test('should apply default values to banner', () => {
            const banner = {
                id: 'test-banner',
                title: 'Test Banner',
                location: BannerLocation.APP_HEADER,
            };

            sharedBannerServiceController.addBanner(banner);

            const addedBanner = sharedBannerServiceController.banners[0];

            expect(addedBanner.persistenceType).toBe(
                BannerPersistenceType.PERSISTENT,
            );
            expect(addedBanner.autoHideDuration).toBe(5000);
        });

        test('should replace existing banner with same ID', () => {
            const banner1 = {
                id: 'test-banner',
                title: 'First Banner',
                location: BannerLocation.APP_HEADER,
            };

            const banner2 = {
                id: 'test-banner',
                title: 'Second Banner',
                location: BannerLocation.PAGE_HEADER,
            };

            sharedBannerServiceController.addBanner(banner1);
            sharedBannerServiceController.addBanner(banner2);

            const { banners } = sharedBannerServiceController;

            expect(banners).toHaveLength(1);
            expect(banners[0].title).toBe('Second Banner');
            expect(banners[0].location).toBe(BannerLocation.PAGE_HEADER);
        });
    });

    describe('removeBanner', () => {
        test('should remove existing banner', () => {
            const banner = {
                id: 'test-banner',
                title: 'Test Banner',
                location: BannerLocation.APP_HEADER,
            };

            sharedBannerServiceController.addBanner(banner);
            expect(sharedBannerServiceController.banners).toHaveLength(1);

            sharedBannerServiceController.removeBanner('test-banner');
            expect(sharedBannerServiceController.banners).toHaveLength(0);
        });

        test('should handle removing non-existent banner gracefully', () => {
            sharedBannerServiceController.removeBanner('non-existent');
            expect(sharedBannerServiceController.banners).toHaveLength(0);
        });
    });

    describe('dismissBanner', () => {
        test('should dismiss banner (with animation timeout)', async () => {
            const banner = {
                id: 'test-banner',
                title: 'Test Banner',
                location: BannerLocation.APP_HEADER,
            };

            sharedBannerServiceController.addBanner(banner);
            expect(sharedBannerServiceController.banners).toHaveLength(1);

            sharedBannerServiceController.dismissBanner('test-banner');

            // Banner should be marked as dismissing but not yet removed
            expect(
                sharedBannerServiceController.isBannerDismissing('test-banner'),
            ).toBeTruthy();
            expect(sharedBannerServiceController.banners).toHaveLength(1);

            // Wait for the timeout to complete
            await new Promise<void>((resolve) => {
                setTimeout(resolve, 700);
            });

            // Now banner should be removed
            expect(sharedBannerServiceController.banners).toHaveLength(0);
            expect(
                sharedBannerServiceController.isBannerDismissing('test-banner'),
            ).toBeFalsy();
        });
    });

    describe('getBannersForLocation', () => {
        beforeEach(() => {
            // Add test banners for different locations
            sharedBannerServiceController.addBanner({
                id: 'app-header-banner',
                title: 'App Header Banner',
                location: BannerLocation.APP_HEADER,
            });

            sharedBannerServiceController.addBanner({
                id: 'page-header-banner',
                title: 'Page Header Banner',
                location: BannerLocation.PAGE_HEADER,
            });

            sharedBannerServiceController.addBanner({
                id: 'domain-content-banner',
                title: 'Domain Content Banner',
                location: BannerLocation.DOMAIN_CONTENT,
            });
        });

        test('should filter banners by location', () => {
            const appHeaderLocationBanners =
                sharedBannerServiceController.getBannersForLocation(
                    BannerLocation.APP_HEADER,
                );
            const pageHeaderLocationBanners =
                sharedBannerServiceController.getBannersForLocation(
                    BannerLocation.PAGE_HEADER,
                );
            const domainContentLocationBanners =
                sharedBannerServiceController.getBannersForLocation(
                    BannerLocation.DOMAIN_CONTENT,
                );

            expect(appHeaderLocationBanners).toHaveLength(1);
            expect(appHeaderLocationBanners[0].id).toBe('app-header-banner');

            expect(pageHeaderLocationBanners).toHaveLength(1);
            expect(pageHeaderLocationBanners[0].id).toBe('page-header-banner');

            expect(domainContentLocationBanners).toHaveLength(1);
            expect(domainContentLocationBanners[0].id).toBe(
                'domain-content-banner',
            );
        });

        test('should combine manually added banners with route-based banners', () => {
            // Mock route controller matches with banner data
            const mockMatches = [
                {
                    id: 'route-1',
                    pathname: '/test',
                    params: {},
                    handle: {},
                    data: {
                        banners: [
                            {
                                id: 'route-specific-banner',
                                title: 'Route Specific Banner',
                                location: BannerLocation.APP_HEADER,
                                severity: 'education',
                                persistenceType:
                                    BannerPersistenceType.ROUTE_SCOPED,
                            },
                        ],
                    },
                },
            ] as UIMatch[];

            // Mock the routeController.matches getter
            vi.spyOn(routeController, 'matches', 'get').mockReturnValue(
                mockMatches,
            );

            const banners = sharedBannerServiceController.getBannersForLocation(
                BannerLocation.APP_HEADER,
            );

            // Should include the route-based banner and the original manually added app-header-banner
            expect(banners).toHaveLength(2);
            expect(
                banners.some((b) => b.id === 'route-specific-banner'),
            ).toBeTruthy();
            expect(
                banners.some((b) => b.id === 'app-header-banner'),
            ).toBeTruthy();
        });
    });

    describe('bannersFromRoutes', () => {
        test('should return banners from route matches', () => {
            const mockMatches = [
                {
                    id: 'route-1',
                    pathname: '/test1',
                    params: {},
                    handle: {},
                    data: {
                        banners: [
                            {
                                id: 'route-banner-1',
                                title: 'Route Banner 1',
                                location: BannerLocation.APP_HEADER,
                                severity: 'education',
                                persistenceType:
                                    BannerPersistenceType.ROUTE_SCOPED,
                            },
                            {
                                id: 'route-banner-2',
                                title: 'Route Banner 2',
                                location: BannerLocation.PAGE_HEADER,
                                severity: 'warning',
                                persistenceType:
                                    BannerPersistenceType.ROUTE_SCOPED,
                            },
                        ],
                    },
                },
                {
                    id: 'route-2',
                    pathname: '/test2',
                    params: {},
                    handle: {},
                    data: {
                        banners: [
                            {
                                id: 'route-banner-3',
                                title: 'Route Banner 3',
                                location: BannerLocation.DOMAIN_CONTENT,
                                severity: 'success',
                                persistenceType:
                                    BannerPersistenceType.ROUTE_SCOPED,
                            },
                        ],
                    },
                },
            ] as UIMatch[];

            vi.spyOn(routeController, 'matches', 'get').mockReturnValue(
                mockMatches,
            );

            const routeBanners =
                sharedBannerServiceController.bannersFromRoutes;

            expect(routeBanners).toHaveLength(3);
            expect(routeBanners[0].id).toBe('route-banner-1');
            expect(routeBanners[1].id).toBe('route-banner-2');
            expect(routeBanners[2].id).toBe('route-banner-3');
        });

        test('should return empty array when no route banners exist', () => {
            const mockMatches = [
                {
                    id: 'route-1',
                    pathname: '/test1',
                    params: {},
                    handle: {},
                    data: {},
                },
                {
                    id: 'route-2',
                    pathname: '/test2',
                    params: {},
                    handle: {},
                    data: { banners: [] },
                },
            ] as UIMatch[];

            vi.spyOn(routeController, 'matches', 'get').mockReturnValue(
                mockMatches,
            );

            const routeBanners =
                sharedBannerServiceController.bannersFromRoutes;

            expect(routeBanners).toHaveLength(0);
        });
    });

    describe('clearAllBanners', () => {
        test('should clear all banners', () => {
            sharedBannerServiceController.addBanner({
                id: 'banner-1',
                title: 'Banner 1',
                location: BannerLocation.APP_HEADER,
            });

            sharedBannerServiceController.addBanner({
                id: 'banner-2',
                title: 'Banner 2',
                location: BannerLocation.PAGE_HEADER,
            });

            expect(sharedBannerServiceController.banners).toHaveLength(2);

            sharedBannerServiceController.clearAllBanners();

            expect(sharedBannerServiceController.banners).toHaveLength(0);
        });
    });

    describe('auto-dismiss functionality', () => {
        test('should auto-dismiss TIMER_BASED banners', async () => {
            const banner = {
                id: 'timer-banner',
                title: 'Timer Banner',
                location: BannerLocation.APP_HEADER,
                persistenceType: BannerPersistenceType.TIMER_BASED,
                autoHideDuration: 100, // 100ms for quick test
            };

            sharedBannerServiceController.addBanner(banner);
            expect(sharedBannerServiceController.banners).toHaveLength(1);

            // Wait for auto-dismiss timer (100ms) + dismiss animation timeout (600ms)
            await new Promise<void>((resolve) => {
                setTimeout(() => {
                    resolve();
                }, 800); // 100ms auto-dismiss + 600ms animation + buffer
            });

            expect(sharedBannerServiceController.banners).toHaveLength(0);
        });

        test('should not auto-dismiss PERSISTENT banners', async () => {
            const banner = {
                id: 'persistent-banner',
                title: 'Persistent Banner',
                location: BannerLocation.APP_HEADER,
                persistenceType: BannerPersistenceType.PERSISTENT,
                autoHideDuration: 100, // Should be ignored for persistent banners
            };

            sharedBannerServiceController.addBanner(banner);
            expect(sharedBannerServiceController.banners).toHaveLength(1);

            // Wait longer than the auto-hide duration
            await new Promise<void>((resolve) => {
                setTimeout(() => {
                    resolve();
                }, 150);
            });

            // Banner should still be there
            expect(sharedBannerServiceController.banners).toHaveLength(1);
        });
    });
});
