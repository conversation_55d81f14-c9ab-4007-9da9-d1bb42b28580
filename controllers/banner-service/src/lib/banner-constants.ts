/**
 * Shared banner timing constants.
 *
 * These constants synchronize CSS animations and controller removal timing
 * to ensure smooth banner exit without race conditions.
 */
export const BANNER_EXIT_ANIMATION_MS = 400; /** AnimatedBanner exit animation duration (ms). */
export const BANNER_ONCLOSE_DELAY_MS = 500; /** Delay before onClose is invoked (ms). */
export const BANNER_CONTROLLER_REMOVE_DELAY_MS = 600; /** Controller removal delay after dismiss (ms). */
export const BANNER_ENTRY_ANIMATION_MS = 700; /** AnimatedBanner entry animation duration (ms). */
