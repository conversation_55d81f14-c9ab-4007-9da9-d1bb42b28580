import { BANNER_MESSAGE_DEFAULTS } from './banner-message.constants';
import type { BannerMessage } from './banner-message.types';
import { generateBannerId } from './banner-service.helpers';

/**
 * Creates a complete banner message with defaults applied.
 * TypeScript ensures type safety at compile time.
 *
 * Only title is required - all other fields have sensible defaults:
 * - severity: 'primary'
 * - location: BannerLocation.APP_HEADER
 * - persistenceType: BannerPersistenceType.PERSISTENT
 * - autoHideDuration: 5000ms (only used for TIMER_BASED).
 */
export function createBannerMessage(
    banner: Partial<BannerMessage>,
): BannerMessage {
    // Generate ID if not provided
    const id = banner.id ?? generateBannerId(banner.title ?? 'banner');

    return {
        // Start with all provided properties (passes through title, body, onClose, etc.)
        ...banner,
        // Override with generated/default values
        id,
        severity: banner.severity ?? BANNER_MESSAGE_DEFAULTS.severity,
        location: banner.location ?? BANNER_MESSAGE_DEFAULTS.location,
        persistenceType:
            banner.persistenceType ?? BANNER_MESSAGE_DEFAULTS.persistenceType,
        autoHideDuration:
            banner.autoHideDuration ?? BANNER_MESSAGE_DEFAULTS.autoHideDuration,
        routePattern:
            banner.routePattern ?? BANNER_MESSAGE_DEFAULTS.routePattern,
    } as BannerMessage;
}

/**
 * Simple route pattern matching function
 * Supports wildcards (star) for path segments
 * Example: "/workspaces/star/compliance" matches "/workspaces/123/compliance".
 */
export function matchesRoutePattern(
    pathname: string,
    pattern: string | null,
): boolean {
    if (!pattern) {
        return true; // No pattern means match all routes
    }

    // Normalize paths by removing trailing slashes
    const normalizedPathname = pathname.replace(/\/$/, '') || '/';
    const normalizedPattern = pattern.replace(/\/$/, '') || '/';

    // Split into segments
    const pathSegments = normalizedPathname.split('/');
    const patternSegments = normalizedPattern.split('/');

    // Must have same number of segments
    if (pathSegments.length !== patternSegments.length) {
        return false;
    }

    // Check each segment
    for (const [i, pathSegment] of pathSegments.entries()) {
        const patternSegment = patternSegments[i];

        // Wildcard matches any segment
        if (patternSegment === '*') {
            continue;
        }

        // Exact match required
        if (pathSegment !== patternSegment) {
            return false;
        }
    }

    return true;
}
