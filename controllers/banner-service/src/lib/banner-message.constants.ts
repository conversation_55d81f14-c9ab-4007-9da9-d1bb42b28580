/**
 * Object defining where banners can be displayed in the application.
 */
export const BannerLocation = {
    APP_HEADER: 'APP_HEADER',
    PAGE_HEADER: 'PAGE_HEADER',
    DOMAIN_CONTENT: 'DOMAIN_CONTENT',
} as const;

export type BannerLocation =
    (typeof BannerLocation)[keyof typeof BannerLocation];

/**
 * Object defining how banners should be cleaned up/dismissed.
 */
export const BannerPersistenceType = {
    /** Remains until manually dismissed. */
    PERSISTENT: 'PERSISTENT',
    /** Cleared when route changes. */
    ROUTE_SCOPED: 'ROUTE_SCOPED',
    /** Auto-dismissed after specified duration. */
    TIMER_BASED: 'TIMER_BASED',
    /** Cleared on page refresh/session end. */
    SESSION_SCOPED: 'SESSION_SCOPED',
} as const;

export type BannerPersistenceType =
    (typeof BannerPersistenceType)[keyof typeof BannerPersistenceType];

/**
 * Default values for optional banner message fields.
 * These defaults make the API much more developer-friendly by requiring only title.
 */
export const BANNER_MESSAGE_DEFAULTS = {
    severity: 'primary' as const,
    location: BannerLocation.APP_HEADER,
    persistenceType: BannerPersistenceType.PERSISTENT,
    autoHideDuration: 5000, // 5 seconds
    routePattern: null,
} as const;
