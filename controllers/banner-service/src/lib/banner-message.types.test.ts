/* eslint-disable custom/no-hardcoded-workspace-urls -- Need this for testing */
import { describe, expect, test } from 'vitest';
import {
    BannerLocation,
    BannerPersistenceType,
} from './banner-message.constants';
import {
    createBannerMessage,
    matchesRoutePattern,
} from './banner-message.helpers';

describe('bannerMessage Types', () => {
    describe('createBannerMessage', () => {
        test('should create banner with defaults', () => {
            const banner = createBannerMessage({
                id: 'test-banner',
                title: 'Test Banner',
                location: BannerLocation.APP_HEADER,
            });

            expect(banner.persistenceType).toBe(
                BannerPersistenceType.PERSISTENT,
            );
            expect(banner.autoHideDuration).toBe(5000);
            expect(banner.routePattern).toBeNull();
        });

        test('should preserve provided optional values', () => {
            const banner = createBannerMessage({
                id: 'test-banner',
                title: 'Test Banner',
                location: BannerLocation.PAGE_HEADER,
                persistenceType: BannerPersistenceType.PERSISTENT,
                autoHideDuration: 10000,
                routePattern: '/workspaces/*',
            });

            expect(banner.persistenceType).toBe(
                BannerPersistenceType.PERSISTENT,
            );
            expect(banner.autoHideDuration).toBe(10000);
            expect(banner.routePattern).toBe('/workspaces/*');
        });
    });

    describe('matchesRoutePattern', () => {
        test('should match when no pattern is provided', () => {
            expect(matchesRoutePattern('/any/path', null)).toBeTruthy();
            expect(matchesRoutePattern('/any/path', '')).toBeTruthy();
        });

        test('should match exact paths', () => {
            expect(
                matchesRoutePattern(
                    '/workspaces/123/compliance',
                    '/workspaces/123/compliance',
                ),
            ).toBeTruthy();
            expect(
                matchesRoutePattern(
                    '/workspaces/123/compliance',
                    '/workspaces/456/compliance',
                ),
            ).toBeFalsy();
        });

        test('should match wildcard patterns', () => {
            expect(
                matchesRoutePattern(
                    '/workspaces/123/compliance',
                    '/workspaces/*/compliance',
                ),
            ).toBeTruthy();
            expect(
                matchesRoutePattern(
                    '/workspaces/456/compliance',
                    '/workspaces/*/compliance',
                ),
            ).toBeTruthy();
            expect(
                matchesRoutePattern(
                    '/workspaces/123/governance',
                    '/workspaces/*/compliance',
                ),
            ).toBeFalsy();
        });

        test('should handle multiple wildcards', () => {
            expect(
                matchesRoutePattern(
                    '/workspaces/123/domains/456/compliance',
                    '/workspaces/*/domains/*/compliance',
                ),
            ).toBeTruthy();
            expect(
                matchesRoutePattern(
                    '/workspaces/123/domains/456/governance',
                    '/workspaces/*/domains/*/compliance',
                ),
            ).toBeFalsy();
        });

        test('should handle trailing slashes', () => {
            expect(
                matchesRoutePattern('/workspaces/123/', '/workspaces/*'),
            ).toBeTruthy();
            expect(
                matchesRoutePattern('/workspaces/123', '/workspaces/*/'),
            ).toBeTruthy();
            expect(
                matchesRoutePattern('/workspaces/123/', '/workspaces/*/'),
            ).toBeTruthy();
        });

        test('should handle root path', () => {
            expect(matchesRoutePattern('/', '/')).toBeTruthy();
            expect(matchesRoutePattern('/', '/workspaces')).toBeFalsy();
        });

        test('should require same number of segments', () => {
            expect(
                matchesRoutePattern(
                    '/workspaces/123',
                    '/workspaces/*/compliance',
                ),
            ).toBeFalsy();
            expect(
                matchesRoutePattern(
                    '/workspaces/123/compliance/extra',
                    '/workspaces/*/compliance',
                ),
            ).toBeFalsy();
        });
    });
});
