import type { BannerProps } from '@cosmos/components/banner';
import type {
    BannerLocation,
    BannerPersistenceType,
} from './banner-message.constants';

/**
 * Extended banner interface that adds banner service specific fields to Cosmos BannerProps.
 */
export interface BannerMessage extends BannerProps {
    /** Unique identifier for the banner (required). */
    id: string;

    /** Where the banner should be displayed (required). */
    location: BannerLocation;

    /** How banner should be cleaned up (default: PERSISTENT). */
    persistenceType?: BannerPersistenceType;

    /** Custom auto-dismiss time in milliseconds (default: 5000). */
    autoHideDuration?: number;

    /** Pathname pattern for route targeting (e.g., "/workspaces/star/compliance") (default: null). */
    routePattern?: string;
}

/**
 * Route data interface that supports both static and reactive banners.
 */
export interface RouteDataWithBanners {
    banners?: BannerMessage[] | (() => BannerMessage[]);
}
