import type {
    BannerLocation,
    BannerPersistenceType,
} from './banner-message.constants';

/**
 * Helper functions for common banner operations.
 * These functions provide a simplified API for the most common banner use cases.
 */

/**
 * Common banner options that can be overridden.
 */
export interface BannerOptions {
    /** Banner styling and color (default: 'primary'). */
    severity?:
        | 'success'
        | 'critical'
        | 'warning'
        | 'primary'
        | 'ai'
        | 'education';
    /** Where the banner should be displayed. */
    location?: BannerLocation;
    /** How the banner should be cleaned up. */
    persistenceType?: BannerPersistenceType;
    /** Auto-dismiss duration in milliseconds (only for TIMER_BASED). */
    autoHideDuration?: number;
    /** Display priority (higher numbers appear first). */
    priority?: number;
    /** Route pattern for targeting specific routes. */
    routePattern?: string;
    /** Additional body text. */
    body?: string;
}

// Default options removed - use sharedBannerServiceController.addBanner() with explicit options

/**
 * Generate a unique banner ID based on title and timestamp.
 * This utility function can be used when creating banners manually.
 */
export function generateBannerId(title: string): string {
    const timestamp = Date.now();
    const titleSlug = title
        .toLowerCase()
        .replaceAll(/[^a-z0-9]/g, '-')
        .replaceAll(/-+/g, '-')
        .replaceAll(/^-$|-$/g, '');

    return `${titleSlug}-${timestamp}`;
}

// Helper functions removed to maintain proper MobX proxy architecture
// Use sharedBannerServiceController.addBanner() and sharedBannerServiceController.dismissBanner() directly
