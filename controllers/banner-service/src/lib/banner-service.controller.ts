import {
    isEmpty,
    isError,
    isFunction,
    isNil,
    isNumber,
    isObject,
} from 'lodash-es';
import { routeController } from '@controllers/route';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, reaction, runInAction } from '@globals/mobx';
import { BANNER_CONTROLLER_REMOVE_DELAY_MS } from './banner-constants';
import {
    BannerLocation,
    BannerPersistenceType,
} from './banner-message.constants';
import {
    createBannerMessage,
    matchesRoutePattern,
} from './banner-message.helpers';
import type {
    BannerMessage,
    RouteDataWithBanners,
} from './banner-message.types';
import { BannerTimerController } from './banner-timer.controller';

/**
 * Internal banner with timer management.
 */
interface ManagedBanner {
    banner: BannerMessage;
    timer?: BannerTimerController;
}

/**
 * Centralized Banner Service Controller
 * Manages all banners across the entire Multiverse application.
 */
export class BannerServiceController {
    /** All active banners with their timers and metadata. */
    managedBanners: Map<string, ManagedBanner> = new Map();

    /** IDs of banners currently being dismissed (playing exit animation). */
    dismissingBannerIds: Set<string> = new Set();

    /**
     * Optimized indexes for O(1) access patterns.
     */
    bannersByPersistence: Map<BannerPersistenceType, Set<string>> = new Map();
    bannersByLocation: Map<BannerLocation, Set<string>> = new Map();

    /** Reaction disposer for route-scoped cleanup. */
    routeDisposer: (() => void) | null = null;
    /** Track last known workspace to support SESSION_SCOPED behavior. */
    lastWorkspaceId: string | null = null;

    /**
     * Route-based banner helpers.
     */
    routeBannerTimers: Map<string, BannerTimerController> = new Map();
    dismissedRouteBannerIds: Set<string> = new Set();

    routeDismissingBannerIds: Set<string> = new Set();
    constructor() {
        makeAutoObservable(this, {
            // Make managedBanners and dismissingBannerIds observable so components react to changes
        });

        // Initialize persistence type indexes
        this.initializeIndexes();

        // Auto-clear ROUTE_SCOPED banners on route changes
        this.routeDisposer = reaction(
            () => routeController.matches.map((m) => m.pathname).join('|'),
            () => {
                this.clearBannersForRoute();
                // Reset route-based banner runtime state on navigation
                this.routeBannerTimers.forEach((timer) => {
                    timer.clear();
                });
                this.routeBannerTimers.clear();
                this.routeDismissingBannerIds.clear();
                this.dismissedRouteBannerIds.clear();
            },
        );
        // SESSION_SCOPED semantics: clear when workspaceId changes
        reaction(
            () => {
                const params =
                    (
                        routeController as unknown as {
                            currentParams?: Record<string, string>;
                        }
                    ).currentParams ?? {};

                return params.workspaceId || null;
            },
            (workspaceId) => {
                // Initialize lastWorkspaceId if unset
                if (this.lastWorkspaceId === null && workspaceId) {
                    this.lastWorkspaceId = workspaceId;

                    return;
                }

                if (
                    workspaceId &&
                    this.lastWorkspaceId &&
                    workspaceId !== this.lastWorkspaceId
                ) {
                    const sessionIds = this.bannersByPersistence.get(
                        BannerPersistenceType.SESSION_SCOPED,
                    );

                    if (sessionIds && sessionIds.size > 0) {
                        // Collect to avoid mutation during iteration
                        const ids = [...sessionIds];

                        for (const id of ids) {
                            this.removeBanner(id);
                        }
                    }
                    this.lastWorkspaceId = workspaceId;
                }
            },
        );
    }

    /**
     * Initialize the index Maps with all possible enum values.
     */
    private initializeIndexes(): void {
        // Initialize persistence type indexes
        Object.values(BannerPersistenceType).forEach((type) => {
            this.bannersByPersistence.set(type, new Set());
        });

        // Initialize location indexes
        Object.values(BannerLocation).forEach((location) => {
            this.bannersByLocation.set(location, new Set());
        });
    }

    /**
     * Get all active banners as an array.
     */
    get banners(): BannerMessage[] {
        return [...this.managedBanners.values()].map(
            (managed) => managed.banner,
        );
    }

    /**
     * Type guard to check if match data contains banners.
     */
    private isMatchDataWithBanners(
        data: unknown,
    ): data is RouteDataWithBanners {
        if (isNil(data) || !isObject(data)) {
            return false;
        }

        // Check if banners property exists and is an array or function
        return (
            'banners' in data &&
            (Array.isArray(data.banners) || isFunction(data.banners))
        );
    }

    /**
     * Get banners from current route matches.
     * Computed property that automatically updates when routes change.
     * Supports both static banner arrays and reactive banner functions.
     */
    get bannersFromRoutes(): BannerMessage[] {
        const banners: BannerMessage[] = [];

        for (const match of routeController.matches) {
            if (this.isMatchDataWithBanners(match.data)) {
                const { banners: routeBanners } = match.data;

                if (isFunction(routeBanners)) {
                    // Reactive banners - call function to get current banners
                    banners.push(...routeBanners());
                } else if (Array.isArray(routeBanners)) {
                    // Static banners - use as-is
                    banners.push(...routeBanners);
                }
            }
        }

        return banners;
    }

    /**
     * Add a new banner to the service
     * Replaces existing banner if ID already exists and logs error for duplicates.
     */
    addBanner(partialBanner: Partial<BannerMessage>): void {
        try {
            // Create complete banner with defaults (TypeScript ensures type safety)
            const banner = createBannerMessage(partialBanner);

            // Check for duplicate ID and log differences
            const existingManaged = this.managedBanners.get(banner.id);

            if (existingManaged) {
                this.logDuplicateBannerError(existingManaged.banner, banner);
                // Clear existing timer
                existingManaged.timer?.clear();
                // Remove from old indexes
                this.removeFromIndexes(banner.id, existingManaged.banner);
            }

            // Create managed banner with timer if needed
            const managedBanner = this.createManagedBanner(banner);

            // Store the banner
            this.managedBanners.set(banner.id, managedBanner);

            // Add to indexes
            this.addToIndexes(banner.id, banner);

            logger.info({
                message: 'Banner added successfully',
                additionalInfo: {
                    bannerId: banner.id,
                    location: banner.location,
                    persistenceType: banner.persistenceType,
                },
            });
        } catch (error) {
            logger.error({
                message: 'Failed to add banner: unexpected error',
                additionalInfo: {
                    bannerId: partialBanner.id,
                    banner: partialBanner,
                },
                errorObject: {
                    message: isError(error) ? error.message : String(error),
                    statusCode: '500',
                },
            });
        }
    }

    /**
     * Remove a banner by ID.
     */
    removeBanner(id: string): void {
        const managedBanner = this.managedBanners.get(id);

        if (!managedBanner) {
            logger.warn({
                message: 'Attempted to remove non-existent banner',
                additionalInfo: { bannerId: id },
            });

            return;
        }

        // Clear timer if exists
        managedBanner.timer?.clear();

        // Remove from indexes
        this.removeFromIndexes(id, managedBanner.banner);

        // Remove from map
        this.managedBanners.delete(id);

        logger.info({
            message: 'Banner removed successfully',
            additionalInfo: { bannerId: id },
        });
    }

    /**
     * Dismiss a banner with animation support.
     * Marks the banner as dismissing and lets UI components handle the animation.
     */
    dismissBanner(id: string): void {
        const managedBanner = this.managedBanners.get(id);

        if (!managedBanner) {
            logger.warn({
                message: 'Attempted to dismiss non-existent banner',
                additionalInfo: { bannerId: id },
            });

            return;
        }

        // Mark as dismissing to trigger UI animations
        runInAction(() => {
            this.dismissingBannerIds.add(id);
        });

        // Clear any auto-dismiss timer since we're manually dismissing
        managedBanner.timer?.clear();

        // Give UI components time to animate, then remove
        setTimeout(() => {
            runInAction(() => {
                this.removeBanner(id);
                this.dismissingBannerIds.delete(id);
            });
        }, BANNER_CONTROLLER_REMOVE_DELAY_MS); // Slightly longer than animation duration to ensure it completes

        logger.info({
            message: 'Banner dismissal started',
            additionalInfo: { bannerId: id },
        });
    }

    /**
     * Check if a banner is currently being dismissed.
     */
    isBannerDismissing(id: string): boolean {
        return this.dismissingBannerIds.has(id);
    }

    /**
     * Check if banners should be rendered for a location.
     * Business logic: Only render if there are banners to show.
     */
    shouldRenderBannersForLocation(location: BannerLocation): boolean {
        const banners = this.getBannersForLocation(location);

        return !isEmpty(banners);
    }

    /**
     * Get banner props ready for rendering at a specific location.
     * Business logic: Maps banner data to component props with all necessary attributes.
     */
    getBannerPropsForLocation(
        location: BannerLocation,
        dataIdPrefix: string,
    ): {
        key: string;
        bannerId: string;
        /**
         * All props for AnimatedBanner component - consistent grouping.
         */
        animatedBannerProps: {
            title: string;
            body?: React.ReactNode;
            severity?:
                | 'primary'
                | 'success'
                | 'warning'
                | 'critical'
                | 'ai'
                | 'education';
            displayMode?: 'section' | 'full';
            action?: React.ReactElement;
            onClose: () => void;
            closeButtonAriaLabel: string;
            isExiting: boolean;
            'data-id': string;
        };
        onDismiss: () => void;
    }[] {
        const banners = this.getBannersForLocation(location);

        return banners.map((banner) => {
            const bannerId = banner.id;
            const isRouteBanner = !this.managedBanners.has(bannerId);

            // Determine exiting state: managed banners use dismissingBannerIds; route banners use routeDismissingBannerIds
            const isExiting = isRouteBanner
                ? this.routeDismissingBannerIds.has(bannerId)
                : this.isBannerDismissing(bannerId);

            const closeTitle = banner.title;

            // Map banner message to AnimatedBanner props - consistent grouping
            const animatedBannerProps = {
                title: banner.title,
                body: banner.body,
                severity: banner.severity,
                displayMode: banner.displayMode,
                action: banner.action,
                onClose: () => {
                    if (isRouteBanner) {
                        this.dismissRouteBanner(bannerId);
                    } else {
                        this.handleDismissWithAnimation(bannerId);
                    }
                },
                closeButtonAriaLabel: t`Dismiss ${closeTitle} banner`,
                isExiting,
                'data-id': `${dataIdPrefix}-${bannerId}`,
            };

            return {
                key: bannerId,
                bannerId,
                animatedBannerProps,
                onDismiss: () => {
                    if (isRouteBanner) {
                        this.dismissRouteBanner(bannerId);
                    } else {
                        this.handleDismissWithAnimation(bannerId);
                    }
                },
            };
        });
    }

    /**
     * Handle dismissal of a route-based banner with animation markers.
     */
    private dismissRouteBanner(id: string): void {
        this.routeDismissingBannerIds.add(id);
        this.routeBannerTimers.get(id)?.clear();
        this.routeBannerTimers.delete(id);
        setTimeout(() => {
            this.dismissedRouteBannerIds.add(id);
            this.routeDismissingBannerIds.delete(id);
        }, BANNER_CONTROLLER_REMOVE_DELAY_MS);
    }
    /**
     * Get container data-id for a location.
     * Business logic: Converts banner prefix to container prefix.
     */
    getContainerDataId(dataIdPrefix: string): string {
        return dataIdPrefix.endsWith('-banner')
            ? dataIdPrefix.replace('-banner', '-banners')
            : `${dataIdPrefix}s`;
    }

    /**
     * Handle banner dismissal with animation.
     * Business logic: Coordinates dismissal with UI animation.
     */
    private handleDismissWithAnimation(bannerId: string): void {
        this.dismissBanner(bannerId);
    }

    /**
     * Get all banners for a specific location.
     * Combines manually added banners with route-based banners.
     * Filters by route pattern if specified.
     * Returns banners sorted by priority (higher priority first).
     */
    getBannersForLocation(location: BannerLocation): BannerMessage[] {
        const result: BannerMessage[] = [];

        // Derive current pathname from routeController to avoid direct window usage
        const { matches } = routeController;
        const currentPathname = isEmpty(matches)
            ? ''
            : (matches[matches.length - 1]?.pathname ?? '');

        // Get manually added banners
        const locationBannerIds =
            this.bannersByLocation.get(location) ?? new Set<string>();

        for (const id of locationBannerIds) {
            const managedBanner = this.managedBanners.get(id);

            if (!managedBanner) {
                continue;
            }

            const { banner } = managedBanner;

            // Filter by route pattern if specified
            if (banner.routePattern) {
                if (matchesRoutePattern(currentPathname, banner.routePattern)) {
                    result.push(banner);
                }
            } else {
                // No route pattern means show on all routes
                result.push(banner);
            }
        }

        // Get route-based banners (these are already route-scoped by design)
        const routeBannersRaw = this.bannersFromRoutes.filter(
            (banner) => banner.location === location,
        );

        // For route banners: start timers for TIMER_BASED and filter out dismissed ones
        for (const rb of routeBannersRaw) {
            const { id } = rb;

            if (this.dismissedRouteBannerIds.has(id)) {
                continue; // Hide dismissed route banners until navigation
            }
            const { persistenceType, autoHideDuration } = rb;

            if (
                persistenceType === BannerPersistenceType.TIMER_BASED &&
                isNumber(autoHideDuration) &&
                autoHideDuration > 0 &&
                !this.routeBannerTimers.has(id)
            ) {
                const delay: number = autoHideDuration;
                const timer = new BannerTimerController(() => {
                    runInAction(() => {
                        this.dismissRouteBanner(id);
                    });
                }, delay);

                this.routeBannerTimers.set(id, timer);
                timer.start();
            }
            result.push(rb);
        }

        // FIFO/time-based ordering: return in insertion order (route banners already scoped)
        return result;
    }

    /**
     * Clear all banners with ROUTE_SCOPED persistence type.
     * Optimized: O(k) where k = route-scoped banners, instead of O(n) where n = all banners.
     */
    clearBannersForRoute(): void {
        const routeScopedBannerIds =
            this.bannersByPersistence.get(BannerPersistenceType.ROUTE_SCOPED) ??
            new Set();

        // Convert to array to avoid Set modification during iteration
        const idsToRemove = [...routeScopedBannerIds];

        for (const id of idsToRemove) {
            this.removeBanner(id);
        }

        if (!isEmpty(idsToRemove)) {
            logger.info({
                message: 'Cleared route-scoped banners',
                additionalInfo: {
                    count: idsToRemove.length,
                    bannerIds: idsToRemove,
                },
            });
        }
    }

    /**
     * Clear all banners (useful for testing or cleanup).
     * Optimized: Direct Map operations instead of individual removeBanner calls.
     */
    clearAllBanners(): void {
        const bannerCount = this.managedBanners.size;

        // Clear all timers
        for (const managedBanner of this.managedBanners.values()) {
            managedBanner.timer?.clear();
        }

        // Clear all data structures
        this.managedBanners.clear();
        this.dismissingBannerIds.clear();

        // Clear all indexes
        this.bannersByPersistence.forEach((set) => {
            set.clear();
        });
        this.bannersByLocation.forEach((set) => {
            set.clear();
        });

        logger.info({
            message: 'Cleared all banners',
            additionalInfo: { count: bannerCount },
        });
    }

    /**
     * Add banner ID to all relevant indexes.
     */
    private addToIndexes(id: string, banner: BannerMessage): void {
        // Add to persistence type index
        const persistenceType =
            banner.persistenceType ?? BannerPersistenceType.TIMER_BASED;
        const persistenceSet = this.bannersByPersistence.get(persistenceType);

        persistenceSet?.add(id);

        // Add to location index
        const locationSet = this.bannersByLocation.get(banner.location);

        locationSet?.add(id);
    }

    /**
     * Remove banner ID from all relevant indexes.
     */
    private removeFromIndexes(id: string, banner: BannerMessage): void {
        // Remove from persistence type index
        const persistenceType =
            banner.persistenceType ?? BannerPersistenceType.TIMER_BASED;
        const persistenceSet = this.bannersByPersistence.get(persistenceType);

        persistenceSet?.delete(id);

        // Remove from location index
        const locationSet = this.bannersByLocation.get(banner.location);

        locationSet?.delete(id);
    }

    /**
     * Create a managed banner with timer if needed.
     */
    private createManagedBanner(banner: BannerMessage): ManagedBanner {
        const managedBanner: ManagedBanner = { banner };

        // Set up auto-dismiss timer for TIMER_BASED banners
        if (
            banner.persistenceType === BannerPersistenceType.TIMER_BASED &&
            banner.autoHideDuration &&
            banner.autoHideDuration > 0
        ) {
            managedBanner.timer = new BannerTimerController(() => {
                runInAction(() => {
                    this.dismissBanner(banner.id);
                });
            }, banner.autoHideDuration);
            managedBanner.timer.start();
        }

        return managedBanner;
    }

    /**
     * Log error when duplicate banner ID is detected.
     */
    private logDuplicateBannerError(
        existingBanner: BannerMessage,
        newBanner: BannerMessage,
    ): void {
        const differingFields: string[] = [];

        // Check each field for differences
        const fieldsToCheck: (keyof BannerMessage)[] = [
            'title',
            'body',
            'severity',
            'location',
            'persistenceType',
            'autoHideDuration',
            'routePattern',
        ];

        for (const field of fieldsToCheck) {
            if (existingBanner[field] !== newBanner[field]) {
                differingFields.push(field);
            }
        }

        logger.error({
            message: 'Duplicate banner ID detected - replacing existing banner',
            additionalInfo: {
                bannerId: newBanner.id,
                differingFields,
                existingBanner: {
                    title: existingBanner.title,
                    location: existingBanner.location,
                    persistenceType: existingBanner.persistenceType,
                },
                newBanner: {
                    title: newBanner.title,
                    location: newBanner.location,
                    persistenceType: newBanner.persistenceType,
                },
            },
        });
    }

    /**
     * Cleanup method for clearing timers.
     */
    dispose(): void {
        // Clear all timers
        for (const managedBanner of this.managedBanners.values()) {
            managedBanner.timer?.clear();
        }

        // Clear all data structures
        this.managedBanners.clear();
        this.dismissingBannerIds.clear();

        // Clear all indexes
        this.bannersByPersistence.forEach((set) => {
            set.clear();
        });
        this.bannersByLocation.forEach((set) => {
            set.clear();
        });

        logger.info({
            message: 'Banner service controller disposed',
        });
    }
}

export const sharedBannerServiceController = new BannerServiceController();
