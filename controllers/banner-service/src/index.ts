// Controller - shared singleton instance and class type
export {
    BannerServiceController,
    sharedBannerServiceController,
} from './lib/banner-service.controller';

// Helper functions
export {
    createBannerMessage,
    matchesRoutePattern,
} from './lib/banner-message.helpers';

// Shared banner constants
export {
    BANNER_CONTROLLER_REMOVE_DELAY_MS,
    BANNER_ENTRY_ANIMATION_MS,
    BANNER_EXIT_ANIMATION_MS,
    BANNER_ONCLOSE_DELAY_MS,
} from './lib/banner-constants';

// Timer utility controller
export { BannerTimerController } from './lib/banner-timer.controller';

// Types
export type { BannerMessage } from './lib/banner-message.types';

// Constants and enums
export {
    BANNER_MESSAGE_DEFAULTS,
    BannerLocation,
    BannerPersistenceType,
} from './lib/banner-message.constants';

// Utility functions and types (helper functions removed for MobX compatibility)
export {
    type BannerOptions,
    generateBannerId,
} from './lib/banner-service.helpers';
