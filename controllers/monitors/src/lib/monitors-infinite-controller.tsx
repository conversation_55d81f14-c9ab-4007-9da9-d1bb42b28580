import { isNil } from 'lodash-es';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Metadata } from '@cosmos/components/metadata';
import { monitorsControllerListControlTestInstancesInfiniteOptions } from '@globals/api-sdk/queries';
import type { ControlTestResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

interface MonitorData {
    id: number;
    testId: number;
    checkStatus: string;
}

interface MonitorListBoxItemData extends ListBoxItemData {
    monitorData?: MonitorData;
    tag?: {
        label?: string;
        colorScheme?: string;
    };
}

export interface SelectedMonitor {
    id: string;
    name: string;
    testId: number;
    checkResultStatus?: string;
}

class MonitorsInfiniteController {
    #lastSearchQuery = '';
    selectedMonitors: SelectedMonitor[] = [];

    constructor() {
        makeAutoObservable(this);
    }

    addSelectedMonitors(monitors: MonitorListBoxItemData[]): void {
        this.selectedMonitors = monitors.map((monitor) => ({
            id: monitor.id,
            name: monitor.label,
            testId: monitor.monitorData?.testId as number,
            checkResultStatus: monitor.monitorData?.checkStatus || undefined,
        }));
    }

    removeSelectedMonitor(monitorId: string): void {
        this.selectedMonitors = this.selectedMonitors.filter(
            (monitor) => monitor.id !== monitorId,
        );
    }

    clearSelectedMonitors(): void {
        this.selectedMonitors = [];
    }

    monitorsInfiniteQuery = new ObservedInfiniteQuery(
        monitorsControllerListControlTestInstancesInfiniteOptions,
    );

    get monitorsInfiniteList(): ControlTestResponseDto[] {
        // Filter out duplicates by creating a Map with monitor ID as key
        const uniqueMonitors = new Map<number, ControlTestResponseDto>();

        this.monitorsInfiniteQuery.data?.pages.forEach((page) => {
            (page?.data ?? []).forEach((monitor) => {
                if (!uniqueMonitors.has(monitor.id)) {
                    uniqueMonitors.set(monitor.id, monitor);
                }
            });
        });

        return [...uniqueMonitors.values()];
    }

    loadInfiniteMonitors = ({
        controlId,
        excludeControlId,
    }: {
        controlId?: number | null;
        excludeControlId?: number;
    }): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            console.error('No workspace selected');

            return;
        }

        this.monitorsInfiniteQuery.load({
            path: { xProductId: workspaceId },
            query: {
                q: this.#lastSearchQuery,
                excludeControlId,
                includeDrafts: true,
                ...(isNil(controlId) ? {} : { controlId }),
            },
        });
    };

    search = (searchTerm: string, excludeControlId?: number): void => {
        if (searchTerm === this.#lastSearchQuery) {
            return;
        }

        this.#lastSearchQuery = searchTerm;
        this.monitorsInfiniteQuery.unload();
        this.loadInfiniteMonitors({ excludeControlId });
    };

    get monitorsComboboxOptions(): ListBoxItemData[] {
        return this.monitorsInfiniteList.map((monitor) => ({
            id: String(monitor.id),
            label: monitor.name,
            value: String(monitor.id),
            monitorData: {
                id: monitor.id,
                testId: monitor.testId,
                checkStatus: monitor.checkStatus,
            },
            endSlot: (
                <Metadata
                    type="tag"
                    colorScheme="neutral"
                    label={
                        monitor.checkStatus.charAt(0).toUpperCase() +
                        monitor.checkStatus.slice(1).toLowerCase()
                    }
                />
            ),
        }));
    }

    get hasError(): boolean {
        return this.monitorsInfiniteQuery.hasError;
    }

    get isLoading(): boolean {
        return this.monitorsInfiniteQuery.isLoading;
    }

    get hasNextPage(): boolean {
        return this.monitorsInfiniteQuery.hasNextPage;
    }

    loadNextPage = ({ search }: { search?: string }): void => {
        if (search !== this.#lastSearchQuery) {
            this.monitorsInfiniteQuery.unload();
            this.#lastSearchQuery = search ?? '';
            this.loadInfiniteMonitors({});

            return;
        }

        this.monitorsInfiniteQuery.nextPage();
    };
}

export const sharedMonitorsInfiniteController =
    new MonitorsInfiniteController();
