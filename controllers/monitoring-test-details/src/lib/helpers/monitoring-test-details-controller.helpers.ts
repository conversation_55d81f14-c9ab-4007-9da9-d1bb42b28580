import { findKey, get } from 'lodash-es';
import { routeController } from '@controllers/route';
import type {
    MonitorInstanceMetadataResponseDto,
    MonitorInstanceResponseDto,
} from '@globals/api-sdk/types';
import { MONITOR_FIX_NOW_DESTINATION } from '../constants/monitoring-test-details.constants';

export const getPolicyMetaData = (
    monitor: MonitorInstanceResponseDto | undefined | null,
): {
    policyScope: string | null;
    policyName: string;
    policyGroups: MonitorInstanceMetadataResponseDto['groups'];
} => {
    const policyScope = get(monitor, 'metadata[0].policyScope', null);
    const policyName = get(monitor, 'metadata[0].policyName', '-');
    const policyGroups = get(monitor, 'metadata[0].groups', []);

    return { policyScope, policyName, policyGroups };
};

export interface MonitorFixNowDestinationResult {
    tests: string[];
    destination: string;
    route: string;
}

const getPolicyGroupsMonitorFixNowRoute = (
    policyGroups: { id: string }[],
): string => {
    const groupsUrlParams = policyGroups.map(
        (group, index) => `groupIds%5B${index}%5D=${group.id}`,
    );

    return `${routeController.userPartOfUrl}/governance/personnel/?${groupsUrlParams.join(
        '&',
    )}&sort=ACCEPTED_POLICIES&sortDir=ASC`;
};

export type MonitorFixNowDestinationOutput =
    | MonitorFixNowDestinationResult
    | null
    | string;

export const getMonitorFixNowDestination = ({
    testId,
    policyGroups,
    monitorHasPolicyGroups = false,
    isPolicyScopeNone = false,
}: {
    testId: string;
    policyGroups: { id: string }[];
    monitorHasPolicyGroups: boolean;
    isPolicyScopeNone: boolean;
}): MonitorFixNowDestinationOutput => {
    if (monitorHasPolicyGroups) {
        return getPolicyGroupsMonitorFixNowRoute(policyGroups);
    }

    const route = findKey(MONITOR_FIX_NOW_DESTINATION, (destination) =>
        destination.tests.includes(testId),
    );

    if (!route || isPolicyScopeNone) {
        return null;
    }

    return {
        ...MONITOR_FIX_NOW_DESTINATION[route],
        route: `${routeController.userPartOfUrl}${route}`,
    } as MonitorFixNowDestinationResult;
};
