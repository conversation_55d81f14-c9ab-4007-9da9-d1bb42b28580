import { sharedRiskInsightsController } from '@controllers/risk';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    calculatePreviousDate,
    getMonthsForPeriod,
} from './helpers/period-calculations.helper';
import {
    extractAvailableRiskLevels,
    generateFakeDataForMissingMonths,
    sortAndLimitDataPoints,
    transformRiskOverTimeData,
} from './helpers/risk-data-transformer.helpers';
import {
    getPeriodMonths,
    type PeriodValue,
    type RiskOverTimeData,
} from './risk-insights-over-time.types';

class RiskInsightsOverTimeController {
    constructor() {
        makeAutoObservable(this);
    }

    timePeriod: PeriodValue = '6months';

    get data(): RiskOverTimeData | null {
        const dashboardData = sharedRiskInsightsController.riskInsights;

        if (!dashboardData) {
            return null;
        }

        const availableRiskLevels = extractAvailableRiskLevels(dashboardData);

        const transformedData = transformRiskOverTimeData(
            dashboardData,
            availableRiskLevels,
        );

        const requiredMonths = getPeriodMonths(this.timePeriod);
        const dataWithFakeMonths = generateFakeDataForMissingMonths(
            transformedData,
            requiredMonths,
            availableRiskLevels,
        );

        const sortedData = sortAndLimitDataPoints(
            dataWithFakeMonths,
            this.timePeriod,
        );

        return {
            data: sortedData,
            period: this.timePeriod,
        };
    }

    get period(): PeriodValue {
        return this.timePeriod;
    }

    get availableRiskLevels(): string[] {
        const dashboardData = sharedRiskInsightsController.riskInsights;

        return dashboardData ? extractAvailableRiskLevels(dashboardData) : [];
    }

    setPeriod = (period: PeriodValue) => {
        this.timePeriod = period;
        this.load();
    };

    load = () => {
        if (!sharedWorkspacesController.currentWorkspace?.id) {
            return;
        }

        const previousDate = this.calculatePreviousDate(this.timePeriod);

        sharedRiskInsightsController.load({
            isScored: true,
            previousDate,
        });
    };

    calculatePreviousDate = (period: PeriodValue): string => {
        return calculatePreviousDate(period);
    };

    getMonthsForPeriod = (): string[] => {
        return getMonthsForPeriod(this.timePeriod);
    };
}

export const sharedRiskInsightsOverTimeController =
    new RiskInsightsOverTimeController();
