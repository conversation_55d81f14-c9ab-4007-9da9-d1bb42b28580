import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest';
import {
    calculatePreviousDate,
    getMonthsForPeriod,
} from './period-calculations.helper';

describe('period-calculations helper', () => {
    const mockDate = new Date('2024-06-15T12:00:00Z');

    beforeEach(() => {
        vi.useFakeTimers();
        vi.setSystemTime(mockDate);
    });

    afterEach(() => {
        vi.useRealTimers();
    });

    describe('calculatePreviousDate', () => {
        test('calculates previous date for 2 months', () => {
            const result = calculatePreviousDate('2months');

            expect(result).toBe('2024-04-01');
        });

        test('calculates previous date for 3 months', () => {
            const result = calculatePreviousDate('3months');

            expect(result).toBe('2024-03-01');
        });

        test('calculates previous date for 6 months', () => {
            const result = calculatePreviousDate('6months');

            expect(result).toBe('2023-12-01');
        });

        test('calculates previous date for 9 months', () => {
            const result = calculatePreviousDate('9months');

            expect(result).toBe('2023-09-01');
        });

        test('calculates previous date for 1 year', () => {
            const result = calculatePreviousDate('1year');

            expect(result).toBe('2023-06-01');
        });

        test('handles month overflow correctly', () => {
            // Set current date to January to test year rollback
            vi.setSystemTime(new Date('2024-01-15T12:00:00Z'));

            const result = calculatePreviousDate('3months');

            expect(result).toBe('2023-10-01');
        });
    });

    describe('getMonthsForPeriod', () => {
        test('returns all 12 months for 1 year period', () => {
            const result = getMonthsForPeriod('1year');

            expect(result).toHaveLength(12);
            expect(result).toStrictEqual([
                'Jan',
                'Feb',
                'Mar',
                'Apr',
                'May',
                'Jun',
                'Jul',
                'Aug',
                'Sep',
                'Oct',
                'Nov',
                'Dec',
            ]);
        });

        test('returns correct months for 2 months period', () => {
            // Current month is June (index 5), so should return Apr, May, June
            const result = getMonthsForPeriod('2months');

            expect(result).toHaveLength(3);
            expect(result).toStrictEqual(['Apr', 'May', 'Jun']);
        });

        test('returns correct months for 3 months period', () => {
            // Current month is June (index 5), so should return Mar, Apr, May, June
            const result = getMonthsForPeriod('3months');

            expect(result).toHaveLength(4);
            expect(result).toStrictEqual(['Mar', 'Apr', 'May', 'Jun']);
        });

        test('returns correct months for 6 months period', () => {
            // Current month is June (index 5), so should return Dec, Jan, Feb, Mar, Apr, May, June
            const result = getMonthsForPeriod('6months');

            expect(result).toHaveLength(7);
            expect(result).toStrictEqual([
                'Dec',
                'Jan',
                'Feb',
                'Mar',
                'Apr',
                'May',
                'Jun',
            ]);
        });

        test('returns correct months for 9 months period', () => {
            const result = getMonthsForPeriod('9months');

            expect(result).toHaveLength(10);
            expect(result).toStrictEqual([
                'Sep',
                'Oct',
                'Nov',
                'Dec',
                'Jan',
                'Feb',
                'Mar',
                'Apr',
                'May',
                'Jun',
            ]);
        });

        test('handles year rollover correctly', () => {
            // Set current date to February to test year rollback
            vi.setSystemTime(new Date('2024-02-15T12:00:00Z'));

            const result = getMonthsForPeriod('6months');

            expect(result).toHaveLength(7);
            expect(result).toStrictEqual([
                'Aug',
                'Sep',
                'Oct',
                'Nov',
                'Dec',
                'Jan',
                'Feb',
            ]);
        });

        test('handles edge case at beginning of year', () => {
            // Set current date to January
            vi.setSystemTime(new Date('2024-01-15T12:00:00Z'));

            const result = getMonthsForPeriod('3months');

            expect(result).toHaveLength(4);
            expect(result).toStrictEqual(['Oct', 'Nov', 'Dec', 'Jan']);
        });
    });
});
