import {
    getMonthName,
    getPeriodMonths,
    type PeriodValue,
} from '../risk-insights-over-time.types';

const MONTHS_PER_YEAR = 12;

export const calculatePreviousDate = (period: PeriodValue): string => {
    const now = new Date();
    const monthsToSubtract = getPeriodMonths(period);
    const previousDate = new Date(now);

    previousDate.setMonth(now.getMonth() - monthsToSubtract);
    previousDate.setDate(1);

    return previousDate.toISOString().split('T')[0];
};

export const getMonthsForPeriod = (period: PeriodValue): string[] => {
    const monthCount = getPeriodMonths(period);
    const result: string[] = [];

    if (monthCount === MONTHS_PER_YEAR) {
        for (let i = 0; i < MONTHS_PER_YEAR; i = i + 1) {
            result.push(getMonthName(i));
        }

        return result;
    }

    const now = new Date();
    const currentMonth = now.getMonth();

    // Include current month + previous months (monthCount total becomes monthCount + 1)
    const totalMonthsToShow = monthCount + 1;

    for (let i = 0; i < totalMonthsToShow; i = i + 1) {
        const monthIndex =
            (currentMonth - (totalMonthsToShow - 1 - i) + MONTHS_PER_YEAR) %
            MONTHS_PER_YEAR;

        result.push(getMonthName(monthIndex));
    }

    return result;
};
