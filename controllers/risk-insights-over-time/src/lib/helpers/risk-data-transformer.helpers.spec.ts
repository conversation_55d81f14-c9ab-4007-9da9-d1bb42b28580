import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest';
import type { DashboardResponseDto } from '@globals/api-sdk/types';
import type { RiskOverTimeDataPoint } from '../risk-insights-over-time.types';
import {
    extractAvailableRiskLevels,
    generateFakeDataForMissingMonths,
    sortAndLimitDataPoints,
    transformRiskOverTimeData,
} from './risk-data-transformer.helpers';

const createMockDashboardData = (
    overrides: Partial<DashboardResponseDto> = {},
): DashboardResponseDto => ({
    riskPosture: {},
    riskOverTime: {},
    treatmentOverview: {},
    riskHeatmap: [],
    categoryBreakdown: [],
    scored: 0,
    remaining: 0,
    ...overrides,
});

describe('risk data transformer helpers', () => {
    describe('extractAvailableRiskLevels', () => {
        test('returns empty array when no risk data exists', () => {
            const dashboardData = createMockDashboardData();

            const result = extractAvailableRiskLevels(dashboardData);

            expect(result).toStrictEqual([]);
        });

        test('extracts risk levels from riskPosture only', () => {
            const dashboardData = createMockDashboardData({
                riskPosture: {
                    HIGH_ID_123: 5,
                    MEDIUM_ID_456: 3,
                },
            });

            const result = extractAvailableRiskLevels(dashboardData);

            expect(result).toStrictEqual(['high', 'medium']);
        });

        test('extracts risk levels from riskOverTime only', () => {
            const dashboardData = createMockDashboardData({
                riskOverTime: {
                    '2024': {
                        '1': {
                            CRITICAL_ID_789: 2,
                            LOW_ID_101: 1,
                        },
                    },
                },
            });

            const result = extractAvailableRiskLevels(dashboardData);

            expect(result).toStrictEqual(['critical', 'low']);
        });

        test('combines and deduplicates risk levels from both sources', () => {
            const dashboardData = createMockDashboardData({
                riskPosture: {
                    HIGH_ID_123: 5,
                    MEDIUM_ID_456: 3,
                },
                riskOverTime: {
                    '2024': {
                        '1': {
                            HIGH_ID_789: 2,
                            CRITICAL_ID_101: 1,
                        },
                    },
                },
            });

            const result = extractAvailableRiskLevels(dashboardData);

            expect(result).toStrictEqual(['critical', 'high', 'medium']);
        });

        test('ignores invalid key formats', () => {
            const dashboardData = createMockDashboardData({
                riskPosture: {
                    HIGH_ID_123: 5,
                    INVALID_KEY: 3,
                    MEDIUM_ID_: 2,
                    _ID_456: 1,
                },
            });

            const result = extractAvailableRiskLevels(dashboardData);

            expect(result).toStrictEqual(['high']);
        });
    });

    describe('transformRiskOverTimeData', () => {
        test('returns empty array when riskOverTime is empty', () => {
            const dashboardData = createMockDashboardData();
            const availableRiskLevels = ['high', 'medium'];

            const result = transformRiskOverTimeData(
                dashboardData,
                availableRiskLevels,
            );

            expect(result).toStrictEqual([]);
        });

        test('transforms risk over time data correctly', () => {
            const dashboardData = createMockDashboardData({
                riskOverTime: {
                    '2024': {
                        '1': {
                            HIGH_ID_123: 5,
                            HIGH_ID_456: 3,
                            MEDIUM_ID_789: 2,
                        },
                    },
                },
            });
            const availableRiskLevels = ['high', 'medium', 'low'];

            const result = transformRiskOverTimeData(
                dashboardData,
                availableRiskLevels,
            );

            expect(result).toStrictEqual([
                {
                    month: 'Jan',
                    year: 2024,
                    high: 8,
                    medium: 2,
                    low: 0,
                },
            ]);
        });

        test('handles multiple months and years', () => {
            const dashboardData = createMockDashboardData({
                riskOverTime: {
                    '2023': {
                        '12': {
                            HIGH_ID_123: 3,
                        },
                    },
                    '2024': {
                        '1': {
                            MEDIUM_ID_456: 2,
                        },
                    },
                },
            });
            const availableRiskLevels = ['high', 'medium'];

            const result = transformRiskOverTimeData(
                dashboardData,
                availableRiskLevels,
            );

            expect(result).toStrictEqual([
                {
                    month: 'Dec',
                    year: 2023,
                    high: 3,
                    medium: 0,
                },
                {
                    month: 'Jan',
                    year: 2024,
                    high: 0,
                    medium: 2,
                },
            ]);
        });
    });

    describe('sortAndLimitDataPoints', () => {
        test('sorts data points chronologically', () => {
            const dataPoints = [
                { month: 'Mar', year: 2024, high: 1 },
                { month: 'Jan', year: 2024, high: 2 },
                { month: 'Dec', year: 2023, high: 3 },
            ];

            const result = sortAndLimitDataPoints(dataPoints, '1year');

            expect(result).toStrictEqual([
                { month: 'Dec', year: 2023, high: 3 },
                { month: 'Jan', year: 2024, high: 2 },
                { month: 'Mar', year: 2024, high: 1 },
            ]);
        });

        test('limits data points to specified period', () => {
            const dataPoints = [
                { month: 'Jan', year: 2024, high: 1 },
                { month: 'Feb', year: 2024, high: 2 },
                { month: 'Mar', year: 2024, high: 3 },
                { month: 'Apr', year: 2024, high: 4 },
                { month: 'May', year: 2024, high: 5 },
                { month: 'Jun', year: 2024, high: 6 },
                { month: 'Jul', year: 2024, high: 7 },
            ];

            const result = sortAndLimitDataPoints(dataPoints, '3months');

            expect(result).toStrictEqual([
                { month: 'Apr', year: 2024, high: 4 },
                { month: 'May', year: 2024, high: 5 },
                { month: 'Jun', year: 2024, high: 6 },
                { month: 'Jul', year: 2024, high: 7 },
            ]);
        });
    });

    describe('generateFakeDataForMissingMonths', () => {
        beforeEach(() => {
            // Mock the current date to August 15, 2024 for consistent testing
            vi.useFakeTimers();
            vi.setSystemTime(new Date('2024-08-15'));
        });

        afterEach(() => {
            vi.useRealTimers();
        });

        test('returns existing data when it has enough months', () => {
            const existingData: RiskOverTimeDataPoint[] = [
                { month: 'Jun', year: 2024, high: 5, medium: 3 },
                { month: 'Jul', year: 2024, high: 4, medium: 2 },
                { month: 'Aug', year: 2024, high: 6, medium: 1 },
            ];
            /**
             * +1 = 3 total months needed.
             */
            const requiredMonths = 2;
            const availableRiskLevels = ['high', 'medium'];

            const result = generateFakeDataForMissingMonths(
                existingData,
                requiredMonths,
                availableRiskLevels,
            );

            // Should return the existing data as-is since we have 3 months and need 3
            expect(result).toHaveLength(3);
            expect(result).toContain(existingData[0]);
            expect(result).toContain(existingData[1]);
            expect(result).toContain(existingData[2]);
        });

        test('generates fake data for missing months from current month backwards', () => {
            const existingData: RiskOverTimeDataPoint[] = [
                { month: 'Jul', year: 2024, high: 4, medium: 2 },
            ];
            /**
             * +1 = 3 total months needed (Jun, Jul, Aug).
             */
            const requiredMonths = 2;
            const availableRiskLevels = ['high', 'medium'];

            const result = generateFakeDataForMissingMonths(
                existingData,
                requiredMonths,
                availableRiskLevels,
            );

            expect(result).toHaveLength(3);
            expect(result).toStrictEqual([
                { month: 'Jun', year: 2024, high: 0, medium: 0 }, // Fake data
                { month: 'Jul', year: 2024, high: 4, medium: 2 }, // Existing data
                { month: 'Aug', year: 2024, high: 0, medium: 0 }, // Fake data
            ]);
        });

        test('handles empty existing data by generating all fake data', () => {
            const existingData: RiskOverTimeDataPoint[] = [];
            /**
             * +1 = 3 total months needed.
             */
            const requiredMonths = 2;
            const availableRiskLevels = ['high', 'medium', 'low'];

            const result = generateFakeDataForMissingMonths(
                existingData,
                requiredMonths,
                availableRiskLevels,
            );

            expect(result).toHaveLength(3);
            expect(result).toStrictEqual([
                { month: 'Jun', year: 2024, high: 0, medium: 0, low: 0 },
                { month: 'Jul', year: 2024, high: 0, medium: 0, low: 0 },
                { month: 'Aug', year: 2024, high: 0, medium: 0, low: 0 },
            ]);
        });

        test('handles cross-year boundaries correctly', () => {
            // Set current date to January 2024
            vi.setSystemTime(new Date('2024-01-15'));

            const existingData: RiskOverTimeDataPoint[] = [
                { month: 'Dec', year: 2023, high: 3, medium: 1 },
            ];
            /**
             * +1 = 3 total months needed (Nov 2023, Dec 2023, Jan 2024).
             */
            const requiredMonths = 2;
            const availableRiskLevels = ['high', 'medium'];

            const result = generateFakeDataForMissingMonths(
                existingData,
                requiredMonths,
                availableRiskLevels,
            );

            expect(result).toHaveLength(3);
            expect(result).toStrictEqual([
                { month: 'Nov', year: 2023, high: 0, medium: 0 }, // Fake data
                { month: 'Dec', year: 2023, high: 3, medium: 1 }, // Existing data
                { month: 'Jan', year: 2024, high: 0, medium: 0 }, // Fake data
            ]);
        });

        test('preserves existing data values and adds zeros for missing risk levels', () => {
            const existingData: RiskOverTimeDataPoint[] = [
                { month: 'Jul', year: 2024, high: 5 }, // Missing medium and low
            ];
            /**
             * +1 = 2 total months needed.
             */
            const requiredMonths = 1;
            const availableRiskLevels = ['high', 'medium', 'low'];

            const result = generateFakeDataForMissingMonths(
                existingData,
                requiredMonths,
                availableRiskLevels,
            );

            expect(result).toHaveLength(2);
            expect(result).toStrictEqual([
                { month: 'Jul', year: 2024, high: 5 }, // Existing data preserved as-is
                { month: 'Aug', year: 2024, high: 0, medium: 0, low: 0 }, // Fake data
            ]);
        });

        test('handles longer time periods correctly', () => {
            const existingData: RiskOverTimeDataPoint[] = [
                { month: 'May', year: 2024, high: 2, medium: 1 },
                { month: 'Aug', year: 2024, high: 4, medium: 3 },
            ];
            /**
             * +1 = 6 total months needed (Mar, Apr, May, Jun, Jul, Aug).
             */
            const requiredMonths = 5;
            const availableRiskLevels = ['high', 'medium'];

            const result = generateFakeDataForMissingMonths(
                existingData,
                requiredMonths,
                availableRiskLevels,
            );

            expect(result).toHaveLength(6);
            expect(result).toStrictEqual([
                { month: 'Mar', year: 2024, high: 0, medium: 0 }, // Fake
                { month: 'Apr', year: 2024, high: 0, medium: 0 }, // Fake
                { month: 'May', year: 2024, high: 2, medium: 1 }, // Existing
                { month: 'Jun', year: 2024, high: 0, medium: 0 }, // Fake
                { month: 'Jul', year: 2024, high: 0, medium: 0 }, // Fake
                { month: 'Aug', year: 2024, high: 4, medium: 3 }, // Existing
            ]);
        });

        test('handles no risk levels by creating data points with only month and year', () => {
            const existingData: RiskOverTimeDataPoint[] = [];
            /**
             * +1 = 2 total months needed.
             */
            const requiredMonths = 1;
            const availableRiskLevels: string[] = [];

            const result = generateFakeDataForMissingMonths(
                existingData,
                requiredMonths,
                availableRiskLevels,
            );

            expect(result).toHaveLength(2);
            expect(result).toStrictEqual([
                { month: 'Jul', year: 2024 },
                { month: 'Aug', year: 2024 },
            ]);
        });
    });
});
