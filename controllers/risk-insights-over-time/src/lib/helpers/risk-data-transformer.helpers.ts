import { isEmpty, isObject } from 'lodash-es';
import type { DashboardResponseDto } from '@globals/api-sdk/types';
import {
    getMonthName,
    getPeriodMonths,
    type PeriodValue,
    type RiskOverTimeDataPoint,
} from '../risk-insights-over-time.types';

/**
 * Gets the month order array using translated month names.
 * This ensures consistent ordering regardless of locale.
 *
 * @returns Array of translated month names in chronological order.
 * @example
 * // Returns: ['Jan', 'Feb', 'Mar', ...] (translated to current locale)
 * const months = getMonthOrder();
 */
const getMonthOrder = (): string[] => {
    return Array.from({ length: 12 }, (_, index) => getMonthName(index));
};

/**
 * Extracts unique risk levels from dashboard data.
 * Combines risk levels from both riskPosture and riskOverTime data sources.
 * Parses risk keys with format "LEVEL_ID_123" to extract the level name.
 * Ignores keys that don't match the expected format.
 *
 * @param dashboardData - Dashboard response containing risk posture and risk over time data.
 * @returns Array of unique risk levels in lowercase, sorted alphabetically.
 * @example
 * // Input: \{
 * //   riskPosture: \{ "HIGH_ID_123": 5, "MEDIUM_ID_456": 3 \},
 * //   riskOverTime: \{ "2024": \{ "1": \{ "CRITICAL_ID_789": 2, "LOW_ID_101": 1 \} \} \}
 * // \}
 * // Output: ["critical", "high", "low", "medium"]
 * const levels = extractAvailableRiskLevels(dashboardData);
 */
export const extractAvailableRiskLevels = (
    dashboardData: DashboardResponseDto,
): string[] => {
    // Extract keys from both data sources - properties should exist per type definition
    const riskPostureKeys = Object.keys(dashboardData.riskPosture);
    const riskOverTimeKeys = Object.values(dashboardData.riskOverTime)
        .flatMap((yearData) =>
            Object.values(yearData as Record<string, unknown>),
        )
        .flatMap((monthData) =>
            Object.keys(monthData as Record<string, unknown>),
        );

    const allKeys = [...riskPostureKeys, ...riskOverTimeKeys];

    // Extract and normalize risk levels from keys
    const riskLevels = allKeys
        .map((key) => {
            const parts = key.split('_ID_');

            return parts.length === 2 && parts[0] && parts[1]
                ? parts[0].toLowerCase()
                : '';
        })
        .filter(Boolean);

    // Remove duplicates and sort alphabetically
    return [...new Set(riskLevels)].sort((a, b) => a.localeCompare(b));
};

/**
 * Transforms raw dashboard risk over time data into chart-ready data points.
 * Aggregates risk values by level for each month/year combination.
 *
 * @param dashboardData - Dashboard response containing riskOverTime data.
 * @param availableRiskLevels - Array of risk levels to include in transformation.
 * @returns Array of data points with month, year, and aggregated risk level values.
 * @example
 * // Input riskOverTime: \{ "2024": \{ "1": \{ "HIGH_ID_123": 5, "HIGH_ID_456": 3 \} \} \}
 * // Input availableRiskLevels: ["high", "medium"]
 * // Output: [\{ month: "Jan", year: 2024, high: 8, medium: 0 \}]
 * const dataPoints = transformRiskOverTimeData(dashboardData, ["high", "medium"]);
 */
export const transformRiskOverTimeData = (
    dashboardData: DashboardResponseDto,
    availableRiskLevels: string[],
): RiskOverTimeDataPoint[] => {
    const { riskOverTime } = dashboardData;

    if (isEmpty(Object.keys(riskOverTime))) {
        return [];
    }

    const dataPoints: RiskOverTimeDataPoint[] = [];

    Object.entries(riskOverTime).forEach(([year, months]) => {
        if (isObject(months)) {
            Object.entries(months).forEach(([month, riskData]) => {
                const monthName = getMonthName(parseInt(month) - 1);
                const dataPoint: RiskOverTimeDataPoint = {
                    month: monthName,
                    year: parseInt(year),
                };

                availableRiskLevels.forEach((level) => {
                    const levelEntries = Object.entries(
                        riskData as Record<string, unknown>,
                    ).filter(([key]) =>
                        key.toLowerCase().startsWith(level.toLowerCase()),
                    );

                    const totalValue = levelEntries.reduce((sum, [, value]) => {
                        return sum + (Number(value) || 0);
                    }, 0);

                    dataPoint[level] = totalValue;
                });

                dataPoints.push(dataPoint);
            });
        }
    });

    return dataPoints;
};

/**
 * Sorts data points chronologically and limits to the specified time period.
 * Uses translated month names for consistent sorting across locales.
 *
 * @param dataPoints - Array of risk data points to sort and limit.
 * @param period - Time period to limit data to (e.g., '6months', '1year').
 * @returns Sorted and limited array of data points (most recent entries).
 * @example
 * // Input: Mixed order data points from Jan 2023 to Dec 2024
 * // Period: '6months'
 * // Output: Last 6 months of data, sorted chronologically
 * const recentData = sortAndLimitDataPoints(allDataPoints, '6months');
 */
export const sortAndLimitDataPoints = (
    dataPoints: RiskOverTimeDataPoint[],
    period: PeriodValue,
): RiskOverTimeDataPoint[] => {
    /**
     * +1 to include current month.
     */
    const maxMonths = getPeriodMonths(period) + 1;

    const sortedPoints = dataPoints.toSorted((a, b) => {
        const monthOrder = getMonthOrder();

        if (a.year !== b.year) {
            return a.year - b.year;
        }

        return monthOrder.indexOf(a.month) - monthOrder.indexOf(b.month);
    });

    return sortedPoints.slice(-maxMonths);
};

/**
 * Generates fake data points to fill missing months when transformedData
 * has fewer months than required by the time period.
 *
 * @param existingData - Array of existing data points.
 * @param requiredMonths - Number of months required by the time period.
 * @param availableRiskLevels - Array of risk levels to include in fake data.
 * @returns Array of data points with fake data for missing months, including existing data.
 */
export const generateFakeDataForMissingMonths = (
    existingData: RiskOverTimeDataPoint[],
    requiredMonths: number,
    availableRiskLevels: string[],
): RiskOverTimeDataPoint[] => {
    /**
     * +1 to include current month.
     */
    const totalRequiredMonths = requiredMonths + 1;

    const now = new Date();

    const existingDataMap = new Map<string, RiskOverTimeDataPoint>();

    existingData.forEach((dataPoint) => {
        const key = `${dataPoint.year}-${dataPoint.month}`;

        existingDataMap.set(key, dataPoint);
    });

    const dataPoints = Array.from(
        { length: totalRequiredMonths },
        (_, index) => {
            const targetDate = new Date(
                now.getFullYear(),
                now.getMonth() - index,
                1,
            );
            const monthName = getMonthName(targetDate.getMonth());
            const year = targetDate.getFullYear();
            const key = `${year}-${monthName}`;

            // Use existing data if available, otherwise create fake data
            const existingDataPoint = existingDataMap.get(key);

            if (existingDataPoint) {
                return existingDataPoint;
            }

            const fakeDataPoint: RiskOverTimeDataPoint = {
                month: monthName,
                year,
            };

            // Adds zero values for all faked risk levels
            availableRiskLevels.forEach((level) => {
                fakeDataPoint[level] = 0;
            });

            return fakeDataPoint;
        },
    );

    // Reverse to get chronological order (oldest to newest)
    return dataPoints.reverse();
};
