import type { Note<PERSON><PERSON><PERSON><PERSON> } from '@components/utilities';
import { snackbarController } from '@controllers/snackbar';
import {
    grcControllerCreateNoteMutation,
    grcControllerDeleteNoteMutation,
    grcControllerGetNotesOptions,
    grcControllerUpdateNoteMutation,
} from '@globals/api-sdk/queries';
import type { NoteRequestDto, NoteResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    action,
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

class ControlNotesController {
    constructor() {
        makeAutoObservable(this);
    }

    createNoteMutation = new ObservedMutation(grcControllerCreateNoteMutation);
    updateNoteMutation = new ObservedMutation(grcControllerUpdateNoteMutation);
    deleteNoteMutation = new ObservedMutation(grcControllerDeleteNoteMutation);
    getNoteQuery = new ObservedQuery(grcControllerGetNotesOptions);

    controlId: number | null = null;

    get isCreating(): boolean {
        return this.createNoteMutation.isPending;
    }

    get hasError(): boolean {
        return this.createNoteMutation.hasError;
    }

    get isUpdating(): boolean {
        return this.updateNoteMutation.isPending;
    }

    get hasUpdateError(): boolean {
        return this.updateNoteMutation.hasError;
    }

    get isDeleting(): boolean {
        return this.deleteNoteMutation.isPending;
    }

    get hasDeleteError(): boolean {
        return this.deleteNoteMutation.hasError;
    }

    get isLoading(): boolean {
        return this.getNoteQuery.isLoading;
    }

    get list(): NoteResponseDto[] {
        return this.getNoteQuery.data?.data ?? [];
    }

    get total(): number {
        return this.getNoteQuery.data?.total ?? 0;
    }

    load = (controlId: number): void => {
        this.controlId = controlId;

        this.getNoteQuery.load({
            path: { id: controlId },
        });
    };

    invalidate = (): void => {
        this.getNoteQuery.invalidate();
    };

    updateNote = (noteId: string, note: NoteRequestDto): void => {
        this.updateNoteMutation.mutate({
            path: { noteId },
            body: { comment: note.comment },
        });

        when(
            () => !this.isUpdating,
            () => {
                if (this.updateNoteMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'control-note-update-error',
                        props: {
                            title: t`Unable to update note`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.getNoteQuery.invalidate();

                snackbarController.addSnackbar({
                    id: 'control-note-update-success',
                    props: {
                        title: t`Note updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    deleteNote = (noteId: string): void => {
        openConfirmationModal({
            title: t`Delete note?`,
            body: t`This action is permanent. The note and all related data will be removed`,
            confirmText: t`Delete note`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: action(() => {
                const timestamp = new Date().toISOString();

                this.deleteNoteMutation.mutate({
                    path: {
                        noteId,
                    },
                });

                when(
                    () => !this.isDeleting,
                    () => {
                        if (this.hasDeleteError) {
                            snackbarController.addSnackbar({
                                id: `${timestamp}-deleted-event-note-error`,
                                props: {
                                    title: t`Unable to delete note`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            return;
                        }

                        this.getNoteQuery.invalidate();

                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-event-note-success`,
                            props: {
                                title: t`Note deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    },
                );
            }),
            onCancel: closeConfirmationModal,
        });
    };

    createNote = (note: NoteCreateDto, onSuccess?: () => void): void => {
        if (!this.controlId) {
            throw new Error('Control ID is not set');
        }

        this.createNoteMutation.mutate({
            path: { id: this.controlId },
            body: { comment: note.comment ?? '' },
        });

        when(
            () => !this.isCreating,
            () => {
                if (this.createNoteMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'control-note-create-error',
                        props: {
                            title: t`Unable to create note`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.getNoteQuery.invalidate();
                onSuccess?.();

                snackbarController.addSnackbar({
                    id: 'control-note-create-success',
                    props: {
                        title: t`Note created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedControlNotesController = new ControlNotesController();
