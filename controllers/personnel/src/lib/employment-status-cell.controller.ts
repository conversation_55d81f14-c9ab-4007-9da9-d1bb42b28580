import React from 'react';
import { modalController } from '@controllers/modal';
import { personnelControllerUpdateStatusMutation } from '@globals/api-sdk/queries';
import type { EmploymentStatusEnum } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { createEmploymentStatusMutationOptions } from './employment-status-mutation.helper';

type EmploymentStatus = EmploymentStatusEnum;

export class EmploymentStatusCellController {
    personnelId: number;
    employmentStatus: EmploymentStatus;

    constructor(personnelId: number, status: EmploymentStatus) {
        this.personnelId = personnelId;
        this.employmentStatus = status;
        makeAutoObservable(this);
    }

    updateStatusMutation = new ObservedMutation(
        personnelControllerUpdateStatusMutation,
        createEmploymentStatusMutationOptions('update-status'),
    );

    get isUpdating(): boolean {
        return this.updateStatusMutation.isPending;
    }

    updateEmploymentStatus = (newStatus: EmploymentStatus): void => {
        this.updateStatusMutation.mutate({
            path: { personnelId: this.personnelId },
            body: { employmentStatus: newStatus },
        });
    };

    updateEmploymentStatusWithDates = (
        newStatus: EmploymentStatus,
        startDate: string,
        separationDate: string,
    ): void => {
        this.updateStatusMutation.mutate({
            path: { personnelId: this.personnelId },
            body: {
                employmentStatus: newStatus,
                startDate,
                separationDate,
            },
        });
    };

    openSeparationDateModal = (
        _currentStartDate: string | null,
        _newEmploymentStatus: EmploymentStatus,
        modalContent: React.ComponentType,
        separationDateModalId: string,
    ): void => {
        modalController.openModal({
            id: separationDateModalId,
            content: () => React.createElement(modalContent),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'md',
        });
    };

    closeSeparationDateModal = (separationDateModalId: string): void => {
        modalController.closeModal(separationDateModalId);
    };

    handleSeparationDateSubmit = (
        data: {
            startDate: string;
            separationDate: string;
            employmentStatus: EmploymentStatus;
        },
        separationDateModalId?: string,
    ): void => {
        // Create a new mutation with onSuccess callback to close the modal
        const separationDateMutation = new ObservedMutation(
            personnelControllerUpdateStatusMutation,
            createEmploymentStatusMutationOptions(
                'update-separation-date',
                () => {
                    // Close the modal if modalId is provided
                    if (separationDateModalId) {
                        modalController.closeModal(separationDateModalId);
                    }
                },
            ),
        );

        separationDateMutation.mutate({
            path: { personnelId: this.personnelId },
            body: {
                employmentStatus: data.employmentStatus,
                startDate: data.startDate,
                separationDate: data.separationDate,
            },
        });
    };
}
