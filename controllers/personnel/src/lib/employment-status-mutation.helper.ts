import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';
import { sharedPersonnelController } from '@views/personnel';

/**
 * Creates standard mutation options for employment status updates
 * with optional additional success/error callbacks.
 */
export const createEmploymentStatusMutationOptions = (
    scope: string,
    additionalOnSuccess?: () => void,
    additionalOnError?: () => void,
): {
    onSuccess: () => void;
    onError: () => void;
} => ({
    onSuccess: () => {
        // Invalidate the personnel query to refresh the data
        sharedPersonnelController.personnelQuery.invalidate();

        // Show success snackbar with scope-based ID
        snackbarController.addSnackbar({
            id: `employment-status-update-${scope}`,
            props: {
                title: t`Employment status updated successfully`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });

        // Call additional onSuccess callback if provided
        if (additionalOnSuccess) {
            additionalOnSuccess();
        }
    },
    onError: () => {
        // Invalidate the personnel query to refresh the data
        sharedPersonnelController.personnelQuery.invalidate();

        // Show error snackbar with scope-based ID
        snackbarController.addSnackbar({
            id: `employment-status-update-error-${scope}`,
            props: {
                title: t`Failed to update employment status`,
                description: t`An error occurred while updating the employment status. Please try again.`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });

        // Call additional onError callback if provided
        if (additionalOnError) {
            additionalOnError();
        }
    },
});
