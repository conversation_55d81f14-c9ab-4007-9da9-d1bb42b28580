import { snackbarController } from '@controllers/snackbar';
import { personnelControllerUpdateStatusMutation } from '@globals/api-sdk/queries';
import type { EmploymentStatusEnum } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedPersonnelController } from '@views/personnel';

export class NotHumanReasonModalController {
    constructor() {
        makeAutoObservable(this);
    }

    updateStatusMutation = new ObservedMutation(
        personnelControllerUpdateStatusMutation,
        {
            onSuccess: () => {
                // Invalidate the personnel query to refresh the data
                sharedPersonnelController.personnelQuery.invalidate();

                // Show success snackbar
                snackbarController.addSnackbar({
                    id: 'employment-status-update-success',
                    props: {
                        title: t`Employment status updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                // Invalidate the personnel query to refresh the data
                sharedPersonnelController.personnelQuery.invalidate();
                // Show error snackbar
                snackbarController.addSnackbar({
                    id: 'employment-status-update-error',
                    props: {
                        title: t`Failed to update employment status`,
                        description: t`An error occurred while updating the employment status. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get isUpdating(): boolean {
        return this.updateStatusMutation.isPending;
    }

    updateEmploymentStatusWithReason = (
        personnelId: number,
        employmentStatus: EmploymentStatusEnum,
        notHumanReason: string,
    ): void => {
        this.updateStatusMutation.mutate({
            path: { personnelId },
            body: {
                employmentStatus,
                notHumanReason,
            },
        });
    };
}

export const sharedNotHumanReasonModalController =
    new NotHumanReasonModalController();
