import { isEmpty, sortBy, uniqueId } from 'lodash-es';
import { openLinkControlsModalWithWorkspace } from '@components/evidence-library';
import { sharedRiskPartiallyMutationController } from '@controllers/risk';
import { snackbarController } from '@controllers/snackbar';
import type { DatatableRowSelectionState } from '@cosmos/components/datatable';
import type { RiskControlResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, runInAction, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedRiskDetailsController } from './risk-details-controller';

class RiskMitigationControlsController {
    selectedControlIds: string[] = [];
    isAllRowsSelected = false;

    constructor() {
        makeAutoObservable(this);
    }

    handleRowSelection(
        currentRowSelectionState: DatatableRowSelectionState,
    ): void {
        const { selectedRows, isAllRowsSelected: allSelected } =
            currentRowSelectionState;

        runInAction(() => {
            this.selectedControlIds = Object.keys(selectedRows);
            this.isAllRowsSelected = allSelected;
        });
    }

    resetSelections(): void {
        runInAction(() => {
            this.selectedControlIds = [];
            this.isAllRowsSelected = false;
        });
    }

    get hasSelections(): boolean {
        return this.isAllRowsSelected || !isEmpty(this.selectedControlIds);
    }

    get canMapControls(): boolean {
        const { hasRiskManagePermission } = sharedFeatureAccessModel;
        const { currentWorkspace } = sharedWorkspacesController;

        return hasRiskManagePermission && Boolean(currentWorkspace?.primary);
    }

    get canPerformRestrictedOperations(): boolean {
        const { hasRiskManagePermission, isRiskManagerWithRestrictedView } =
            sharedFeatureAccessModel;

        return hasRiskManagePermission && !isRiskManagerWithRestrictedView;
    }

    get canUnmapControls(): boolean {
        return this.canPerformRestrictedOperations;
    }

    get canBulkUnmapControls(): boolean {
        return this.canUnmapControls;
    }

    get isRestrictedView(): boolean {
        return sharedFeatureAccessModel.isRiskManagerWithRestrictedView;
    }

    get canViewControls(): boolean {
        const { currentWorkspace } = sharedWorkspacesController;

        return Boolean(currentWorkspace?.primary);
    }

    get canCreateControls(): boolean {
        return this.canPerformRestrictedOperations;
    }

    sortControlsByName = (
        controls: { id: number; name?: string }[],
    ): { id: number }[] => {
        const sortedControls = sortBy(
            controls,
            (control) => control.name?.toLowerCase() || '',
        );

        return sortedControls.map((control) => ({ id: control.id }));
    };

    showPermissionError = (actionName: string): void => {
        snackbarController.addSnackbar({
            id: `${actionName}-permission-error-${uniqueId()}`,
            props: {
                title: t`Access denied`,
                description: this.isRestrictedView
                    ? t`Contact your administrator to ${actionName} controls from risks.`
                    : t`You don't have permission to ${actionName} controls from risks.`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };

    showSuccessMessage = (message: string): void => {
        snackbarController.addSnackbar({
            id: `control-action-success-${uniqueId()}`,
            props: {
                title: message,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };

    showErrorMessage = (title: string, description: string): void => {
        snackbarController.addSnackbar({
            id: `control-action-error-${uniqueId()}`,
            props: {
                title,
                description,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };

    handleUnmapControl = (
        riskId: string,
        control: RiskControlResponseDto,
        onSuccess?: () => void,
    ): void => {
        if (!this.canUnmapControls) {
            this.showPermissionError('unmap');

            return;
        }

        const { controls } = sharedRiskDetailsController;

        openConfirmationModal({
            title: t`Unmap control?`,
            body: t`This control will no longer be mapped to this requirement.`,
            confirmText: t`Unmap control`,
            cancelText: t`Cancel`,
            type: 'danger',
            size: 'sm',
            disableClickOutsideToClose: true,
            isLoading: () => sharedRiskPartiallyMutationController.isPending,
            onCancel: () => {
                closeConfirmationModal();
            },
            onConfirm: () => {
                const remainingControlsWithNames = controls
                    .filter((riskControl) => riskControl.id !== control.id)
                    .map((riskControl) => ({
                        id: riskControl.id,
                        name: riskControl.name,
                    }));

                const sortedRemainingControls = this.sortControlsByName(
                    remainingControlsWithNames,
                );

                sharedRiskPartiallyMutationController.updateRiskPartially(
                    riskId,
                    {
                        controls: sortedRemainingControls,
                    },
                );

                when(
                    () => !sharedRiskPartiallyMutationController.isPending,
                    () => {
                        if (sharedRiskPartiallyMutationController.hasError) {
                            this.showErrorMessage(
                                t`Failed to unmap control`,
                                t`An error occurred while removing the control mapping. Please try again.`,
                            );
                        } else {
                            this.showSuccessMessage(
                                t`Control unmapped successfully`,
                            );
                            closeConfirmationModal();
                            sharedRiskDetailsController.loadRiskDetails(riskId);
                            onSuccess?.();
                        }
                    },
                );
            },
        });
    };

    handleMapControls(riskId: string): void {
        if (!this.canMapControls) {
            snackbarController.addSnackbar({
                id: `map-controls-permission-error-${uniqueId()}`,
                props: {
                    title: t`Access denied`,
                    description: this.isRestrictedView
                        ? t`Contact your administrator to map controls to risks.`
                        : t`You don't have permission to map controls to risks.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const { currentWorkspace } = sharedWorkspacesController;
        const { controls } = sharedRiskDetailsController;

        if (!currentWorkspace || !riskId) {
            return;
        }

        openLinkControlsModalWithWorkspace({
            objectType: 'risk',
            onConfirm: (selectedControls) => {
                // Map the selected controls to the risk
                const newControlsData = selectedControls
                    .map((item) => ({
                        id: item.controlData.id,
                        name: item.controlData.name,
                    }))
                    .filter(
                        (control): control is { id: number; name: string } =>
                            control.id !== undefined &&
                            control.name !== undefined,
                    );

                const allControlsWithNames = [
                    ...controls.map((control) => ({
                        id: control.id,
                        name: control.name,
                    })),
                    ...newControlsData,
                ];

                const sortedControls =
                    this.sortControlsByName(allControlsWithNames);

                sharedRiskPartiallyMutationController.updateRiskPartially(
                    riskId,
                    {
                        controls: sortedControls,
                    },
                );

                when(
                    () => !sharedRiskPartiallyMutationController.isPending,
                    () => {
                        if (sharedRiskPartiallyMutationController.hasError) {
                            snackbarController.addSnackbar({
                                id: `map-controls-error-${uniqueId()}`,
                                props: {
                                    title: t`Failed to map controls`,
                                    description: t`An error occurred while mapping the controls. Please try again.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                        } else {
                            snackbarController.addSnackbar({
                                id: `map-controls-success-${uniqueId()}`,
                                props: {
                                    title: t`Controls mapped successfully`,
                                    severity: 'success',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            sharedRiskDetailsController.loadRiskDetails(riskId);
                        }
                    },
                );
            },
            excludeControlIds: controls.map((control) => control.id),
            workspaceId: currentWorkspace.id,
        });
    }

    handleBulkUnmapControls(riskId: string, onSuccess?: () => void): void {
        if (!this.canBulkUnmapControls) {
            snackbarController.addSnackbar({
                id: `bulk-unmap-controls-permission-error-${uniqueId()}`,
                props: {
                    title: t`Access denied`,
                    description: this.isRestrictedView
                        ? t`Contact your administrator to unmap controls from risks.`
                        : t`You don't have permission to unmap controls from risks.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        if (!riskId || !this.hasSelections) {
            return;
        }

        const { controls } = sharedRiskDetailsController;

        openConfirmationModal({
            title: t`Unmap selected controls?`,
            body: t`These controls will no longer be mapped to this requirement.`,
            confirmText: t`Unmap controls`,
            cancelText: t`Cancel`,
            type: 'danger',
            size: 'sm',
            disableClickOutsideToClose: true,
            isLoading: () => sharedRiskPartiallyMutationController.isPending,
            onCancel: () => {
                closeConfirmationModal();
            },
            onConfirm: () => {
                let remainingControls: { id: number }[];

                if (this.isAllRowsSelected) {
                    // If "Select all" was used, unmap all controls
                    remainingControls = [];
                } else {
                    const selectedIds = this.selectedControlIds.map(
                        (id: string) => parseInt(id, 10),
                    );

                    const remainingControlsWithNames = controls
                        .filter((control) => !selectedIds.includes(control.id))
                        .map((control) => ({
                            id: control.id,
                            name: control.name,
                        }));

                    remainingControls = this.sortControlsByName(
                        remainingControlsWithNames,
                    );
                }

                sharedRiskPartiallyMutationController.updateRiskPartially(
                    riskId,
                    {
                        controls: remainingControls,
                    },
                );

                when(
                    () => !sharedRiskPartiallyMutationController.isPending,
                    () => {
                        if (sharedRiskPartiallyMutationController.hasError) {
                            snackbarController.addSnackbar({
                                id: `bulk-unmap-controls-error-${uniqueId()}`,
                                props: {
                                    title: t`Failed to unmap controls`,
                                    description: t`An error occurred while removing the control mappings. Please try again.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                        } else {
                            snackbarController.addSnackbar({
                                id: `bulk-unmap-controls-success-${uniqueId()}`,
                                props: {
                                    title: t`Controls unmapped successfully`,
                                    severity: 'success',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            closeConfirmationModal();
                            onSuccess?.();
                            sharedRiskDetailsController.loadRiskDetails(riskId);
                        }
                    },
                );
            },
        });
    }
}

export const sharedRiskMitigationControlsController =
    new RiskMitigationControlsController();
