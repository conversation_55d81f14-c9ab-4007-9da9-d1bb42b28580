import type { NoteCreateDto } from '@components/utilities';
import { snackbarController } from '@controllers/snackbar';
import {
    riskManagementControllerAppendNoteToRiskMutation,
    riskManagementControllerDeleteRiskNoteMutation,
    riskManagementControllerUpdateRiskNoteMutation,
} from '@globals/api-sdk/queries';
import type { RiskNoteRequestDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import {
    action,
    makeAutoObservable,
    ObservedMutation,
    when,
} from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedRiskDetailsController } from './risk-details-controller';

class RiskNotesController {
    constructor() {
        makeAutoObservable(this);
    }

    createNoteMutation = new ObservedMutation(
        riskManagementControllerAppendNoteToRiskMutation,
    );
    updateNoteMutation = new ObservedMutation(
        riskManagementControllerUpdateRiskNoteMutation,
    );
    deleteNoteMutation = new ObservedMutation(
        riskManagementControllerDeleteRiskNoteMutation,
    );

    riskId: string | null = null;

    get isCreating(): boolean {
        return this.createNoteMutation.isPending;
    }

    get hasError(): boolean {
        return this.createNoteMutation.hasError;
    }

    get isUpdating(): boolean {
        return this.updateNoteMutation.isPending;
    }

    get hasUpdateError(): boolean {
        return this.updateNoteMutation.hasError;
    }

    get isDeleting(): boolean {
        return this.deleteNoteMutation.isPending;
    }

    get hasDeleteError(): boolean {
        return this.deleteNoteMutation.hasError;
    }

    setRiskId = (riskId: string): void => {
        this.riskId = riskId;
    };

    createNote = (note: NoteCreateDto, onSuccess?: () => void): void => {
        if (!this.riskId) {
            throw new Error('Risk ID is not set');
        }

        this.createNoteMutation.mutate({
            path: { risk_id: this.riskId },
            body: { comment: note.comment ?? '' },
        });

        when(
            () => !this.isCreating,
            () => {
                if (this.createNoteMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'risk-note-create-error',
                        props: {
                            title: t`Unable to create note`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Refresh risk details to show updated notes
                if (this.riskId) {
                    sharedRiskDetailsController.loadRiskDetails(this.riskId);
                }

                onSuccess?.();

                snackbarController.addSnackbar({
                    id: 'risk-note-create-success',
                    props: {
                        title: t`Note created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    updateNote = (noteId: string, note: RiskNoteRequestDto): void => {
        if (!this.riskId) {
            throw new Error('Risk ID is not set');
        }

        this.updateNoteMutation.mutate({
            path: { risk_id: this.riskId, note_id: Number(noteId) },
            body: { comment: note.comment },
        });

        when(
            () => !this.isUpdating,
            () => {
                if (this.updateNoteMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'risk-note-update-error',
                        props: {
                            title: t`Unable to update note`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Refresh risk details to show updated notes
                if (this.riskId) {
                    sharedRiskDetailsController.loadRiskDetails(this.riskId);
                }

                snackbarController.addSnackbar({
                    id: 'risk-note-update-success',
                    props: {
                        title: t`Note updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    get isReadOnly(): boolean {
        return !sharedCurrentUserController.hasUserPermission('Risk', 'MANAGE');
    }

    deleteNote = (noteId: string): void => {
        openConfirmationModal({
            title: t`Delete note?`,
            body: t`This action is permanent. The note and all related data will be removed.`,
            confirmText: t`Delete note`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: action(() => {
                if (!this.riskId) {
                    throw new Error('Risk ID is not set');
                }

                const timestamp = new Date().toISOString();

                this.deleteNoteMutation.mutate({
                    path: {
                        risk_id: this.riskId,
                        note_id: Number(noteId),
                    },
                });

                when(
                    () => !this.isDeleting,
                    () => {
                        if (this.hasDeleteError) {
                            snackbarController.addSnackbar({
                                id: `${timestamp}-deleted-risk-note-error`,
                                props: {
                                    title: t`Unable to delete note`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            return;
                        }

                        // Refresh risk details to show updated notes
                        if (this.riskId) {
                            sharedRiskDetailsController.loadRiskDetails(
                                this.riskId,
                            );
                        }

                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-risk-note-success`,
                            props: {
                                title: t`Note deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    },
                );
            }),
            onCancel: closeConfirmationModal,
        });
    };
}

export const sharedRiskNotesController = new RiskNotesController();
