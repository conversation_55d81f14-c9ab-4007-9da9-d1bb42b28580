import { isObject, isString, orderBy } from 'lodash-es';
import type { ClosedTicketsControllerBase } from '@components/utilities';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { snackbarController } from '@controllers/snackbar';
import {
    riskManagementTicketsControllerGetTicketsByRiskIdInfiniteOptions,
    riskManagementTicketsControllerGetTicketsByRiskIdOptions,
    riskManagementTicketsControllerUnlinkTicketMutation,
} from '@globals/api-sdk/queries';
import type { TicketResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedMutation,
    ObservedQuery,
} from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

const getObjectName = (): string => t`risk`;
const getObjectNamePlural = (): string => t`risks`;

const getErrorStatusCode = (error: unknown): string | number => {
    if (isObject(error) && 'status' in error) {
        return error.status as string | number;
    }
    if (isObject(error) && 'statusCode' in error) {
        return error.statusCode as string | number;
    }

    return 'unknown';
};

class RiskTicketsController implements ClosedTicketsControllerBase {
    ticketsInProgressForRisksQuery = new ObservedInfiniteQuery(
        riskManagementTicketsControllerGetTicketsByRiskIdInfiniteOptions,
    );
    ticketsCompletedForRisksQuery = new ObservedQuery(
        riskManagementTicketsControllerGetTicketsByRiskIdOptions,
    );
    ticketsCompletedInfiniteQuery = new ObservedInfiniteQuery(
        riskManagementTicketsControllerGetTicketsByRiskIdInfiniteOptions,
    );

    unlinkTicketMutation = new ObservedMutation(
        riskManagementTicketsControllerUnlinkTicketMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get objectName(): string {
        return getObjectName();
    }

    get objectNamePlural(): string {
        return getObjectNamePlural();
    }

    get ticketsInProgress(): TicketResponseDto[] {
        const tickets =
            this.ticketsInProgressForRisksQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? [];

        return orderBy(tickets, 'createdAt', 'desc');
    }

    get hasNextPage(): boolean {
        return this.ticketsInProgressForRisksQuery.hasNextPage;
    }

    loadNextPage = (): void => {
        this.ticketsInProgressForRisksQuery.nextPage();
    };

    get isLoadingTicketsInProgress(): boolean {
        return this.ticketsInProgressForRisksQuery.isLoading;
    }

    get totalTicketsInProgress(): number {
        return this.ticketsInProgress.length;
    }

    get ticketsCompleted(): TicketResponseDto[] {
        return this.ticketsCompletedForRisksQuery.data?.data ?? [];
    }

    get isLoadingTicketsCompleted(): boolean {
        return this.ticketsCompletedForRisksQuery.isLoading;
    }

    get totalTicketsCompleted(): number {
        return this.ticketsCompletedForRisksQuery.data?.total ?? 0;
    }

    get userHasPermissionToCreateTicket(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'RiskManagementTicket',
            'MANAGE',
        );
    }

    get isUnlinkingTicket(): boolean {
        return this.unlinkTicketMutation.isPending;
    }

    get ticketsCompletedInfinite(): TicketResponseDto[] {
        return (
            this.ticketsCompletedInfiniteQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get totalTicketsCompletedInfinite(): number {
        return this.ticketsCompletedInfinite.length;
    }

    get isLoadingTicketsCompletedInfinite(): boolean {
        return this.ticketsCompletedInfiniteQuery.isLoading;
    }

    get hasNextPageCompleted(): boolean {
        return this.ticketsCompletedInfiniteQuery.hasNextPage;
    }

    loadNextPageCompleted = (): void => {
        this.ticketsCompletedInfiniteQuery.nextPage();
    };

    unlinkTicket = (ticketId: number): void => {
        const stringRiskId = sharedRiskDetailsController.riskDetails?.riskId;

        if (!stringRiskId) {
            snackbarController.addSnackbar({
                id: 'risk-not-loaded',
                props: {
                    title: t`Risk details not loaded`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const ticket = this.ticketsInProgress.find(
            (ticketItem) => ticketItem.id === ticketId,
        );

        if (!ticket) {
            snackbarController.addSnackbar({
                id: 'risk-ticket-not-found',
                props: {
                    title: t`Unable to find ticket with ID ${ticketId}`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const handleConfirm = () => {
            this.unlinkTicketMutation
                .mutateAsync({
                    path: {
                        risk_id: stringRiskId,
                        ticket_id: ticketId,
                    },
                })
                .then(() => {
                    const unlinkedTicketTitle = ticket.title;

                    snackbarController.addSnackbar({
                        id: 'risk-ticket-unlink-success',
                        props: {
                            title: t`Successfully unlinked ticket "${unlinkedTicketTitle}"`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    this.load(stringRiskId);
                    closeConfirmationModal();
                })
                .catch((error: unknown) => {
                    logger.error({
                        message: 'Failed to unlink risk ticket',
                        additionalInfo: {
                            riskId: stringRiskId,
                            ticketId,
                            ticketTitle: ticket.title,
                        },
                        errorObject: {
                            message:
                                isObject(error) &&
                                'message' in error &&
                                isString(error.message)
                                    ? error.message
                                    : 'Unknown error',
                            statusCode: getErrorStatusCode(error),
                        },
                    });

                    let errorMessage = t`Failed to unlink ticket`;

                    if (
                        isObject(error) &&
                        'message' in error &&
                        isString(error.message)
                    ) {
                        errorMessage = error.message;
                    }

                    snackbarController.addSnackbar({
                        id: 'risk-ticket-unlink-error',
                        props: {
                            title: t`Failed to unlink ticket`,
                            description: errorMessage,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    closeConfirmationModal();
                });
        };

        const ticketTitle = ticket.title;
        const objectName = getObjectName();

        openConfirmationModal({
            title: t`Unlink Ticket`,
            body: t`Are you sure you want to unlink "${ticketTitle}" from this ${objectName}?`,
            confirmText: t`Unlink`,
            type: 'danger',
            onConfirm: handleConfirm,
            onCancel: closeConfirmationModal,
        });
    };

    load = (riskId: string) => {
        this.ticketsInProgressForRisksQuery.load({
            path: { risk_id: riskId },
            query: { isCompleted: false, limit: 5 },
        });
        this.ticketsCompletedForRisksQuery.load({
            path: { risk_id: riskId },
            query: { isCompleted: true },
        });
    };

    loadClosedTicketsInfinite = (riskId: string) => {
        this.ticketsCompletedInfiniteQuery.load({
            path: { risk_id: riskId },
            query: { isCompleted: true, limit: 5 },
        });
    };
}

export const sharedRiskTicketsController = new RiskTicketsController();
