import { isEmpty, isError, isNil } from 'lodash-es';
import { z } from 'zod';
import { snackbarController } from '@controllers/snackbar';
import {
    customFrameworksControllerGetCustomFrameworksOptions,
    customFrameworksControllerGetProductCustomFrameworksCountsOptions,
    customFrameworksControllerGetRequirementsCsvTemplateOptions,
    customFrameworksControllerPostCustomFrameworksMutation,
    customFrameworksControllerValidateCsvFileMutation,
    customFrameworksControllerValidateCustomFrameworkOptions,
} from '@globals/api-sdk/queries';
import type {
    CreateCustomFrameworkResponseDto,
    ValidateCustomFrameworkResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    reaction,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { FrameworkCreateModel } from '@models/framework-create';
import type { FormSchema } from '@ui/forms';
import { getFileIssueErrorMessage } from '../constants/csv-upload-messages.const';
import { getDetailsErrorMessage } from '../constants/validation-messages.const';

const InvalidTextOnCsvImport = [
    '/etc',
    '/dev',
    '/usr',
    '/home',
    '/lib',
    '/sbin',
];

export class FrameworkCreateController {
    postFrameworkMutation = new ObservedMutation(
        customFrameworksControllerPostCustomFrameworksMutation,
    );

    validateCsvMutation = new ObservedMutation(
        customFrameworksControllerValidateCsvFileMutation,
    );

    getCsvTemplateQuery = new ObservedQuery(
        customFrameworksControllerGetRequirementsCsvTemplateOptions,
    );

    getValidateCustomFrameworkQuery = new ObservedQuery(
        customFrameworksControllerValidateCustomFrameworkOptions,
    );

    getCustomFrameworkQuery = new ObservedQuery(
        customFrameworksControllerGetCustomFrameworksOptions,
    );

    getCustomFrameworksCountsQuery = new ObservedQuery(
        customFrameworksControllerGetProductCustomFrameworksCountsOptions,
    );

    frameworkCreateModel = new FrameworkCreateModel();

    createdFrameworkId: number | null = null;

    isFileUploadMissingFileError = false;

    hasReachedLimit: boolean | null = null;

    constructor() {
        makeAutoObservable(this, undefined, { autoBind: true });

        reaction(
            () => {
                return {
                    data: this.getCustomFrameworksCountsQuery.data,
                    isLoading:
                        this.getCustomFrameworksCountsQuery.isLoading ||
                        this.getCustomFrameworksCountsQuery.isReady,
                    limit: sharedCurrentCompanyController.company
                        ?.customFrameworksLimit as number | undefined,
                };
            },
            (result) => {
                if (result.isLoading || !result.data) {
                    this.hasReachedLimit = null;

                    return;
                }

                const count = result.data.customFrameworksCount;

                if (isNil(result.limit)) {
                    this.hasReachedLimit = false;

                    return;
                }

                if (count >= result.limit) {
                    this.hasReachedLimit = true;
                } else {
                    this.hasReachedLimit = false;
                }
            },
        );
    }

    get isLoadingValidateCustomFramework(): boolean {
        return this.getValidateCustomFrameworkQuery.isLoading;
    }

    get isPendingValidateCsv(): boolean {
        return this.validateCsvMutation.isPending;
    }

    get isPendingCreateCustomFramework(): boolean {
        return this.postFrameworkMutation.isPending;
    }

    get isLoadingWizard(): boolean {
        return (
            this.isLoadingValidateCustomFramework ||
            this.isPendingValidateCsv ||
            this.isPendingCreateCustomFramework
        );
    }

    load = (): void => {
        this.getCustomFrameworksCountsQuery.load({});
    };

    get configureCreateFormSchema(): FormSchema {
        return {
            name: {
                type: 'text',
                label: t`Name`,
                helpText: t`This name will appear on the Frameworks page.`,
                initialValue: this.frameworkCreateModel.name,
                validator: z.string().min(1, 'Name is required'),
            },
            shortName: {
                type: 'text',
                label: t`Short Name`,
                helpText: t`This short name is used for filters on the Controls page.`,
                initialValue: this.frameworkCreateModel.shortName,
                validator: z
                    .string()
                    .min(1, t`Short Name is required`)
                    .max(16, t`Short Name must be 16 characters or less`),
            },
            description: {
                type: 'textarea',
                label: t`Description`,
                helpText: t`This description will appear at the top of the Frameworks overview page.`,
                initialValue: this.frameworkCreateModel.description,
                validator: z.string().min(1, t`Description is required`),
            },
        };
    }

    resetRequirements(): void {
        this.frameworkCreateModel.resetRequirements();
    }

    lookForInvalidDataOnCSV(file: File): Promise<[boolean, string]> {
        return new Promise((resolve, reject) => {
            try {
                const reader = new FileReader();

                reader.onload = () => {
                    const content = reader.result;

                    InvalidTextOnCsvImport.forEach((invalidConstant) => {
                        const regex = new RegExp(
                            `${invalidConstant}(\\/|\\s|$)`,
                        );

                        if (regex.test(content as string)) {
                            resolve([true, invalidConstant]);
                        }
                    });

                    resolve([false, '']);
                };
                reader.onerror = () => {
                    reject(
                        new Error(
                            reader.error?.message || t`Error reading file`,
                        ),
                    );
                };
                reader.readAsText(file);
            } catch (error) {
                reject(isError(error) ? error : new Error(String(error)));
            }
        });
    }

    validationIssues: ValidateCustomFrameworkResponseDto['issues'] = [];

    validateDetailsStep = async (): Promise<boolean> => {
        await when(() => !isNil(sharedWorkspacesController.currentWorkspace));

        if (!sharedWorkspacesController.currentWorkspace) {
            return false;
        }

        this.getValidateCustomFrameworkQuery.load({
            path: {
                xProductId: sharedWorkspacesController.currentWorkspace.id,
            },
            query: {
                name: this.frameworkCreateModel.name,
                slug: this.frameworkCreateModel.shortName,
                pill: this.frameworkCreateModel.shortName,
            },
        });

        await when(() => !this.isLoadingValidateCustomFramework);

        const issues = this.getValidateCustomFrameworkQuery.data?.issues;

        if (!isNil(issues) && !isEmpty(issues)) {
            const errorMessages = issues.map((issue) => {
                return getDetailsErrorMessage(issue.type);
            });

            snackbarController.addSnackbar({
                id: 'framework-create-error',
                props: {
                    title: t`Failed to create framework`,
                    description: errorMessages.join('\n'),
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });

            return false;
        }

        return true;
    };

    parseCSVRequirementsFile = async (file: File): Promise<boolean> => {
        try {
            this.isFileUploadMissingFileError = false;

            if (isNil(sharedWorkspacesController.currentWorkspace)) {
                return false;
            }

            const [isDataInvalid, invalidDataFound] =
                await this.lookForInvalidDataOnCSV(file);

            if (isDataInvalid) {
                snackbarController.addSnackbar({
                    id: 'framework-create-error',
                    props: {
                        title: t`Failed to upload CSV file`,
                        description: t`There's invalid data in your CSV file that is not allowed to be uploaded: ${invalidDataFound}`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                return false;
            }

            const workspaceId = sharedWorkspacesController.currentWorkspace.id;

            await this.validateCsvMutation.mutateAsync({
                path: { xProductId: workspaceId },
                body: { file },
            });

            const { response } = this.validateCsvMutation;

            if (
                response?.status === 'ERROR' &&
                isEmpty(response.requirements)
            ) {
                const errors = response.issues.filter(
                    (issue) => issue.status === 'ERROR',
                );

                const errorMessages = errors.map((error) =>
                    getFileIssueErrorMessage(error.type),
                );

                snackbarController.addSnackbar({
                    id: 'framework-create-error',
                    props: {
                        title: t`Failed to upload CSV file`,
                        description: errorMessages.join('\n'),
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });

                return false;
            }

            this.frameworkCreateModel.setParsedRequirements(
                response?.requirements ?? [],
            );

            this.frameworkCreateModel.setValidationStatistics(
                response?.validationStatistics ?? {},
            );

            return true;
        } catch (error) {
            console.error('Failed to validate CSV file:', error);
            snackbarController.addSnackbar({
                id: 'framework-create-error',
                props: {
                    title: t`Failed to upload CSV file`,
                    description: t`Please fix the errors in the CSV file.`,
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });

            return false;
        }
    };

    handleUploadStep = (): Promise<boolean> => {
        const files = this.frameworkCreateModel.requirementsFiles;

        this.isFileUploadMissingFileError = false;

        if (!isEmpty(files)) {
            return this.parseCSVRequirementsFile(files[0].file);
        }

        this.isFileUploadMissingFileError = true;

        return Promise.resolve(false);
    };

    *handleCreateFramework(): Generator<Promise<void>, boolean, unknown> {
        if (this.postFrameworkMutation.isPending) {
            return false;
        }

        yield this.postFrameworkMutation.mutateAsync({
            path: {
                xProductId:
                    sharedWorkspacesController.currentWorkspace?.id ?? 1,
            },
            body: {
                name: this.frameworkCreateModel.name,
                shortName: this.frameworkCreateModel.shortName,
                description: this.frameworkCreateModel.description,
                requirements: this.frameworkCreateModel.parsedRequirements,
                validationStatistics:
                    this.frameworkCreateModel.validationStatistics,
            },
        });

        if (this.postFrameworkMutation.hasError) {
            const { error } = this.postFrameworkMutation;

            console.error(error);

            snackbarController.addSnackbar({
                id: 'framework-create-error',
                props: {
                    title: t`Failed to create framework`,
                    description: t`An error occurred while creating the framework. Try again later.`,
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });

            return false;
        }

        snackbarController.addSnackbar({
            id: 'framework-create-success',
            hasTimeout: true,
            props: {
                title: t`Framework created`,
                description: t`The framework was created successfully.`,
                severity: 'success',
                closeButtonAriaLabel: 'Close',
            },
        });
        const { id } = this.postFrameworkMutation
            .response as CreateCustomFrameworkResponseDto;

        this.createdFrameworkId = id;
        this.frameworkCreateModel.resetAll();

        return true;
    }

    handleGetCsvTemplate = (): void => {
        this.getCsvTemplateQuery.load({});

        when(
            () => !this.getCsvTemplateQuery.isLoading,
            () => {
                const csvData = this.getCsvTemplateQuery.data;

                if (!csvData) {
                    return;
                }

                window.open(csvData, 'Requirements-Template.csv');
            },
        );
    };

    getDroppedRowsWarningMessage = (): string => {
        const droppedRows = this.frameworkCreateModel.validationStatistics
            .droppedRows as number[];

        if (isEmpty(droppedRows)) {
            return '';
        }

        const MAX_CHARACTERS_LENGTH = 30;

        const errorsRowsList: string[] = [];

        const listFormatter = new Intl.ListFormat();

        let extraRowsCount = 0;

        droppedRows.forEach((row) => {
            if (
                [...errorsRowsList, row.toString()].join(', ').length <
                MAX_CHARACTERS_LENGTH
            ) {
                errorsRowsList.push(row.toString());

                return;
            }
            extraRowsCount = extraRowsCount + 1;
        });

        const errorsRows = extraRowsCount
            ? errorsRowsList.join(', ')
            : listFormatter.format(errorsRowsList);
        const extraRows = extraRowsCount ? t`+ ${extraRowsCount} more` : '';

        if (droppedRows.length === 1) {
            return t`Row ${errorsRows} was excluded due to missing, invalid or duplicated data.`;
        }

        return t`Rows ${errorsRows} ${extraRows} were excluded due to missing, invalid or duplicated data.`;
    };

    getStatistics = (): {
        totalRequirementsCount: number;
        validRequirementsCount: number;
    } => {
        const { totalRequirementsCount, validRequirementsCount } =
            this.frameworkCreateModel.validationStatistics;

        return {
            totalRequirementsCount: totalRequirementsCount as number,
            validRequirementsCount: validRequirementsCount as number,
        };
    };
}

export const sharedFrameworkCreateController = new FrameworkCreateController();
