import { isEmpty, isError, isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { CosmosFileObject } from '@cosmos/components/file-upload';
import {
    customFrameworksControllerGetCustomFrameworksOptions,
    customFrameworksControllerGetRequirementsCsvTemplateOptions,
    customFrameworksControllerSaveCustomRequirementsMutation,
    customFrameworksControllerValidateCsvFileMutation,
    customFrameworksControllerValidateCustomFrameworkOptions,
} from '@globals/api-sdk/queries';
import type {
    CustomFrameworkFileValidationResponseDto,
    ValidateCustomFrameworkResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getFileIssueErrorMessage } from '../constants/csv-upload-messages.const';
import { InvalidTextOnCsvImport } from '../constants/invalid-text-csv-import';
import { sharedCustomFrameworkRequirementsController } from './custom-framework-requirements-controller';
import { sharedFrameworkDetailsController } from './framework-details-controller';

export class FrameworkRequirementsUploadController {
    validateCsvMutation = new ObservedMutation(
        customFrameworksControllerValidateCsvFileMutation,
    );

    getCsvTemplateQuery = new ObservedQuery(
        customFrameworksControllerGetRequirementsCsvTemplateOptions,
    );

    getValidateCustomFrameworkQuery = new ObservedQuery(
        customFrameworksControllerValidateCustomFrameworkOptions,
    );

    getCustomFrameworkQuery = new ObservedQuery(
        customFrameworksControllerGetCustomFrameworksOptions,
    );

    postCustomRequirementsMutation = new ObservedMutation(
        customFrameworksControllerSaveCustomRequirementsMutation,
    );

    requirementsFiles: CosmosFileObject[] = [];
    parsedRequirements: CustomFrameworkFileValidationResponseDto['requirements'] =
        [];
    validationStatistics: CustomFrameworkFileValidationResponseDto['validationStatistics'] =
        {};

    isFileUploadMissingFileError = false;

    hasReachedLimit: boolean | null = null;

    frameworkId: number | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    get isPendingValidateCsv(): boolean {
        return this.validateCsvMutation.isPending;
    }

    get isPendingUpdateRequirement(): boolean {
        return this.postCustomRequirementsMutation.isPending;
    }

    setFrameworkId(frameworkId: number): void {
        this.frameworkId = frameworkId;
    }

    resetFrameworkId(): void {
        this.frameworkId = null;
    }

    lookForInvalidDataOnCSV(file: File): Promise<[boolean, string]> {
        return new Promise((resolve, reject) => {
            try {
                const reader = new FileReader();

                reader.onload = () => {
                    const content = reader.result;

                    for (const invalidConstant of InvalidTextOnCsvImport) {
                        const regex = new RegExp(
                            `${invalidConstant}(\\/|\\s|$)`,
                        );

                        if (regex.test(content as string)) {
                            resolve([true, invalidConstant]);

                            return;
                        }
                    }

                    resolve([false, '']);
                };
                reader.onerror = () => {
                    reject(
                        new Error(
                            reader.error?.message || t`Error reading file`,
                        ),
                    );
                };
                reader.readAsText(file);
            } catch (error) {
                reject(isError(error) ? error : new Error(String(error)));
            }
        });
    }

    validationIssues: ValidateCustomFrameworkResponseDto['issues'] = [];

    parseCSVRequirementsFile = async (file: File): Promise<boolean> => {
        try {
            runInAction(() => {
                this.isFileUploadMissingFileError = false;
            });

            if (isNil(sharedWorkspacesController.currentWorkspace)) {
                return false;
            }

            const [isDataInvalid, invalidDataFound] =
                await this.lookForInvalidDataOnCSV(file);

            if (isDataInvalid) {
                snackbarController.addSnackbar({
                    id: 'framework-create-error',
                    props: {
                        title: t`Failed to upload CSV file`,
                        description: t`There's invalid data in your CSV file that is not allowed to be uploaded: ${invalidDataFound}`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                return false;
            }

            const workspaceId = sharedWorkspacesController.currentWorkspace.id;

            await this.validateCsvMutation.mutateAsync({
                path: { xProductId: workspaceId },
                body: {
                    frameworkId: this.frameworkId ?? undefined,
                    file,
                },
            });

            const { response } = this.validateCsvMutation;

            if (
                response?.status === 'ERROR' &&
                isEmpty(response.requirements)
            ) {
                const errors = response.issues.filter(
                    (issue) => issue.status === 'ERROR',
                );

                const errorMessages = errors.map((error) =>
                    getFileIssueErrorMessage(error.type),
                );

                snackbarController.addSnackbar({
                    id: 'framework-create-error',
                    props: {
                        title: t`Failed to upload CSV file`,
                        description: errorMessages.join('\n'),
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });

                return false;
            }

            runInAction(() => {
                this.parsedRequirements = response?.requirements ?? [];
                this.validationStatistics =
                    response?.validationStatistics ?? {};
            });

            return true;
        } catch (error) {
            console.error('Failed to validate CSV file:', error);
            snackbarController.addSnackbar({
                id: 'framework-create-error',
                props: {
                    title: t`Failed to upload CSV file`,
                    description: t`Please fix the errors in the CSV file.`,
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });

            return false;
        }
    };

    *handleUploadStep(): Generator<Promise<boolean>, boolean, unknown> {
        const files = this.requirementsFiles;

        runInAction(() => {
            this.isFileUploadMissingFileError = false;
        });

        if (!isEmpty(files)) {
            const result = yield this.parseCSVRequirementsFile(files[0].file);

            return result as boolean;
        }

        runInAction(() => {
            this.isFileUploadMissingFileError = true;
        });

        return false;
    }

    handleGetCsvTemplate = (): void => {
        this.getCsvTemplateQuery.load({});

        when(
            () => !this.getCsvTemplateQuery.isLoading,
            () => {
                const csvData = this.getCsvTemplateQuery.data;

                if (!csvData) {
                    return;
                }

                window.open(csvData, 'Requirements-Template.csv');
            },
        );
    };

    *handleUploadRequirements(): Generator<Promise<void>, boolean, unknown> {
        try {
            if (isNil(sharedWorkspacesController.currentWorkspace)) {
                return false;
            }

            if (!this.frameworkId) {
                console.error(
                    'Framework ID is required for uploading requirements',
                );

                return false;
            }

            const workspaceId = sharedWorkspacesController.currentWorkspace.id;

            yield this.postCustomRequirementsMutation.mutateAsync({
                body: {
                    requirements: this.parsedRequirements,
                    frameworkId: this.frameworkId,
                    validationStatistics: this.validationStatistics,
                    workspaceId,
                },
            });

            const { response } = this.postCustomRequirementsMutation;

            if (!response) {
                return false;
            }

            snackbarController.addSnackbar({
                id: 'framework-create-success',
                hasTimeout: true,
                props: {
                    title: t`Requirements uploaded`,
                    description: t`The requirements were uploaded successfully.`,
                    severity: 'success',
                    closeButtonAriaLabel: 'Close',
                },
            });
            sharedCustomFrameworkRequirementsController.customFrameworkRequirementsQuery.invalidate();
            sharedFrameworkDetailsController.frameworkDetailsQuery.invalidate();

            return true;
        } catch (error) {
            console.error(t`Failed to upload requirements:`, error);
            snackbarController.addSnackbar({
                id: 'framework-create-error',
                props: {
                    title: t`Failed to upload requirements`,
                    description: t`Please fix the errors in the requirements.`,
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });

            return false;
        }
    }

    getDroppedRowsWarningMessage = (): string => {
        const droppedRows = this.validationStatistics.droppedRows as number[];

        if (isEmpty(droppedRows)) {
            return '';
        }

        const MAX_CHARACTERS_LENGTH = 30;

        const errorsRowsList: string[] = [];

        const listFormatter = new Intl.ListFormat();

        let extraRowsCount = 0;

        droppedRows.forEach((row) => {
            if (
                [...errorsRowsList, row.toString()].join(', ').length <
                MAX_CHARACTERS_LENGTH
            ) {
                errorsRowsList.push(row.toString());

                return;
            }
            extraRowsCount = extraRowsCount + 1;
        });

        const errorsRows = extraRowsCount
            ? errorsRowsList.join(', ')
            : listFormatter.format(errorsRowsList);
        const extraRows = extraRowsCount ? t`+ ${extraRowsCount} more` : '';

        if (droppedRows.length === 1) {
            return t`Row ${errorsRows} was excluded due to missing, invalid or duplicated data.`;
        }

        return t`Rows ${errorsRows} ${extraRows} were excluded due to missing, invalid or duplicated data.`;
    };

    getStatistics = (): {
        totalRequirementsCount: number;
        validRequirementsCount: number;
    } => {
        const { totalRequirementsCount, validRequirementsCount } =
            this.validationStatistics;

        return {
            totalRequirementsCount: totalRequirementsCount as number,
            validRequirementsCount: validRequirementsCount as number,
        };
    };

    resetRequirements = (): void => {
        this.requirementsFiles = [];
        this.parsedRequirements = [];
        this.validationStatistics = {};
    };
}

export const sharedFrameworkRequirementsUploadController =
    new FrameworkRequirementsUploadController();
