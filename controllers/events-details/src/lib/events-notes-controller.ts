import { isEmpty } from 'lodash-es';
import type { NoteUpdateDto } from '@components/utilities';
import { snackbarController } from '@controllers/snackbar';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import {
    eventsControllerCreateNoteMutation,
    eventsControllerDeleteNoteFileMutation,
    eventsControllerDeleteNoteMutation,
    eventsControllerGetEventNoteFileOptions,
    eventsControllerGetNotesInfiniteOptions,
    eventsControllerGetNotesOptions,
    eventsControllerUpdateNoteMutation,
    eventsControllerUploadNotesMutation,
} from '@globals/api-sdk/queries';
import type {
    EventsControllerCreateNoteData,
    EventsControllerDeleteNoteError,
    NoteResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

class EventsNotesController {
    eventNotesQuery = new ObservedQuery(eventsControllerGetNotesOptions);
    eventNoteCreateMutation = new ObservedMutation(
        eventsControllerCreateNoteMutation,
        {
            onSuccess: () => {
                this.eventNotesQuery.invalidate();
                this.eventNotesInfiniteQuery.invalidate();
            },
        },
    );
    eventNoteUpdateMutation = new ObservedMutation(
        eventsControllerUpdateNoteMutation,
    );
    eventNoteDeleteMutation = new ObservedMutation(
        eventsControllerDeleteNoteMutation,
    );
    eventDownloadAttachmentQuery = new ObservedQuery(
        eventsControllerGetEventNoteFileOptions,
    );
    eventNoteUploadMutation = new ObservedMutation(
        eventsControllerUploadNotesMutation,
    );
    eventNoteDeleteFileMutation = new ObservedMutation(
        eventsControllerDeleteNoteFileMutation,
    );

    eventNotesInfiniteQuery = new ObservedInfiniteQuery(
        eventsControllerGetNotesInfiniteOptions,
    );

    eventId: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    get list(): NoteResponseDto[] {
        return this.eventNotesQuery.data?.data ?? [];
    }

    get total(): number {
        return this.eventNotesQuery.data?.total ?? 0;
    }

    get isLoading(): boolean {
        return this.eventNotesQuery.isLoading;
    }

    get isCreatingNote(): boolean {
        return this.eventNoteCreateMutation.isPending;
    }

    get hasCreateError(): boolean {
        return this.eventNoteCreateMutation.hasError;
    }

    get infiniteNotes(): NoteResponseDto[] {
        return (
            this.eventNotesInfiniteQuery.data?.pages.flatMap(
                (page) => page?.data ?? [],
            ) ?? []
        );
    }

    get hasNextPage(): boolean {
        return this.eventNotesInfiniteQuery.hasNextPage;
    }

    get isFetchingMore(): boolean {
        return this.eventNotesInfiniteQuery.isFetching;
    }

    loadNotes = (eventId: string, params?: FetchDataResponseParams): void => {
        this.eventId = eventId;
        const { pagination } = params ?? {
            pagination: { pageSize: 20, page: 1 },
        };

        const { pageSize, page } = pagination;

        const query = {
            page,
            limit: pageSize,
        };

        this.eventNotesQuery.load({
            path: { id: eventId },
            query: {
                ...query,
            },
        });
    };

    loadNotesInfinite = (eventId: string, limit: number): void => {
        this.eventId = eventId;
        this.eventNotesInfiniteQuery.load({
            path: { id: eventId },
            query: {
                page: 1,
                limit,
            },
        });
    };

    loadNextPage = (): void => {
        if (this.hasNextPage && !this.isFetchingMore) {
            this.eventNotesInfiniteQuery.nextPage();
        }
    };

    invalidateInfiniteNotes = (): void => {
        this.eventNotesInfiniteQuery.invalidate();
    };

    createNote = (
        body: EventsControllerCreateNoteData['body'],
        onSuccess?: () => void,
    ): void => {
        if (!this.eventId) {
            throw new Error('Event ID is not set');
        }

        const { fileMetadata } = body;

        this.eventNoteCreateMutation.mutate({
            path: { id: this.eventId },
            body: {
                ...body,
                // TODO: improve this, this is a hack to get the file metadata
                fileMetadata: fileMetadata
                    ?.map((meta) => JSON.stringify(meta))
                    .join(',') as unknown as {
                    originalFile?: string;
                    name?: string;
                    creationDate?: string;
                }[],
            },
        });

        when(
            () => !this.isCreatingNote,
            () => {
                const timestamp = new Date().toISOString();

                if (this.hasCreateError) {
                    snackbarController.addSnackbar({
                        id: `${timestamp}-created-event-note-error`,
                        props: {
                            title: t`Couldn't create note. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `${timestamp}-created-event-note-success`,
                    props: {
                        title: t`Note created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                onSuccess?.();
            },
        );
    };

    updateNote = (noteId: string, note: NoteUpdateDto): void => {
        const timestamp = new Date().toISOString();
        const { 'files[]': files, filesToDelete, comment, fileMetadata } = note;

        if (comment && !isEmpty(comment)) {
            this.eventNoteUpdateMutation.mutate({
                path: { noteId },
                body: { comment },
            });

            when(
                () => !this.eventNoteUpdateMutation.isPending,
                () => {
                    if (this.eventNoteUpdateMutation.hasError) {
                        this.handleUpdateError(timestamp);

                        return;
                    }
                    this.processFileOperations(
                        noteId,
                        timestamp,
                        files,
                        filesToDelete,
                        fileMetadata,
                    );
                },
            );
        } else {
            this.processFileOperations(
                noteId,
                timestamp,
                files,
                filesToDelete,
                fileMetadata,
            );
        }
    };

    processFileOperations = (
        noteId: string,
        timestamp: string,
        files?: (File | Blob)[],
        filesToDelete?: string[],
        fileMetadata?: Record<string, unknown>[],
    ): void => {
        if (files && !isEmpty(files)) {
            this.uploadFiles(noteId, files, fileMetadata, timestamp, () => {
                if (filesToDelete) {
                    this.deleteFiles(filesToDelete, timestamp);
                } else {
                    this.handleUpdateSuccess(timestamp);
                }
            });
        } else if (filesToDelete) {
            this.deleteFiles(filesToDelete, timestamp);
        } else {
            this.handleUpdateSuccess(timestamp);
        }
    };

    uploadFiles = (
        noteId: string,
        files: (File | Blob)[],
        fileMetadata: Record<string, unknown>[] | undefined,
        timestamp: string,
        onComplete?: () => void,
    ): void => {
        const [file, ...remainingFiles] = files;
        const metadata = fileMetadata?.[0];
        const fileName = file instanceof File ? file.name : `file-0`;

        this.eventNoteUploadMutation.mutate({
            path: { noteId },
            body: {
                file,
                creationDate:
                    (metadata?.creationDate as string) ||
                    new Date().toISOString(),
                name: (metadata?.name as string) || fileName,
            },
        });

        when(
            () => !this.eventNoteUploadMutation.isPending,
            () => {
                if (this.eventNoteUploadMutation.hasError) {
                    this.handleUpdateError(timestamp);

                    return;
                }

                if (!isEmpty(remainingFiles)) {
                    this.uploadFiles(
                        noteId,
                        remainingFiles,
                        fileMetadata?.slice(1),
                        timestamp,
                        onComplete,
                    );
                } else if (onComplete) {
                    onComplete();
                }
            },
        );
    };

    deleteFiles = (filesToDelete: string[], timestamp: string): void => {
        if (isEmpty(filesToDelete)) {
            this.handleUpdateSuccess(timestamp);

            return;
        }

        const [fileId, ...remainingFiles] = filesToDelete;

        this.eventNoteDeleteFileMutation.mutate({
            path: { noteFileId: fileId },
        });

        when(
            () => !this.eventNoteDeleteFileMutation.isPending,
            () => {
                if (this.eventNoteDeleteFileMutation.hasError) {
                    this.handleUpdateError(timestamp);

                    return;
                }

                if (isEmpty(remainingFiles)) {
                    this.handleUpdateSuccess(timestamp);
                } else {
                    this.deleteFiles(remainingFiles, timestamp);
                }
            },
        );
    };

    handleUpdateSuccess = (timestamp: string): void => {
        this.eventNotesInfiniteQuery.invalidate();
        snackbarController.addSnackbar({
            id: `${timestamp}-updated-event-note-success`,
            props: {
                title: t`Note updated`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };

    handleUpdateError = (timestamp: string): void => {
        snackbarController.addSnackbar({
            id: `${timestamp}-updated-event-note-error`,
            props: {
                title: t`Unable to update note`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };

    deleteNote = (noteId: string): void => {
        openConfirmationModal({
            title: t`Delete note?`,
            body: t`This action is permanent. The note and all related data will be removed.`,
            confirmText: t`Delete note`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                const timestamp = new Date().toISOString();

                this.eventNoteDeleteMutation
                    .mutateAsync({
                        path: { noteId },
                    })
                    .then(() => {
                        this.eventNotesInfiniteQuery.invalidate();
                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-event-note-success`,
                            props: {
                                title: t`Note deleted`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeConfirmationModal();
                    })
                    .catch((error: EventsControllerDeleteNoteError) => {
                        const title =
                            error.statusCode === 403
                                ? t`Only the same owner can delete a note`
                                : t`Unable to delete note at the moment`;

                        snackbarController.addSnackbar({
                            id: `${timestamp}-deleted-event-note-error`,
                            props: {
                                title,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    get isReadOnly(): boolean {
        return !sharedCurrentUserController.hasUserPermission(
            'Event',
            'MANAGE',
        );
    }

    downloadNoteAttachment = (noteFileId: string): void => {
        this.eventDownloadAttachmentQuery.load({
            path: { noteFileId },
        });
        when(() => !this.eventDownloadAttachmentQuery.isLoading)
            .then(() => {
                const { data } = this.eventDownloadAttachmentQuery;
                const { signedUrl } = data ?? {};

                if (!signedUrl) {
                    return;
                }
                downloadFileFromSignedUrl(signedUrl);
            })
            .catch(() => {
                const timestamp = new Date().toISOString();

                snackbarController.addSnackbar({
                    id: `${timestamp}-download-note-attachment-error`,
                    props: {
                        title: t`Unable to download attachment`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };
}

export const sharedEventsNotesController = new EventsNotesController();
