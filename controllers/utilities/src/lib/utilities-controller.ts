import { routeController, type UtilitiesName } from '@controllers/route';
import { makeAutoObservable, reaction } from '@globals/mobx';
import { sharedCustomerRequestUtilitiesNotesController } from './customer-request-utilities-notes-controller';
import type { UtilitiesBase, UtilitiesBaseConfig } from './utilities-base';
import { sharedUtilitiesNotesController } from './utilities-notes-controller';
import { sharedUtilitiesObservationsController } from './utilities-observations-controller';
import { sharedUtilitiesResourceGuideController } from './utilities-resource-guide-controller';
import { sharedUtilitiesTasksController } from './utilities-tasks-controller';
import { sharedUtilitiesTicketsController } from './utilities-tickets-controller';
import { sharedUtilitiesVrmAgentController } from './utilities-vrm-agent-controller';

export class UtilitiesController {
    routeDisposer?: () => void;

    constructor() {
        makeAutoObservable(this);
        this.setupRouteReaction();
    }

    setupRouteReaction(): void {
        this.routeDisposer = reaction(
            () => JSON.stringify(routeController.currentParams),
            () => {
                // Close all utilities when route changes
                this.closeAllUtilities();
            },
        );
    }

    dispose = (): void => {
        this.routeDisposer?.();
    };

    get utilitiesList(): UtilitiesName[] {
        return routeController.utilities?.utilitiesList ?? [];
    }

    get isOpen(): boolean {
        return this.utilitiesList.some(
            (utility) => this.getUtilityControllerByName(utility).isOpen,
        );
    }

    get enabledUtilities(): UtilitiesName[] {
        return this.utilitiesList.filter(
            (utilityName) =>
                this.getUtilityControllerByName(utilityName).isEnabled,
        );
    }

    getUtilityControllerByName = (
        name: UtilitiesName,
    ): UtilitiesBase<UtilitiesBaseConfig> => {
        switch (name) {
            case 'notes_for_events':
            case 'notes_for_access_review_active_period_user':
            case 'notes_for_access_review_active_application':
            case 'notes_for_completed_application_on_completed_review':
            case 'notes_for_controls':
            case 'notes_for_monitoring_tests':
            case 'notes_for_risk_management': {
                return sharedUtilitiesNotesController;
            }
            case 'notes_for_customer_requests': {
                return sharedCustomerRequestUtilitiesNotesController;
            }
            case 'tasks': {
                return sharedUtilitiesTasksController;
            }
            case 'tasks_for_controls':
            case 'tasks_for_risks': {
                return sharedUtilitiesTasksController;
            }
            case 'tickets_for_access_review_active_period_user':
            case 'tickets_for_controls':
            case 'tickets_for_risk_management': {
                return sharedUtilitiesTicketsController;
            }
            case 'observations': {
                return sharedUtilitiesObservationsController;
            }
            case 'resource_guide': {
                return sharedUtilitiesResourceGuideController;
            }
            case 'vrm_agent_assessment':
            case 'vrm_agent_summary':
            case 'vrm_agent_criteria': {
                return sharedUtilitiesVrmAgentController;
            }
            //
            default: {
                // Not sure how to handle this
                return sharedUtilitiesNotesController;
            }
        }
    };

    closeAllUtilities = (): void => {
        this.utilitiesList.forEach((utilityName) => {
            this.getUtilityControllerByName(utilityName).closeUtility();
        });
    };

    handleOpenCloseAllTabsClick = (): void => {
        if (this.isOpen) {
            this.closeAllUtilities();
        } else {
            this.getUtilityControllerByName(
                this.utilitiesList[0],
            ).openUtility();
        }
    };
}

export const sharedUtilitiesController = new UtilitiesController();
