import { UtilitiesBase, type UtilitiesBaseConfig } from './utilities-base';

export interface UtilitiesResourceGuideConfig extends UtilitiesBaseConfig {
    overrides?: {
        enabled?: boolean;
    };
}

export class UtilitiesResourceGuideController extends UtilitiesBase<UtilitiesResourceGuideConfig> {
    // No additional properties or methods needed so far - using inherited functionality from UtilitiesBase
}

export const sharedUtilitiesResourceGuideController =
    new UtilitiesResourceGuideController();
