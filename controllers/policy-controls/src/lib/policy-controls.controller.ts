import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { snackbarController } from '@controllers/snackbar';
import { DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import { policiesControllerUpdatePolicyMutation } from '@globals/api-sdk/queries';
import type {
    ControlListResponseDto,
    PolicyControlSummaryResponseDto,
    UpdatePolicyDetailsRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

class PolicyControlsController {
    currentPage = 1;
    pageSize = DEFAULT_PAGE_SIZE;
    search = '';

    policyUpdateMutation = new ObservedMutation(
        policiesControllerUpdatePolicyMutation,
        {
            onSuccess: () => {
                sharedPolicyBuilderController.policyControlsAssociatedQuery.invalidate();
            },
        },
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isLoading(): boolean {
        return sharedPolicyBuilderController.isPolicyBuilderControlsLoading;
    }

    get allLinkedControlsToCurrentPolicy(): PolicyControlSummaryResponseDto[] {
        return sharedPolicyBuilderController.policyControlsAssociated.filter(
            (control) =>
                control.name.toLowerCase().includes(this.search.toLowerCase()),
        );
    }

    get policyControls(): PolicyControlSummaryResponseDto[] {
        // This is fake pagination since we have them all
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;

        const filteredResults = this.allLinkedControlsToCurrentPolicy;

        return filteredResults.slice(startIndex, endIndex);
    }

    get policyControlsIds(): number[] {
        return this.policyControls.map((control) => control.id);
    }

    get total(): number {
        return this.allLinkedControlsToCurrentPolicy.length;
    }

    setPagination({
        page = 1,
        pageSize = DEFAULT_PAGE_SIZE,
        search = '',
    }: {
        page: number;
        pageSize: number;
        search?: string;
    }) {
        this.currentPage = page;
        this.pageSize = pageSize;
        this.search = search;
    }

    refreshPolicyControls = (): void => {
        sharedPolicyBuilderController.policyControlsAssociatedQuery.invalidate();
    };

    linkControlsToPolicy = (controls: ControlListResponseDto[]): void => {
        const controlsMapped = controls.map((control) => ({
            id: control.id,
            name: control.name,
            code: control.code,
            description: control.description,
            isReady: control.isReady,
        }));

        const controlsUpdated = [
            ...this.allLinkedControlsToCurrentPolicy,
            ...controlsMapped,
        ];

        this.updatePolicy(
            'LINK',
            controlsUpdated.map((control) => control.id),
        );
    };

    unlinkControlFromPolicy = (
        controlId: number,
        onSuccess?: () => void,
    ): void => {
        openConfirmationModal({
            title: t`Unlink control?`,
            body: t`This control will no longer be mapped to this policy.`,
            confirmText: t`Unlink control`,
            cancelText: t`Cancel`,
            type: 'danger',
            size: 'md',
            isLoading: () => this.policyUpdateMutation.isPending,
            onConfirm: () => {
                const controlsFiltered =
                    this.allLinkedControlsToCurrentPolicy.filter(
                        (control) => control.id !== controlId,
                    );

                this.updatePolicy(
                    'UNLINK',
                    controlsFiltered.map((control) => control.id),
                    () => {
                        closeConfirmationModal();
                        onSuccess?.();
                    },
                );
            },
        });
    };

    updatePolicy(
        action: 'LINK' | 'UNLINK',
        controlIds: number[],
        onSuccess?: () => void,
    ): void {
        const { policy, currentVersion } = sharedPolicyBuilderController;

        try {
            if (!policy) {
                throw new Error('No policy found');
            }

            if (!currentVersion) {
                throw new Error('No policy version found');
            }
            //
            const body: UpdatePolicyDetailsRequestDto = {
                controlIds,
                assignedTo: policy.assignedTo,
                notifyGroups: policy.notifyGroups,
                groupIds: policy.groups.map((group) => group.id),
                name: policy.name,
                description: currentVersion.description || '',
                userId: policy.currentOwner.id,
                material: false,
                weekTimeFrameSLARequests: currentVersion.weekTimeFrameSLAs?.map(
                    (sla) => ({
                        policyWeekTimeFrameSLAId: sla.policyWeekTimeFrameSLAId,
                        timeFrame: sla.timeFrame,
                    }),
                ),
                gracePeriodSLARequests: currentVersion.gracePeriodSLAs?.map(
                    (sla) => ({
                        policyGracePeriodSLAId: sla.policyGracePeriodSLAId,
                        gracePeriod: sla.gracePeriod,
                    }),
                ),
                p3MatrixPolicySLARequests: currentVersion.p3MatrixSLAs?.map(
                    (sla) => ({
                        policyP3MatrixSLAId: sla.policyP3MatrixSLAId,
                        matrixItems: [
                            {
                                definition: sla.definition,
                                severity: sla.severity,
                                timeFrame: sla.timeFrame,
                                examples: sla.examples,
                            },
                        ],
                    }),
                ),
                replacedPoliciesIds: policy.replacedPolicies.map(
                    (p) => p.templateId,
                ),
                disclaimer: policy.disclaimer,
                changesExplanation: currentVersion.changesExplanation || '',
                renewalDate: currentVersion.renewalDate || policy.renewalDate,
                newPolicySlaRequest: {
                    weekTimeFrameSLAs: undefined,
                    gracePeriodSLAs: undefined,
                    p3MatrixSLAs: undefined,
                },
            };

            this.policyUpdateMutation.mutate({
                path: {
                    id: policy.id,
                    versionId: currentVersion.id,
                    xProductId:
                        sharedWorkspacesController.currentWorkspace?.id ?? 1,
                },
                body,
            });

            when(
                () => !this.policyUpdateMutation.isPending,
                () => {
                    if (this.policyUpdateMutation.hasError) {
                        const title =
                            action === 'LINK'
                                ? t`Failed to link control`
                                : t`Failed to unlink control`;
                        const description =
                            action === 'LINK'
                                ? t`An error occurred while linking the control. Please try again.`
                                : t`An error occurred while unlinking the control. Please try again.`;

                        snackbarController.addSnackbar({
                            id: 'policy-update-error',
                            props: {
                                title,
                                description,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    } else {
                        const title =
                            action === 'LINK'
                                ? t`Control linked successfully`
                                : t`Control unlinked successfully`;

                        snackbarController.addSnackbar({
                            id: 'policy-update-success',
                            props: {
                                title,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        onSuccess?.();
                    }
                    this.refreshPolicyControls();
                },
            );
        } catch (error) {
            logger.error({
                message: 'Failed to update policy',
                additionalInfo: {
                    error,
                    action,
                },
            });

            const title =
                action === 'LINK'
                    ? t`Failed to link control`
                    : t`Failed to unlink control`;
            const description =
                action === 'LINK'
                    ? t`An error occurred while linking the control. Please try again.`
                    : t`An error occurred while unlinking the control. Please try again.`;

            snackbarController.addSnackbar({
                id: 'policy-update-error',
                props: {
                    title,
                    description,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    }
}

export const sharedPolicyControlsController = new PolicyControlsController();
