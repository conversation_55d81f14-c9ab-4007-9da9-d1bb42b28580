import { isNil } from 'lodash-es';
import {
    type FrameworkWithStringId,
    sharedAuditCreationWizardController,
} from '@controllers/audit-creation-wizard';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerGetFrameworksForNewAuditOptions } from '@globals/api-sdk/queries';
import type { FrameworkForNewAuditResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    convertFrameworksToStringIds,
    filterAndSortFrameworks,
} from '@helpers/framework-filtering';

class AuditCreationFrameworksController {
    frameworksQuery = new ObservedQuery(
        grcControllerGetFrameworksForNewAuditOptions,
    );
    constructor() {
        makeAutoObservable(this);
    }

    get isLoading(): boolean {
        return this.frameworksQuery.isLoading;
    }

    get hasError(): boolean {
        return this.frameworksQuery.hasError;
    }

    get frameworks() {
        return this.frameworksQuery.data ?? [];
    }

    /**
     * Get filtered and sorted frameworks for the form select options.
     */
    get filteredFrameworks(): FrameworkWithStringId[] {
        // Validation to avoid typed sdk never[] for FrameworkForNewAuditResponseDto
        if (Array.isArray(this.frameworks)) {
            return [];
        }

        const ignoredFrameworks = ['FEDRAMP'];
        const filtered = filterAndSortFrameworks(
            this.frameworks.frameworks,
            ignoredFrameworks,
        );

        return convertFrameworksToStringIds(filtered);
    }

    handleFrameworkChange = (value: FrameworkForNewAuditResponseDto) => {
        const { addAuditDetails } = sharedAuditCreationWizardController;

        addAuditDetails({
            framework: value,
            date: { startingDate: '', endingDate: '' },
        });
    };

    loadFrameworks(auditType?: 'FULL_AUDIT' | 'DOWNLOAD_ONLY_AUDIT'): void {
        when(
            () => !isNil(sharedWorkspacesController.currentWorkspaceId),
            () => {
                if (isNil(sharedWorkspacesController.currentWorkspaceId)) {
                    return;
                }
                this.frameworksQuery.load({
                    query: {
                        workspaceId:
                            sharedWorkspacesController.currentWorkspaceId,
                        ...(auditType && { auditType }),
                    },
                });
            },
        );

        when(
            () =>
                !this.frameworksQuery.isLoading &&
                this.frameworksQuery.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'frameworks-load-error',
                    props: {
                        title: t`Unable to load frameworks for new audit`,
                        description: t`Please refresh the page and try again. If the problem persists, contact support.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }
}

export const sharedAuditCreationFrameworksController =
    new AuditCreationFrameworksController();
