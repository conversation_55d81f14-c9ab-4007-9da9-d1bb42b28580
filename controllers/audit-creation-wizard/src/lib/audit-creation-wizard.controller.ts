import { isEmpty } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { makeAutoObservable, toJS } from '@globals/mobx';
import {
    type AuditCreationWizardData,
    type AuditDetailsType,
    FRAMEWORK_TYPES,
} from './audit-creation-wizard.types';

class AuditCreationWizardController {
    wizardData: AuditCreationWizardData = {
        conductAuditOption: '',
        auditDetails: {
            framework: undefined,
            date: {
                startingDate: '',
                endingDate: '',
            },
        },
        assignedAuditors: [],
    };

    constructor() {
        makeAutoObservable(this);
    }

    addAuditDetails = (data: AuditDetailsType): void => {
        this.wizardData.auditDetails = {
            ...this.wizardData.auditDetails,
            ...data,
        };
    };

    get auditDetails() {
        return toJS(this.wizardData.auditDetails);
    }

    /**
     * Conduct audit step methods.
     */
    setConductAuditOption = (option: string): void => {
        this.wizardData.conductAuditOption = option;
    };

    get conductAuditOption(): string {
        return this.wizardData.conductAuditOption;
    }

    /**
     * Validation methods.
     */
    validateConductAuditStep = (): boolean => {
        return !isEmpty(this.wizardData.conductAuditOption);
    };

    validateAssignAuditorsStep = (): boolean => {
        // TODO: Implement auditors validation in https://drata.atlassian.net/browse/ENG-72181
        return true;
    };

    get data(): AuditCreationWizardData {
        return toJS(this.wizardData);
    }

    get isSoc2Type1(): boolean {
        return (
            this.data.auditDetails?.framework?.type ===
            FRAMEWORK_TYPES.SOC_2_TYPE_1
        );
    }

    get isSoc2Type2(): boolean {
        return (
            this.data.auditDetails?.framework?.type ===
            FRAMEWORK_TYPES.SOC_2_TYPE_2
        );
    }

    get isIso27001(): boolean {
        return (
            this.data.auditDetails?.framework?.type ===
            FRAMEWORK_TYPES.ISO_27001
        );
    }

    get hasControls(): boolean {
        return (
            !isEmpty(this.data.auditDetails?.framework) &&
            this.data.auditDetails.framework.hasControls
        );
    }

    /**
     * Reset wizard data.
     */
    resetWizardData = (): void => {
        this.wizardData = {
            conductAuditOption: '',
        };
    };

    completeWizard = (): void => {
        // TODO: Implement actual audit creation API call in https://drata.atlassian.net/browse/ENG-72181

        sharedProgrammaticNavigationController.navigateTo(
            `${routeController.userPartOfUrl}/compliance/audits`,
        );
    };

    cancelWizard = (): void => {
        sharedProgrammaticNavigationController.navigateTo(
            `${routeController.userPartOfUrl}/compliance/audits`,
        );
    };
}

export const sharedAuditCreationWizardController =
    new AuditCreationWizardController();
