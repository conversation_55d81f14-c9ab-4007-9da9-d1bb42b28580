import type { FrameworkForNewAuditResponseDto } from '@globals/api-sdk/types';

export interface Dates {
    startingDate: string;
    endingDate?: string;
}
export interface AuditDetailsType {
    name?: string;
    description?: string;
    framework?: FrameworkForNewAuditResponseDto;
    date?: Dates;
}
export interface AuditCreationWizardData {
    conductAuditOption: string;
    auditDetails?: AuditDetailsType;
    assignedAuditors?: string[];
}

/**
 * Framework type with string ID for form handling.
 */
export type FrameworkWithStringId = Omit<
    FrameworkForNewAuditResponseDto,
    'id'
> & {
    id: string;
};

// Framework type constants using SDK types for type safety
export const FRAMEWORK_TYPES = {
    SOC_2_TYPE_1: 'SOC_2_TYPE_1' as FrameworkForNewAuditResponseDto['type'],
    SOC_2_TYPE_2: 'SOC_2_TYPE_2' as FrameworkForNewAuditResponseDto['type'],
    ISO_27001: 'ISO_27001' as FrameworkForNewAuditResponseDto['type'],
} as const;
