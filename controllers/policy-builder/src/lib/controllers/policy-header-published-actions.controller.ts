import { isEmpty } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import type { Action } from '@cosmos/components/action-stack';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import {
    policyVersionControllerPostPolicyVersionMutation,
    policyVersionControllerRenewalCurrentPublishedVersionMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    runInAction,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import {
    closePolicyRenewalDateModal,
    openPolicyRenewalDateModal,
} from '../helpers/open-policy-renewal-date-modal.helper';
import { sharedPolicyBuilderController } from '../policy-builder.controller';
import { sharedPolicyHeaderSharedActionsController } from './policy-header-shared-actions.controller';

// TODO: Improve and refactor the entire logic of this controller

export class PolicyHeaderPublishedActions {
    pendingRenewalRequiresApproval = false;

    createNewVersionMutation = new ObservedMutation(
        policyVersionControllerPostPolicyVersionMutation,
        {
            onSuccess: (data) => {
                const newVersionId = data.id;

                if (newVersionId && sharedPolicyBuilderModel.policyId) {
                    sharedProgrammaticNavigationController.navigateTo(
                        `/workspaces/${sharedWorkspacesController.currentWorkspaceId}/governance/policies/${sharedPolicyBuilderModel.policyId}/builder/${newVersionId}`,
                    );
                }
            },
            onError: (error) => {
                logger.error({
                    message:
                        'Failed to create new version from published policy',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        action: 'createVersionFromPublished',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'create-version-error',
                    props: {
                        title: t`Failed to create new version`,
                        description:
                            error.message ||
                            t`An error occurred while creating the version`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    renewalDateMutation = new ObservedMutation(
        policyVersionControllerRenewalCurrentPublishedVersionMutation,
        {
            onSuccess: () => {
                closePolicyRenewalDateModal();

                snackbarController.addSnackbar({
                    id: 'renewal-date-success',
                    props: {
                        title: t`Renewal date updated`,
                        description: t`The policy renewal date has been updated successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // If approval was required, a new version was created
                // We need to reload the policy to get the new version
                if (this.pendingRenewalRequiresApproval) {
                    runInAction(() => {
                        const { policyId } = sharedPolicyBuilderModel;

                        if (policyId) {
                            // First invalidate queries to ensure we get fresh data
                            sharedPolicyBuilderController.invalidatePolicyQueries();

                            // Use setTimeout to escape the current reactive context
                            setTimeout(() => {
                                runInAction(() => {
                                    // Load the policy with all data - this will get the new version
                                    // The controller's reaction will automatically switch to the new version
                                    sharedPolicyBuilderController.loadPolicyWithAllData(
                                        policyId,
                                    );
                                });
                            }, 100);
                        }
                    });
                } else {
                    // For renewals without approval, just invalidate queries
                    sharedPolicyBuilderController.invalidatePolicyQueries();
                }

                // Reset the flag
                this.pendingRenewalRequiresApproval = false;
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to renew published policy',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        publishedVersionId:
                            sharedPolicyBuilderModel.publishedVersionId,
                        action: 'renewalFromModal',
                        requiresApproval: this.pendingRenewalRequiresApproval,
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                // Don't close the modal on error - let user try again
                snackbarController.addSnackbar({
                    id: 'renewal-date-error',
                    props: {
                        title: t`Failed to update renewal date`,
                        description:
                            error.message ||
                            t`An error occurred while updating the renewal date. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    constructor() {
        makeAutoObservable(this);
    }

    getActions(): Action[] {
        const actions: Action[] = [];

        const dropdownActions = this.getDropdownActions();

        if (this.shouldShowRenewWithoutUpdatesButton) {
            actions.push(this.renewWithoutUpdatesAction);
        }

        if (sharedPolicyBuilderModel.hasDraftVersion) {
            actions.push(this.goToDraftOrEditPolicyAction);
        } else if (!isEmpty(dropdownActions)) {
            actions.push({
                id: 'published-dropdown-actions',
                actionType: 'dropdown',
                typeProps: {
                    label: t`Edit policy`,
                    level: 'primary',
                    colorScheme: 'primary',
                    endIconName: 'ChevronDown',
                    items: dropdownActions,
                },
            });
        }

        return actions;
    }

    get renewWithoutUpdatesAction(): Action {
        return {
            id: 'renew-without-updates-button',
            actionType: 'button',
            typeProps: {
                label: t`Renew without updates`,
                level: 'secondary',
                colorScheme: 'primary',
                onClick: this.handleRenewWithoutUpdates,
                isLoading: this.renewalDateMutation.isPending,
            },
        };
    }

    get goToDraftOrEditPolicyAction(): Action {
        return {
            id: 'go-to-draft-button',
            actionType: 'button',
            typeProps: {
                label: t`Go to draft`,
                level: 'primary',
                colorScheme: 'primary',
                onClick: this.handleGoToDraftOrEditPolicy,
                isLoading: this.createNewVersionMutation.isPending,
            },
        };
    }

    getDropdownActions(): SchemaDropdownItemData[] {
        const actions: SchemaDropdownItemData[] = [];

        if (
            sharedPolicyHeaderSharedActionsController.shouldShowExternalPolicyActions
        ) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.importFileAction,
                sharedPolicyHeaderSharedActionsController.syncFileAction,
            );
        }

        if (
            sharedPolicyHeaderSharedActionsController.shouldShowAuthoredPolicyActions ||
            sharedPolicyHeaderSharedActionsController.shouldShowUploadedPolicyActions
        ) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.uploadFileAction,
                sharedPolicyHeaderSharedActionsController.authorPolicyAction,
            );
        }

        return actions;
    }

    get shouldShowRenewWithoutUpdatesButton(): boolean {
        const {
            isPolicyVersionOwner,
            latestIsDraft,
            hasExternalConnection,
            isAuthoredPolicy,
            isUploadedPolicy,
            isNewDrataTemplatePolicy,
        } = sharedPolicyBuilderModel;

        return (
            isPolicyVersionOwner &&
            !latestIsDraft &&
            !hasExternalConnection &&
            (isAuthoredPolicy || isUploadedPolicy || isNewDrataTemplatePolicy)
        );
    }

    get isRenewing(): boolean {
        return this.renewalDateMutation.isPending;
    }

    handleRenewWithoutUpdates = (): void => {
        openPolicyRenewalDateModal();
    };

    handleGoToDraftOrEditPolicy = (): void => {
        const { draftVersionId, isTheLastPolicyVersionInDraftStatus } =
            sharedPolicyBuilderModel;

        if (isTheLastPolicyVersionInDraftStatus && draftVersionId) {
            sharedPolicyBuilderController.switchToPolicyVersion(draftVersionId);
        }
    };

    handleRenewalFromModal = (data: {
        renewalDate: string;
        requiresAcknowledgment: boolean;
        requiresApproval: boolean;
        shouldNotifyEmployees: boolean;
    }): void => {
        const { policyId, publishedVersionId } = sharedPolicyBuilderModel;

        if (!policyId || !publishedVersionId) {
            return;
        }
        this.executeRenewal(data, data.shouldNotifyEmployees);
    };

    executeRenewal = (
        data: {
            renewalDate: string;
            requiresAcknowledgment: boolean;
            requiresApproval: boolean;
        },
        shouldNotifyEmployees: boolean,
    ): void => {
        const { policyId, publishedVersionId } = sharedPolicyBuilderModel;

        if (!policyId || !publishedVersionId) {
            return;
        }

        // Store whether this renewal requires approval
        this.pendingRenewalRequiresApproval = data.requiresApproval;

        this.renewalDateMutation.mutate({
            path: {
                policyId,
                versionId: publishedVersionId,
            },
            body: {
                renewalDate: data.renewalDate,
                requiresAcknowledgment: data.requiresAcknowledgment,
                requiresApproval: data.requiresApproval,
                shouldNotifyEmployees,
                assignedTo: 'ALL' as const,
                groupIds: [],
            },
        });
    };
}
