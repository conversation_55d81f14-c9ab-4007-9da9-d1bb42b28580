import { isEmpty } from 'lodash-es';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import type { Action } from '@cosmos/components/action-stack';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import {
    policyApprovalsControllerOverrideApprovalMutation,
    policyApprovalsControllerPostApprovalReviewMutation,
    policyVersionControllerPutPolicyVersionStatusMutation,
} from '@globals/api-sdk/queries';
import { sharedCurrentUserController } from '@globals/current-user';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { openPolicyCancelApprovalModal } from '../helpers/open-policy-cancel-approval-modal.helper';
import { openPolicyOverrideApprovalModal } from '../helpers/open-policy-override-approval-modal.helper';
import { openPolicyRequestChangesModal } from '../helpers/open-policy-request-changes-modal.helper';
import { sharedPolicyBuilderController } from '../policy-builder.controller';
import { sharedPolicyHeaderSharedActionsController } from './policy-header-shared-actions.controller';

export class PolicyHeaderNeedsApprovalActions {
    approveMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionStatusMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'policy-approve-success',
                    props: {
                        title: t`Policy approved successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to approve policy',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        action: 'approvePolicy',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'policy-approve-error',
                    props: {
                        title: t`Failed to approve policy`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    requestChangesMutation = new ObservedMutation(
        policyApprovalsControllerPostApprovalReviewMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'policy-request-changes-success',
                    props: {
                        title: t`Changes requested successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                modalController.closeModal('policy-request-changes-modal');

                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to request changes',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        action: 'requestChanges',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'policy-request-changes-error',
                    props: {
                        title: t`Failed to request changes`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    cancelApprovalMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionStatusMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'policy-cancel-approval-success',
                    props: {
                        title: t`Approval cancelled successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                modalController.closeModal('policy-cancel-approval-modal');

                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to cancel approval',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        action: 'cancelApproval',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'policy-cancel-approval-error',
                    props: {
                        title: t`Failed to cancel approval`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    overrideApprovalMutation = new ObservedMutation(
        policyApprovalsControllerOverrideApprovalMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'policy-override-approval-success',
                    props: {
                        title: t`Approval overridden successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                modalController.closeModal('policy-override-approval-modal');

                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to override approval',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        action: 'overrideApproval',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'policy-override-approval-error',
                    props: {
                        title: t`Failed to override approval`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    editPolicyMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionStatusMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'policy-action-result',
                    props: {
                        title: t`Policy moved to draft for editing`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                sharedPolicyBuilderController.invalidatePolicyQueries();
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to edit policy',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        action: 'editPolicy',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'policy-action-result',
                    props: {
                        title: t`Failed to edit policy`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    constructor() {
        makeAutoObservable(this);
    }

    getActions(): Action[] {
        const actions: Action[] = [];

        if (sharedPolicyBuilderModel.hasChangesRequested) {
            const changesRequestedDropdownActions = [];

            if (this.shouldShowOverrideApproval) {
                changesRequestedDropdownActions.push({
                    id: 'override-approval-action',
                    type: 'item',
                    label: t`Override policy approval`,
                    value: 'override-policy-approval',
                    onSelect: this.handleOverrideApproval,
                });
            }

            if (!isEmpty(changesRequestedDropdownActions)) {
                actions.push({
                    id: 'changes-requested-dropdown-actions',
                    actionType: 'dropdown',
                    typeProps: {
                        label: '',
                        level: 'tertiary',
                        colorScheme: 'primary',
                        startIconName: 'HorizontalMenu',
                        isIconOnly: true,
                        items: changesRequestedDropdownActions,
                    },
                });
            }

            if (
                sharedPolicyHeaderSharedActionsController.shouldShowAuthoredPolicyActions
            ) {
                actions.push({
                    id: 'pdf-preview-button',
                    actionType: 'button',
                    typeProps: {
                        label: t`PDF preview`,
                        level: 'secondary',
                        colorScheme: 'primary',
                        onClick:
                            sharedPolicyHeaderSharedActionsController.handlePdfPreview,
                    },
                });
            }

            if (sharedPolicyBuilderModel.isUploadedPolicy) {
                actions.push({
                    id: 'edit-policy-button',
                    actionType: 'button',
                    typeProps: {
                        label: t`Edit policy`,
                        level: 'primary',
                        colorScheme: 'primary',
                        onClick: this.handleEditPolicy,
                        isLoading: this.editPolicyMutation.isPending,
                    },
                });
            }

            return actions;
        }

        const dropdownActions = this.getDropdownActions();

        if (!isEmpty(dropdownActions)) {
            actions.push({
                id: 'needs-approval-dropdown-actions',
                actionType: 'dropdown',
                typeProps: {
                    label: '',
                    level: 'tertiary',
                    colorScheme: 'primary',
                    startIconName: 'HorizontalMenu',
                    items: dropdownActions,
                },
            });
        }

        if (this.shouldShowRequestChangesButton) {
            actions.push({
                id: 'request-changes-button',
                actionType: 'button',
                typeProps: {
                    label: t`Request changes`,
                    level: 'secondary',
                    colorScheme: 'primary',
                    onClick: this.handleRequestChanges,
                },
            });
        }

        if (this.shouldShowApproveButton) {
            actions.push({
                id: 'approve-button',
                actionType: 'button',
                typeProps: {
                    label: t`Approve`,
                    level: 'primary',
                    colorScheme: 'primary',
                    onClick: this.handleApprove,
                    isLoading: this.approveMutation.isPending,
                },
            });
        }

        return actions;
    }

    getDropdownActions(): SchemaDropdownItemData[] {
        const actions = [];

        if (this.shouldShowOverrideApproval) {
            actions.push({
                id: 'override-approval-action',
                type: 'item',
                label: t`Override policy approval`,
                value: 'override-policy-approval',
                onSelect: this.handleOverrideApproval,
            });
        }

        if (this.shouldShowCancelApproval) {
            actions.push({
                id: 'cancel-approval-action',
                type: 'item',
                label: t`Cancel approval`,
                value: 'cancel-approval',
                onSelect: this.handleCancelApproval,
            });
        }

        if (
            sharedPolicyHeaderSharedActionsController.shouldShowAuthoredPolicyActions
        ) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.pdfPreviewAction,
            );
        }

        return actions;
    }

    get isApprover(): boolean {
        const { currentVersion } = sharedPolicyBuilderController;
        const currentUserId = sharedCurrentUserController.entryId;

        if (!currentUserId) {
            return false;
        }

        if (currentVersion?.approver?.entryId === currentUserId) {
            return true;
        }

        return sharedPolicyBuilderModel.isCurrentUserApprover;
    }

    get isApproverReadyForReview(): boolean {
        return this.isApprover && sharedPolicyBuilderModel.requiresApproval;
    }

    get canOverridePolicy(): boolean {
        return (
            sharedFeatureAccessModel.hasPolicyManagePermission ||
            sharedPolicyBuilderModel.isPolicyVersionOwner
        );
    }

    get shouldShowApproveButton(): boolean {
        return (
            this.isApprover &&
            this.isApproverReadyForReview &&
            !sharedPolicyBuilderModel.isApproved
        );
    }

    get shouldShowRequestChangesButton(): boolean {
        return (
            !sharedPolicyBuilderModel.isApproved &&
            this.isApprover &&
            this.isApproverReadyForReview
        );
    }

    get shouldShowCancelApproval(): boolean {
        return (
            this.isApprovalPending &&
            sharedPolicyBuilderModel.isPolicyVersionOwner
        );
    }

    get shouldShowOverrideApproval(): boolean {
        return this.canOverridePolicy && this.isApprovalPending;
    }

    private get isApprovalPending(): boolean {
        return !sharedPolicyBuilderModel.isApproved;
    }

    handleApprove = (): void => {
        const { policyId, currentVersionId, currentUserReviewId } =
            sharedPolicyBuilderModel;

        if (!policyId || !currentVersionId || !currentUserReviewId) {
            snackbarController.addSnackbar({
                id: 'approve-policy-error',
                props: {
                    title: t`Unable to approve policy: Missing required data`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.approveMutation.mutate({
            path: {
                policyId,
                policyVersionId: currentVersionId,
            },
            body: {
                policyVersionStatus: 'APPROVED',
                reviewId: currentUserReviewId,
            },
        });
    };

    handleRequestChanges = (): void => {
        openPolicyRequestChangesModal();
    };

    handleCancelApproval = (): void => {
        openPolicyCancelApprovalModal();
    };

    handleCancelApprovalSubmit = (): void => {
        const { policyId, currentVersionId } = sharedPolicyBuilderModel;

        if (!policyId || !currentVersionId) {
            snackbarController.addSnackbar({
                id: 'policy-error',
                props: {
                    title: t`Unable to cancel approval: Missing policy or version ID`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.cancelApprovalMutation.mutate({
            path: {
                policyId,
                policyVersionId: currentVersionId,
            },
            body: {
                policyVersionStatus: 'DRAFT',
            },
        });
    };

    handleOverrideApproval = (): void => {
        openPolicyOverrideApprovalModal();
    };

    handleRequestChangesSubmit = (reason: string): void => {
        const { policyId, currentVersionId, latestApprovalId } =
            sharedPolicyBuilderModel;

        if (!policyId || !currentVersionId || !latestApprovalId) {
            snackbarController.addSnackbar({
                id: 'policy-error',
                props: {
                    title: t`Unable to request changes: Missing required data`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.requestChangesMutation.mutate({
            path: {
                policyId,
                versionId: currentVersionId,
                approvalId: latestApprovalId,
            },
            body: {
                status: 'CHANGES_REQUESTED',
                changeRequest: reason,
            },
        });
    };

    handleOverrideApprovalSubmit = (overrideReason: string): void => {
        const { policyId, latestApprovalId } = sharedPolicyBuilderModel;

        if (!policyId) {
            snackbarController.addSnackbar({
                id: 'policy-error',
                props: {
                    title: t`Unable to override approval: Missing policy ID`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const approvalId = latestApprovalId;

        if (!approvalId) {
            snackbarController.addSnackbar({
                id: 'policy-error',
                props: {
                    title: t`Unable to override approval: No approval found`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.overrideApprovalMutation.mutate({
            path: { policyId, approvalId },
            body: {
                overrideReason,
            },
        });
    };

    handleEditPolicy = (): void => {
        const { policyId, currentVersionId } = sharedPolicyBuilderModel;

        if (!policyId || !currentVersionId) {
            logger.error({
                message: 'Missing IDs for edit policy',
                additionalInfo: {
                    policyId,
                    currentVersionId,
                    action: 'editPolicy',
                },
            });

            snackbarController.addSnackbar({
                id: 'policy-error',
                props: {
                    title: t`Unable to edit policy: Missing policy or version ID`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.editPolicyMutation.mutate({
            path: {
                policyId,
                policyVersionId: currentVersionId,
            },
            body: {
                policyVersionStatus: 'DRAFT',
            },
        });
    };
}
