import { isEmpty } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import type { Action } from '@cosmos/components/action-stack';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import {
    policyVersionControllerPutPolicyVersionExplanationOfChangesMutation,
    policyVersionControllerPutPolicyVersionStatusMutation,
    policyVersionControllerUpdatePolicyHtmlMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { openPolicyAcknowledgmentPublishModal } from '../helpers/open-policy-acknowledgment-publish-modal.helper';
import { openPolicyNotifyPersonnelApprovedModal } from '../helpers/open-policy-notify-personnel-approved-modal.helper';
import {
    closePolicySaveChangesModal,
    openPolicySaveChangesModal,
} from '../helpers/open-policy-save-changes-modal.helper';
import {
    closePolicyUploadThenSaveModal,
    openPolicyUploadThenSaveModal,
} from '../helpers/open-policy-upload-then-save-modal.helper';
import { sharedPolicyBuilderController } from '../policy-builder.controller';
import { sharedPolicyCkEditorController } from './policy-ckeditor.controller';
import { sharedPolicyHeaderSharedActionsController } from './policy-header-shared-actions.controller';

export class PolicyHeaderApprovedActions {
    workspaceId = sharedWorkspacesController.currentWorkspaceId;

    updateHtmlMutation = new ObservedMutation(
        policyVersionControllerUpdatePolicyHtmlMutation,
        {
            onSuccess: () => {
                if (this.hasPendingSaveChangesData) {
                    this.proceedWithVersionStatusUpdate();
                } else {
                    snackbarController.addSnackbar({
                        id: 'policy-saved-success',
                        props: {
                            title: t`Policy saved successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    sharedPolicyBuilderController.invalidatePolicyQueries();
                }
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to update policy HTML content',
                    additionalInfo: {
                        action: 'updateHtmlMutation',
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        hasPendingData: this.hasPendingSaveChangesData,
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'policy-saved-error',
                    props: {
                        title: t`Failed to save policy`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                this.clearPendingSaveChangesData();
            },
        },
    );

    updateExplanationMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionExplanationOfChangesMutation,
        {
            onSuccess: () => {
                if (this.pendingDataRequiresApproval) {
                    this.updateStatusToNeedsApproval();
                }
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to update policy explanation of changes',
                    additionalInfo: {
                        action: 'updateExplanationMutation',
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        requiresApproval: this.pendingDataRequiresApproval,
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'policy-explanation-error',
                    props: {
                        title: t`Failed to update explanation`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                this.clearPendingSaveChangesData();
            },
        },
    );

    updatePolicyStatusMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionStatusMutation,
        {
            onSuccess: () => {
                if (this.isUploadThenSaveContext) {
                    snackbarController.addSnackbar({
                        id: 'policy-upload-and-save-success',
                        props: {
                            title: t`File uploaded and changes saved successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'policy-changes-saved-success',
                        props: {
                            title: t`Changes saved successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }

                if (this.isUploadThenSaveContext) {
                    sharedPolicyBuilderController.invalidateUploadedPolicyQueries();
                } else {
                    sharedPolicyBuilderController.invalidatePolicyQueries();
                }

                // Check context before clearing it
                const isUploadFlow = this.isUploadThenSaveContext;

                this.clearPendingSaveChangesData();

                if (isUploadFlow) {
                    closePolicyUploadThenSaveModal();
                } else {
                    closePolicySaveChangesModal();
                }
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to update policy status to NEEDS_APPROVAL',
                    additionalInfo: {
                        action: 'updatePolicyStatusMutation',
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        targetStatus: 'NEEDS_APPROVAL',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'policy-status-error',
                    props: {
                        title: t`Failed to update policy status`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                this.clearPendingSaveChangesData();
            },
        },
    );

    publishMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionStatusMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'policy-published-success',
                    props: {
                        title: t`Policy published successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                sharedPolicyBuilderController.invalidatePolicyQueries();

                sharedProgrammaticNavigationController.navigateTo(
                    `/workspaces/${this.workspaceId}/governance/policies/active`,
                );
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to publish policy',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        action: 'publishPolicy',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'policy-published-error',
                    props: {
                        title: t`Failed to publish policy`,
                        description: t`Please try again. If the problem persists, contact support.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    pendingSaveChangesData: {
        requiresApproval: boolean;
        changesExplanation?: string;
    } | null = null;

    isUploadThenSaveContext = false;

    private get hasPendingSaveChangesData(): boolean {
        return this.pendingSaveChangesData !== null;
    }

    private clearPendingSaveChangesData(): void {
        this.pendingSaveChangesData = null;
        this.isUploadThenSaveContext = false;
    }

    private get pendingDataRequiresApproval(): boolean {
        return this.pendingSaveChangesData?.requiresApproval === true;
    }

    private getPendingSaveChangesData(): {
        requiresApproval: boolean;
        changesExplanation?: string;
    } {
        if (!this.hasPendingSaveChangesData) {
            logger.error({
                message: 'Cannot get pending save changes data: data is null',
            });

            return {
                requiresApproval: false,
            };
        }

        return this.pendingSaveChangesData as {
            requiresApproval: boolean;
            changesExplanation?: string;
        };
    }

    constructor() {
        makeAutoObservable(this);
    }

    getActions(): Action[] {
        const actions: Action[] = [];

        const dropdownActions = this.getDropdownActions();

        if (!isEmpty(dropdownActions)) {
            actions.push({
                id: 'approved-dropdown-actions',
                actionType: 'dropdown',
                typeProps: {
                    label: '',
                    level: 'tertiary',
                    colorScheme: 'primary',
                    startIconName: 'HorizontalMenu',
                    isIconOnly: true,
                    items: dropdownActions,
                },
            });
        }

        if (this.shouldShowPublishButton) {
            actions.push(this.publishAction);
        }

        if (this.shouldShowSaveChangesButton) {
            actions.push(this.saveChangesAction);
        }

        return actions;
    }

    get publishAction(): Action {
        return {
            id: 'publish-button',
            actionType: 'button',
            typeProps: {
                label: t`Publish`,
                level: 'primary',
                colorScheme: 'primary',
                onClick: this.handlePublish,
                isLoading: this.isPublishingWithoutModal,
                a11yLoadingLabel: t`Publishing policy`,
            },
        };
    }

    get saveChangesAction(): Action {
        return {
            id: 'save-changes-button',
            actionType: 'button',
            typeProps: {
                label: t`Save changes`,
                level: 'primary',
                colorScheme: 'primary',
                onClick: this.handleSaveChanges,
                a11yLoadingLabel: t`Saving changes`,
            },
        };
    }

    getDropdownActions(): SchemaDropdownItemData[] {
        const actions = [];

        if (
            sharedPolicyHeaderSharedActionsController.shouldShowAuthoredPolicyActions
        ) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.pdfPreviewAction,
            );
        }

        if (this.shouldShowEditPolicy) {
            actions.push({
                id: 'edit-policy-action',
                type: 'item',
                label: t`Edit policy`,
                value: 'edit-policy',
                onSelect: this.handleEditPolicy,
                a11yLoadingLabel: t`Editing policy`,
            });
        }

        if (this.shouldShowUploadFile) {
            actions.push(this.uploadThenSaveAction);
        }

        if (
            sharedPolicyHeaderSharedActionsController.shouldShowExternalPolicyActions &&
            sharedPolicyBuilderModel.isPolicyVersionOwner
        ) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.importFileAction,
                sharedPolicyHeaderSharedActionsController.syncFileAction,
            );
        }

        if (sharedPolicyCkEditorController.isCkEditorInEditMode) {
            actions.push({
                id: 'cancel-action',
                type: 'item',
                label: t`Cancel`,
                value: 'cancel',
                onSelect: this.handleCancel,
                a11yLoadingLabel: t`Cancelling`,
            });
        }

        return actions;
    }

    get uploadThenSaveAction(): SchemaDropdownItemData {
        return {
            id: 'upload-then-save-action',
            type: 'item',
            label: t`Upload file`,
            value: 'upload-then-save',
            onSelect: openPolicyUploadThenSaveModal,
            disabled: this.isUploadingAndSaving,
        };
    }

    get shouldShowPublishButton(): boolean {
        return (
            sharedPolicyBuilderModel.isPolicyVersionOwner &&
            !sharedPolicyCkEditorController.isCkEditorInEditMode
        );
    }

    get shouldShowSaveChangesButton(): boolean {
        return (
            sharedPolicyCkEditorController.isCkEditorInEditMode &&
            sharedPolicyBuilderModel.isAuthoredPolicy &&
            sharedPolicyBuilderModel.isPolicyVersionOwner
        );
    }

    get shouldShowEditPolicy(): boolean {
        return (
            !sharedPolicyCkEditorController.isCkEditorInEditMode &&
            sharedPolicyBuilderModel.isAuthoredPolicy &&
            sharedPolicyBuilderModel.isPolicyVersionOwner
        );
    }

    get shouldShowUploadFile(): boolean {
        return (
            sharedPolicyHeaderSharedActionsController.shouldShowUploadedPolicyActions &&
            sharedPolicyBuilderModel.isPolicyVersionOwner
        );
    }

    get isSavingChanges(): boolean {
        return (
            this.updateHtmlMutation.isPending ||
            this.updateExplanationMutation.isPending ||
            this.updatePolicyStatusMutation.isPending
        );
    }

    get isUploadingAndSaving(): boolean {
        return (
            this.isSavingChanges ||
            sharedPolicyHeaderSharedActionsController.isUploadingFile
        );
    }

    get isPublishing(): boolean {
        return this.publishMutation.isPending;
    }

    get isPublishingWithoutModal(): boolean {
        const { requiresAcknowledgment } = sharedPolicyBuilderModel;

        return requiresAcknowledgment === false && this.isPublishing;
    }

    handlePublish = (): void => {
        const { requiresAcknowledgment } = sharedPolicyBuilderModel;

        if (requiresAcknowledgment === true) {
            openPolicyNotifyPersonnelApprovedModal();
        } else if (requiresAcknowledgment === null) {
            openPolicyAcknowledgmentPublishModal();
        } else {
            this.publishDirectly();
        }
    };

    handlePublishFromModal = (data: {
        requiresAcknowledgment: boolean;
        shouldNotifyEmployees: boolean;
    }): void => {
        const { policyId, currentVersionId: versionId } =
            sharedPolicyBuilderModel;

        if (!policyId || !versionId) {
            return;
        }
        this.publishMutation.mutate({
            path: {
                policyId,
                policyVersionId: versionId,
            },
            body: {
                policyVersionStatus: 'PUBLISHED',
                requiresAcknowledgment: data.requiresAcknowledgment,
                shouldNotifyEmployees: data.shouldNotifyEmployees,
            },
        });
    };

    handleSaveChanges = (): void => {
        const { htmlContent } = sharedPolicyCkEditorController;

        if (!htmlContent) {
            snackbarController.addSnackbar({
                id: 'save-changes-empty-content',
                props: {
                    title: t`Unable to save changes`,
                    description: t`Policy content cannot be empty. Please add content before saving changes.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        openPolicySaveChangesModal();
    };

    handleSaveChangesFromModal = (formValues: {
        requiresApproval?: string;
        changesExplanation?: string;
    }): void => {
        const data = {
            requiresApproval: formValues.requiresApproval === 'true',
            changesExplanation: formValues.changesExplanation,
        };
        const { policyId } = sharedPolicyBuilderModel;
        const { htmlContent } = sharedPolicyCkEditorController;

        this.pendingSaveChangesData = data;

        this.updateHtmlMutation.mutate({
            path: { id: policyId },
            body: {
                html: htmlContent,
            },
        });
    };

    handleUploadThenSave = (data: {
        file: File[];
        requiresApproval: boolean;
        changesExplanation: string;
    }): void => {
        // Set the pending save changes data for after upload
        this.pendingSaveChangesData = {
            requiresApproval: data.requiresApproval,
            changesExplanation: data.changesExplanation,
        };

        this.isUploadThenSaveContext = true;

        // Use the shared actions controller to handle the file upload
        // Pass a callback to proceed with save changes after upload
        sharedPolicyHeaderSharedActionsController.handleFileUpload(
            data,
            this.handleFileUploadComplete,
        );
    };

    handleEditPolicy = (): void => {
        const { policyId } = sharedPolicyBuilderModel;

        sharedProgrammaticNavigationController.navigateTo(
            `/workspaces/${this.workspaceId}/governance/policies/builder/${policyId}/policy`,
        );

        sharedPolicyCkEditorController.enterEditMode();
    };

    handleCancel = (): void => {
        sharedPolicyCkEditorController.cancelEdit();
        sharedPolicyBuilderController.invalidatePolicyQueries();
    };

    proceedWithVersionStatusUpdate = (): void => {
        const { currentVersionId: versionId, policyId } =
            sharedPolicyBuilderModel;

        if (!versionId || !policyId) {
            logger.error({
                message:
                    'Cannot proceed with version status update: missing required IDs',
                additionalInfo: { versionId, policyId },
            });

            return;
        }

        if (!this.hasPendingSaveChangesData) {
            logger.error({
                message:
                    'Cannot proceed with version status update: pendingSaveChangesData is null',
            });

            return;
        }

        const { requiresApproval, changesExplanation } =
            this.getPendingSaveChangesData();

        if (requiresApproval) {
            this.updateExplanationMutation.mutate({
                path: { policyId, policyVersionId: versionId },
                body: { policyVersionExplanationOfChanges: changesExplanation },
            });
        } else {
            snackbarController.addSnackbar({
                id: 'policy-changes-saved-success',
                props: {
                    title: t`Changes saved successfully`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            sharedPolicyBuilderController.invalidatePolicyQueries();
            this.clearPendingSaveChangesData();
            closePolicySaveChangesModal();
        }

        sharedPolicyCkEditorController.exitEditMode();
    };

    updateStatusToNeedsApproval = (): void => {
        const { currentVersionId: versionId, policyId } =
            sharedPolicyBuilderModel;

        if (!versionId || !policyId) {
            return;
        }

        this.updatePolicyStatusMutation.mutate({
            path: { policyId, policyVersionId: versionId },
            body: { policyVersionStatus: 'NEEDS_APPROVAL' },
        });
    };

    publishDirectly = (): void => {
        this.handlePublishFromModal({
            requiresAcknowledgment: false,
            shouldNotifyEmployees: false,
        });
    };

    /**
     * Called by shared actions controller when file upload is complete
     * to proceed with save changes if there's pending data.
     */
    handleFileUploadComplete = (): void => {
        if (this.hasPendingSaveChangesData) {
            this.proceedWithVersionStatusUpdateFromUpload();
        }
    };

    /**
     * Handles version status update specifically from upload-then-save flow
     * Closes the upload-then-save modal instead of save-changes modal.
     */
    proceedWithVersionStatusUpdateFromUpload = (): void => {
        const { currentVersionId: versionId, policyId } =
            sharedPolicyBuilderModel;

        if (!versionId || !policyId) {
            logger.error({
                message:
                    'Cannot proceed with version status update: missing required IDs',
                additionalInfo: { versionId, policyId },
            });

            return;
        }

        if (!this.hasPendingSaveChangesData) {
            logger.error({
                message:
                    'Cannot proceed with version status update: pendingSaveChangesData is null',
            });

            return;
        }

        const { requiresApproval, changesExplanation } =
            this.getPendingSaveChangesData();

        if (requiresApproval) {
            this.updateExplanationMutation.mutate({
                path: { policyId, policyVersionId: versionId },
                body: { policyVersionExplanationOfChanges: changesExplanation },
            });
        } else {
            snackbarController.addSnackbar({
                id: 'policy-upload-and-save-success',
                props: {
                    title: t`File uploaded and changes saved successfully`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            sharedPolicyBuilderController.invalidateUploadedPolicyQueries();
            this.clearPendingSaveChangesData();
            closePolicyUploadThenSaveModal();
        }

        sharedPolicyCkEditorController.exitEditMode();
    };
}
