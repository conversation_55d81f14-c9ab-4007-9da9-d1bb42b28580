import { isEmpty, isNil } from 'lodash-es';
import {
    policiesControllerGetInProgressPolicyPdfDownloadUrlOptions,
    policiesControllerGetPolicyAssociatedControlsOptions,
    policiesControllerGetPolicyAssociatedFrameworksOptions,
    policiesControllerGetPolicyOptions,
    policyApprovalsControllerGetApprovalOptions,
    policyVersionControllerGetPolicyVersionOptions,
} from '@globals/api-sdk/queries';
import type {
    PolicyVersionResponseDto,
    PolicyWithReplaceResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedQuery,
    reaction,
    runInAction,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { sharedPolicyHeaderActionsController } from './policy-header-actions.controller';

class PolicyBuilderController {
    /**
     * Note: policy ID is required.
     */
    policyId: number | null = null;
    currentVersionId: number | null = null;
    pendingVersionId: number | null = null;
    shouldLoadVersion = false;

    isAddingVersionGracePeriodSLAs = false;
    isAddingVersionP3MatrixSLAs = false;
    isAddingVersionWeekTimeFrameSLAs = false;

    constructor() {
        makeAutoObservable(this);
        this.setupReactions();
    }

    resetAddingVersionSLAs = (): void => {
        this.isAddingVersionGracePeriodSLAs = false;
        this.isAddingVersionP3MatrixSLAs = false;
        this.isAddingVersionWeekTimeFrameSLAs = false;
    };

    setIsAddingVersionGracePeriodSLAs = (value: boolean): void => {
        this.isAddingVersionGracePeriodSLAs = value;
    };

    setIsAddingVersionP3MatrixSLAs = (value: boolean): void => {
        this.isAddingVersionP3MatrixSLAs = value;
    };

    setIsAddingVersionWeekTimeFrameSLAs = (value: boolean): void => {
        this.isAddingVersionWeekTimeFrameSLAs = value;
    };

    setPolicyId(policyId: number): void {
        this.policyId = policyId;
    }

    setupReactions(): void {
        reaction(
            () => ({
                policy: this.policy,
                isLoading: this.policyQuery.isLoading,
                shouldLoadVersion: this.shouldLoadVersion,
                pendingVersionId: this.pendingVersionId,
                hasError: this.policyQuery.hasError,
            }),
            ({
                policy,
                isLoading,
                shouldLoadVersion,
                pendingVersionId,
                hasError,
            }) => {
                if (hasError) {
                    runInAction(() => {
                        this.shouldLoadVersion = false;
                        this.pendingVersionId = null;
                    });

                    return;
                }

                if (!policy || isLoading || !shouldLoadVersion) {
                    return;
                }

                const targetVersionId =
                    pendingVersionId || this.getVersionIdToLoad();

                if (targetVersionId) {
                    this.switchToPolicyVersion(targetVersionId);
                }

                runInAction(() => {
                    this.shouldLoadVersion = false;
                    this.pendingVersionId = null;
                });
            },
        );

        reaction(
            () => ({
                currentVersionId: this.currentVersionId,
                policyId: this.policyId,
                isVersionLoading: this.policyVersionQuery.isLoading,
                hasVersionData: Boolean(this.currentVersion),
            }),
            ({
                currentVersionId,
                policyId,
                isVersionLoading,
                hasVersionData,
            }) => {
                if (
                    currentVersionId &&
                    policyId &&
                    !isVersionLoading &&
                    hasVersionData
                ) {
                    this.loadApprovalData(currentVersionId);
                }
            },
        );
    }

    policyQuery = new ObservedQuery(policiesControllerGetPolicyOptions);
    policyVersionQuery = new ObservedQuery(
        policyVersionControllerGetPolicyVersionOptions,
    );
    policyControlsAssociatedQuery = new ObservedQuery(
        policiesControllerGetPolicyAssociatedControlsOptions,
    );
    policyFrameworksAssociatedQuery = new ObservedQuery(
        policiesControllerGetPolicyAssociatedFrameworksOptions,
    );

    approvalQuery = new ObservedQuery(
        policyApprovalsControllerGetApprovalOptions,
    );

    policyPdfDownloadUrlQuery = new ObservedQuery(
        policiesControllerGetInProgressPolicyPdfDownloadUrlOptions,
    );

    get isPolicyLoading(): boolean {
        return this.policyQuery.isLoading;
    }

    get isVersionLoading(): boolean {
        return this.policyVersionQuery.isLoading;
    }

    get isPolicyBuilderControlsLoading(): boolean {
        return (
            sharedPolicyBuilderModel.isPolicyBuilderFirstLoading ||
            (this.policyControlsAssociatedQuery.isLoading &&
                isEmpty(this.policyControlsAssociatedQuery.data))
        );
    }

    get isControlsLoading(): boolean {
        return this.policyControlsAssociatedQuery.isLoading;
    }

    get isFrameworksLoading(): boolean {
        return this.policyFrameworksAssociatedQuery.isLoading;
    }

    get isPdfSignedUrlLoading(): boolean {
        return this.policyPdfDownloadUrlQuery.isLoading;
    }

    get isApprovalLoading(): boolean {
        return this.approvalQuery.isLoading;
    }

    get policy(): PolicyWithReplaceResponseDto | null {
        return this.policyQuery.data;
    }

    get currentVersion(): PolicyVersionResponseDto | null {
        return this.policyVersionQuery.data;
    }

    get approvalData() {
        return this.approvalQuery.data;
    }

    get latestApprovalId(): number | null {
        const approvals = this.approvalData?.approvals;

        if (!approvals || isEmpty(approvals)) {
            return null;
        }

        return approvals[0]?.id ?? null;
    }

    get currentUserReviewId(): number | null {
        const approvals = this.approvalData?.approvals;
        const currentUserId = sharedCurrentUserController.user?.id;
        const currentUserEntryId = sharedCurrentUserController.entryId;

        if (!approvals || isEmpty(approvals) || !currentUserId) {
            return null;
        }

        for (const approval of approvals) {
            for (const reviewGroup of approval.reviewGroups) {
                for (const review of reviewGroup.reviews) {
                    if (
                        (review.userId === currentUserId ||
                            String(review.userId) === currentUserEntryId) &&
                        review.reviewStatus === 'READY_FOR_REVIEW'
                    ) {
                        return review.reviewId || null;
                    }
                }
            }
        }

        return null;
    }

    get hasChangesRequested(): boolean {
        const approvals = this.approvalData?.approvals;

        if (!approvals || isEmpty(approvals)) {
            return false;
        }

        return approvals.some(
            (approval) =>
                approval.status === 'CHANGES_REQUESTED' ||
                approval.reviewGroups.some(
                    (reviewGroup) =>
                        reviewGroup.consensusDecision === 'CHANGES_REQUESTED' ||
                        reviewGroup.reviews.some(
                            (review) =>
                                review.reviewStatus === 'CHANGES_REQUESTED',
                        ),
                ),
        );
    }

    get changesRequestedBy(): string | null {
        const approvals = this.approvalData?.approvals;

        if (!approvals || isEmpty(approvals)) {
            return null;
        }

        for (const approval of approvals) {
            for (const reviewGroup of approval.reviewGroups) {
                for (const review of reviewGroup.reviews) {
                    if (review.reviewStatus === 'CHANGES_REQUESTED') {
                        return review.name || 'Unknown user';
                    }
                }
            }
        }

        return null;
    }

    get isCurrentUserApprover(): boolean {
        const approvals = this.approvalData?.approvals;
        const currentUserId = sharedCurrentUserController.user?.id;
        const currentUserEntryId = sharedCurrentUserController.entryId;

        if (!approvals || isEmpty(approvals) || !currentUserId) {
            return false;
        }

        // Check legacy single-approver
        if (this.currentVersion?.approver?.entryId === currentUserEntryId) {
            return true;
        }

        // Check multi-approver
        for (const approval of approvals) {
            for (const reviewGroup of approval.reviewGroups) {
                for (const review of reviewGroup.reviews) {
                    if (
                        review.userId === currentUserId ||
                        String(review.userId) === currentUserEntryId
                    ) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    get policyControlsAssociated() {
        return this.policyControlsAssociatedQuery.data?.controls ?? [];
    }

    get policyFrameworksAssociated() {
        return this.policyFrameworksAssociatedQuery.data?.frameworks ?? [];
    }

    get policyPdfSignedUrl(): string | undefined {
        return this.policyPdfDownloadUrlQuery.data?.signedUrl;
    }

    loadPolicyPdfDownload(policyId: number): void {
        this.policyPdfDownloadUrlQuery.load({ path: { id: policyId } });
    }

    getVersionIdToLoad(): number | null {
        if (!this.policy) {
            return null;
        }

        const latestVersionId = this.policy.latestPolicyVersion?.id;
        const publishedVersionId =
            this.policy.currentPublishedPolicyVersion?.id;

        if (latestVersionId) {
            return latestVersionId;
        }

        if (publishedVersionId) {
            return publishedVersionId;
        }

        return null;
    }

    loadPolicyWithAllData = (policyId: number, versionId?: number): void => {
        if (isNil(policyId) || policyId <= 0) {
            return;
        }

        // Always clear actions cache to ensure fresh state
        // This prevents stale mutation states when navigating back to the same policy
        sharedPolicyHeaderActionsController.clearActionsCache();

        runInAction(() => {
            this.pendingVersionId = versionId ?? null;
            this.shouldLoadVersion = true;
        });

        this.loadPolicy(policyId);
    };

    switchToPolicyVersion = (versionId: number): void => {
        if (this.currentVersionId === versionId) {
            return;
        }

        runInAction(() => {
            this.currentVersionId = versionId;
        });

        this.loadPolicyVersion(versionId);
    };

    loadPolicy = (policyId: number): void => {
        this.policyId = policyId;
        this.policyQuery.load({ path: { id: this.policyId } });
    };

    loadControlsAssociatedData = (): void => {
        if (isNil(sharedWorkspacesController.currentWorkspace)) {
            when(
                () => !isNil(sharedWorkspacesController.currentWorkspace),
                () => {
                    this.loadControlsAssociatedData();
                },
            );

            return;
        }

        if (isNil(this.policyId)) {
            logger.warn(
                'PolicyBuilderController: loadControlsAssociatedData called before policyId is set.',
            );

            return;
        }

        const loadParams = {
            path: {
                xProductId: sharedWorkspacesController.currentWorkspace.id,
                id: this.policyId,
            },
        };

        this.policyControlsAssociatedQuery.load(loadParams);
    };

    loadFrameworksAssociatedData = (): void => {
        if (isNil(sharedWorkspacesController.currentWorkspace)) {
            when(
                () => !isNil(sharedWorkspacesController.currentWorkspace),
                () => {
                    this.loadFrameworksAssociatedData();
                },
            );

            return;
        }

        if (isNil(this.policyId)) {
            logger.warn(
                'PolicyBuilderController: loadFrameworksAssociatedData called before policyId is set.',
            );

            return;
        }

        const loadParams = {
            path: {
                xProductId: sharedWorkspacesController.currentWorkspace.id,
                id: this.policyId,
            },
        };

        this.policyFrameworksAssociatedQuery.load(loadParams);
    };

    loadPolicyVersion = (versionId: number): void => {
        if (isNil(this.policyId)) {
            return;
        }

        this.policyVersionQuery.load({
            path: {
                policyId: this.policyId,
                policyVersionId: versionId,
            },
        });
        // Note: Approval data is loaded automatically by the reaction when version data is available
    };

    loadApprovalData = (versionId: number): void => {
        if (isNil(this.policyId)) {
            return;
        }

        // Load approval data with latest: true parameter (same as web)
        this.approvalQuery.load({
            path: { policyId: this.policyId, versionId },
            query: { latest: true },
        });
    };

    invalidatePolicyQueries = (): void => {
        this.policyQuery.invalidate();
        this.policyVersionQuery.invalidate();
        this.approvalQuery.invalidate();
    };

    invalidateUploadedPolicyQueries = (): void => {
        this.invalidatePolicyQueries();
        this.policyPdfDownloadUrlQuery.invalidate();
    };
}

export const sharedPolicyBuilderController = new PolicyBuilderController();
