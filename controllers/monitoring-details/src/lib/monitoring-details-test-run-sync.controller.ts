import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { SocketEvent } from '@drata/enums';
import { makeAutoObservable, runInAction, when } from '@globals/mobx';
import { sharedSocketController } from '@globals/socket';

class MonitoringDetailsTestRunSyncController {
    isInitialized = false;

    constructor() {
        makeAutoObservable(this);
    }

    init(): void {
        if (this.isInitialized) {
            return;
        }

        when(
            () => sharedSocketController.isInitialized,
            () => {
                this.subscribeToChannel();
                runInAction(() => {
                    this.isInitialized = true;
                });
            },
        );
    }

    subscribeToChannel(): void {
        sharedSocketController.subscribe({
            channelType: 'company',
            eventName: SocketEvent.MONITOR_TEST_RUN,
            callback: sharedMonitoringTestDetailsController.invalidate,
        });
    }
}

export const sharedMonitoringDetailsTestRunSyncController =
    new MonitoringDetailsTestRunSyncController();
