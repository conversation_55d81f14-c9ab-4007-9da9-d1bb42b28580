import { isEmpty } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import {
    monitorExclusionsControllerListMonitorInstanceExclusionsConnectionClientAliasesInfiniteOptions,
    monitorExclusionsControllerListMonitorInstanceExclusionsDesignatorsInfiniteOptions,
    monitorExclusionsControllerListMonitorInstanceExclusionsOptions,
    monitorExclusionsControllerListMonitorInstanceExclusionsResourcesInfiniteOptions,
} from '@globals/api-sdk/queries';
import type {
    MonitorExclusionResponseDto,
    MonitorExclusionsConnectionClientAliasResponseDto,
    MonitorExclusionsDesignatorResponseDto,
    MonitorExclusionsResourceResponseDto,
} from '@globals/api-sdk/types';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { processMonitoringExclusionsFilters } from '../helpers/monitoring-exclusions-filters.helper';

class MonitoringDetailsExclusionsController {
    constructor() {
        makeAutoObservable(this);
    }

    testId: number | null = null;

    exclusionsQuery = new ObservedQuery(
        monitorExclusionsControllerListMonitorInstanceExclusionsOptions,
    );

    resourcesQuery = new ObservedInfiniteQuery(
        monitorExclusionsControllerListMonitorInstanceExclusionsResourcesInfiniteOptions,
    );

    connectionsQuery = new ObservedInfiniteQuery(
        monitorExclusionsControllerListMonitorInstanceExclusionsConnectionClientAliasesInfiniteOptions,
    );

    designatorsQuery = new ObservedInfiniteQuery(
        monitorExclusionsControllerListMonitorInstanceExclusionsDesignatorsInfiniteOptions,
    );

    setTestId(testId: number) {
        this.testId = testId;
    }

    get isLoading(): boolean {
        return this.exclusionsQuery.isLoading;
    }

    get exclusionsTotal(): number {
        return this.exclusionsQuery.data?.total || 0;
    }

    get exclusionsData(): MonitorExclusionResponseDto[] {
        return this.exclusionsQuery.data?.data ?? [];
    }

    get resourcesData(): MonitorExclusionsResourceResponseDto[] {
        return (this.resourcesQuery.data?.pages ?? []).flatMap(
            (page) => page?.data ?? [],
        );
    }

    get isResourcesLoading(): boolean {
        return this.resourcesQuery.isLoading;
    }

    get hasMoreResources(): boolean {
        return this.resourcesQuery.hasNextPage;
    }

    get connectionsData(): MonitorExclusionsConnectionClientAliasResponseDto[] {
        return (this.connectionsQuery.data?.pages ?? []).flatMap(
            (page) => page?.data ?? [],
        );
    }

    get isConnectionsLoading(): boolean {
        return this.connectionsQuery.isLoading;
    }

    get hasMoreConnections(): boolean {
        return this.connectionsQuery.hasNextPage;
    }

    get designatorsData(): MonitorExclusionsDesignatorResponseDto[] {
        return (
            this.designatorsQuery.data?.pages.flatMap(
                (page) => page?.data ?? [],
            ) ?? []
        );
    }

    get isDesignatorsLoading(): boolean {
        return this.designatorsQuery.isLoading;
    }

    get hasMoreDesignators(): boolean {
        return this.designatorsQuery.hasNextPage;
    }

    invalidate = () => {
        this.exclusionsQuery.invalidate();
    };

    #lastResourcesSearchQuery = '';

    loadResources = (search?: string): void => {
        const testId = Number(this.testId);

        if (isNaN(testId)) {
            throw new TypeError('testId must be numbers');
        }

        if (search !== this.#lastResourcesSearchQuery) {
            this.#lastResourcesSearchQuery = search ?? '';
            this.resourcesQuery.unload();
        }

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspaceId),
            () => {
                const workspaceId =
                    sharedWorkspacesController.currentWorkspaceId;

                if (!workspaceId) {
                    return;
                }

                this.resourcesQuery.load({
                    path: {
                        testId,
                        workspaceId,
                    },
                    query: {
                        ...(search && { q: search }),
                        page: 1,
                        limit: 20,
                    },
                });
            },
        );
    };

    loadNextResourcesPage = (): void => {
        this.resourcesQuery.nextPage();
    };

    onFetchResources = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            this.loadNextResourcesPage();

            return;
        }

        this.loadResources(search);
    };

    #lastConnectionsSearchQuery = '';

    loadConnections = (search?: string): void => {
        const testId = Number(this.testId);

        if (isNaN(testId)) {
            throw new TypeError('testId must be numbers');
        }

        if (search !== this.#lastConnectionsSearchQuery) {
            this.#lastConnectionsSearchQuery = search ?? '';
            this.connectionsQuery.unload();
        }

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspaceId),
            () => {
                const workspaceId =
                    sharedWorkspacesController.currentWorkspaceId;

                if (!workspaceId) {
                    return;
                }

                this.connectionsQuery.load({
                    path: {
                        testId,
                        workspaceId,
                    },
                    query: {
                        ...(search && { q: search }),
                        page: 1,
                        limit: 20,
                    },
                });
            },
        );
    };

    loadNextConnectionsPage = (): void => {
        this.connectionsQuery.nextPage();
    };

    onFetchConnections = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            this.loadNextConnectionsPage();

            return;
        }

        this.loadConnections(search);
    };

    #lastDesignatorsSearchQuery = '';

    loadDesignators = (search?: string): void => {
        const testId = Number(this.testId);

        if (isNaN(testId)) {
            throw new TypeError('testId must be numbers');
        }

        if (search !== this.#lastDesignatorsSearchQuery) {
            this.#lastDesignatorsSearchQuery = search ?? '';
            this.designatorsQuery.unload();
        }

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspaceId),
            () => {
                const workspaceId =
                    sharedWorkspacesController.currentWorkspaceId;

                if (!workspaceId) {
                    return;
                }

                this.designatorsQuery.load({
                    path: {
                        testId,
                        workspaceId,
                    },
                    query: {
                        ...(search && { q: search }),
                        page: 1,
                        limit: 20,
                    },
                });
            },
        );
    };

    loadNextDesignatorsPage = (): void => {
        this.designatorsQuery.nextPage();
    };

    onFetchDesignators = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            this.loadNextDesignatorsPage();

            return;
        }

        this.loadDesignators(search);
    };

    load = (params?: FetchDataResponseParams): void => {
        const testId = Number(this.testId);

        if (isNaN(testId)) {
            throw new TypeError('testId must be numbers');
        }

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspaceId),
            () => {
                const workspaceId =
                    sharedWorkspacesController.currentWorkspaceId;

                if (!workspaceId) {
                    return;
                }

                const { pagination, globalFilter, sorting } = params ?? {
                    pagination: { page: 1, pageSize: 20 },
                    globalFilter: { search: '', filters: {} },
                    sorting: [],
                };
                const { page, pageSize } = pagination;
                const { search, filters } = globalFilter;

                type Query = Required<
                    Parameters<
                        typeof monitorExclusionsControllerListMonitorInstanceExclusionsOptions
                    >
                >[0]['query'];

                const processedFilters =
                    processMonitoringExclusionsFilters(filters);

                const query: Query = {
                    page,
                    limit: pageSize,
                    ...(!isEmpty(search) && { q: search }),
                    ...(!isEmpty(processedFilters) && processedFilters),
                };

                if (!isEmpty(sorting)) {
                    query.sort = sorting[0].id as
                        | 'TARGET_NAME'
                        | 'EXCLUSION_REASON'
                        | 'START_DATE'
                        | 'CREATED_BY'
                        | 'CONNECTION_CLIENT_ALIAS'
                        | undefined;

                    query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
                }

                this.exclusionsQuery.load({
                    path: {
                        testId,
                        workspaceId,
                    },
                    query,
                });
            },
        );
    };

    initializeFilterData = (): void => {
        this.loadConnections();
        this.loadResources();
        this.loadDesignators();
    };
}

export const sharedMonitoringDetailsExclusionsController =
    new MonitoringDetailsExclusionsController();
