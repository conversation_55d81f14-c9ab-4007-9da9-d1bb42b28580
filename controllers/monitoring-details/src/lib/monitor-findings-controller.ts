import { monitorsV2ControllerGetFindingsOptions } from '@globals/api-sdk/queries';
import type { MonitorV2FindingsResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export class MonitorFindingsController {
    constructor() {
        makeAutoObservable(this);
    }

    findingsResponse = new ObservedQuery(
        monitorsV2ControllerGetFindingsOptions,
    );

    get ticketsFindings(): MonitorV2FindingsResponseDto | null {
        return this.findingsResponse.data ?? null;
    }

    get failingResources(): number {
        return this.ticketsFindings?.failingResourcesCount ?? 0;
    }

    get isLoading(): boolean {
        return this.findingsResponse.isLoading;
    }

    get monitorHasFindings(): boolean {
        return this.failingResources > 0;
    }

    loadFindingsResponse = (testId: number): void => {
        when(
            () => sharedWorkspacesController.isLoaded,
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    throw new Error('Workspace not found');
                }

                this.findingsResponse.load({
                    path: { testId, workspaceId: currentWorkspace.id },
                });
            },
        );
    };
}

export const sharedMonitorFindingsController = new MonitorFindingsController();
