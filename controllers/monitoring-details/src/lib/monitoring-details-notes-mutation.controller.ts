import type { NoteCreateDto } from '@components/utilities';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { snackbarController } from '@controllers/snackbar';
import {
    monitorsControllerCreateNoteMutation,
    monitorsControllerDeleteNoteMutation,
    monitorsControllerUpdateNoteMutation,
} from '@globals/api-sdk/queries';
import type { NoteRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    action,
    makeAutoObservable,
    ObservedMutation,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedMonitoringDetailsNotesController } from './monitoring-details-notes.controller';

export class MonitoringDetailsNotesMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    createNoteMutation = new ObservedMutation(
        monitorsControllerCreateNoteMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'monitoring-note-create-success',
                    props: {
                        title: t`Note created`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'monitoring-note-create-error',
                    props: {
                        title: t`Unable to create note`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    updateNoteMutation = new ObservedMutation(
        monitorsControllerUpdateNoteMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'monitoring-note-update-success',
                    props: {
                        title: t`Note updated`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'monitoring-note-update-error',
                    props: {
                        title: t`Unable to update note`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    deleteNoteMutation = new ObservedMutation(
        monitorsControllerDeleteNoteMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'monitoring-note-delete-success',
                    props: {
                        title: t`Note deleted`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'monitoring-note-delete-error',
                    props: {
                        title: t`Unable to delete note`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get isCreating(): boolean {
        return this.createNoteMutation.isPending;
    }

    get isUpdating(): boolean {
        return this.updateNoteMutation.isPending;
    }

    get isDeleting(): boolean {
        return this.deleteNoteMutation.isPending;
    }

    createNote = (noteData: NoteCreateDto, onSuccess?: () => void): void => {
        const { currentWorkspaceId } = sharedWorkspacesController;
        const { testDetails } = sharedMonitoringTestDetailsController;

        if (!currentWorkspaceId) {
            logger.error({
                message: 'Failed to create note. Workspace not found',
            });

            return;
        }

        if (!testDetails) {
            logger.error({
                message: 'Failed to create note. Test details not found',
            });

            return;
        }

        this.createNoteMutation.mutate({
            path: {
                xProductId: currentWorkspaceId,
                testId: testDetails.testId,
            },
            body: {
                comment: noteData.comment ?? '',
            },
        });

        when(
            () => !this.isCreating,
            () => {
                if (this.createNoteMutation.hasError) {
                    return;
                }

                sharedMonitoringDetailsNotesController.invalidateNotes();
                onSuccess?.();
            },
        );
    };

    updateNote = (noteId: string, note: NoteRequestDto): void => {
        this.updateNoteMutation.mutate({
            path: { noteId },
            body: { comment: note.comment },
        });

        when(
            () => !this.isUpdating,
            () => {
                sharedMonitoringDetailsNotesController.invalidateNotes();
            },
        );
    };

    deleteNote = (noteId: string): void => {
        openConfirmationModal({
            title: t`Delete note?`,
            body: t`This action is permanent. The note and all related data will be removed.`,
            confirmText: t`Delete note`,
            cancelText: t`Cancel`,
            type: 'danger',
            isLoading: () => this.isDeleting,
            onConfirm: action(() => {
                this.deleteNoteMutation.mutate({
                    path: {
                        noteId,
                    },
                });

                when(
                    () => !this.isDeleting,
                    () => {
                        sharedMonitoringDetailsNotesController.invalidateNotes();
                        closeConfirmationModal();
                    },
                );
            }),
            onCancel: closeConfirmationModal,
        });
    };
}

export const sharedMonitoringDetailsNotesMutationController =
    new MonitoringDetailsNotesMutationController();
