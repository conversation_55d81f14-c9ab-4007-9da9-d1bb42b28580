import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import type { Action } from '@cosmos/components/action-stack';
import type { ColorScheme } from '@cosmos/components/text';
import { makeAutoObservable } from '@globals/mobx';
import {
    createHorizontalMenuItems,
    createPageHeaderItems,
} from '../helpers/monitoring-details-actions.helpers';

interface ActionConfiguration {
    headerActions: Action[];
    horizontalMenuOptions: {
        id: string;
        label: string;
        colorScheme?: ColorScheme;
        onSelect: () => void;
    }[];
}

interface ActionParams {
    isTestDisabledOrUnused: boolean;
    horizontalMenuItems: ReturnType<typeof createHorizontalMenuItems>;
    pageHeaderItems: ReturnType<typeof createPageHeaderItems>;
    hasBeenTested: boolean;
}

interface CustomTestActionParams extends ActionParams {
    draft: boolean;
}

export class MonitoringDetailsActionsController {
    constructor() {
        makeAutoObservable(this);
    }

    getActionConfiguration(): ActionConfiguration {
        const { testDetails, source } = sharedMonitoringTestDetailsController;

        if (!testDetails) {
            return { headerActions: [], horizontalMenuOptions: [] };
        }

        const horizontalMenuItems = createHorizontalMenuItems();
        const pageHeaderItems = createPageHeaderItems();
        const { checkStatus, draft, lastCheck } = testDetails;
        const isTestDisabledOrUnused =
            checkStatus === 'UNUSED' || checkStatus === 'DISABLED';

        const hasBeenTested = Boolean(
            lastCheck && !isNaN(new Date(lastCheck).getTime()),
        );

        if (source === 'CUSTOM') {
            return this.getCustomTestActions({
                draft,
                isTestDisabledOrUnused,
                horizontalMenuItems,
                pageHeaderItems,
                hasBeenTested,
            });
        }

        return this.getDefaultTestActions({
            isTestDisabledOrUnused,
            horizontalMenuItems,
            pageHeaderItems,
            hasBeenTested,
        });
    }

    private getCustomTestActions({
        draft,
        isTestDisabledOrUnused,
        horizontalMenuItems,
        pageHeaderItems,
        hasBeenTested,
    }: CustomTestActionParams): ActionConfiguration {
        if (draft) {
            if (isTestDisabledOrUnused) {
                return {
                    headerActions: [pageHeaderItems.publishDraft],
                    horizontalMenuOptions: [horizontalMenuItems.deleteDraft],
                };
            }

            return {
                headerActions: [pageHeaderItems.testNow],
                horizontalMenuOptions: [
                    horizontalMenuItems.publishDraft,
                    horizontalMenuItems.deleteDraft,
                ],
            };
        }

        // Published custom test
        if (isTestDisabledOrUnused) {
            if (hasBeenTested) {
                return {
                    headerActions: [pageHeaderItems.goToEvidence],
                    horizontalMenuOptions: [horizontalMenuItems.deleteTest],
                };
            }

            return {
                headerActions: [pageHeaderItems.deleteTest],
                horizontalMenuOptions: [],
            };
        }

        // Active published test
        const horizontalMenuOptions = [horizontalMenuItems.deleteTest];

        if (hasBeenTested) {
            horizontalMenuOptions.unshift(horizontalMenuItems.goToEvidence);
        }

        return {
            headerActions: [pageHeaderItems.testNow],
            horizontalMenuOptions,
        };
    }

    private getDefaultTestActions({
        isTestDisabledOrUnused,
        horizontalMenuItems,
        pageHeaderItems,
        hasBeenTested,
    }: ActionParams): ActionConfiguration {
        if (isTestDisabledOrUnused) {
            return {
                headerActions: hasBeenTested
                    ? [pageHeaderItems.goToEvidence]
                    : [],
                horizontalMenuOptions: [],
            };
        }

        return {
            headerActions: [pageHeaderItems.testNow],
            horizontalMenuOptions: hasBeenTested
                ? [horizontalMenuItems.goToEvidence]
                : [],
        };
    }
}

export const sharedMonitoringDetailsActionsController =
    new MonitoringDetailsActionsController();
