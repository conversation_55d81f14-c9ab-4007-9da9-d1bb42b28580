import { PublishTestModal } from '@components/monitoring-details-overview';
import { modalController } from '@controllers/modal';
import { sharedMonitoringController } from '@controllers/monitoring';
import {
    sharedMonitoringDeleteTestMutationController,
    sharedMonitoringPublishTestMutationController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import type { Action } from '@cosmos/components/action-stack';
import type { ColorScheme } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { MONITORING_DETAILS_ACTION_IDS } from '../constants/monitoring-details-actions.constants';
import { PUBLISH_TEST_MODAL_ID } from '../constants/monitoring-details-modals.constants';

let isTestNowLoading = false;

const handleTestNow = action(() => {
    const { testDetails } = sharedMonitoringTestDetailsController;

    if (testDetails?.testId) {
        sharedMonitoringController.handleTestNow(testDetails.testId);
        isTestNowLoading = true;
    } else {
        snackbarController.addSnackbar({
            id: 'test-now-error',
            props: {
                title: t`Failed to start test`,
                description: t`An error occurred while starting the test. Please try again.`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    }
});

const handleGoToEvidence = action(() => {
    const { testDetails } = sharedMonitoringTestDetailsController;

    if (!testDetails?.libraryDocument?.id) {
        snackbarController.addSnackbar({
            id: 'go-to-evidence-error',
            props: {
                title: t`Failed to navigate to evidence`,
                description: t`An error occurred while navigating to the evidence. Please try again.`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });

        return;
    }

    sharedProgrammaticNavigationController.navigateTo(
        `${routeController.userPartOfUrl}/compliance/evidence/${testDetails.libraryDocument.id}/overview`,
    );
});

const createDeleteConfirmationModal = (type: 'draft' | 'published') => {
    const isDraft = type === 'draft';

    openConfirmationModal({
        title: isDraft ? t`Delete draft?` : t`Delete test?`,
        body: isDraft
            ? t`You can not recover this draft once it is deleted.`
            : t`You can not recover this test once it is deleted. If this published test is linked to a draft version, the linked draft will also be deleted. This action can not be undone.`,
        confirmText: isDraft ? t`Delete draft` : t`Delete test`,
        cancelText: t`Cancel`,
        type: 'danger',
        onConfirm: () => {
            sharedMonitoringDeleteTestMutationController.deleteTest();
        },
        onCancel: closeConfirmationModal,
        isLoading: () =>
            sharedMonitoringDeleteTestMutationController.isDeleting,
    });
};

const onSelectDeletePublished = () => {
    createDeleteConfirmationModal('published');
};

const onSelectDeleteDraft = () => {
    createDeleteConfirmationModal('draft');
};

const handlePublishDraft = () => {
    const { testDetails } = sharedMonitoringTestDetailsController;

    if (testDetails?.parentTestId) {
        // Draft with published version - show PublishTestModal
        modalController.openModal({
            id: PUBLISH_TEST_MODAL_ID,
            content: () => (
                <PublishTestModal
                    data-id={PUBLISH_TEST_MODAL_ID}
                    isLoading={
                        sharedMonitoringPublishTestMutationController.isPublishing
                    }
                    onConfirm={() => {
                        sharedMonitoringPublishTestMutationController.publishTest();
                        modalController.closeModal(PUBLISH_TEST_MODAL_ID);
                    }}
                    onCancel={() => {
                        modalController.closeModal(PUBLISH_TEST_MODAL_ID);
                    }}
                />
            ),
            size: 'md',
            centered: true,
            disableClickOutsideToClose: true,
        });
    } else {
        sharedMonitoringPublishTestMutationController.publishTest();
    }
};

/**
 * For horizontal menu items for the dropdown menu (three-dot menu).
 * These items appear in a dropdown when the horizontal menu button is clicked.
 */
export const createHorizontalMenuItems = (): Record<
    string,
    {
        id: string;
        label: string;
        colorScheme?: ColorScheme;
        onSelect: () => void;
    }
> => ({
    publishDraft: {
        id: MONITORING_DETAILS_ACTION_IDS.PUBLISH_DRAFT_OPTION,
        label: t`Publish draft`,
        onSelect: handlePublishDraft,
    },
    deleteDraft: {
        id: MONITORING_DETAILS_ACTION_IDS.DELETE_DRAFT_OPTION,
        label: t`Delete draft`,
        colorScheme: 'critical' as ColorScheme,
        onSelect: onSelectDeleteDraft,
    },
    deleteTest: {
        id: MONITORING_DETAILS_ACTION_IDS.DELETE_TEST_OPTION,
        label: t`Delete test`,
        colorScheme: 'critical' as ColorScheme,
        onSelect: onSelectDeletePublished,
    },
    goToEvidence: {
        id: MONITORING_DETAILS_ACTION_IDS.GO_TO_EVIDENCE_OPTION,
        label: t`Go to evidence`,
        onSelect: handleGoToEvidence,
    },
});

/**
 * Creates page header action items that appear as individual buttons in the header.
 */
export const createPageHeaderItems = (): Record<string, Action> => ({
    publishDraft: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.PUBLISH_DRAFT_BUTTON,
        typeProps: {
            label: t`Publish draft`,
            level: 'tertiary',
            onClick: handlePublishDraft,
        },
    },
    testNow: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.TEST_NOW_BUTTON,
        typeProps: {
            label: t`Test now`,
            level: 'secondary',
            onClick: handleTestNow,
            isLoading:
                sharedMonitoringTestDetailsController.testDetails
                    ?.checkStatus === 'TESTING' ||
                sharedMonitoringController.isSingleTesting ||
                isTestNowLoading,
        },
    },
    deleteTest: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.DELETE_TEST_BUTTON,
        typeProps: {
            label: t`Delete test`,
            level: 'tertiary',
            colorScheme: 'danger',
            onClick: () => {
                // TODO: Implement delete test logic
            },
        },
    },
    goToEvidence: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.GO_TO_EVIDENCE_BUTTON,
        typeProps: {
            label: t`Go to evidence`,
            level: 'tertiary',
            onClick: handleGoToEvidence,
        },
    },
});
