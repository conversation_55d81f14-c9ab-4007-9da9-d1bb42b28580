import { isEmpty, isObject, isString } from 'lodash-es';
import { getDateRange } from './date-filter.helper';

const isFilterObject = (
    obj: unknown,
): obj is { filterType: string; value?: unknown } => {
    return isObject(obj) && 'filterType' in obj;
};

const hasValueProperty = (obj: unknown): obj is { value: unknown } => {
    return isObject(obj) && 'value' in obj;
};

/**
 * Helper functions to make the main function more readable.
 */
const extractValueFromFilter = (filterData: unknown, key: string): unknown => {
    if (isFilterObject(filterData) && hasValueProperty(filterData)) {
        return filterData.value;
    }

    // Special handling for connections filter
    if (
        hasValueProperty(filterData) &&
        !isFilterObject(filterData) &&
        key === 'connections'
    ) {
        let { value } = filterData;

        if (Array.isArray(value)) {
            value = value
                .map((item) => {
                    if (isObject(item) && 'value' in item) {
                        return String(item.value);
                    }

                    return String(item);
                })
                .filter(
                    (item): item is string =>
                        isString(item) && item.trim() !== '',
                );
        } else if (isObject(value) && 'value' in value) {
            value = value.value;
        }

        return value;
    }

    return filterData;
};

const processDateFilter = (
    actualValue: unknown,
): Record<string, unknown> | null => {
    let dateValue: string;

    if (isString(actualValue)) {
        dateValue = actualValue;
    } else if (isObject(actualValue) && 'value' in actualValue) {
        dateValue = actualValue.value as string;
    } else {
        return null;
    }

    if (dateValue === 'ALL_DATES') {
        return null;
    }

    return getDateRange(dateValue);
};

const processConnectionItem = (item: unknown): unknown => {
    if (isString(item)) {
        return JSON.parse(item);
    }

    if (isObject(item) && 'value' in item) {
        const extractedValue = item.value;

        if (isString(extractedValue)) {
            try {
                return JSON.parse(extractedValue);
            } catch {
                return extractedValue;
            }
        }

        return extractedValue;
    }

    return item;
};

const processConnectionsArray = (actualValue: unknown[]): string => {
    const connectionObjects = actualValue.map(processConnectionItem);

    return JSON.stringify(connectionObjects);
};

const processTargetNameArray = (actualValue: unknown[]): unknown => {
    const firstItem = actualValue[0];

    return isObject(firstItem) && 'value' in firstItem
        ? firstItem.value
        : firstItem;
};

export const processMonitoringExclusionsFilters = (
    filters: Record<string, unknown>,
): Record<string, unknown> => {
    const apiParams: Record<string, unknown> = {};

    Object.entries(filters).forEach(([key, filterData]) => {
        // Skip invalid filter objects
        if (isFilterObject(filterData) && !hasValueProperty(filterData)) {
            return;
        }

        const actualValue = extractValueFromFilter(filterData, key);

        if (isEmpty(actualValue)) {
            return;
        }

        // Handle date filter
        if (key === 'dateExcluded') {
            const dateRange = processDateFilter(actualValue);

            if (dateRange) {
                Object.assign(apiParams, dateRange);
            }

            return;
        }

        // Handle string values
        if (isString(actualValue) && actualValue.trim() !== '') {
            if (key === 'createdBy') {
                apiParams['createdBy[]'] = [actualValue];
            } else {
                apiParams[key] = actualValue;
            }

            return;
        }

        // Handle array values
        if (Array.isArray(actualValue)) {
            switch (key) {
                case 'targetName': {
                    apiParams.targetName = processTargetNameArray(actualValue);
                    break;
                }
                case 'connections': {
                    apiParams.connections =
                        processConnectionsArray(actualValue);
                    break;
                }
                case 'createdBy': {
                    apiParams['createdBy[]'] = actualValue;
                    break;
                }
                default: {
                    apiParams[key] = actualValue;
                }
            }

            return;
        }

        // Handle object values (mainly for createdBy single selection)
        if (
            key === 'createdBy' &&
            isObject(actualValue) &&
            'value' in actualValue
        ) {
            apiParams['createdBy[]'] = [actualValue.value];
        } else {
            apiParams[key] = actualValue;
        }
    });

    return apiParams;
};
