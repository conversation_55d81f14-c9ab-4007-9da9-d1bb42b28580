import { addDaysToDate, formatDate } from '@helpers/date-time';

/**
 * Converts date filter options to API date range parameters.
 */
export const getDateRange = (
    filterValue: string,
): { startDateFrom?: string } => {
    const now = new Date();

    switch (filterValue) {
        case 'LAST_SEVEN_DAYS': {
            const startDate = addDaysToDate(now, -7);

            return { startDateFrom: formatDate('timestamp', startDate) };
        }
        case 'LAST_THIRTY_DAYS': {
            const startDate = addDaysToDate(now, -30);

            return { startDateFrom: formatDate('timestamp', startDate) };
        }
        case 'LAST_SIX_MONTHS': {
            const startDate = new Date(now);

            startDate.setMonth(startDate.getMonth() - 6);

            return { startDateFrom: formatDate('timestamp', startDate) };
        }
        case 'LAST_TWELVE_MONTHS': {
            const startDate = new Date(now);

            startDate.setMonth(startDate.getMonth() - 12);

            return { startDateFrom: formatDate('timestamp', startDate) };
        }
        case 'ALL_DATES':
        default: {
            return {};
        }
    }
};
