import { isEmpty } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import {
    aiExecutionGroupFeedbackGroupsControllerSaveAiExecutionGroupFeedbackMutation,
    aiExecutionGroupFeedbackGroupsControllerUpdateAiExecutionGroupFeedbackMutation,
} from '@globals/api-sdk/queries';
import type {
    AiExecutionFeedbackCreateRequestDto,
    AiExecutionFeedbackUpdateRequestDto,
    AiExecutionGroupResponseDto,
    SummaryFeedbackResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { closeAiFeedbackModal } from '@helpers/ai-feedback-modal';
import type { FormValues } from '@ui/forms';

class AiFeedbackController {
    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Feedback functionality.
     */
    saveFeedbackMutation = new ObservedMutation(
        aiExecutionGroupFeedbackGroupsControllerSaveAiExecutionGroupFeedbackMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'ai-feedback-success',
                    props: {
                        title: t`Feedback submitted successfully`,
                        description: t`Thank you for your feedback!`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                closeAiFeedbackModal();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'ai-feedback-general-error',
                    props: {
                        title: t`Something went wrong`,
                        description: t`An unexpected error occurred. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    updateFeedbackMutation = new ObservedMutation(
        aiExecutionGroupFeedbackGroupsControllerUpdateAiExecutionGroupFeedbackMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'ai-feedback-success',
                    props: {
                        title: t`Feedback submitted successfully`,
                        description: t`Thank you for your feedback!`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                closeAiFeedbackModal();
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'ai-feedback-general-error',
                    props: {
                        title: t`Something went wrong`,
                        description: t`An unexpected error occurred. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get testId(): number {
        return (
            sharedWorkspaceMonitorsController.workspaceMonitorTestOverview
                ?.testId ?? -1
        );
    }

    /**
     * Feedback actions.
     */

    saveFeedback = ({
        aiExecutionGroupId,
        feedback,
    }: {
        aiExecutionGroupId: number;
        feedback: AiExecutionFeedbackCreateRequestDto;
    }): void => {
        this.saveFeedbackMutation.mutate({
            path: { aiExecutionGroupId },
            body: feedback,
        });
    };

    updateFeedback = ({
        aiExecutionGroupId,
        feedbackId,
        feedback,
    }: {
        aiExecutionGroupId: number;
        feedbackId: number;
        feedback: AiExecutionFeedbackUpdateRequestDto;
    }): void => {
        this.updateFeedbackMutation.mutate({
            path: { aiExecutionGroupId, feedbackId },
            body: feedback,
        });
    };

    submitFeedback = ({
        feedbackStatus,
        feedbackText,
        feedbackReason,
        executionGroups,
    }: {
        feedbackStatus: SummaryFeedbackResponseDto['feedbackStatusType'];
        feedbackText: string;
        feedbackReason?: SummaryFeedbackResponseDto['feedbackTypes'];
        executionGroups: AiExecutionGroupResponseDto[];
    }): void => {
        const feedbackData = {
            status: feedbackStatus,
            opinion: feedbackText,
            reasons: feedbackReason ?? undefined,
        };

        if (isEmpty(executionGroups)) {
            return;
        }

        const firstGroup = executionGroups[0];

        if (isEmpty(firstGroup.feedbacks)) {
            this.saveFeedback({
                aiExecutionGroupId: firstGroup.id,
                feedback: feedbackData,
            });
        } else {
            this.updateFeedback({
                aiExecutionGroupId: firstGroup.id,
                // There's always only one feedback
                feedbackId: firstGroup.feedbacks[0].id,
                feedback: feedbackData,
            });
        }
    };

    handleSubmitAiFeedback = ({
        formValues,
        executionGroups,
    }: {
        formValues: FormValues;
        executionGroups: AiExecutionGroupResponseDto[];
    }): void => {
        this.submitFeedback({
            feedbackStatus:
                formValues.feedbackStatus as SummaryFeedbackResponseDto['feedbackStatusType'],
            feedbackText: (formValues.feedbackText as string) || '',
            feedbackReason:
                formValues.feedbackStatus === 'NOT_USEFUL' &&
                formValues.feedbackReason
                    ? (formValues.feedbackReason as SummaryFeedbackResponseDto['feedbackTypes'])
                    : [],
            executionGroups,
        });
    };
}

export const sharedAiFeedbackController = new AiFeedbackController();
