import { isError, isFunction, isObject, uniqueId } from 'lodash-es';
import {
    ImpactLikelihoodFormField,
    RiskLevelDefinitionsFormField,
    ThresholdsFormField,
} from '@components/risk-settings-scoring';
import { createNumericOptions } from '@controllers/risk';
import { sharedRiskSettingsController } from '@controllers/risk-settings';
import { snackbarController } from '@controllers/snackbar';
import { riskManagementControllerUpdateRiskSettingsMutation } from '@globals/api-sdk/queries';
import type {
    RiskSettingsRequestDto,
    RiskSettingsResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    runInAction,
    when,
} from '@globals/mobx';
import {
    calculateThresholdRanges,
    createRiskSettingsFormSchema,
    generateDefaultThresholdPositions,
    getDefaultThresholdData,
    transformRiskSettingsFormData,
} from '@helpers/risk-settings-forms';
import type { FormSchema } from '@ui/forms';

const MAX_THRESHOLDS_ALLOWED = 5;
/**
 *TODO: manage threshold integration  https://drata.atlassian.net/browse/ENG-73827.
 */
const DEFAULT_THRESHOLD_COLOR = '#B90410';
const DEFAULT_THRESHOLD_MIN_INCREMENT = 1;
const DEFAULT_THRESHOLD_MAX_INCREMENT = 2;
const DEFAULT_NEW_THRESHOLD_MAX = 5;

const createNumericOptionsWrapper = (
    maxValue: number | undefined,
): {
    id: string;
    label: string;
    value?: string;
}[] => {
    return createNumericOptions(maxValue).map(({ id, label, value }) => ({
        id,
        label,
        value,
    }));
};

class RiskSettingsScoringController {
    _deletedThresholdIds: Set<number> = new Set();

    _formSchemaVersion = 0;

    constructor() {
        makeAutoObservable(this);
    }

    private isValidFormData(data: unknown): data is Record<string, unknown> {
        return isObject(data);
    }

    updateRiskSettingsMutation = new ObservedMutation(
        riskManagementControllerUpdateRiskSettingsMutation,
        {
            onSuccess: () => {
                this._pendingThresholdChanges = {};
                this._deletedThresholdIds.clear();
                sharedRiskSettingsController.riskSettingsQuery.invalidate();
            },
        },
    );

    _pendingThresholdChanges: { boundaries?: number[] } = {};

    get isSubmitting(): boolean {
        return this.updateRiskSettingsMutation.isPending;
    }

    get hasSubmissionError(): boolean {
        return this.updateRiskSettingsMutation.hasError;
    }

    get submissionError(): Error | null {
        return this.updateRiskSettingsMutation.error;
    }

    get filteredThresholds() {
        const { riskSettings } = sharedRiskSettingsController;

        if (!riskSettings?.thresholds) {
            return [];
        }

        return riskSettings.thresholds.filter(
            (threshold) => !this._deletedThresholdIds.has(threshold.id),
        );
    }

    get currentRiskSettings(): RiskSettingsResponseDto | null {
        const { riskSettings } = sharedRiskSettingsController;

        if (!riskSettings) {
            return null;
        }

        return riskSettings;
    }

    handleThresholdChange(
        index: number,
        field: string,
        newValue: string,
    ): void {
        const currentSettings = sharedRiskSettingsController.riskSettings;

        if (
            !this.validateThresholdChangeRequest(
                currentSettings,
                index,
                field,
                newValue,
            )
        ) {
            return;
        }

        switch (field) {
            case 'delete': {
                this.handleThresholdDeletion(index);
                break;
            }
            case 'boundaries': {
                this.handleBoundariesChange(index, newValue);
                break;
            }
            case 'name':
            case 'description': {
                this.handleThresholdFieldUpdate(index, field, newValue);
                break;
            }
            default: {
                logger.info({
                    message: 'Threshold field change requested',
                    additionalInfo: { index, field, newValue },
                });
            }
        }
    }

    private validateThresholdChangeRequest(
        currentSettings: RiskSettingsResponseDto | null,
        index: number,
        field: string,
        newValue: string,
    ): boolean {
        if (!currentSettings?.thresholds) {
            logger.error({
                message:
                    'Invalid threshold change request - no settings or thresholds',
                additionalInfo: {
                    index,
                    field,
                    newValue,
                    hasSettings: Boolean(currentSettings),
                    hasThresholds: Boolean(currentSettings?.thresholds),
                },
            });

            return false;
        }

        if (index >= currentSettings.thresholds.length) {
            logger.error({
                message:
                    'Invalid threshold change request - index out of bounds',
                additionalInfo: {
                    index,
                    field,
                    newValue,
                    thresholdCount: currentSettings.thresholds.length,
                },
            });

            return false;
        }

        return true;
    }

    private handleThresholdDeletion(index: number): void {
        const { filteredThresholds } = this;

        if (index >= filteredThresholds.length) {
            logger.error({
                message:
                    'Invalid threshold deletion - index out of bounds in filtered array',
                additionalInfo: {
                    index,
                    filteredLength: filteredThresholds.length,
                },
            });

            return;
        }

        const thresholdId = filteredThresholds[index].id;

        logger.info({
            message: 'Threshold deletion requested',
            additionalInfo: { index, thresholdId },
        });

        runInAction(() => {
            const { _deletedThresholdIds, _formSchemaVersion } = this;

            _deletedThresholdIds.add(thresholdId);
            this._formSchemaVersion = _formSchemaVersion + 1;
        });
    }

    private handleBoundariesChange(index: number, newValue: string): void {
        const boundaries = newValue
            .split(',')
            .map((val) => parseFloat(val.trim()))
            .filter((val) => !isNaN(val));

        this._pendingThresholdChanges.boundaries = boundaries;
        this.updateThresholdsFromBoundaries(boundaries);

        logger.info({
            message: 'Threshold boundaries change applied',
            additionalInfo: { index, boundaries },
        });
    }

    private handleThresholdFieldUpdate(
        index: number,
        field: 'name' | 'description',
        newValue: string,
    ): void {
        const { filteredThresholds } = this;

        if (index >= filteredThresholds.length) {
            logger.error({
                message:
                    'Invalid threshold update - index out of bounds in filtered array',
                additionalInfo: {
                    index,
                    field,
                    filteredLength: filteredThresholds.length,
                },
            });

            return;
        }

        const targetThreshold = filteredThresholds[index];

        runInAction(() => {
            if (field === 'name') {
                targetThreshold.name = newValue;
            } else {
                targetThreshold.description = newValue;
            }
        });

        logger.info({
            message: 'Threshold field updated',
            additionalInfo: {
                index,
                field,
                newValue,
                thresholdId: targetThreshold.id,
            },
        });
    }

    private updateThresholdsFromBoundaries(boundaries: number[]): void {
        const currentSettings = sharedRiskSettingsController.riskSettings;

        if (!currentSettings) {
            logger.error({
                message:
                    'Cannot update thresholds - no current settings available',
            });

            return;
        }

        const maxLimit = currentSettings.impact * currentSettings.likelihood;
        const currentThresholds = this.filteredThresholds;

        const ranges = calculateThresholdRanges(boundaries, maxLimit);
        const updatedThresholds = ranges.map((range, index) => {
            if (index < currentThresholds.length) {
                const existingThreshold = currentThresholds[index];

                return {
                    ...existingThreshold,
                    minThreshold: range.minThreshold,
                    maxThreshold: range.maxThreshold,
                };
            }

            const defaultData = getDefaultThresholdData(index);

            return {
                id: -(index + 1),
                name: defaultData.name,
                description: defaultData.description,
                color: defaultData.color,
                minThreshold: range.minThreshold,
                maxThreshold: range.maxThreshold,
            };
        });

        runInAction(() => {
            currentSettings.thresholds = updatedThresholds;
            this._formSchemaVersion = this._formSchemaVersion + 1;
        });

        logger.info({
            message: 'Thresholds updated from boundaries',
            additionalInfo: {
                boundaries,
                updatedThresholdCount: updatedThresholds.length,
            },
        });
    }

    private calculateDefaultThresholds(
        sliderValues: number[],
        maxThresholdValue: number,
        currentThresholds: RiskSettingsResponseDto['thresholds'],
    ): RiskSettingsResponseDto['thresholds'] {
        const ranges = calculateThresholdRanges(
            sliderValues,
            maxThresholdValue,
        );

        const newThresholds: RiskSettingsResponseDto['thresholds'] = [];

        for (const [i, range] of ranges.entries()) {
            const defaultData = getDefaultThresholdData(i);
            const thresholdId =
                i < currentThresholds.length
                    ? currentThresholds[i].id
                    : -(i + 1);

            newThresholds.push({
                id: thresholdId,
                name: defaultData.name,
                description: defaultData.description,
                color: defaultData.color,
                minThreshold: range.minThreshold,
                maxThreshold: range.maxThreshold,
            });
        }

        return newThresholds;
    }

    resetThresholdsToDefaults(): void {
        const currentSettings = sharedRiskSettingsController.riskSettings;

        if (!currentSettings) {
            logger.error({
                message:
                    'Cannot reset thresholds - no current settings available',
            });

            return;
        }

        const currentThresholds = this.filteredThresholds;
        const maxLimit = currentSettings.impact * currentSettings.likelihood;
        const intervalsSize = currentThresholds.length;

        const newResetHandlesPosition = generateDefaultThresholdPositions(
            intervalsSize,
            maxLimit,
        );

        const resetValues = newResetHandlesPosition.map((value) =>
            value.toFixed(2),
        );
        const newThresholds = this.calculateDefaultThresholds(
            newResetHandlesPosition,
            maxLimit,
            currentThresholds,
        );

        runInAction(() => {
            currentSettings.thresholds = newThresholds;
            this._deletedThresholdIds.clear();
            this._formSchemaVersion = this._formSchemaVersion + 1;
        });

        logger.info({
            message: 'Thresholds reset to defaults using legacy algorithm',
            additionalInfo: {
                previousThresholdCount: currentThresholds.length,
                newThresholdCount: newThresholds.length,
                maxLimit,
                intervalsSize,
                resetHandlePositions: newResetHandlesPosition,
                resetValues,
            },
        });
    }

    handleResetToDefaults = (): void => {
        this.resetThresholdsToDefaults();

        snackbarController.addSnackbar({
            id: 'risk-thresholds-reset-success',
            props: {
                title: t`Thresholds reset to defaults`,
                description: t`Risk thresholds have been reset to default values and colors.`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });
    };

    isThresholdDeleted = (thresholdId: number): boolean => {
        return this._deletedThresholdIds.has(thresholdId);
    };

    addNewThreshold = (): void => {
        const { riskSettings } = sharedRiskSettingsController;

        if (!riskSettings?.thresholds) {
            logger.error({
                message: 'Cannot add threshold: risk settings not available',
            });

            return;
        }

        const currentThresholds = this.filteredThresholds;

        if (currentThresholds.length >= MAX_THRESHOLDS_ALLOWED) {
            logger.warn({
                message: `Cannot add threshold: maximum of ${MAX_THRESHOLDS_ALLOWED} thresholds allowed`,
                additionalInfo: {
                    currentCount: currentThresholds.length,
                },
            });

            return;
        }

        const newThresholdId =
            Math.min(
                ...riskSettings.thresholds.map((threshold) => threshold.id),
                ...this._deletedThresholdIds,
            ) - 1;
        const newThreshold = {
            id: newThresholdId,
            name: `Threshold ${currentThresholds.length + 1}`,
            description: '',
            color: DEFAULT_THRESHOLD_COLOR,
            minThreshold:
                currentThresholds.length > 0
                    ? Math.max(
                          ...currentThresholds.map(
                              (threshold) => threshold.maxThreshold,
                          ),
                      ) + DEFAULT_THRESHOLD_MIN_INCREMENT
                    : DEFAULT_THRESHOLD_MIN_INCREMENT,
            maxThreshold:
                currentThresholds.length > 0
                    ? Math.max(
                          ...currentThresholds.map(
                              (threshold) => threshold.maxThreshold,
                          ),
                      ) + DEFAULT_THRESHOLD_MAX_INCREMENT
                    : DEFAULT_NEW_THRESHOLD_MAX,
        };

        runInAction(() => {
            riskSettings.thresholds.push(newThreshold);
            this._formSchemaVersion = this._formSchemaVersion + 1;
        });

        logger.info({
            message: 'New threshold added',
            additionalInfo: {
                thresholdId: newThresholdId,
                name: newThreshold.name,
                totalThresholds: this.filteredThresholds.length,
            },
        });
    };

    handleFormSubmit = (values: unknown): void => {
        if (!this.validateFormSubmission(values)) {
            return;
        }

        const currentSettings = sharedRiskSettingsController.riskSettings;

        if (!currentSettings) {
            this.showFormSubmissionError(
                'risk-settings-not-available',
                t`Unable to save changes`,
                t`Risk settings are not available. Please refresh the page and try again.`,
            );

            return;
        }

        try {
            const requestData = this.transformFormData(values, currentSettings);

            this.updateRiskSettingsMutation.mutate({
                body: requestData,
            });

            when(
                () => !this.isSubmitting,
                () => {
                    if (this.hasSubmissionError) {
                        this.showFormSubmissionError(
                            `risk-settings-update-error-${uniqueId()}`,
                            t`Failed to update risk settings`,
                            this.submissionError?.message ||
                                t`Please try again.`,
                        );

                        return;
                    }

                    snackbarController.addSnackbar({
                        id: 'risk-settings-update-success',
                        props: {
                            title: t`Risk settings updated successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                },
            );
        } catch (error) {
            logger.error({
                message: 'Failed to process form data',
                additionalInfo: { error },
            });

            this.showFormSubmissionError(
                'risk-settings-processing-error',
                t`Failed to process form data`,
                isError(error)
                    ? error.message
                    : t`Please check your input and try again.`,
            );
        }
    };

    private validateFormSubmission(values: unknown): boolean {
        try {
            if (!this.isValidFormData(values)) {
                this.showFormSubmissionError(
                    'risk-settings-invalid-data',
                    t`Invalid form data`,
                    t`Please check your input and try again.`,
                );

                return false;
            }

            return true;
        } catch (error) {
            logger.error({
                message: 'Error validating form data',
                additionalInfo: { error },
            });

            this.showFormSubmissionError(
                'risk-settings-validation-error',
                t`Validation error`,
                t`Unable to validate form data. Please try again.`,
            );

            return false;
        }
    }

    private showFormSubmissionError(
        id: string,
        title: string,
        description: string,
    ): void {
        snackbarController.addSnackbar({
            id,
            props: {
                title,
                description,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    }

    get formSchema(): FormSchema {
        const { riskSettingsQuery } = sharedRiskSettingsController;
        const { currentRiskSettings, _formSchemaVersion } = this;

        if (!currentRiskSettings || riskSettingsQuery.hasError) {
            return {};
        }

        // Ensure _formSchemaVersion is accessed for MobX reactivity
        if (_formSchemaVersion < 0) {
            return {};
        }

        try {
            const validRiskSettings: RiskSettingsResponseDto =
                currentRiskSettings;
            const createFormSchema = createRiskSettingsFormSchema;

            if (!isFunction(createFormSchema)) {
                throw new TypeError(
                    'createRiskSettingsFormSchema is not available',
                );
            }

            const schema: FormSchema = createFormSchema(
                validRiskSettings,
                {
                    ImpactLikelihoodFormField,
                    RiskLevelDefinitionsFormField,
                    ThresholdsFormField,
                },
                createNumericOptionsWrapper,
            );

            return schema;
        } catch (error) {
            logger.error({
                message: 'Failed to create form schema',
                additionalInfo: { error },
            });

            return {};
        }
    }

    transformFormData(
        formValues: unknown,
        currentSettings: RiskSettingsResponseDto,
    ): RiskSettingsRequestDto {
        if (!this.isValidFormData(formValues)) {
            throw new Error('Invalid form data structure');
        }

        const validatedFormValues: Record<string, unknown> = formValues;

        if (!isFunction(transformRiskSettingsFormData)) {
            throw new TypeError(
                'transformRiskSettingsFormData is not available',
            );
        }

        const settingsWithFilteredThresholds: RiskSettingsResponseDto = {
            ...currentSettings,
            thresholds: this.filteredThresholds,
        };

        const result: RiskSettingsRequestDto = transformRiskSettingsFormData(
            validatedFormValues,
            settingsWithFilteredThresholds,
            this._pendingThresholdChanges.boundaries,
        );

        return result;
    }
}

export const sharedRiskSettingsScoringController =
    new RiskSettingsScoringController();
