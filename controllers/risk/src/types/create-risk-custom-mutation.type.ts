import type { ListBoxItemData } from '@cosmos/components/list-box';

export interface CreateRiskCustomMutationType {
    riskSourceGroup?: {
        riskSource: string;
    };
    statusGroup?: {
        status: {
            value: string;
            label: string;
            id: string;
        };
    };
    riskDetailsGroup?: {
        title: string;
        description: string;
        identifiedAt?: string;
        categories: ListBoxItemData[];
        owners: ListBoxItemData[];
        vendor?: ListBoxItemData;
        documents: {
            path: string;
            relativePath: string;
        }[];
    };
    inherentScoreGroup?: {
        impact?: {
            id: string;
            label: string;
            value: string;
        };
        likelihood?: {
            id: string;
            label: string;
            value: string;
        };
    };
    treatmentGroup?: {
        treatment?: {
            id: string;
            label: string;
            value: string;
        };
        treatmentPlan?: string;
        residualScoreGroup?: {
            residualImpact?: {
                id: string;
                label: string;
                value: string;
            };
            residualLikelihood?: {
                id: string;
                label: string;
                value: string;
            };
        };
        anticipatedCompletionDate?: string;
        completedDate?: string;
        reviewers?: ListBoxItemData[];
    };
    customFields?: Record<string, string>;
    controls?: ListBoxItemData[];
}
