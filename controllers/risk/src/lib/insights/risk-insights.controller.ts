import { every, isEmpty, isNil, isObject } from 'lodash-es';
import { riskManagementControllerGetDashboardOptions } from '@globals/api-sdk/queries';
import type {
    DashboardResponseDto,
    RiskManagementControllerGetDashboardData,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

const validateEmptyRiskInsights = (
    dashboardData: DashboardResponseDto | null,
): boolean => {
    return every(dashboardData, (chart) =>
        isObject(chart) ? isEmpty(chart) : chart === 0,
    );
};

class RiskInsightsController {
    isScored = true;
    currentFilters: RiskManagementControllerGetDashboardData['query'] = {};
    isInitialized = false;

    constructor() {
        makeAutoObservable(this);
    }

    riskInsightsQuery = new ObservedQuery(
        riskManagementControllerGetDashboardOptions,
    );

    get isLoading(): boolean {
        return this.riskInsightsQuery.isLoading;
    }

    get riskInsights(): DashboardResponseDto | null {
        return this.riskInsightsQuery.data;
    }

    get currentFiltersSelected(): RiskManagementControllerGetDashboardData['query'] {
        return this.currentFilters;
    }

    get isEmptyRiskInsights(): boolean {
        return validateEmptyRiskInsights(this.riskInsights);
    }

    get hasOnlyRemainingAndScored(): boolean {
        if (!this.riskInsights) {
            return false;
        }

        const {
            categoryBreakdown,
            riskOverTime,
            riskHeatmap,
            riskPosture,
            remaining,
            scored,
        } = this.riskInsights;

        const visualDataFields = [
            categoryBreakdown,
            riskOverTime,
            riskHeatmap,
            riskPosture,
        ];
        const allVisualDataEmpty = visualDataFields.every(
            (field) => isNil(field) || isEmpty(field),
        );
        const hasRemainingOrScoredData = remaining !== 0 || scored !== 0;

        return allVisualDataEmpty && hasRemainingOrScoredData;
    }

    initialize = (): void => {
        if (this.isInitialized) {
            return;
        }

        this.riskInsightsQuery.load({
            query: {
                isScored: true,
            },
        });

        this.isInitialized = true;
    };

    updateFilters = (
        newFilters: RiskManagementControllerGetDashboardData['query'],
    ): void => {
        // Merge new filters with existing ones
        this.currentFilters = {
            ...this.currentFilters,
            ...newFilters,
        };

        // Clean up empty values
        const cleanFilters: RiskManagementControllerGetDashboardData['query'] =
            {};

        if (this.currentFilters.categoriesIds?.length) {
            cleanFilters.categoriesIds = this.currentFilters.categoriesIds;
        }
        if (this.currentFilters.ownersIds?.length) {
            cleanFilters.ownersIds = this.currentFilters.ownersIds;
        }
        if (this.currentFilters.riskFilter) {
            cleanFilters.riskFilter = this.currentFilters.riskFilter;
        }
        if (this.currentFilters['status[]']?.length) {
            cleanFilters['status[]'] = this.currentFilters['status[]'];
        }
        if (this.currentFilters.classifications?.length) {
            cleanFilters.classifications = this.currentFilters.classifications;
        }
        if (this.currentFilters.scoreType) {
            cleanFilters.scoreType = this.currentFilters.scoreType;
        }
        if (this.currentFilters.previousDate) {
            cleanFilters.previousDate = this.currentFilters.previousDate;
        }

        this.currentFilters = cleanFilters;
        this.loadWithCurrentFilters();
    };

    clearAllFilters = (): void => {
        this.currentFilters = {};
        this.loadWithCurrentFilters();
    };

    loadWithCurrentFilters = (): void => {
        const queryParams: RiskManagementControllerGetDashboardData['query'] = {
            isScored: this.isScored,
            ...this.currentFilters,
        };

        this.riskInsightsQuery.load({
            query: queryParams,
        });
    };

    load = (
        params: RiskManagementControllerGetDashboardData['query'] = {},
    ): void => {
        this.updateFilters(params);
    };
}

export const sharedRiskInsightsController = new RiskInsightsController();
