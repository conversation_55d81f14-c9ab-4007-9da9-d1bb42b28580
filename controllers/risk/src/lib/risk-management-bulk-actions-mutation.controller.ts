import { riskManagementControllerRiskBulkActionsMutation } from '@globals/api-sdk/queries';
import type {
    RiskManagementControllerRiskBulkActionsResponse,
    RisksBulkActionsRequestDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';

export class RiskManagementBulkActionsMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    bulkUpdateRisksMutation = new ObservedMutation(
        riskManagementControllerRiskBulkActionsMutation,
    );

    get isPending(): boolean {
        return this.bulkUpdateRisksMutation.isPending;
    }

    get hasError(): boolean {
        return this.bulkUpdateRisksMutation.hasError;
    }

    get error(): Error | null {
        return this.bulkUpdateRisksMutation.error;
    }

    get response(): RiskManagementControllerRiskBulkActionsResponse | null {
        return this.bulkUpdateRisksMutation.response;
    }

    bulkUpdateRisks = (params: RisksBulkActionsRequestDto): void => {
        this.bulkUpdateRisksMutation.mutate({ body: params });
    };
}

export const sharedRiskManagementBulkActionsMutationController =
    new RiskManagementBulkActionsMutationController();
