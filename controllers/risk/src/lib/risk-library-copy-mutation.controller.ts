import { riskLibraryControllerCopyRisksFromLibraryMutation } from '@globals/api-sdk/queries';
import type {
    CopyRisksFromLibraryRequestDto,
    RiskLibraryControllerCopyRisksFromLibraryResponse,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';

export class RiskLibraryCopyController {
    constructor() {
        makeAutoObservable(this);
    }

    copyRisksFromLibraryMutation = new ObservedMutation(
        riskLibraryControllerCopyRisksFromLibraryMutation,
    );

    get isPending(): boolean {
        return this.copyRisksFromLibraryMutation.isPending;
    }

    get hasError(): boolean {
        return this.copyRisksFromLibraryMutation.hasError;
    }

    get response(): RiskLibraryControllerCopyRisksFromLibraryResponse | null {
        return this.copyRisksFromLibraryMutation.response;
    }

    copyRisksFromLibrary = (params: CopyRisksFromLibraryRequestDto): void => {
        this.copyRisksFromLibraryMutation.mutate({ body: params });
    };
}

export const sharedRiskLibraryCopyController = new RiskLibraryCopyController();
