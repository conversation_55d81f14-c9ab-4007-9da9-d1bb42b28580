import { snackbarController } from '@controllers/snackbar';
import { riskManagementControllerDeleteRiskMutation } from '@globals/api-sdk/queries';
import type { RisksBulkActionsRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedRiskManagementBulkActionsMutationController } from './risk-management-bulk-actions-mutation.controller';

class RiskManagementMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    riskManagementControllerDeleteRiskMutation = new ObservedMutation(
        riskManagementControllerDeleteRiskMutation,
    );

    get isDeleteRiskPending(): boolean {
        return this.riskManagementControllerDeleteRiskMutation.isPending;
    }

    deleteRisk = (riskId: string) => {
        this.riskManagementControllerDeleteRiskMutation.mutate({
            body: {
                risksIds: [riskId],
            },
        });
    };

    addRiskOwnersInBulk = (risksIds: string[], ownerIds: number[]) => {
        sharedRiskManagementBulkActionsMutationController.bulkUpdateRisks({
            // @ts-expect-error -- the API docs are wrong, we can omit the other props
            body: {
                bulkActionType: 'ASSOCIATE_RISK_OWNERS',
                risksIds,
                ownerIds,
            },
        });

        when(
            () => !sharedRiskManagementBulkActionsMutationController.isPending,
            () => {
                if (
                    !sharedRiskManagementBulkActionsMutationController.hasError
                ) {
                    snackbarController.addSnackbar({
                        id: 'bulk-add-risk-owners-success',
                        props: {
                            title: t`Risk owners added`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                logger.error({
                    message: 'Failed to add risk owners in bulk',
                    additionalInfo: {
                        error: sharedRiskManagementBulkActionsMutationController.error,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'bulk-add-risk-owners-error',
                    props: {
                        title: t`Failed to add risk owners`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    assignRiskCategoryInBulk = (risksIds: string[], categoryIds: number[]) => {
        sharedRiskManagementBulkActionsMutationController.bulkUpdateRisks({
            // @ts-expect-error -- the API docs are wrong, we can omit the other props
            body: {
                bulkActionType: 'ASSOCIATE_RISK_CATEGORIES',
                risksIds,
                categoryIds,
            },
        });

        when(
            () => !sharedRiskManagementBulkActionsMutationController.isPending,
            () => {
                if (
                    !sharedRiskManagementBulkActionsMutationController.hasError
                ) {
                    snackbarController.addSnackbar({
                        id: 'bulk-add-risk-categories-success',
                        props: {
                            title: t`Risk categories added`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                logger.error({
                    message: 'Failed to add risk categories in bulk',
                    additionalInfo: {
                        error: sharedRiskManagementBulkActionsMutationController.error,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'bulk-add-risk-owners-error',
                    props: {
                        title: t`Failed to add risk categories`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    updateRiskStatusInBulk = (
        risksIds: string[],
        status: RisksBulkActionsRequestDto['status'],
    ) => {
        sharedRiskManagementBulkActionsMutationController.bulkUpdateRisks({
            // @ts-expect-error -- the API docs are wrong, we can omit the other props
            body: {
                bulkActionType: 'STATUS',
                risksIds,
                status,
            },
        });

        when(
            () => !sharedRiskManagementBulkActionsMutationController.isPending,
            () => {
                if (
                    !sharedRiskManagementBulkActionsMutationController.hasError
                ) {
                    snackbarController.addSnackbar({
                        id: 'bulk-update-risk-status-success',
                        props: {
                            title: t`Risk status updated`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                logger.error({
                    message: 'Failed to update risk status in bulk',
                    additionalInfo: {
                        error: sharedRiskManagementBulkActionsMutationController.error,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'bulk-update-risk-status-error',
                    props: {
                        title: t`Failed to update risk status`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    deleteRiskInBulkQuery = new ObservedMutation(
        riskManagementControllerDeleteRiskMutation,
    );

    get isDeleteRiskInBulkPending(): boolean {
        return this.deleteRiskInBulkQuery.isPending;
    }

    deleteRiskInBulk = (risksIds: string[]) => {
        this.deleteRiskInBulkQuery.mutate({
            body: {
                risksIds,
            },
        });
    };
}

export const sharedRiskManagementMutationController =
    new RiskManagementMutationController();
