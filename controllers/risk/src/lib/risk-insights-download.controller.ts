import { uniqueId } from 'lodash-es';
import type { RefObject } from 'react';
import { snackbarController } from '@controllers/snackbar';
import { riskManagementControllerGetRiskDashboardReportMutation } from '@globals/api-sdk/queries';
import type { CategoryBreakdownReportDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    toJS,
    when,
} from '@globals/mobx';
import { sharedSnapdomController } from '@globals/snapdom';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedRiskInsightsScoreTypeModel } from '@models/risk-insights';
import type { CategoryBreakdownRef } from '@views/risk-insights';
import {
    type DownloadSource,
    requestBuilders,
} from './helpers/risk-insights-download.helper';
import { sharedRiskInsightsController } from './insights/risk-insights.controller';

class RiskInsightsDownloadController {
    constructor() {
        makeAutoObservable(this);
    }

    isPreparingDownloadAllDto = false;

    postureCardRef: HTMLElement | null = null;
    overTimeChartRef: RefObject<HTMLElement> | null = null;
    heatmapCardRef: RefObject<HTMLElement> | null = null;
    categoryBreakdownRef: CategoryBreakdownRef[] | null = null;

    postureCardBase64: string | null = null;
    overTimeChartBase64: string | null = null;
    heatmapCardBase64: string | null = null;
    categoryBreakdownBase64: CategoryBreakdownReportDto[] | null = null;

    riskInsightsReportMutation = new ObservedMutation(
        riskManagementControllerGetRiskDashboardReportMutation,
    );

    get isDownloadLoading(): boolean {
        return this.riskInsightsReportMutation.isPending;
    }

    get hasError(): boolean {
        return this.riskInsightsReportMutation.hasError;
    }

    get isPreparingDownloadAll(): boolean {
        return this.isPreparingDownloadAllDto;
    }

    downloadAll = async () => {
        this.isPreparingDownloadAllDto = true;
        const { hasOnlyRemainingAndScored, riskInsights } =
            sharedRiskInsightsController;
        const assessmentBuilder = requestBuilders.ASSESSMENT_PROGRESS;
        const treatmentBuilder = requestBuilders.TREATMENT_OVERVIEW;

        try {
            if (hasOnlyRemainingAndScored && riskInsights) {
                const requestBodyScored = {
                    ...assessmentBuilder(toJS(riskInsights)),
                    ...treatmentBuilder(toJS(riskInsights)),
                };

                this.riskInsightsReportMutation.mutate({
                    body: requestBodyScored,
                });

                when(
                    () => !this.riskInsightsReportMutation.isPending,
                    () => {
                        if (!this.riskInsightsReportMutation.hasError) {
                            this.isPreparingDownloadAllDto = false;

                            return;
                        }

                        logger.error(
                            'Failed to download risk insights report from reference',
                        );
                    },
                );

                return;
            }

            this.postureCardBase64 = this.postureCardRef
                ? await sharedSnapdomController.captureAndGetElementFromHtmlAsBase64(
                      toJS(this.postureCardRef),
                  )
                : null;

            this.overTimeChartBase64 = this.overTimeChartRef
                ? await sharedSnapdomController.captureAndGetElementAsBase64(
                      toJS(this.overTimeChartRef),
                  )
                : null;

            this.heatmapCardBase64 = this.heatmapCardRef
                ? await sharedSnapdomController.captureAndGetElementAsBase64(
                      toJS(this.heatmapCardRef),
                  )
                : null;

            await this.transformCategoryBreakdownRefsToBase64();

            const requestBody = {
                ...(riskInsights && assessmentBuilder(riskInsights)),
                ...(riskInsights && treatmentBuilder(riskInsights)),
                ...(this.postureCardBase64 && {
                    riskPosture: this.postureCardBase64,
                }),
                ...(this.overTimeChartBase64 && {
                    risksOverTime: this.overTimeChartBase64,
                }),
                ...(this.heatmapCardBase64 && {
                    riskHeatmap: this.heatmapCardBase64,
                }),
                ...(this.categoryBreakdownBase64 && {
                    categoryBreakdown: this.categoryBreakdownBase64,
                }),
            };

            this.riskInsightsReportMutation.mutate({
                body: requestBody,
            });

            this.isPreparingDownloadAllDto = false;
        } catch {
            logger.error('Failed to capture element as base64 on download all');
            snackbarController.addSnackbar({
                id: `risk-download-error-${uniqueId()}`,
                props: {
                    title: t`Failed to capture image`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    };

    transformCategoryBreakdownRefsToBase64 = async () => {
        const categoryBreakdownBase64Array: CategoryBreakdownReportDto[] = [];

        for (const ref of this.categoryBreakdownRef ?? []) {
            if (!ref.uri) {
                logger.error(
                    "Category breakdown ref from download all URI's is null",
                );
                continue;
            }

            try {
                const base64Result =
                    await sharedSnapdomController.captureAndGetElementFromHtmlAsBase64(
                        ref.uri,
                    );

                if (!base64Result) {
                    logger.error('Failed to capture element as base64');
                    snackbarController.addSnackbar({
                        id: `risk-download-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to capture image`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    continue;
                }

                categoryBreakdownBase64Array.push({
                    ...ref,
                    uri: base64Result,
                });
            } catch {
                logger.error('Failed to capture category breakdown element');
            }
        }

        this.categoryBreakdownBase64 = categoryBreakdownBase64Array;
    };

    downloadRiskInsightsCategoryBreakdown = async (): Promise<void> => {
        await this.transformCategoryBreakdownRefsToBase64();

        if (this.categoryBreakdownBase64) {
            this.riskInsightsReportMutation.mutate({
                body: {
                    categoryBreakdown: this.categoryBreakdownBase64,
                },
            });
        }
    };

    downloadRiskInsightsReportFromHtml = (htmlRef: HTMLElement) => {
        const riskInsights = toJS(sharedRiskInsightsController.riskInsights);
        const scoreType = toJS(sharedRiskInsightsScoreTypeModel.scoreType);

        sharedSnapdomController
            .captureElementFromHtmlAsBase64(htmlRef)
            .then(() => {
                if (!riskInsights) {
                    return;
                }

                const base64Result = toJS(sharedSnapdomController.base64Result);

                if (!base64Result) {
                    logger.error('Failed to capture element as base64');
                    snackbarController.addSnackbar({
                        id: `risk-download-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to capture image`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const requestBody = {
                    riskPosture: base64Result || '',
                    scoreType,
                };

                this.riskInsightsReportMutation.mutate({
                    body: requestBody,
                });

                when(
                    () => !this.riskInsightsReportMutation.isPending,
                    () => {
                        if (!this.riskInsightsReportMutation.hasError) {
                            return;
                        }

                        logger.error(
                            'Failed to download risk insights report from reference',
                        );
                        snackbarController.addSnackbar({
                            id: `risk-download-error-${uniqueId()}`,
                            props: {
                                title: t`Failed to submit report request`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    },
                );
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `risk-download-error-${uniqueId()}`,
                    props: {
                        title: t`Failed to download report`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                logger.error('Failed to download Heatmap report');
            });
    };

    downloadRiskInsightsReportFromReference = (
        captureAreaRef: RefObject<HTMLElement>,
        cardType: 'posture' | 'heatmap' | 'over-time',
    ) => {
        sharedSnapdomController
            .captureElementAsBase64(captureAreaRef)
            .then(() => {
                const riskInsights = toJS(
                    sharedRiskInsightsController.riskInsights,
                );

                if (!riskInsights) {
                    return;
                }

                if (!sharedSnapdomController.base64Result) {
                    logger.error(
                        `Failed to capture ${cardType} element as base64`,
                    );
                    snackbarController.addSnackbar({
                        id: `risk-download-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to capture image`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                let requestBody: Record<string, unknown>;

                switch (cardType) {
                    case 'posture': {
                        requestBody = {
                            riskPosture: sharedSnapdomController.base64Result,
                            scoreType:
                                sharedRiskInsightsScoreTypeModel.scoreType,
                        };
                        break;
                    }
                    case 'heatmap': {
                        requestBody = {
                            riskHeatmap: sharedSnapdomController.base64Result,
                            scoreType:
                                sharedRiskInsightsScoreTypeModel.scoreType,
                        };
                        break;
                    }
                    case 'over-time': {
                        requestBody = {
                            risksOverTime: sharedSnapdomController.base64Result,
                            scoreType:
                                sharedRiskInsightsScoreTypeModel.scoreType,
                        };
                        break;
                    }
                    default: {
                        logger.error({
                            message: 'Invalid card type',
                            additionalInfo: { cardType },
                        });

                        return;
                    }
                }

                this.riskInsightsReportMutation.mutate({
                    body: requestBody,
                });

                when(
                    () => !this.riskInsightsReportMutation.isPending,
                    () => {
                        if (!this.riskInsightsReportMutation.hasError) {
                            return;
                        }

                        logger.error(
                            `Failed to download risk insights ${cardType} report from reference`,
                        );
                        snackbarController.addSnackbar({
                            id: `risk-download-error-${uniqueId()}`,
                            props: {
                                title: t`Failed to submit report request`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    },
                );
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: `risk-download-error-${uniqueId()}`,
                    props: {
                        title: t`Failed to download report`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                logger.error(`Failed to download ${cardType} report`);
            });
    };

    downloadRiskInsightsHeatmap = async (): Promise<void> => {
        if (!this.heatmapCardRef) {
            snackbarController.addSnackbar({
                id: `risk-download-error-${uniqueId()}`,
                props: {
                    title: t`Failed to heatmap report`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
            logger.error(`Failed to download heatmap report`);

            return;
        }

        this.heatmapCardBase64 =
            await sharedSnapdomController.captureAndGetElementAsBase64(
                toJS(this.heatmapCardRef),
            );

        if (this.heatmapCardBase64) {
            this.riskInsightsReportMutation.mutate({
                body: {
                    riskHeatmap: this.heatmapCardBase64,
                },
            });
        }
    };

    downloadRiskInsightsReport = (source: DownloadSource) => {
        const currentWorkspace = toJS(
            sharedWorkspacesController.currentWorkspace,
        );
        const riskInsights = toJS(sharedRiskInsightsController.riskInsights);

        if (!currentWorkspace || !riskInsights) {
            return;
        }

        const builder = requestBuilders[source];

        const requestBody = builder(toJS(riskInsights));

        this.riskInsightsReportMutation.mutate({
            body: requestBody,
        });

        when(
            () => !this.isDownloadLoading,
            () => {
                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: `risk-download-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to download report`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };
}

export const sharedRiskInsightsDownloadController =
    new RiskInsightsDownloadController();
