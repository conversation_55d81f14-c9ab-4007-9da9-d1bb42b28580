# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚨 CRITICAL: Check Conventions First!

**BEFORE writing ANY code**, ALWAYS check `.llm/PATTERN_INDEX.md` for the correct patterns to follow.

**MANDATORY FIRST STEP** when implementing ANY feature:
1. Read `.llm/PATTERN_INDEX.md`
2. Search for keywords related to the task
3. Follow the files it points to for detailed patterns

⚠️ **DO NOT** assume patterns or search the codebase directly without first checking PATTERN_INDEX.md!

## Development Commands

### Quick Start
```sh
# Install dependencies, update API SDK, generate tokens, and start dev server
pnpm run qs
```

### Core Commands
```sh
pnpm run app:drata:dev     # Development server
pnpm run test              # Run tests
pnpm run typecheck         # Type check
pnpm run lint              # Lint
pnpm run format            # Format code
pnpm run update-api-sdk    # Update API SDK from OpenAPI spec
```

### Module Generators (Interactive)
```sh
pnpm run generate:component
pnpm run generate:controller
pnpm run generate:model
pnpm run generate:view
```

## Architecture Overview

- **Pattern**: Custom MVC with MobX state management and Remix routing
- **Modules**: Controllers (business logic), Models (state), Views (pages), Components (UI)
- **State**: MobX with singleton controllers (`sharedXxxController`)
- **API**: Auto-generated SDK from OpenAPI specs
- **Routing**: Remix file-based routing in `/apps/drata/app/routes/`

## Key Reminders

### After Editing TypeScript
ALWAYS run: `pnpm run format && pnpm run typecheck && pnpm run lint`

### Critical Rules (will break build)
- Import violations - use global modules (`@globals/mobx`, `@globals/i18n/macro`)
- NEVER use `disabled` prop on buttons - use `isLoading` or `cosmosUseWithCaution_isDisabled`
- All user-facing text must use i18n - no hardcoded strings
- No default exports (except routes)

## Directory Structure
```
/controllers/    → Business logic with ObservedQuery/Mutation
/models/        → State containers with MobX
/views/         → Page components with observer HOC
/components/    → Reusable UI components
/globals/       → Shared services (api-sdk, i18n, mobx, auth)
```

## AI Agents

Use specialized agents for complex tasks to improve efficiency and quality:

### Available Agents

#### general-purpose
For researching complex questions, searching code, and executing multi-step tasks. Use when searching for keywords/files with uncertain matches.

#### architecture-guardian
Reviews architectural decisions and ensures code follows established patterns. Use after:
- Implementing significant features
- Refactoring code
- Making architectural changes
- Wanting to verify new features integrate properly

#### frontend-staff-engineer
Expert-level frontend engineering guidance. Use for:
- Complex React/TypeScript performance issues
- Architectural decisions and state management (MobX)
- Senior-level code reviews
- Accessibility compliance
- Complex feature implementation

#### frontend-requirements-planner
Creates detailed requirements and specifications. Use for:
- Breaking down feature requests into technical requirements
- Defining user stories and acceptance criteria
- Outlining component structures and data flows
- Documenting UI/UX requirements

#### frontend-unit-test-writer
Writes comprehensive unit tests following project patterns. Use for:
- Testing React components
- Testing MobX stores, controllers, models, views
- Following multiverse testing conventions from PATTERN_INDEX.md

### Usage Examples
```
"Use the architecture-guardian agent to review this implementation"
"Use the frontend-staff-engineer agent to optimize this component's performance"
"Use the frontend-requirements-planner agent to plan the dashboard feature"
"Use the frontend-unit-test-writer agent to write tests for UserController"
```

### Adding New Agents
When creating a new agent:
1. Add it to this CLAUDE.md file with:
   - Agent name
   - Clear description of its purpose
   - Specific use cases
   - Example usage
2. Follow the existing format for consistency

## 📍 For ALL Patterns and Conventions

Check `.llm/PATTERN_INDEX.md` - it's your single source of truth for:
- Import rules and violations
- Component patterns
- API SDK usage
- Form patterns
- Testing patterns
- And much more...

The PATTERN_INDEX.md uses a keyword-based lookup system. Search for your task keywords to find the right documentation quickly.