import type { ClientTypeEnum } from '@globals/api-sdk/types';

// INTEGRATION QUICK FIND: Client Type
// https://www.notion.so/drata/f295b355f9ac4b72ae7ab103c543f258
export const clientType = {
    GOOGLE: 1,
    OKTA: 2,
    MICROSOFT_365: 3,
    AWS: 4,
    GCP: 5,
    A<PERSON><PERSON><PERSON>: 6,
    <PERSON>EROKU: 7,
    GITHUB: 8, // this is used as the client type in the connections entity for version control and SDLC
    GITLAB: 9,
    BITBUCKET: 10,
    SHORTCUT: 11,
    JIRA: 12,
    ASANA: 13,
    GUSTO: 14,
    KARMACHECK: 15,
    GITHUB_ISSUES: 16,
    RIPPLING: 17,
    CERTN: 18,
    MERGEDEV_ADP_WORKFORCE_NOW: 19,
    MERGEDEV_BAMBOO_HR: 20,
    MERGEDEV_HR_PARTNER: 21,
    MERGEDEV_HI_BOB: 22,
    MERGEDEV_HUMAANS: 23,
    MER<PERSON><PERSON><PERSON>_PERSONIO: 24,
    <PERSON>R<PERSON><PERSON><PERSON>_SAGE: 25,
    <PERSON><PERSON><PERSON><PERSON>_OCEAN: 26, // was added to main prior to this effort - leave in position 26
    MERGEDEV_KALLIDUS: 27,
    MERGEDEV_TRINET: 28,
    MERGEDEV_TRINET_HR: 29,
    MERGEDEV_FRESHTEAM: 30,
    MERGEDEV_SAP_SUCCESSFACTORS: 31,
    TRELLO: 32,
    GITLAB_ISSUES: 33,
    MERGEDEV_JUSTWORKS: 34,
    LINEAR: 35,
    PIVOTAL_TRACKER: 36,
    WORK_OS: 37,
    MERGEDEV_PAYLOCITY: 38,
    MONGO_DB_ATLAS: 39,
    TARGET_PROCESS: 40,
    CLICKUP: 41,
    CHECKR: 42,
    MERGEDEV_WORKDAY: 43,
    ZOHO: 44,
    CLOUDFLARE: 45,
    JAMF: 46,
    AZURE_BOARDS: 47,
    AZURE_REPOS: 48,
    MERGEDEV_HR_CLOUD: 49,
    INTUNE: 50,
    OKTA_IDENTITY: 51,
    GOODHIRE: 52,
    KANDJI: 53,
    AWS_CODECOMMIT: 54,
    FIBERY: 55,
    CURRICULA: 56,
    JUMPCLOUD: 57,
    HEXNODE_UEM: 58,
    RIPPLING_MDM: 59,
    AWS_ORG_UNITS: 60,
    KNOWBE4: 61,
    MERGEDEV_UKG_PRO: 62,
    MERGEDEV_UKG_READY: 63,
    DATADOG: 64,
    SLACK: 65,
    DOCUSIGN: 66,
    MICROSOFT_TEAMS: 67,
    AWS_INSPECTOR: 68,
    MERGEDEV_ONELOGIN: 69,
    MERGEDEV_JUMPCLOUD: 70,
    CONFLUENCE: 71,
    WORKSPACE_ONE: 72,
    NEW_RELIC: 73,
    MERGEDEV_SERVICENOW: 74,
    RAPID7: 75,
    MERGEDEV_AHA: 76,
    MERGEDEV_BASECAMP: 77,
    MERGEDEV_BITBUCKET: 78,
    MERGEDEV_FRESHDESK: 79,
    MERGEDEV_FRESHSERVICE: 80,
    MERGEDEV_HEIGHT: 81,
    MERGEDEV_HIVE: 82,
    MERGEDEV_TEAMWORK: 83,
    MERGEDEV_WRIKE: 84,
    MERGEDEV_ZENDESK: 85,
    CSV_IDP: 86,
    APIDECK: 87,
    AWS_GOV_CLOUD: 88,
    SENTINEL_ONE: 89,
    MERGEDEV_NAMELY: 90,
    MERGEDEV_INSPERITY_PREMIER: 91,
    MERGEDEV_DAYFORCE: 92,
    MERGEDEV_ALEXISHR: 93,
    MERGEDEV_BREATHE: 94,
    MERGEDEV_CHARLIE: 95,
    MERGEDEV_CHARTHOP: 96,
    MERGEDEV_DEEL: 97,
    MERGEDEV_FACTORIAL: 98,
    MERGEDEV_INTELLIHR: 99,
    MERGEDEV_KEKA: 100,
    MERGEDEV_LUCCA: 101,
    MERGEDEV_OFFICIENT: 102,
    MERGEDEV_PAYCHEX: 103,
    MERGEDEV_PEOPLE_HR: 104,
    MERGEDEV_OYSTERHR: 105,
    MERGEDEV_PAYCOR: 106,
    HUBSPOT: 107,
    ZOOM: 108,
    AUTH0: 109,
    SENTRY: 110,
    ZAPIER: 111,
    SNOWFLAKE: 112,
    MIRO: 113,
    MERGEDEV_EMPLOYMENT_HERO: 114,
    SEGMENT: 115,
    WIZ: 116,
    XERO: 117,
    GITHUB_CODE: 118,
    ATLASSIAN: 119,
    PAGER_DUTY: 120,
    STACKONE_SMARTRECRUITERS: 121,
    STACKONE_TEAMTAILOR: 122,
    CROWDSTRIKE: 123,
    GITHUB_ENTERPRISE: 124,
    NOTION: 125,
    STACKONE_KLAVIYO: 126,
    STACKONE_LASTPASS: 127,
    STACKONE_LEAPSOME: 128,
    STACKONE_LEVER: 129,
    STACKONE_ORACLEHCM: 130,
    STACKONE_PINPOINT: 131,
    STACKONE_PIPEDRIVE: 132,
    STACKONE_RECRUITEE: 133,
    STACKONE_WEBEX: 134,
    STACKONE_WORKABLE: 135,
    STACKONE_ZELT: 136,
    STACKONE_BITWARDEN: 137,
    STACKONE_SALESLOFT: 138,
    STACKONE_DIXA: 139,
    STACKONE_FRESHSALES: 140,
    STACKONE_CANVA: 141,
    STACKONE_GREENHOUSE: 142,
    STACKONE_ASHBY: 143,
    STACKONE_ATTIO: 144,
    STACKONE_CONTENTFUL: 145,
    COVERDASH: 146,
    MERGEDEV_FRONT: 147,
    SALESFORCE: 148,
    STACKONE_ELASTIC: 149,
    STACKONE_RENDER: 150,
    STACKONE_TERRAFORM: 151,
    STACKONE_DOMO: 152,
    STACKONE_ENVOY: 153,
    STACKONE_SCALEWAY: 154,
    STACKONE_JETBRAINS: 155,
    STACKONE_FIVETRAN: 156,
    STACKONE_INTERCOM: 157,
    STACKONE_AUTODESK: 158,
    GITHUB_ACTIONS: 159,
    AZURE_ORG_UNITS: 160,
    MICROSOFT_365_GCC_HIGH: 161,
    STACKONE_AIRCALL: 162,
    STACKONE_15FIVE: 163,
    STACKONE_ROLLBAR: 164,
    STACKONE_EGNYTE: 165,
    STACKONE_QLIK: 166,
    STACKONE_BULLHORN: 167,
    STACKONE_OPENVPN: 168,
    STACKONE_SOPHOS: 169,
    STACKONE_MEISTERTASK: 170,
    STACKONE_TALENTLMS: 171,
    STACKONE_ONEFLOW: 172,
    STACKONE_RING_CENTRAL: 173,
    STACKONE_ARTICULATE: 174,
    STACKONE_DIALPAD: 175,
    STACKONE_TABLEAU: 176,
    AZURE_DEVOPS: 177,
    STACKONE_1PASSWORD: 178,
    STACKONE_MIXPANEL: 179,
    STACKONE_SONARCLOUD: 180,
    STACKONE_TWILIO: 181,
    STACKONE_ANSIBLE: 182,
    STACKONE_LATTICE: 183,
    STACKONE_WEBFLOW: 184,
    STACKONE_LACEWORK: 185,
    STACKONE_DATABRICKS: 186,
    STACKONE_IFS: 187,
    STACKONE_TRAVISCI: 188,
    STACKONE_MATILLIONETL: 189,
    STACKONE_OPTIMIZELY: 190,
    UAR_CSV: 191,
    STERLING: 192,
    HIRERIGHT: 193,
    VETTY: 194,
    MERGEDEV_CYBERARK: 195,
    SALESFORCE_UAR: 196,
    GOOGLE_ADMIN_CONSOLE: 197,
    VERCEL: 198,
    STACKONE_DUO: 199,
    STACKONE_GONG: 200,
    STACKONE_IRONCLAD: 201,
    STACKONE_SCORO: 202,
    STACKONE_TEAMVIEWER_REMOTE: 203,
    STACKONE_SPOTDRAFT: 204,
    STACKONE_SPENDESK: 205,
    STACKONE_SENDGRID: 206,
    STACKONE_SMARTSHEET: 207,
    STACKONE_CHECKMK: 208,
    LEEN_TENABLE: 209,
    LEEN_QUALYS: 210,
    LEEN_SEMGREP: 211,
    LEEN_SNYK: 212,
    LEEN_CROWDSTRIKE_VMS: 213,
    LEEN_MS_DEFENDER_VMS: 214,
    LEEN_SENTINELONE_VMS: 215,
    GITLAB_ON_PREM: 216,
    CUSTOM: 217,
    LEEN_RAPID7_VMS: 218,
    BAMBOO_HR: 219,
    GITLAB_ISSUES_ON_PREM: 220,
    GITHUB_ISSUES_ENTERPRISE: 221,
    BITBUCKET_CODE: 222,
    LEEN_ARNICA: 223,
    STACKONE_NETLIFY: 224,
    STACKONE_OPENAI: 225,
    KOLIDE: 226,
    MERGEDEV_JIRA_DATA_CENTER: 227,
    MERGEDEV_PINGONE: 228,
    GOOGLE_ADMIN_CONSOLE_OAUTH: 229,
    GOOGLE_OAUTH: 230,
    STACKONE_ANTHROPIC: 231,
    INTUNE_GCC_HIGH: 232,
    STACKONE_DROPBOX: 233,
    STACKONE_DROPBOX_SIGN: 234,
    STACKONE_HARVEST: 235,
    STACKONE_KAMELEOON: 236,
    STACKONE_MAKE: 237,
    STACKONE_RETOOL: 238,
    STACKONE_TOGGL: 239,
    STACKONE_BOX: 240,
    MERGEDEV_ZOHO_PEOPLE: 241,
    MERGEDEV_ZOHO_DESK: 242,
    LEEN_WIZ_VMS: 243,
    LEEN_WIZ_CODE: 244,
    LEEN_AIKIDO: 245,
    MERGEDEV_LATTICE_HRIS: 246,
    MERGEDEV_DARWINBOX: 247,
    MERGEDEV_PAYCOM: 248,
    AZURE_GCC_HIGH: 249,
    AZURE_MG_GCC_HIGH: 250,
    STACKONE_EASY_LLAMA: 251,
    MERGEDEV_LEAPSOME: 252,
    // CLI_CLIENT_TYPE_TAG_KEY
} as const satisfies Record<ClientTypeEnum, number>;

export interface AsyncCompanyEventResponseDataType {
    /**
     * Type this whenevery you need it.
     */
    accountEvent: unknown;
    /**
     * This is the client type that was resynced.
     */
    clientTypeEvent: ClientTypeEnum;
}
