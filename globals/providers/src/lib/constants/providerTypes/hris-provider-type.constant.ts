import type { BaseProviderType } from '../../types/base-provider-type.type';

export const HRIS_PROVIDER_TYPE: BaseProviderType = {
    id: 'HRIS',
    name: 'HRIS',
    providers: [
        'GUSTO',
        'RIPPLING',
        'MERGEDEV_BAMBOO_HR',
        'MERGEDEV_JUSTWORKS',
        'MERGEDEV_PAYLOCITY',
        'MERGEDEV_FRESHTEAM',
        'MERGEDEV_TRINET_HR',
        'MERGEDEV_HI_BOB',
        'MERGEDEV_ADP_WORKFORCE_NOW',
        'MERGEDEV_HR_PARTNER',
        'MERGEDEV_HUMAANS',
        'MERGEDEV_PERSONIO',
        'MERGEDEV_SAP_SUCCESSFACTORS',
        'MERGEDEV_SAGE',
        'MERGEDEV_KALLIDUS',
        'MERGEDEV_TRINET',
        'MERGEDEV_WORKDAY',
        'MERGEDEV_HR_CLOUD',
        'MERGEDEV_UKG_PRO',
        'MERGEDEV_UKG_READY',
        'MERGEDEV_PAYCOR',
        'MERGEDEV_NAMELY',
        'MERGEDEV_INSPERITY_PREMIER',
        'MERGEDEV_DAYFORCE',
        'MERGEDEV_ALEXISHR',
        'MERGEDEV_BREATHE',
        'MERGEDEV_CHARLIE',
        'MERGEDEV_CHARTHOP',
        'MERGEDEV_DEEL',
        'MERGEDEV_FACTORIAL',
        'MERGEDEV_INTELLIHR',
        'MERGEDEV_KEKA',
        'MERGEDEV_LUCCA',
        'MERGEDEV_OFFICIENT',
        'MERGEDEV_PAYCHEX',
        'MERGEDEV_PEOPLE_HR',
        'MERGEDEV_OYSTERHR',
        'MERGEDEV_EMPLOYMENT_HERO',
        'MERGEDEV_ZOHO_PEOPLE',
        'MERGEDEV_LEAPSOME',
        'BAMBOO_HR',
    ],
} as const;
