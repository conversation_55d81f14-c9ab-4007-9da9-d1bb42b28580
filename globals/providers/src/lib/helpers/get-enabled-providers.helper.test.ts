import { beforeEach, describe, expect, test, vi } from 'vitest';
import type { BaseProvider } from '../types/base-provider.type';
import type { Provider } from '../types/provider.type';
import type { ProviderType } from '../types/provider-type.type';
import { getEnabledProviders } from './get-enabled-providers.helper';

// Mock the logger
vi.mock('@globals/logger');

// Mock providers for testing using a partial record
const mockProviders = {
    AWS: {
        id: 'AWS' as Provider,
        name: 'Amazon Web Services',
        companyName: 'Amazon',
        companyUrl: 'https://aws.amazon.com/',
        logo: 'aws-logo.svg',
        providerTypes: ['CSPM' as ProviderType],
        getIsEnabled: () => true,
    } as BaseProvider,
    GOOGLE: {
        id: 'GOOGLE' as Provider,
        name: 'Google',
        companyName: 'Google',
        companyUrl: 'https://google.com/',
        logo: 'google-logo.svg',
        providerTypes: ['CSPM' as ProviderType],
        getIsEnabled: () => false,
    } as BaseProvider,
    OKTA: {
        id: 'OKTA' as Provider,
        name: 'Ok<PERSON>',
        companyName: 'Okta',
        companyUrl: 'https://okta.com/',
        logo: 'okta-logo.svg',
        providerTypes: ['IDENTITY_MANAGEMENT' as ProviderType],
        // No getIsEnabled function - should be included by default
    } as BaseProvider,
    MICROSOFT_365: {
        id: 'MICROSOFT_365' as Provider,
        name: 'Microsoft 365',
        companyName: 'Microsoft',
        companyUrl: 'https://microsoft.com/',
        logo: 'microsoft-logo.svg',
        providerTypes: ['IDENTITY_MANAGEMENT' as ProviderType],
        getIsEnabled: () => {
            throw new Error('Test error');
        },
    } as BaseProvider,
} as Record<Provider, BaseProvider>;

describe('getEnabledProviders', () => {
    let loggerWarnSpy: ReturnType<typeof vi.spyOn>;

    beforeEach(async () => {
        vi.clearAllMocks();
        const { logger } = await import('@globals/logger');

        loggerWarnSpy = vi.spyOn(logger, 'warn').mockImplementation(vi.fn());
    });

    test('should include providers with getIsEnabled returning true', () => {
        const result = getEnabledProviders(mockProviders);

        expect(result).toHaveProperty('AWS');
        expect(result.AWS).toStrictEqual(mockProviders.AWS);
    });

    test('should exclude providers with getIsEnabled returning false', () => {
        const result = getEnabledProviders(mockProviders);

        expect(result).not.toHaveProperty('GOOGLE');
    });

    test('should include providers without getIsEnabled function', () => {
        const result = getEnabledProviders(mockProviders);

        expect(result).toHaveProperty('OKTA');
        expect(result.OKTA).toStrictEqual(mockProviders.OKTA);
    });

    test('should exclude providers whose getIsEnabled throws an error', () => {
        const result = getEnabledProviders(mockProviders);

        expect(result).not.toHaveProperty('MICROSOFT_365');
        expect(loggerWarnSpy).toHaveBeenCalledWith({
            message:
                'Provider MICROSOFT_365 getIsEnabled function threw an error',
            additionalInfo: {
                error: expect.any(Error),
            },
        });
    });

    test('should not modify the original providers object', () => {
        const originalKeys = Object.keys(mockProviders);

        getEnabledProviders(mockProviders);

        expect(Object.keys(mockProviders)).toStrictEqual(originalKeys);
        expect(mockProviders).toHaveProperty('GOOGLE');
        expect(mockProviders).toHaveProperty('MICROSOFT_365');
    });

    test('should handle empty providers object', () => {
        const emptyProviders = {} as Record<Provider, BaseProvider>;

        const result = getEnabledProviders(emptyProviders);

        expect(result).toStrictEqual({});
    });

    test('should return correct count of enabled providers', () => {
        const result = getEnabledProviders(mockProviders);

        // Should include: AWS, OKTA
        // Should exclude: GOOGLE, MICROSOFT_365
        expect(Object.keys(result)).toHaveLength(2);
        expect(result).toHaveProperty('AWS');
        expect(result).toHaveProperty('OKTA');
    });
});
