import { logger } from '@globals/logger';
import type { BaseProvider } from '../types/base-provider.type';
import type { Provider } from '../types/provider.type';

/**
 * Filters providers based on their getIsEnabled function.
 * If a provider doesn't have getIsEnabled, it's assumed to be enabled.
 */
export const getEnabledProviders = (
    allProviders: Record<Provider, BaseProvider>,
): Record<Provider, BaseProvider> => {
    return Object.fromEntries(
        Object.entries(allProviders).filter(([, provider]) => {
            // If no getIsEnabled function, assume enabled
            if (!provider.getIsEnabled) {
                return true;
            }

            try {
                return provider.getIsEnabled();
            } catch (error) {
                // If getIsEnabled throws an error, assume disabled for safety
                logger.warn({
                    message: `Provider ${provider.id} getIsEnabled function threw an error`,
                    additionalInfo: { error },
                });

                return false;
            }
        }),
    ) as Record<Provider, BaseProvider>;
};
