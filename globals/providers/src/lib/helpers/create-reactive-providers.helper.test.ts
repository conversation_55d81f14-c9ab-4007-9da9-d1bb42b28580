import { beforeEach, describe, expect, test, vi } from 'vitest';
import type { BaseProvider } from '../types/base-provider.type';
import type { Provider } from '../types/provider.type';
import type { ProviderType } from '../types/provider-type.type';
import { createReactiveProviders } from './create-reactive-providers.helper';

// Mock providers for testing
const mockProviders = {
    AWS: {
        id: 'AWS' as Provider,
        name: 'Amazon Web Services',
        companyName: 'Amazon',
        companyUrl: 'https://aws.amazon.com/',
        logo: 'aws-logo.svg',
        providerTypes: ['CSPM' as ProviderType],
        getIsEnabled: () => true,
    } as BaseProvider,
    GOOGLE: {
        id: 'GOOGLE' as Provider,
        name: 'Google',
        companyName: 'Google',
        companyUrl: 'https://google.com/',
        logo: 'google-logo.svg',
        providerTypes: ['CSPM' as ProviderType],
        getIsEnabled: () => false,
    } as Base<PERSON>rovider,
    OKTA: {
        id: 'OKTA' as Provider,
        name: 'Ok<PERSON>',
        companyName: 'Okta',
        companyUrl: 'https://okta.com/',
        logo: 'okta-logo.svg',
        providerTypes: ['IDENTITY_MANAGEMENT' as ProviderType],
        // No getIsEnabled function - should be included by default
    } as BaseProvider,
} as Record<Provider, BaseProvider>;

describe('createReactiveProviders', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        vi.spyOn(console, 'warn').mockImplementation(() => {
            // Empty implementation for testing
        });
    });

    test('should create a reactive providers object', () => {
        const reactiveProviders = createReactiveProviders(mockProviders);

        expect(reactiveProviders).toBeDefined();
        expect(typeof reactiveProviders).toBe('object');
    });

    test('should include enabled providers', () => {
        const reactiveProviders = createReactiveProviders(mockProviders);

        expect(reactiveProviders.AWS).toStrictEqual(mockProviders.AWS);
        expect(reactiveProviders.OKTA).toStrictEqual(mockProviders.OKTA);
    });

    test('should exclude disabled providers', () => {
        const reactiveProviders = createReactiveProviders(mockProviders);

        expect(reactiveProviders.GOOGLE).toBeUndefined();
    });

    test('should work with Object.keys()', () => {
        const reactiveProviders = createReactiveProviders(mockProviders);
        const keys = Object.keys(reactiveProviders);

        expect(keys).toContain('AWS');
        expect(keys).toContain('OKTA');
        expect(keys).not.toContain('GOOGLE');
        expect(keys).toHaveLength(2);
    });

    test('should work with Object.values()', () => {
        const reactiveProviders = createReactiveProviders(mockProviders);
        const values = Object.values(reactiveProviders);

        expect(values).toHaveLength(2);
        expect(values).toContainEqual(mockProviders.AWS);
        expect(values).toContainEqual(mockProviders.OKTA);
        expect(values).not.toContainEqual(mockProviders.GOOGLE);
    });

    test('should work with "in" operator', () => {
        const reactiveProviders = createReactiveProviders(mockProviders);

        expect('AWS' in reactiveProviders).toBeTruthy();
        expect('OKTA' in reactiveProviders).toBeTruthy();
        expect('GOOGLE' in reactiveProviders).toBeFalsy();
    });

    test('should work with Object.getOwnPropertyDescriptor()', () => {
        const reactiveProviders = createReactiveProviders(mockProviders);

        const awsDescriptor = Object.getOwnPropertyDescriptor(
            reactiveProviders,
            'AWS',
        );

        expect(awsDescriptor).toStrictEqual({
            enumerable: true,
            configurable: true,
            writable: false,
            value: mockProviders.AWS,
        });

        const googleDescriptor = Object.getOwnPropertyDescriptor(
            reactiveProviders,
            'GOOGLE',
        );

        expect(googleDescriptor).toBeUndefined();
    });

    test('should handle empty providers object', () => {
        const emptyProviders = {} as Record<Provider, BaseProvider>;
        const reactiveProviders = createReactiveProviders(emptyProviders);

        expect(Object.keys(reactiveProviders)).toHaveLength(0);
        expect(Object.values(reactiveProviders)).toHaveLength(0);
    });
});
