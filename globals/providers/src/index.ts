// Types
import * as _providers from './lib/constants/providers';
import { createReactiveProviders } from './lib/helpers/create-reactive-providers.helper';
import type { BaseProvider } from './lib/types/base-provider.type';
import type { Provider } from './lib/types/provider.type';

export * as providerTypes from './lib/constants/providerTypes';
export { createReactiveProviders } from './lib/helpers/create-reactive-providers.helper';
export { getEnabledProviders } from './lib/helpers/get-enabled-providers.helper';
export { getProviderTypeName } from './lib/helpers/get-provider-type-name.helper';
export { isProviderType } from './lib/helpers/is-provider-type.helper';
export type * from './lib/types/base-provider.type';
export type * from './lib/types/base-provider-type.type';
export type * from './lib/types/provider.type';
export type * from './lib/types/provider-feature.type';
export type * from './lib/types/provider-is-enabled.type';
export type * from './lib/types/provider-type.type';

// Create reactive providers that automatically update when feature flags change
export const providers: Record<Provider, BaseProvider> =
    createReactiveProviders(_providers);
