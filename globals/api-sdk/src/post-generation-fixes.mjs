import fs from 'node:fs';
import path from 'node:path';

/**
 * Update a file's contents.
 *
 * @param {string} file -- The file to update.
 * @param {(contents: string) => string} updater -- the function that updates the file.
 */
function updateFile(file, updater) {
    const filePath = path.resolve(import.meta.dirname, file);
    const contents = fs.readFileSync(filePath, 'utf-8');

    fs.writeFileSync(filePath, updater(contents), 'utf-8');
}

updateFile('./lib/generated/core/bodySerializer.ts', (contents) =>
    contents.replace(
        `JSON.stringify(body, (key, value) =>`,
        `JSON.stringify(body, (_, value) =>`,
    ),
);

updateFile('./lib/generated/client/utils.ts', (contents) =>
    contents.replace(
        `  if (cleanContent.startsWith('text/')) {
    return 'text';
  }
};`,
        `  if (cleanContent.startsWith('text/')) {
    return 'text';
  }

  return;
};`,
    ),
);

// Recursively serialize objects for FormData and URLSearchParams
updateFile('./lib/generated/core/bodySerializer.ts', (contents) =>
    contents
        .replace(
            `const serializeFormDataPair = (data: FormData, key: string, value: unknown) => {
  if (typeof value === 'string' || value instanceof Blob) {
    data.append(key, value);
  } else {
    data.append(key, JSON.stringify(value));
  }
};`,
            `const serializeFormDataPair = (data: FormData, key: string, value: unknown) => {
  if (value === undefined || value === null) {
    return;
  }

  if (value instanceof Blob) {
    data.append(key, value);
    return;
  }

  if (Array.isArray(value)) {
    value.forEach((v, i) => serializeFormDataPair(data, key + '[' + i + ']', v));
    return;
  }

  if (typeof value === 'object') {
    Object.entries(value).forEach(([k, v]) => {
      serializeFormDataPair(data, key + '[' + k + ']', v);
    });
    return;
  }

  if (typeof value === 'bigint') {
    data.append(key, value.toString());
    return;
  }

  data.append(key, String(value));
};`,
        )
        .replace(
            `const serializeUrlSearchParamsPair = (
  data: URLSearchParams,
  key: string,
  value: unknown,
) => {
  if (typeof value === 'string') {
    data.append(key, value);
  } else {
    data.append(key, JSON.stringify(value));
  }
};`,
            `const serializeUrlSearchParamsPair = (
  data: URLSearchParams,
  key: string,
  value: unknown,
) => {
  if (value === undefined || value === null) {
    return;
  }

  if (Array.isArray(value)) {
    value.forEach((v, i) => serializeUrlSearchParamsPair(data, key + '[' + i + ']', v));
    return;
  }

  if (typeof value === 'object') {
    Object.entries(value).forEach(([k, v]) => {
      serializeUrlSearchParamsPair(data, key + '[' + k + ']', v);
    });
    return;
  }

  if (typeof value === 'bigint') {
    data.append(key, value.toString());
    return;
  }

  data.append(key, String(value));
};`,
        ),
);

// Ensure top-level array iteration includes indices for both serializers
// Special handling for keys ending with [] to avoid double indexing
// For single file uploads, don't add array indices to maintain backend compatibility
updateFile('./lib/generated/core/bodySerializer.ts', (contents) =>
    contents
        .replace(
            'value.forEach((v) => serializeFormDataPair(data, key, v));',
            'value.forEach((v, i) => {\n' +
                "        // For single file uploads, don't add array indices\n" +
                '        if (value.length === 1 && v instanceof Blob) {\n' +
                '          serializeFormDataPair(data, key, v);\n' +
                '        } else {\n' +
                '          serializeFormDataPair(data, key.endsWith("[]") ? key : key + "[" + i + "]", v);\n' +
                '        }\n' +
                '      });',
        )
        .replace(
            'value.forEach((v) => serializeUrlSearchParamsPair(data, key, v));',
            'value.forEach((v, i) => serializeUrlSearchParamsPair(data, key.endsWith("[]") ? key : key + "[" + i + "]", v));',
        ),
);

updateFile('./lib/generated/client.gen.ts', (contents) => {
    let updatedContents = contents.replace(
        "import { createClientConfig } from '../../fetch-client';",
        "import { createClientConfig, afterClientSetup } from '../../fetch-client';",
    );

    updatedContents = `${updatedContents}\n\nawait Promise.resolve(afterClientSetup(client));\n`;

    return updatedContents;
});

/**
 * Generate API endpoints data file from the React Query generated file.
 */
function generateApiEndpointsData() {
    const reactQueryPath = path.resolve(
        import.meta.dirname,
        './lib/generated/@tanstack/react-query.gen.ts',
    );
    const outputPath = path.resolve(
        import.meta.dirname,
        './lib/generated/api-endpoints.json',
    );

    try {
        const contents = fs.readFileSync(reactQueryPath, 'utf-8');

        // Match all exported endpoint functions
        const endpointRegex =
            /export const ([a-zA-Z]+Controller)([a-zA-Z]+)(Options|Mutation)/g;
        const matches = [...contents.matchAll(endpointRegex)];

        /** @type {Object.<string, {queries: Array<{name: string, operation: string}>, mutations: Array<{name: string, operation: string}>}>} */
        const endpoints = {};
        const summary = {
            totalEndpoints: 0,
            totalControllers: 0,
            totalQueries: 0,
            totalMutations: 0,
        };

        matches.forEach((match) => {
            const [, controller, operation, type] = match;

            if (!Object.hasOwn(endpoints, controller)) {
                endpoints[controller] = {
                    queries: [],
                    mutations: [],
                };
            }

            const endpointData = {
                name: `${controller}${operation}${type}`,
                operation:
                    operation.charAt(0).toLowerCase() + operation.slice(1),
            };

            if (type === 'Options') {
                endpoints[controller].queries.push(endpointData);
                summary.totalQueries = summary.totalQueries + 1;
            } else {
                endpoints[controller].mutations.push(endpointData);
                summary.totalMutations = summary.totalMutations + 1;
            }
            summary.totalEndpoints = summary.totalEndpoints + 1;
        });

        summary.totalControllers = Object.keys(endpoints).length;

        // Sort controllers and their endpoints
        /** @type {Object.<string, {queries: Array<{name: string, operation: string}>, mutations: Array<{name: string, operation: string}>}>} */
        const sortedEndpoints = {};
        const sortedKeys = Object.keys(endpoints).sort((a, b) =>
            a.localeCompare(b),
        );

        sortedKeys.forEach((controller) => {
            sortedEndpoints[controller] = {
                queries: endpoints[controller].queries.toSorted((a, b) =>
                    a.name.localeCompare(b.name),
                ),
                mutations: endpoints[controller].mutations.toSorted((a, b) =>
                    a.name.localeCompare(b.name),
                ),
            };
        });

        const data = {
            generated: new Date().toISOString(),
            endpoints: sortedEndpoints,
            summary,
        };

        fs.writeFileSync(outputPath, JSON.stringify(data, null, 2), 'utf-8');
        console.info(`✅ Generated API endpoints data: ${outputPath}`);
        console.info(
            `   Found ${summary.totalEndpoints} endpoints across ${summary.totalControllers} controllers`,
        );
    } catch (error) {
        console.error('❌ Failed to generate API endpoints data:', error);
    }
}

// Generate the API endpoints data file
generateApiEndpointsData();
