name: "Build and Deploy - Multiverse Nonprod"
on:
  workflow_dispatch:
    inputs:
      devops:
          description: 'Deploy to devops'
          required: true
          default: false
          type: boolean
      dev:
          description: 'Deploy to dev'
          required: true
          default: false
          type: boolean
      dev-01:
          description: 'Deploy to dev-01'
          required: true
          default: false
          type: boolean
      dev-02:
          description: 'Deploy to dev-02'
          required: true
          default: false
          type: boolean
      dev-03:
          description: 'Deploy to dev-03'
          required: true
          default: false
          type: boolean
      dev-04:
          description: 'Deploy to dev-04'
          required: true
          default: false
          type: boolean
      qa:
          description: 'Deploy to qa'
          required: true
          default: false
          type: boolean
  schedule:
    # Runs at minute 0 and 30 past every hour
    - cron: '*/30 * * * *'

    # TODO: Eventually add back in slack messages.
concurrency:
  group: "${{ github.workflow }}-${{ github.ref }}"
  cancel-in-progress: true

env:
  APPLICATION: "web-multiverse"
  NPM_TOKEN: ${{secrets.NPM_REGISTRY_RO_PAT}}
  region: "us-west-2"
  # SLACK_CHANNEL: "C01FV955EKB" # deployments
  # SLACK_CHANNEL_FAILOVER: "C08ES6KH332" # team-drockstars-alerts
jobs:
  get_branch_info:
    name: "Get Branch Info"
    runs-on: ubuntu-latest
    outputs:
      branch: ${{ steps.determine-trigger.outputs.branch }}
      dynamodb-branch: ${{ steps.determine-trigger.outputs.dynamodb-branch }}
      short-sha: ${{ steps.short-sha.outputs.short-sha }}
    steps:
      - name: "Set friendly branch name"
        id: "branch-name"
        uses: "drata/utils-actions/get-friendly-branch-name@1.0.0"

      - name: "Determine Trigger Source"
        id: "determine-trigger"
        run: |
          if [ "${{ github.ref_type }}" == "branch" ]; then
            echo "branch=${{ steps.branch-name.outputs.branch }}" >> $GITHUB_OUTPUT
            echo "dynamodb-branch=${{ steps.branch-name.outputs.branch }}" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref_type }}" == "tag" ]; then
            echo "branch=heads/${GITHUB_REF#refs/tags/}"  >> $GITHUB_OUTPUT
            echo "dynamodb-branch=${GITHUB_REF#refs/tags/}"  >> $GITHUB_OUTPUT
          else
            echo "Unknown trigger source"
            exit 1
          fi
      - name: "Get short SHA"
        id: "short-sha"
        shell: "bash"
        run: echo "short-sha=${GITHUB_SHA::7}" >> $GITHUB_OUTPUT

  check_for_changes:
    name: Check for Changes (${{ matrix.environment }})
    needs: [deployment_matrix, get_branch_info]
    runs-on: 'runs-on=${{ github.run_id }}/runner=app-build/tag=deployment-matrix'
    outputs:
      project-affected: ${{ steps.check-sha.outputs.project-affected }}
    strategy:
      fail-fast: false
      matrix:
        environment: ${{ fromJson(needs.deployment_matrix.outputs.deployment-matrix) }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Assume Infra role
        uses: drata/iam-actions/assume-role@1.2.0
        id: configure-credentials
        with:
            account: infra
            unset-current-credentials: true
      - name: Check for changes
        id: check-sha
        env:
          application: ${{ env.APPLICATION }}
          environment: ${{ matrix.environment }}
          short_sha: ${{ needs.get_branch_info.outputs.short-sha }}
        run: |
          # Check if the workflow was manually triggered
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "Workflow manually executed. Proceeding with deployment."
            echo "project-affected=true" >> $GITHUB_OUTPUT
            exit 0
          fi

          # This part only runs if it's NOT a manual trigger (i.e., scheduled)
          last_deployed_sha=$(aws dynamodb get-item --table-name dt-infra-jenkins-dynamodb-table --key "{\"Environment\":{\"S\":\"${{ env.environment }}\"},\"Application\":{\"S\":\"${{ env.application }}\"}}" | jq -r '.Item.Commit.S')

          echo "Last Deployed SHA for ${{ matrix.environment }}: $last_deployed_sha"
          echo "Current SHA: $short_sha"

          if [[ "$last_deployed_sha" == "$short_sha" ]]; then
            echo "No changes for ${{ matrix.environment }}. Skipping build."
            echo "project-affected=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected for ${{ matrix.environment }}. Proceeding with deployment."
            echo "project-affected=true" >> $GITHUB_OUTPUT
          fi

  deployment_matrix:
    name: Set deployment matrix
    needs: get_branch_info
    runs-on: 'runs-on=${{ github.run_id }}/runner=app-build/tag=deployment-matrix'
    timeout-minutes: 60
    outputs:
      deployment-matrix: ${{ steps.deployment-matrix.outputs.matrix }}
      # TODO: Add back slack message item here
    steps:
      - name: Assume Infra role
        uses: drata/iam-actions/assume-role@1.2.0
        id: configure-credentials
        if: ${{ !(github.event_name == 'workflow_call') }}
        with:
            account: infra
            unset-current-credentials: true
      - name: Get active branch
        id: active-branch
        if: ${{ !(github.event_name == 'workflow_call') }}
        uses: drata/build-and-deployment-actions/get-active-branch@8595c9863974b30e5d47422ff7bdeb7ce01413a4 # TODO: remove this once the correct way to do this deploy is merged

        with:
            application: ${{ env.APPLICATION }}
      - name: Set deployment matrix
        id: deployment-matrix
        uses: drata/build-and-deployment-actions/set-deployment-matrix@8595c9863974b30e5d47422ff7bdeb7ce01413a4
        with:
            devops: ${{ (github.event_name == 'push' && steps.active-branch.outputs.devops == needs.get_branch_info.outputs.branch) || (github.event.inputs.devops == 'true') }}
            dev: ${{ (github.event_name == 'push' && steps.active-branch.outputs.dev == needs.get_branch_info.outputs.branch) || (github.event.inputs.dev == 'true') }}
            dev-01: ${{ (github.event_name == 'push' && steps.active-branch.outputs.dev-01 == needs.get_branch_info.outputs.branch) || (github.event.inputs.dev-01 == 'true') }}
            dev-02: ${{ (github.event_name == 'push' && steps.active-branch.outputs.dev-02 == needs.get_branch_info.outputs.branch) || (github.event.inputs.dev-02 == 'true') }}
            dev-03: ${{ (github.event_name == 'push' && steps.active-branch.outputs.dev-03 == needs.get_branch_info.outputs.branch) || (github.event.inputs.dev-03 == 'true') }}
            dev-04: ${{ (github.event_name == 'push' && steps.active-branch.outputs.dev-04 == needs.get_branch_info.outputs.branch) || (github.event.inputs.dev-04 == 'true') }}
            qa: ${{ (github.event_name == 'push' && steps.active-branch.outputs.qa == needs.get_branch_info.outputs.branch) || (github.event.inputs.qa == 'true') }}
            main: ${{ needs.get_branch_info.outputs.branch == 'main' || needs.get_branch_info.outputs.branch == 'ENG-67014/set-up-multiverse-for-non-prod-environme' }}

  swagger:
    needs: [deployment_matrix, check_for_changes]
    if: ${{ needs.check_for_changes.outputs.project-affected == 'true' }}
    runs-on: ["runs-on", "runner=app-build", "private=true", "run-id=${{ github.run_id }}"]
    steps:
      - name: "Checkout API Repository"
        uses: "actions/checkout@v4"
        with:
          token: ${{secrets.MULTIVERSE_ACTIONS_PAT}}
          repository: "drata/api"
          ref: "release"
          persist-credentials: true
          submodules: "recursive"
          fetch-depth: 1

      - name: "Install Node"
        uses: "actions/setup-node@v4"
        with:
          node-version-file: ".nvmrc"

      - name: "Cache API npm dependencies"
        uses: "actions/cache@v4"
        id: "api-node-modules-cache"
        with:
          path: "./node_modules"
          key: "${{ runner.os }}-yarn-${{ hashFiles('./yarn.lock') }}"

      - name: "Install API dependencies"
        if: steps.api-node-modules-cache.outputs.cache-hit != 'true'
        run: "yarn install --non-interactive --prefer-offline --frozen-lockfile"

      - name: "Build API Swagger doc"
        run: "NODE_CONFIG_ENV=test yarn run build && NODE_CONFIG_ENV=test SKIP_DB=true ./bin/drata-cli swagger:docs"

      - name: "Upload Artifact"
        uses: "actions/upload-artifact@v4"
        with:
          name: "swagger-json"
          path: "./docs/swagger/swagger.json"
          if-no-files-found: "error"
          retention-days: 1

  build_and_deploy:
    name: "Build and Deploy"
    needs: [swagger, deployment_matrix, check_for_changes, get_branch_info]
    if: ${{ needs.check_for_changes.outputs.project-affected == 'true' }}
    timeout-minutes: 60
    runs-on: ["runs-on", "runner=build-web", "private=true", "run-id=${{ github.run_id }}"]
    concurrency:
        group: ${{ matrix.environment }}
    strategy:
        fail-fast: false
        matrix:
            environment: ${{ fromJson(needs.deployment_matrix.outputs.deployment-matrix) }}
    environment: ${{ matrix.environment }}

    steps:
      - name: "Clone Repository"
        id: "clone-repo"
        uses: "actions/checkout@v4"
        with:
          persist-credentials: true
          fetch-depth: 0

      - name: "Get short region"
        id: "short-region"
        shell: "bash"
        run: echo "short-region=${region::2}" >> $GITHUB_OUTPUT

      - name: "Assume Infra role"
        id: "configure-credentials"
        uses: "drata/iam-actions/assume-role@1.2.0"
        with:
          account: "infra"
          unset-current-credentials: true

      - name: "Setup Node"
        uses: "actions/setup-node@v4"
        with:
          node-version-file: ".nvmrc"

      - name: "Setup pnpm"
        uses: "pnpm/action-setup@v4"

      - name: "Install Dependencies"
        run: "pnpm install --frozen-lockfile"

      - name: "Build Tokens"
        run: "pnpm run tokens"

      - name: "Download Swagger"
        uses: "actions/download-artifact@v4"
        with:
          name: "swagger-json"
          path: "api/docs/swagger"

      - name: "Build API-SDK"
        run: "pnpm run update-api-sdk"
        env:
          API_LOCATION: "../../../api"

      - name: "Build"
        run: "pnpm run app:drata:build"
        env:
          APP_ENV: ${{ matrix.environment }}

      - name: "Deploy CFP"
        id: "deploy-cfp"
        uses: "drata/build-and-deployment-actions/deploy-cloudflare-pages@****************************************" # TODO: remove this once the devops PR is merged in
        with:
          application: ${{ env.APPLICATION }}
          environment: ${{ matrix.environment }}
          directory: "apps/drata/dist/client"
          short-sha: ${{ needs.get_branch_info.outputs.short-sha }}
          sha: ${GITHUB_SHA}
          branch: ${{ needs.get_branch_info.outputs.branch }}
          npm-token: ${{ env.NPM_TOKEN }}
          dynamodb-application: ${{ env.APPLICATION }}
          dynamodb-branch: ${{ needs.get_branch_info.outputs.dynamodb-branch }}
          cfp-project-name: "${{ matrix.environment }}-${{ env.APPLICATION }}-us"
          cloudflare-api-token: ${{ secrets.CLOUDFLARE_PROD_TOKEN }}
