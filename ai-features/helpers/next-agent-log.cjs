#!/usr/bin/env node
/**
 * next-agent-log.cjs
 *
 * Suggests (and optionally creates) the next agents session log file for a feature.
 *
 * Usage:
 *   node ai-features/helpers/next-agent-log.cjs <feature-folder> [--create] [--tz=local|utc]
 *
 * Examples:
 *   node ai-features/helpers/next-agent-log.cjs ai-features/banner-service
 *   node ai-features/helpers/next-agent-log.cjs ai-features/banner-service --create
 *   node ai-features/helpers/next-agent-log.cjs ai-features/banner-service --create --tz=utc
 */

const fs = require('fs');
const path = require('path');

/**
 * Left-pad a number with zeros to a fixed width.
 * @param {number} num
 * @param {number} [size=2]
 * @returns {string}
 */
function pad(num, size = 2) {
    let s = String(num);
    while (s.length < size) s = '0' + s;
    return s;
}

/**
 * Format a compact timestamp string.
 * @param {Date} date
 * @param {string} tz
 * @returns {string}
 */
function formatTimestamp(date, tz) {
    // YYYYMMDD-HHMM in local or UTC
    const useUtc = tz === 'utc';
    const year = useUtc ? date.getUTCFullYear() : date.getFullYear();
    const month = pad((useUtc ? date.getUTCMonth() : date.getMonth()) + 1);
    const day = pad(useUtc ? date.getUTCDate() : date.getDate());
    const hours = pad(useUtc ? date.getUTCHours() : date.getHours());
    const minutes = pad(useUtc ? date.getUTCMinutes() : date.getMinutes());
    return `${year}${month}${day}-${hours}${minutes}`;
}

function humanReadable(date, tz) {
    if (tz === 'utc') {
        return `${date.getUTCFullYear()}-${pad(date.getUTCMonth() + 1)}-${pad(date.getUTCDate())} ${pad(date.getUTCHours())}:${pad(date.getUTCMinutes())} UTC`;
    }
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}`;
}

function findNextN(agentsDir) {
    if (!fs.existsSync(agentsDir)) return 1;
    const files = fs.readdirSync(agentsDir);
    let maxN = 0;
    for (const f of files) {
        const m = /^agent-(\d+)-\d{8}-\d{4}\.md$/.exec(f);
        if (m) {
            const n = parseInt(m[1], 10);
            if (!Number.isNaN(n)) maxN = Math.max(maxN, n);
        }
    }
    return maxN + 1 || 1;
}

function ensureDir(p) {
    if (!fs.existsSync(p)) fs.mkdirSync(p, { recursive: true });
}

function buildTemplate(n, displayTs) {
    return `# Agent ${n} — ${displayTs}\n\n## Plan\n- \n\n## Progress\n- \n\n## Verification\n- Tests: pass/fail/na\n- Lint/Typecheck: pass/fail\n\n## Next\n- [ ] \n`;
}

function main() {
    const args = process.argv.slice(2);
    if (args.length === 0) {
        console.error('Error: missing <feature-folder> argument');
        process.exit(1);
    }
    const featureFolder = args[0].replace(/\\/g, '/');
    const create = args.includes('--create');
    const tzArg = args.find((a) => a.startsWith('--tz='));
    const tz = tzArg ? tzArg.split('=')[1] : 'local';

    const agentsDir = path.join(featureFolder, 'agents');
    const nextN = findNextN(agentsDir);
    const now = new Date();
    const ts = formatTimestamp(now, tz);
    const displayTs = humanReadable(now, tz);
    const filename = `agent-${nextN}-${ts}.md`;
    const fullPath = path.join(agentsDir, filename);

    console.log(`Suggested file: ${fullPath}`);

    if (!create) {
        console.log('Run with --create to write the file.');
        process.exit(0);
    }

    ensureDir(agentsDir);
    if (fs.existsSync(fullPath)) {
        console.error(`Refusing to overwrite existing file: ${fullPath}`);
        process.exit(2);
    }

    const content = buildTemplate(nextN, displayTs);
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`Created: ${fullPath}`);
}

if (require.main === module) {
    try {
        main();
    } catch (err) {
        console.error(err && err.stack ? err.stack : String(err));
        process.exit(1);
    }
}
