# Banner Service — Agent Entry Doc

Purpose: Enable agents to collaborate across sessions on the Banner Service with minimal ramp-up.

## Collaboration flow (ASCII)

```
You → PLAN.md (update) → Work (code/docs/tests) → Verify → Session Summary → Handoff
        ^                                                           |
        └──────────────────────────── Ask user for priorities ──────┘
```

- Update PLAN.md first, then implement
- Keep verification green (tests/lint/typecheck) before handoff
- Add explicit next 1–3 tasks so the next agent can start immediately


## agents/ session logs
- Create a new file at start: `agents/agent-[N]-[timestamp].md`
  - N = next available number (highest existing + 1)
  - timestamp = YYYYMMDD-HHMM (local time)
- Use it to record your plan, progress updates, and final sign-off with next 1–3 tasks
- Before starting, skim the previous agent file (N-1) for notes


## How to use this folder
- Keep PLAN.md up to date before starting work. Update status checkboxes as you go.
- Add concise progress notes and decisions so a new agent can take over quickly.
- Put long historical context in ARCHIVE.md links rather than in the plan.

## Before you start
1) Read `.llm/critical-rules/ai-code-quality-guidelines.md` and `.llm/PATTERN_INDEX.md`
2) Skim README.md in this folder for developer-facing usage and constraints
3) Open PLAN.md and ensure it reflects current state and the next 1–3 tasks
4) Confirm tests and type checks pass locally before deeper changes

## Working principles
- Follow MobX architecture: use the shared controller directly; avoid helper wrappers that bypass reactivity
- No window usage; follow abstractions/controllers
- Keep API simple; prefer explicit LocationBanners usage at integration points
- Preserve backward compatibility; additive changes only unless explicitly approved

## Progress reporting
- Update PLAN.md using checkboxes:
  - [ ] Not started
  - [/] In progress
  - [x] Done
- After each session, add a short "Session Summary" with:
  - What changed (code/docs/tests)
  - Verification performed (tests/linters/builds)
  - Open questions or blockers for next agent

## Handoff checklist (end of your session)
- PLAN.md shows accurate current state and next 1–3 concrete tasks
- Any TODOs are explicit and searchable
- Tests pass locally (or failing tests are noted with context)
- If you created or moved files, ensure imports/links are updated

## When unsure
- Ask the user for priorities before making broad changes
- Prefer minimal safe changes and iterate

## Useful commands
- Quick start: `pnpm run qs`
- Unit tests: `pnpm run test`
- Lint: `pnpm run lint`
- Typecheck: `pnpm run typecheck`

## Links
- Developer README: ./README.md
- Plan: ./PLAN.md
- Features (agent index): ./features/AGENTS.md
- Historical archive: ./ARCHIVE.md

