# Banner Service — Developer Quick Start

Welcome! This folder documents the production Banner Service used across Multiverse. It focuses on how human developers use the feature in day-to-day work.

## What this is
- Central MobX-powered service that manages banners anywhere in the app
- Direct UI usage via <LocationBanners/> component
- Clean API for adding/removing/dismissing banners through the shared controller

## Architecture at a glance (ASCII)

```
+-------------------------------------------+
|      sharedBannerServiceController (MobX) |
|  - addBanner/removeBanner/dismissBanner   |
|  - route-scoped + timers + persistence    |
+---------------------------+---------------+
                            ^  observable state
                            |
                            |
+---------------------------v---------------------------+
|            <LocationBanners/> (observer)             |
|  - reads banners for a specific BannerLocation       |
|  - renders Cosmos Banner stack                       |
|  - calls controller.dismissBanner on close           |
+---------------------------+---------------------------+
                            |
                            v
                Cosmos Banner UI components
```

Key idea: components observe the controller; you add banners via the controller from anywhere, and the UI updates automatically.

## When to use banners
- Communicate contextual information at APP_HEADER, PAGE_HEADER, or DOMAIN_CONTENT areas
- Prefer banners (persistent/route/timer) for prominent notices; use snackbars for brief toasts

## Imports
- LocationBanners: `import { LocationBanners } from '@components/location-banners'`
- Banner enums/types/controller: `import { BannerLocation, BannerPersistenceType, sharedBannerServiceController } from '@controllers/banner-service'`

## Quick start
1) Render banners at a location (place once per layout area):

```tsx
import { LocationBanners } from '@components/location-banners';
import { BannerLocation } from '@controllers/banner-service';

<LocationBanners location={BannerLocation.APP_HEADER} dataIdPrefix="app-header" />
```

2) Add a banner from anywhere:

```ts
import { BannerLocation, BannerPersistenceType, sharedBannerServiceController } from '@controllers/banner-service';

sharedBannerServiceController.addBanner({
  id: 'welcome-1',
  title: 'Welcome!',
  body: 'Thanks for visiting.',
  severity: 'primary',
  location: BannerLocation.APP_HEADER,
  persistenceType: BannerPersistenceType.TIMER_BASED,
  autoHideDuration: 5000,
});
```

3) Dismiss/remove:

```ts
sharedBannerServiceController.dismissBanner('welcome-1');
// or
sharedBannerServiceController.removeBanner('welcome-1');
```

## Route-scoped and reactive banners
- For route-dependent banners, use `persistenceType: ROUTE_SCOPED` so they auto-clear on navigation
- Client route modules may declare reactive banners via a function in clientLoader (MobX-computed friendly)

Example (route-level):
## Visual recipes (ASCII)

1) Where banners render
```
App layout
┌──────────────────────────────────────────────┐
│ APP_HEADER                                  │  <- LocationBanners(APP_HEADER)
├──────────────────────────────────────────────┤
│ PAGE_HEADER                                  │  <- LocationBanners(PAGE_HEADER)
├──────────────────────────────────────────────┤
│ DOMAIN_CONTENT                               │  <- LocationBanners(DOMAIN_CONTENT)
└──────────────────────────────────────────────┘
```

2) Lifecycle overview
```
addBanner() ─┐
             v
   [Controller state] ────> <LocationBanners/> (observer) ───> Cosmos Banner
             ^                          │                          │
             └──── dismissBanner() ◄────┘                      user clicks X
```


```ts
// inside route file clientLoader return value
banners: () => {
  const shouldShow = /* any MobX-observable predicate */ true;
  return shouldShow
    ? [{ id: 'route-tip', title: 'Tip', severity: 'education', location: BannerLocation.PAGE_HEADER, persistenceType: BannerPersistenceType.ROUTE_SCOPED }]
    : [];
}

3) Route-scoped reactive flow (clientLoader)
```
clientLoader
  └─ banners(): () => Banner[]   (MobX-reactive)
             │
             ├─ condition true → returns [Banner]
             │
             └─ condition false → returns []

Route render
  └─ <LocationBanners location=PAGE_HEADER>
           │
           ├─ observes controller.banners + route match
           └─ renders/removes banners accordingly

Navigation
  └─ route changes → controller auto-clears ROUTE_SCOPED banners
```

4) Enum → Location mapping
```
BannerLocation.APP_HEADER     → App-level header region (full-width)
BannerLocation.PAGE_HEADER    → Page header area (above title/actions)
BannerLocation.DOMAIN_CONTENT → Main page content area
```

5) Animation timing coordination (approx.)
```
AnimatedBanner exit (component): ~500ms fade/scale/height
Controller cleanup (onClose):   ~600ms after dismiss starts

Timeline (ms):
0 ───────────────────── 500 ───────────────────────────── 600
   user clicks X            visual exit completes            controller removes
```




## Key conventions in this repo
- Use <LocationBanners/> directly; do not wrap with vanity components
- Prefer FIFO/time-based ordering on display; priority may be read but ordering is local to the UI
- Centralize timing constants in banner-constants; do not hardcode durations in components
## Integration placement decisions
- APP_HEADER: render at very top of the application, full viewport width (above header content)
- PAGE_HEADER: render above the page header (before title/breadcrumbs/actions)
- DOMAIN_CONTENT: render within main page content area

## Key timings/constants
- Entry animation (component): centralized constant, e.g., BANNER_ENTRY_ANIMATION_MS ≈ 700ms
- Exit animation (component): ~500ms fade/scale/height collapse
- Controller cleanup after dismiss: ~600ms to allow coordinated exit
- Keep durations centralized in banner-constants and avoid hardcoding in components

- Use data-id prefixes consistently (e.g., "app-header", "page-header", "domain-content")
- Avoid window usage; follow controller patterns; keep MobX reactivity intact (no helper wrappers that bypass controller)
- In React components, destructure props; avoid `props.` access

## Testing and tooling
- Run tests: `pnpm run test`
- Lint: `pnpm run lint`
- Typecheck: `pnpm run typecheck`
- Storybook (if needed): `pnpm run storybook`

## Capabilities snapshot
- Locations: APP_HEADER, PAGE_HEADER, DOMAIN_CONTENT
- Persistence: PERSISTENT, ROUTE_SCOPED, TIMER_BASED, SESSION_SCOPED
- Stacked rendering with coordinated entry/exit animations
- i18n-ready aria labels and content
- Features: ./features/reactive-banners/README.md


## Links
- Features index (agents): ./features/AGENTS.md

- Controller: controllers/banner-service/
- UI: components/location-banners/
- Agent docs for collaboration: ./AGENTS.md
- Plan/current status: ./PLAN.md
- Historical detail (archived): see ARCHIVE.md

