# Agents Folder — Session Logs

Purpose: Chronological, per-agent session logs to improve handoffs.

## Naming
- agent-[N]-[timestamp].md
  - N = next number after the highest existing file
  - timestamp = YYYYMMDD-HHMM (local time)

## What to put in each file
- Session plan (before work starts)
- Progress updates after major changes
- Sign-off summary: status, next 1–3 tasks, verification state

## Tips
- Keep it concise and useful for the next agent

## Helper (optional)
- Suggest next file (no write): `node ai-features/helpers/next-agent-log.cjs ai-features/banner-service`
- Create your file: `node ai-features/helpers/next-agent-log.cjs ai-features/banner-service --create`
- Tip: Run suggest first; only use `--create` when you are the next agent. This prevents accidental extra files.

- Link to the PLAN.md tasks you worked on
- If blocked, write the question you would ask a human reviewer

