# Reactive Banners (Feature)

Concise how-to for reactive route banners via clientLoader banner functions.

## When to use
- <PERSON> needs to show/hide based on MobX state
- Scoped to a route and should auto-clear on navigation

## Quick start
```ts
export const clientLoader = action((): ClientLoader => ({
  banners: () => condition
    ? [createBannerMessage({
        id: 'my-banner',
        title: t`My banner`,
        severity: 'education',
        location: BannerLocation.PAGE_HEADER,
        persistenceType: BannerPersistenceType.ROUTE_SCOPED,
      })]
    : []
}));

## Checklist: add a route-scoped reactive banner
- In your route file, return `banners: () => []` from clientLoader
- Inside the function, read MobX state to decide when to show
- Return createBannerMessage with `persistenceType: ROUTE_SCOPED`
- Verify it renders in PAGE_HEADER (or your location)
- Navigate away and back to confirm auto-clear behavior
- Use `sharedBannerServiceController.dismissBanner(id)` to test manual dismiss + animation

```

## Flow (ASCII)
```
Route loads
  ↓
clientLoader returns banners(): () => Banner[]
  ↓
bannersFromRoutes (computed) calls function
  ↓
function reads MobX state ← state changes
  ↓                       ↖ recompute
returns Banner[]
  ↓
<LocationBanners/> observes controller & route → renders
  ↓
user dismisses → controller dismissBanner → UI exits → controller cleanup
```

## Recipes

### Feature flag
```ts
banners: () => sharedFeatureAccessModel.isNewFeatureEnabled
  ? [createBannerMessage({ id: 'new-feature', title: t`Try the new feature!`, severity: 'primary', location: BannerLocation.PAGE_HEADER, persistenceType: BannerPersistenceType.ROUTE_SCOPED })]
  : []
```

### User role
```ts
banners: () => sharedCurrentUserController.isAdmin
  ? [createBannerMessage({ id: 'admin-tools', title: t`Admin tools available`, severity: 'primary', location: BannerLocation.PAGE_HEADER })]
  : []
```

### Navigation state
```ts
banners: () => !routeController.isSubNavMinimized
  ? [createBannerMessage({ id: 'nav-expanded', title: t`Navigation is expanded`, severity: 'education', location: BannerLocation.PAGE_HEADER, persistenceType: BannerPersistenceType.ROUTE_SCOPED })]
  : []
```

## Troubleshooting
- Function not running: ensure banners is a function on clientLoader return value, not precomputed
- Not appearing on expected route: verify location enum and route matching; use ROUTE_SCOPED for nav-clearing
- Dismiss doesn’t animate: ensure LocationBanners is reading isBannerDismissing via the public method


## Gotchas
- Use ROUTE_SCOPED for nav-clearing behavior; use SESSION_SCOPED for session/refresh clearing
- Avoid manual reactions in loaders; let the computed call the function
- Keep IDs stable per banner intent to allow proper replace/dismiss behavior

## Do / Don’t
- Do: Use clientLoader `banners: () => []` for reactive banners
- Do: Use ROUTE_SCOPED for navigation-clearing banners; SESSION_SCOPED for session-based clearing
- Do: Keep stable IDs per intent so replacements/dismissals behave predictably
- Don’t: Wire manual MobX reactions for banners in loaders/controllers
- Don’t: Use `window`; rely on routeController + sharedBannerServiceController


## Links
- Back to Banner Service: ../../README.md
- Agent index for features: ../AGENTS.md
- Top-level Agent doc: ../../AGENTS.md
- Plan: ../../PLAN.md
- Archive and history: ../../ARCHIVE.md
- Controller API and enums: ../../../controllers/banner-service/
- LocationBanners UI: ../../../components/location-banners/

