# Reactive Banners — Plan and Status

Keep this short and actionable; link deep history in parent ARCHIVE.md or ticket docs.

## Current status
- Reactive banners available via clientLoader banner functions (MobX-reactive)
- Intended usage: route-scoped, state-driven visibility; auto-clears on navigation
- Docs: README with quick start, ASCII flow, and common recipes

## Tasks
- [ ] Add one end-to-end example in a real route demonstrating `banners: () => []`
- [ ] Verify ROUTE_SCOPED auto-clear on navigation with a minimal repro
- [x] Add a tiny troubleshooting section to README (e.g., function not running, banner not appearing)

## Session summaries
### 2025-08-22 — Seeded plan (aa)
- What changed: Created PLAN.md based on feature template
- Verification: N/A (docs only)
- Next: Implement E2E example in a non-invasive demo route and verify auto-clear

## References
- Feature README: ./README.md
- Banner Service Agent index (features): ../AGENTS.md
- Top-level Agent doc: ../../AGENTS.md
- Parent Banner Service docs: ../../README.md
- Archive/History pointers: ../../ARCHIVE.md

