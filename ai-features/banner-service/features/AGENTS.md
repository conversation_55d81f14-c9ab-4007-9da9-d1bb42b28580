# Banner Service — Features Agent Index

Use this index to coordinate nested features under Banner Service.

## Index
- Reactive Banners: ./reactive-banners/README.md

## Conventions
- Each nested feature SHOULD have a README; add a PLAN.md if it grows beyond a single session
- Keep long history in parent ARCHIVE or ticket docs; link instead of duplicating

## Handoff snippet (copy/paste)
```
Feature: <name>
Status: [ ] [/] [x]
Current focus:
- ...
Next 1–3 tasks:
- [ ] ...
Verification:
- Tests: pass/fail/na, Lint/Typecheck: pass/fail
Notes:
- blockers/decisions
```

