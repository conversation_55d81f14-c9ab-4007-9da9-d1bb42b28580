# Banner Service — Archive and Deep History

This file holds links to historical context and long-form decisions. Keep current docs lean; put detailed history here.

## Primary sources
- Original ticket plan (removed after migration): threads-by-ticket/ENG-71496-banner-service.md
  - Use Git history to view the exact contents post-migration
- Related PRs:
  - feat(banner-service): init [ENG-71496] — https://github.com/drata/multiverse/pull/2331 (merged)
  - feat(banner-service): Remix-driven route banners [ENG-73010] — https://github.com/drata/multiverse/pull/2716 (merged)
  - feat(banner-service): conditional route banners init [ENG-73651] — https://github.com/drata/multiverse/pull/3016 (merged)

## Key decisions (pointers)
- Direct <LocationBanners/> usage; no wrappers
- Timings centralized in banner-constants; avoid component hardcoding
- Reactive route banners via clientLoader `banners: () => []` (ROUTE_SCOPED)
- Replace-on-duplicate ID with logging of differing fields


### PR feedback fixes (highlights)
- Centralize entry animation duration: BANNER_ENTRY_ANIMATION_MS = 700 (no hardcoded component durations)
- Localize close button ARIA label: t`Dismiss ${banner.title} banner`
- Controller naming: extracted BannerTimer → banner-timer.controller.ts (BannerTimerController)
- Remove wrapper components; use <LocationBanners/> directly
- Placement: APP_HEADER full-viewport top; PAGE_HEADER above header; DOMAIN_CONTENT in content area
- Type safety: use sharedBannerServiceController.isBannerDismissing(id) instead of internal state access

### Known gotchas
- Don’t set MobX observable maps to false in makeAutoObservable (breaks reactivity)
- Don’t wire manual reactions for banners; use clientLoader `banners: () => []`
- Don’t use window; rely on controllers/abstractions

### Legacy PR threads (resolved)
- Thread 2: Documentation volume — Trimmed READMEs and moved deep history to ARCHIVE
- Thread 5: Performance/render — Verified `isBannerDismissing(id)` usage and observer patterns do not cause unnecessary renders
- Thread 10: Animation implementation details — Clarified comments; aligned timing and placement; coordinated exit behavior
- Thread 12: Unrelated changes — Explained vendor discovery changes as merge conflict resolution; no functional impact to banner service



### Quality snapshot at merge (from final PR/prompt)
- 42 tests passing across controller and components
- Zero TypeScript errors reported for banner-service files
- APP_HEADER, PAGE_HEADER, DOMAIN_CONTENT validated working
- Coordinated animations verified: ~500ms component exit + ~600ms controller cleanup
- Status: production-ready; treat core banner service as stable (do not modify without explicit approval)

## Notes
- Keep README/PLAN lean; put deep history pointers here
- When major architectural decisions change, add a brief pointer here linking to the PRs

