# <Feature Name> — Agents

Purpose: Enable agents to collaborate across sessions with minimal ramp-up.

## Before you start
- Read `.llm/critical-rules/ai-code-quality-guidelines.md` and `.llm/PATTERN_INDEX.md`
- Skim README.md for developer-facing usage
- Ensure PLAN.md reflects current state and next 1–3 tasks
- Confirm tests and type checks pass locally

## Working principles
- Keep changes minimal and safe; iterate
- Ask user for priorities before broad changes
- Keep docs lean; deep history goes to ARCHIVE

## Progress reporting
- Update PLAN.md checkboxes: [ ] / [/] / [x]
- Session Summary: what changed, verification, next steps, questions

## Handoff checklist
- PLAN shows accurate status and next 1–3 tasks
- Tests/lint/typecheck pass (or issues documented)
- Links updated if files moved/added

## Nested features (optional)
- See ./features/AGENTS.md if this feature has nested areas

