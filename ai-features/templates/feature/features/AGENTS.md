# <Feature Name> — Nested Features (Agents Index)

Use this index to coordinate nested feature areas under this feature.

## Index (add as needed)
- <nested-feature-1>/README.md
- <nested-feature-2>/README.md

## Conventions
- Each nested feature SHOULD have a README; add PLAN.md when complexity grows
- Keep long history in parent ARCHIVE or ticket docs

## Handoff snippet (copy/paste)
```
Nested Feature: <name>
Status: [ ] [/] [x]
Current focus:
- ...
Next 1–3 tasks:
- [ ] ...
Verification:
- Tests: pass/fail/na, Lint/Typecheck: pass/fail
Notes:
- blockers/decisions
```

