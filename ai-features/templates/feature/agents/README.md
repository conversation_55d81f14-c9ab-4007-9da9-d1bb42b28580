# Agents — Session Logs Template

Purpose: Define the agents/ directory usage pattern for new features.

## Naming
- agent-[N]-[timestamp].md
  - N = next available number (highest existing + 1)
  - timestamp = YYYYMMDD-HHMM

## Content for each file
- Session plan (before work starts)
- Progress updates after major changes
- Sign-off summary: status, next 1–3 tasks, verification state

## Tips
- Keep it concise and useful for the next agent
- Link to PLAN.md tasks
- Add blockers/questions explicitly if you need a human decision

