# <Feature Name> — Developer Quick Start

Short, actionable, visual. Keep deeper history in ARCHIVE.md.

## What this is
- One or two bullets on the capability
- The simplest usage path (component or API)

## Architecture at a glance (ASCII)
```
+ Component/API → Controller/Service → UI
```

## Quick start
```ts
// minimal example
```

## Capabilities snapshot
- Bullet list of supported behaviors (kept current to avoid regression)

## Conventions
- List a few project-specific rules relevant here

## Testing/Verification
- Commands to run tests/lint/typecheck

## Links
- AGENTS: ./AGENTS.md
- PLAN: ./PLAN.md
- ARCHIVE: ./ARCHIVE.md
- Nested features: ./features/AGENTS.md (if used)

