# AI Features — START HERE (Agent Guide)

Use this guide when starting a new AI-driven feature or sub-feature plan.

## TL;DR flow (ASCII)
```
Decide feature → Copy template → Rename → Fill README/AGENTS/PLAN → Work → Verify → Session Summary
```

## Steps to start a new feature
1) Choose a folder name under ai-features/, e.g., `ai-features/my-feature`
2) Copy the template from `ai-features/templates/feature/` into your new folder
3) Fill out:
   - README.md: developer-facing quick start and usage
   - AGENTS.md: how agents should work on this feature (before you start, progress, handoff)
   - PLAN.md: current status and next 1–3 tasks (keep it short)
   - ARCHIVE.md: link to long-form history (tickets/PRs/old docs) — don’t duplicate
4) Create `agents/` folder and start your session log: `agents/agent-1-YYYYMMDD-HHMM.md`
   - Optional helper: `node ai-features/helpers/next-agent-log.cjs <feature-folder> --create`
5) If your feature will have nested feature areas, add a `features/AGENTS.md` index to list them
6) Keep docs lean and visual (ASCII diagrams preferred for GitHub)

## Working principles
- Update PLAN before work; keep verification green (tests/lint/typecheck)
- Ask the user for priorities before broad changes
- Prefer minimal safe changes and iterate
- Keep deep history outside the quick-start docs; link via ARCHIVE

## Template contents
- templates/feature/README.md — Developer quick start skeleton
- templates/feature/AGENTS.md — Agent collaboration skeleton
- templates/feature/PLAN.md — Short plan/status with session summaries
- templates/feature/ARCHIVE.md — Where to link long-form history
- templates/feature/agents/README.md — How to use agents/ session logs
- templates/feature/features/AGENTS.md — Index for nested features (optional)

## After creating a feature
- Verify links work in GitHub preview
- Add ASCII diagrams for flows and architecture
- Keep the top-level README focused on developer usage; put lengthy context in ARCHIVE

## Questions
If anything is unclear, add a short note in PLAN.md and proceed with the smallest safe next step.

