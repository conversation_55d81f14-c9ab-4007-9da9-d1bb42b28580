import { useState } from 'react';
import { sharedRiskCategoriesController } from '@controllers/risk';
import { But<PERSON> } from '@cosmos/components/button';
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { TextField } from '@cosmos/components/text-field';
import { dimension2xl } from '@cosmos/constants/tokens';
import { PaginationControls } from '@cosmos-lab/components/pagination-controls';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const RiskSettingsCategoriesView = observer((): React.JSX.Element => {
    const {
        categories,
        addCategoryWithValidation,
        updateNewCategoryName,
        deleteCategoryWithConfirmation,
        newCategoryName,
        validationError,
        isCreatingCategory,
        isDeletingCategory,
        isLoading,
        hasError,
    } = sharedRiskCategoriesController;

    const { hasRiskManagePermission } = sharedFeatureAccessModel;

    const [currentPage, setCurrentPage] = useState(DEFAULT_PAGE);
    const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

    const options = categories.map((category) => ({
        id: String(category.id),
        label: category.name,
        value: String(category.id),
    }));

    const totalItems = options.length;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const currentItems = options.slice(startIndex, endIndex);

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        updateNewCategoryName(event.target.value);
    };

    const handleAddCategory = () => {
        addCategoryWithValidation();
    };

    const handleDeleteCategory = (categoryId: string, categoryName: string) => {
        deleteCategoryWithConfirmation(categoryId, categoryName);
    };

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
    };

    const handlePageSizeChange = (newPageSize: number) => {
        setPageSize(newPageSize);
        setCurrentPage(DEFAULT_PAGE);
    };

    if (isLoading) {
        return (
            <Stack
                direction="column"
                gap="6x"
                data-id="risk-settings-categories-view"
            >
                {hasRiskManagePermission && (
                    <Grid columns="1fr auto" gap="md" align="end">
                        <Skeleton
                            barCount={1}
                            width="100%"
                            barHeight={dimension2xl}
                        />
                        <Skeleton
                            barCount={1}
                            width="120px"
                            barHeight={dimension2xl}
                        />
                    </Grid>
                )}
                <Stack direction="column" gap="md">
                    <Skeleton
                        barCount={1}
                        width="100%"
                        barHeight={dimension2xl}
                    />
                    <Skeleton
                        barCount={1}
                        width="100%"
                        barHeight={dimension2xl}
                    />
                    <Skeleton
                        barCount={1}
                        width="100%"
                        barHeight={dimension2xl}
                    />
                </Stack>
            </Stack>
        );
    }

    if (hasError) {
        return (
            <Stack
                direction="column"
                gap="6x"
                data-id="risk-settings-categories-view"
            >
                {hasRiskManagePermission && (
                    <Grid columns="1fr auto" gap="md" align="end">
                        <TextField
                            name="new-category"
                            label={t`New category`}
                            value={newCategoryName}
                            data-id="new-category-input"
                            formId="risk-categories-form"
                            onChange={handleInputChange}
                        />
                        <Button
                            label={t`Add category`}
                            level="secondary"
                            data-id="add-category-button"
                            isLoading={isCreatingCategory}
                            a11yLoadingLabel={t`Creating category...`}
                            onClick={handleAddCategory}
                        />
                    </Grid>
                )}
                <Stack direction="column" gap="md" align="center">
                    <Text colorScheme="critical">{t`Failed to load categories`}</Text>
                    <Text size="200" colorScheme="neutral">
                        {t`Please refresh the page to try again.`}
                    </Text>
                </Stack>
            </Stack>
        );
    }

    return (
        <Stack
            direction="column"
            gap="6x"
            data-id="risk-settings-categories-view"
        >
            {hasRiskManagePermission && (
                <Grid
                    columns="1fr auto"
                    gap="md"
                    align={validationError ? 'center' : 'end'}
                >
                    <TextField
                        name="new-category"
                        label={t`New category`}
                        value={newCategoryName}
                        data-id="new-category-input"
                        formId="risk-categories-form"
                        feedback={
                            validationError
                                ? {
                                      type: 'error',
                                      message: validationError,
                                  }
                                : undefined
                        }
                        onChange={handleInputChange}
                    />
                    <Button
                        label={t`Add category`}
                        level="secondary"
                        data-id="add-category-button"
                        isLoading={isCreatingCategory}
                        a11yLoadingLabel={t`Creating category...`}
                        onClick={handleAddCategory}
                    />
                </Grid>
            )}

            <Stack direction="column" gap="lg">
                <StackedList
                    data-id="risk-categories-list"
                    aria-label={t`Risk categories`}
                    data-testid="CategoriesContent"
                >
                    {currentItems.map((category) => (
                        <StackedListItem
                            key={category.id}
                            data-id="risk-category-item"
                            primaryColumn={<Text>{category.label}</Text>}
                            action={
                                hasRiskManagePermission && (
                                    <Button
                                        isIconOnly
                                        label={t`Delete category`}
                                        level="tertiary"
                                        startIconName="Trash"
                                        colorScheme="danger"
                                        isLoading={isDeletingCategory}
                                        a11yLoadingLabel={t`Deleting category...`}
                                        onClick={() => {
                                            handleDeleteCategory(
                                                category.id,
                                                category.label,
                                            );
                                        }}
                                    />
                                )
                            }
                        />
                    ))}
                </StackedList>

                {totalItems > pageSize && (
                    <PaginationControls
                        data-id="risk-categories-pagination"
                        total={totalItems}
                        pageSize={pageSize}
                        initialPage={currentPage}
                        onPageChange={handlePageChange}
                        onPageSizeChange={handlePageSizeChange}
                    />
                )}
            </Stack>
        </Stack>
    );
});
