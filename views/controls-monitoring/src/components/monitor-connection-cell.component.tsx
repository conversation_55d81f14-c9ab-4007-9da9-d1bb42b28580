import { isEmpty, isNil } from 'lodash-es';
import { useEffect, useMemo } from 'react';
import { sharedConnectionsController } from '@controllers/connections';
import { WorkspaceMonitorsController } from '@controllers/workspace-monitors';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { OrganizationStack } from '@cosmos-lab/components/organization-stack';
import type { ClientTypeEnum } from '@globals/api-sdk/types';
import { logger } from '@globals/logger';
import { observer } from '@globals/mobx';
import { providers } from '@globals/providers';
import { generateFallbackText } from '@helpers/formatters';
import type { ControlsMonitoringCellProps } from '../types/controls-monitoring-cell.type';

const MAX_VISIBLE_ITEMS = 3;

// TODO: This should probably all use the same code as the monitoring view?
export const MonitorConnectionCell = observer(
    ({ row }: ControlsMonitoringCellProps): React.JSX.Element => {
        const { allConfiguredConnections } = sharedConnectionsController;
        const monitorOverviewController = useMemo(
            () => new WorkspaceMonitorsController(),
            [],
        );

        const {
            isOverviewLoading,
            loadWorkspaceMonitorTestOverview,
            workspaceMonitorTestOverview,
        } = monitorOverviewController;

        useEffect(() => {
            loadWorkspaceMonitorTestOverview(row.original.testId);
        }, [loadWorkspaceMonitorTestOverview, row.original.testId]);

        if (!workspaceMonitorTestOverview || isOverviewLoading) {
            return <EmptyValue label="—" />;
        }

        const { availableConnections, checkTypes } =
            workspaceMonitorTestOverview;

        if (!availableConnections || isEmpty(availableConnections)) {
            return <EmptyValue label="—" />;
        }

        const connectedClientTypes = allConfiguredConnections.map(
            (configureConnection) => configureConnection.clientType,
        );

        const availableClientTypes = availableConnections.flatMap(
            (availableConnection) => {
                if (
                    connectedClientTypes.includes(
                        availableConnection.clientType as ClientTypeEnum,
                    )
                ) {
                    return availableConnection;
                }

                if (checkTypes.includes('CUSTOM')) {
                    return {
                        clientType: 'CUSTOM',
                    };
                }

                return undefined;
            },
        );

        if (isEmpty(availableClientTypes)) {
            return <EmptyValue label="—" />;
        }

        const organizationData = availableClientTypes.flatMap((connection) => {
            if (!connection) {
                return [];
            }

            const providerInfo = Object.values(providers).find(
                (p) => p.id === connection.clientType,
            );

            if (isNil(providerInfo)) {
                logger.warn(
                    `No provider info found for client type ${connection.clientType}`,
                );

                return {
                    primaryLabel: connection.clientType,
                    imgSrc: providers.CUSTOM.logo,
                    fallbackText: generateFallbackText(connection.clientType),
                };
            }

            return {
                primaryLabel: providerInfo.name,
                imgSrc: providerInfo.logo,
                fallbackText: generateFallbackText(providerInfo.name),
            };
        });

        return (
            <OrganizationStack
                maxVisibleItems={MAX_VISIBLE_ITEMS}
                organizationData={organizationData}
                data-id="Q3-D_rtk"
            />
        );
    },
);
