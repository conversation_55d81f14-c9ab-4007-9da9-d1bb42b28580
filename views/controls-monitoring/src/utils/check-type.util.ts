import type { FailsByCategoryResponseDto } from '@globals/api-sdk/types';

export function getCheckTypeLabel(
    checkType: FailsByCategoryResponseDto['category'],
): string {
    switch (checkType) {
        case 'POLICY': {
            return 'Policy';
        }
        case 'IN_DRATA': {
            return 'In Drata';
        }
        case 'AGENT': {
            return 'Device';
        }
        case 'INFRASTRUCTURE': {
            return 'Infrastructure';
        }
        case 'CUSTOM': {
            return 'Custom';
        }
        case 'VERSION_CONTROL': {
            return 'Version Control';
        }
        case 'IDENTITY': {
            return 'Identity Provider';
        }
        case 'TICKETING': {
            return 'Ticketing';
        }
        case 'OBSERVABILITY': {
            return 'Observability';
        }
        default: {
            return checkType;
        }
    }
}
