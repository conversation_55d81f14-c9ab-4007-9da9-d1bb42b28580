import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';

export const IntroductionStep = (): React.JSX.Element => {
    return (
        <Stack
            data-testid="IntroductionStep"
            data-id="introduction-step"
            direction="column"
            gap="xl"
        >
            <Text type="subheadline" size="400">
                <Trans>
                    Risk management is vital to your compliance program
                </Trans>
            </Text>
            <Stack direction="column" gap="xl">
                <Text>
                    <Trans>
                        Risk management is a systematic process of evaluating
                        potential risks, threats, and vulnerabilities that may
                        affect your organization.
                    </Trans>
                </Text>
                <Text>
                    <Trans>
                        The goal of risk management is to identify, analyze, and
                        prioritize risks in order to develop and implement
                        controls that mitigate those risks.
                    </Trans>
                </Text>
            </Stack>
        </Stack>
    );
};
