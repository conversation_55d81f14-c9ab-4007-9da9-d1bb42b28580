import type { default as React } from 'react';
import { sharedAiSettingsController } from '@controllers/ai-settings';
import { sharedFeatureAnnouncementDismissalsController } from '@controllers/feature-announcement-dismissals';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { AISettingsDescription } from '../components/ai-settings-description.component';
import { sharedVendorsSecurityReviewAiGetStartedController } from '../controllers/vendors-security-review-ai-get-started-controller';
import { closeActiveQuestionnaireAIModal } from '../helpers/open-active-questionnaire-ai-modal.helper';

export const QuestionnaireActiveAIModal = observer((): React.JSX.Element => {
    const handleOptIn = action((): void => {
        sharedAiSettingsController.enableAiSettings(() => {
            sharedFeatureAnnouncementDismissalsController.dismissFeatureAnnouncement(
                'ENABLE_AI',
            );

            closeActiveQuestionnaireAIModal();
        });
    });

    const handleDismiss = action((): void => {
        sharedVendorsSecurityReviewAiGetStartedController.aiDismissalsController.dismissAiGetStartedAnnouncement();
        closeActiveQuestionnaireAIModal();
    });

    const isLoading =
        sharedFeatureAnnouncementDismissalsController
            .dismissAnnouncementMutation.isPending ||
        sharedAiSettingsController.isLoading;

    return (
        <>
            <Modal.Header
                title={t`Opt in to using AI`}
                closeButtonAriaLabel={t`Close`}
                onClose={closeActiveQuestionnaireAIModal}
            />
            <Modal.Body>
                <AISettingsDescription />
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Not now`,
                        level: 'secondary',
                        onClick: handleDismiss,
                        cosmosUseWithCaution_isDisabled: isLoading,
                    },
                    {
                        label: t`Opt in to AI`,
                        level: 'primary',
                        onClick: handleOptIn,
                        isLoading,
                    },
                ]}
            />
        </>
    );
});
