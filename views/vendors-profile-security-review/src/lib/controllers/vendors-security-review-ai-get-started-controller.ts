import { FeatureAnnouncementDismissalsController } from '@controllers/feature-announcement-dismissals';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { makeAutoObservable } from '@globals/mobx';

class VendorsSecurityReviewAiGetStartedController {
    aiDismissalsController = new FeatureAnnouncementDismissalsController();

    constructor() {
        makeAutoObservable(this);
    }

    loadAiDismissals(): void {
        this.aiDismissalsController.loadFeatureDismissal({
            query: { type: 'ENABLE_AI' },
        });
    }

    dismissAiGetStartedAnnouncement(): void {
        this.aiDismissalsController.dismissAiGetStartedAnnouncement();
    }

    get isDataReady(): boolean {
        return (
            !sharedCurrentCompanyController.isLoading &&
            !this.aiDismissalsController.isLoading &&
            sharedCurrentCompanyController.isReady &&
            this.aiDismissalsController.featureDismissalData !== null
        );
    }

    get isAiAnnouncementDismissed(): boolean {
        const dismissals =
            this.aiDismissalsController.featureDismissalData
                ?.featureAnnouncementDismissals;

        return Boolean(
            dismissals?.some((dismissal) => dismissal.type === 'ENABLE_AI'),
        );
    }

    get hasRequiredPermissionsAndFeatures(): boolean {
        return (
            sharedFeatureAccessModel.hasVendorManagePermission &&
            sharedFeatureAccessModel.isVendorRiskManagementProEnabled &&
            !sharedCurrentCompanyController.isSettingEnabled('AI_ENABLED')
        );
    }

    /**
     * Main getter that determines if the AI Get Started banner should be displayed.
     * Combines all the validation conditions using private getter methods.
     *
     * @returns True if the banner should be shown, false otherwise.
     */
    get shouldDisplayBanner(): boolean {
        return (
            this.isDataReady &&
            !this.isAiAnnouncementDismissed &&
            this.hasRequiredPermissionsAndFeatures
        );
    }

    get isLoading(): boolean {
        return (
            sharedCurrentCompanyController.isLoading ||
            this.aiDismissalsController.isLoading
        );
    }
}

export const sharedVendorsSecurityReviewAiGetStartedController =
    new VendorsSecurityReviewAiGetStartedController();
