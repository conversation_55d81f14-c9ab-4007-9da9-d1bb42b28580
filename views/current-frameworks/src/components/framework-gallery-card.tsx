import { useMemo } from 'react';
import {
    FrameworkCard,
    type FrameworkCardVariant,
    getMetrics,
    SELECT_FRAMEWORK_LEVEL_MODAL_ID,
    SELECT_FRAMEWORK_PROFILE_MODAL_ID,
    SelectFrameworkLevelModalContent,
    SelectFrameworkProfileModalContent,
    sharedFrameworkReadinessToggleModel,
} from '@components/frameworks';
import { modalController } from '@controllers/modal';
import type { Action } from '@cosmos/components/action-stack';
import { Skeleton } from '@cosmos/components/skeleton';
import type { FrameworkResponseDto } from '@globals/api-sdk/types';
import { useLingui } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { getFrameworkVariantLabel } from '../helpers/get-framework-variant-label.helper';
import { useFrameworkDetailsController } from '../hooks/use-frameworks-details-controller.hook';

const PRERELEASE_FRAMEWORK_TAG: FrameworkResponseDto['tag'][] = ['NIS2'];

export const FrameworkGalleryCard = observer(
    ({ row: framework }: { row: FrameworkResponseDto }): React.JSX.Element => {
        const { t } = useLingui();
        const navigate = useNavigate();

        const { readinessToggle } = sharedFrameworkReadinessToggleModel;

        const frameworksDetailsController = useFrameworkDetailsController(
            framework.id,
        );

        const { needsProfile, needsLevel, isFedramp, frameworkDetails } =
            frameworksDetailsController;

        let variant: FrameworkCardVariant = 'default';

        if (needsProfile) {
            variant = 'pick-profile';
        }

        if (needsLevel) {
            variant = 'pick-level';
        }

        if (isFedramp && needsProfile) {
            variant = 'pick-fedramp-profile';
        }

        if (
            PRERELEASE_FRAMEWORK_TAG.includes(frameworkDetails?.tag ?? 'NONE')
        ) {
            variant = 'prerelease';
        }

        const actionStack = useMemo(() => {
            let actions: Action[] = [];

            switch (variant) {
                case 'pick-fedramp-profile':
                case 'pick-profile': {
                    const defaultLabel = t`Set the baseline`;
                    const label = getFrameworkVariantLabel(
                        frameworkDetails?.tag ?? 'NONE',
                        defaultLabel,
                    );

                    actions = [
                        {
                            actionType: 'button',
                            id: 'framework-card-button-action',
                            typeProps: {
                                label,
                                size: 'md',
                                colorScheme: 'primary',
                                level: 'tertiary',
                                onClick: () => {
                                    modalController.openModal({
                                        id: SELECT_FRAMEWORK_PROFILE_MODAL_ID,
                                        content: () => (
                                            <SelectFrameworkProfileModalContent
                                                data-id="MXT2836R"
                                                frameworksDetailsController={
                                                    frameworksDetailsController
                                                }
                                            />
                                        ),
                                        centered: true,
                                        size: 'md',
                                    });
                                },
                            },
                        },
                    ];
                    break;
                }
                case 'pick-level': {
                    const defaultLabel = t`Set the level`;
                    const label = getFrameworkVariantLabel(
                        frameworkDetails?.tag ?? 'NONE',
                        defaultLabel,
                    );

                    actions = [
                        {
                            actionType: 'button',
                            id: 'framework-card-button-action',
                            typeProps: {
                                label,
                                size: 'md',
                                colorScheme: 'primary',
                                level: 'tertiary',
                                onClick: () => {
                                    modalController.openModal({
                                        id: SELECT_FRAMEWORK_LEVEL_MODAL_ID,
                                        content: () => (
                                            <SelectFrameworkLevelModalContent
                                                data-id="MXT2836R"
                                                frameworksDetailsController={
                                                    frameworksDetailsController
                                                }
                                            />
                                        ),
                                        centered: true,
                                        size: 'md',
                                    });
                                },
                            },
                        },
                    ];
                    break;
                }
                case 'default': {
                    const label = t`Open framework`;

                    actions = [
                        {
                            actionType: 'button',
                            id: 'framework-card-button-action',
                            typeProps: {
                                label,
                                size: 'md',
                                endIconName: 'ChevronRight',
                                colorScheme: 'primary',
                                level: 'tertiary',
                                onClick: () => {
                                    navigate(`${framework.id}`);
                                },
                            },
                        },
                    ];
                    break;
                }
                case 'prerelease': {
                    const defaultLabel = t`Open framework`;
                    const label = getFrameworkVariantLabel(
                        frameworkDetails?.tag ?? 'NONE',
                        defaultLabel,
                    );

                    actions = [
                        {
                            actionType: 'button',
                            id: 'framework-card-button-action',
                            typeProps: {
                                label,
                                size: 'md',
                                endIconName: 'ChevronRight',
                                colorScheme: 'primary',
                                level: 'tertiary',
                                onClick: () => {
                                    navigate(`${framework.id}`);
                                },
                            },
                        },
                    ];
                    break;
                }
            }

            return [
                {
                    id: 'framework-card-action-stack',
                    actions,
                },
            ];
        }, [
            framework.id,
            frameworkDetails?.tag,
            frameworksDetailsController,
            t,
            variant,
            navigate,
        ]);

        const { value, controlsLabel, requirementsLabel } = frameworkDetails
            ? getMetrics(frameworkDetails, readinessToggle === 'control')
            : {
                  value: 0,
                  controlsLabel: '0/0',
                  requirementsLabel: '0/0',
              };

        if (!frameworkDetails) {
            return <Skeleton />;
        }

        return (
            <FrameworkCard
                framework={frameworkDetails}
                value={value}
                controlsLabel={controlsLabel}
                requirementsLabel={requirementsLabel}
                variant={variant}
                data-id="CGk1y-mg"
                actionStack={actionStack}
            />
        );
    },
);
