import { useCallback, useMemo } from 'react';
import {
    sharedVendorsProspectiveController,
    sharedVendorsReportDownloadController,
} from '@controllers/vendors';
import {
    Datatable,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedVendorsProspectiveModel } from '@models/vendors-profile';
import { useNavigate } from '@remix-run/react';
import { getVendorsProspectiveColumns } from './vendors-prospective-table-columns';
import {
    getVendorsProspectiveSearchConfig,
    VENDORS_PROSPECTIVE_INITIAL_SORTING,
    VENDORS_PROSPECTIVE_PAGINATION_CONFIG,
    VENDORS_PROSPECTIVE_TABLE_ID,
} from './vendors-prospective-table-config';

export const VendorsProspectiveView = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const {
        allVendorsProspective,
        isLoading,
        loadVendorsProspective,
        total,
        hasFilters: hasFiltersFromController,
    } = sharedVendorsProspectiveController;

    const handleFetchData = useCallback(
        (params: FetchDataResponseParams): void => {
            loadVendorsProspective(params);
        },
        [loadVendorsProspective],
    );

    // Create dynamic table actions based on current tableParams
    /**
     * Depend on hasFilters from controller.
     */
    const tableActions = useMemo(() => {
        return [
            {
                actionType: 'dropdown' as const,
                id: 'vendors-prospective-table-action-download',
                typeProps: {
                    isIconOnly: false,
                    label: t`Download`,
                    items: [
                        {
                            id: 'vendors-prospective-download-all-vendors',
                            label: t`All vendors`,
                            type: 'item' as const,
                            onClick: action(() => {
                                sharedVendorsReportDownloadController.downloadProspectiveReport();
                            }),
                        },
                        {
                            id: 'vendors-prospective-download-filtered-view',
                            label: t`Filtered view`,
                            type: 'item' as const,
                            isReadOnly: !hasFiltersFromController,
                            readOnlyTooltip: hasFiltersFromController
                                ? ''
                                : t`Apply filters to enable filtered download`,
                            onClick: action(() => {
                                if (!hasFiltersFromController) {
                                    return;
                                }
                                sharedVendorsProspectiveController.downloadFilteredReport();
                            }),
                        },
                    ],
                    level: 'tertiary' as const,
                    startIconName: 'Download' as const,
                },
            },
        ];
    }, [hasFiltersFromController]);

    return (
        <Grid gap="lg" data-testid="VendorsProspectiveView" data-id="K0s0J5Fu">
            <Datatable
                isLoading={isLoading}
                tableId={VENDORS_PROSPECTIVE_TABLE_ID}
                data={allVendorsProspective}
                columns={getVendorsProspectiveColumns()}
                total={total}
                filterProps={sharedVendorsProspectiveModel.filters}
                tableActions={tableActions}
                initialSorting={VENDORS_PROSPECTIVE_INITIAL_SORTING}
                tableSearchProps={getVendorsProspectiveSearchConfig()}
                defaultPaginationOptions={VENDORS_PROSPECTIVE_PAGINATION_CONFIG}
                onFetchData={handleFetchData}
                onRowClick={({ row }) => {
                    navigate(`${row.id}/overview`);
                }}
            />
        </Grid>
    );
});
