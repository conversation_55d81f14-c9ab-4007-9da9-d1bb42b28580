import { isEmpty } from 'lodash-es';
import { RISK_SETTINGS_UI_CONSTANTS } from '@components/risk-settings-scoring';
import {
    sharedRiskSettingsController,
    sharedRiskSettingsScoringController,
} from '@controllers/risk-settings';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form } from '@ui/forms';

export const RiskSettingsScoring = observer((): React.JSX.Element => {
    const { isLoading, riskSettings } = sharedRiskSettingsController;
    const { handleFormSubmit, isSubmitting, formSchema } =
        sharedRiskSettingsScoringController;
    const { hasRiskManagePermission } = sharedFeatureAccessModel;

    if (isLoading) {
        return (
            <Skeleton
                barCount={RISK_SETTINGS_UI_CONSTANTS.SKELETON_BAR_COUNT}
            />
        );
    }

    if (!riskSettings?.thresholds || isEmpty(riskSettings.thresholds)) {
        return (
            <EmptyState
                title={t`Risk Settings Unavailable`}
                description={t`Risk settings could not be loaded. Please try refreshing the page.`}
            />
        );
    }

    return (
        <Stack
            direction="column"
            data-testid={RISK_SETTINGS_UI_CONSTANTS.TEST_ID}
            data-id={RISK_SETTINGS_UI_CONSTANTS.COMPONENT_DATA_ID}
            width="100%"
            gap="6x"
        >
            <Form
                hasExternalSubmitButton
                key={`form-${Object.keys(formSchema).length}`}
                formId={RISK_SETTINGS_UI_CONSTANTS.FORM_ID}
                data-id={RISK_SETTINGS_UI_CONSTANTS.FORM_DATA_ID}
                schema={formSchema}
                isReadOnly={!hasRiskManagePermission}
                onSubmit={handleFormSubmit}
            />
            {hasRiskManagePermission && (
                <Box>
                    <Button
                        type="submit"
                        form={RISK_SETTINGS_UI_CONSTANTS.FORM_ID}
                        label={t`Save settings`}
                        isLoading={isSubmitting}
                        a11yLoadingLabel={t`Saving risk settings...`}
                        data-id="save-settings-button"
                    />
                </Box>
            )}
        </Stack>
    );
});
