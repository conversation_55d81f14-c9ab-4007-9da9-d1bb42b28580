import { Box } from '@cosmos/components/box';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import type { PolicyApprovalReviewGroupConfigurationDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    DEFAULT_TIER_NUMBER,
    FIRST_USER_INDEX,
    MAX_VISIBLE_AVATARS,
    SINGLE_APPROVER_COUNT,
} from '../constants/policy-approval-tier-row.constants';
import {
    formatAvatarStackData,
    formatSingleApproverData,
} from '../helpers/format-avatar-stack-data.helpers';
import { getConsensusRuleLabel } from '../helpers/get-consensus-rule-label.helper';

interface PolicyApprovalTierRowProps {
    reviewGroup: PolicyApprovalReviewGroupConfigurationDto;
    displayTiers: boolean;
}

export const PolicyApprovalTierRow = observer(
    ({ reviewGroup, displayTiers }: PolicyApprovalTierRowProps) => {
        const { userAssignments, consensusRule, timeline, tier, name } =
            reviewGroup;

        const tierNumber = tier ?? DEFAULT_TIER_NUMBER;
        const isSingleApprover =
            userAssignments.length === SINGLE_APPROVER_COUNT;

        return (
            <Stack direction="row" gap="2xl" data-id="tier-row">
                {displayTiers && (
                    <Box width="20%">
                        <KeyValuePair
                            label={t`Tier ${tierNumber}`}
                            value={name}
                            type="TEXT"
                        />
                    </Box>
                )}
                <Box width="20%">
                    <KeyValuePair
                        label={t`Approvers`}
                        type="REACT_NODE"
                        value={
                            isSingleApprover ? (
                                <AvatarIdentity
                                    {...formatSingleApproverData(
                                        userAssignments[FIRST_USER_INDEX],
                                    )}
                                    size="xs"
                                />
                            ) : (
                                <AvatarStack
                                    maxVisibleItems={MAX_VISIBLE_AVATARS}
                                    avatarData={formatAvatarStackData(
                                        userAssignments,
                                    )}
                                />
                            )
                        }
                    />
                </Box>
                <Box width="20%">
                    <KeyValuePair
                        label={t`Level of approval`}
                        value={getConsensusRuleLabel(consensusRule)}
                    />
                </Box>
                <Box flexGrow="1">
                    <KeyValuePair label={t`Time to approve`} value={timeline} />
                </Box>
            </Stack>
        );
    },
);
