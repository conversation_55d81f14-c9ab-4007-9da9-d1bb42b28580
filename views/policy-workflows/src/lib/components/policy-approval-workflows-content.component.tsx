import { PoliciesApprovalEmptyStateComponent } from '@components/policies';
import { Stack } from '@cosmos/components/stack';
import type { PolicyApprovalReviewGroupConfigurationDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { MIN_REVIEW_GROUPS_TO_SHOW_TIERS } from '../constants/policy-approval-tier-row.constants';
import { policyApprovalWorkflowsController } from '../controllers/policy-approval-workflows.controller';
import { PolicyApprovalTierRow } from './policy-approval-tier-row.component';
import { PolicyWorkflowsSkeleton } from './policy-workflows-skeleton.component';

interface PolicyApprovalWorkflowsContentProps {
    reviewGroups: PolicyApprovalReviewGroupConfigurationDto[];
    isLoading: boolean;
}

export const PolicyApprovalWorkflowsContent = observer(
    ({ reviewGroups, isLoading }: PolicyApprovalWorkflowsContentProps) => {
        const { needsWorkflowConfiguration } =
            policyApprovalWorkflowsController;

        if (isLoading) {
            return <PolicyWorkflowsSkeleton />; // TODO: Omar Implement skeleton loading state.
        }

        if (needsWorkflowConfiguration) {
            return <PoliciesApprovalEmptyStateComponent />;
        }

        const displayTiers =
            reviewGroups.length > MIN_REVIEW_GROUPS_TO_SHOW_TIERS;

        return (
            <Stack
                direction="column"
                gap="4x"
                data-testid="approval-workflows-content"
                data-id="approval-content"
            >
                {reviewGroups.map((reviewGroup) => (
                    <PolicyApprovalTierRow
                        key={`tier-${reviewGroup.tier}`}
                        reviewGroup={reviewGroup}
                        displayTiers={displayTiers}
                        data-id="jl1Dr6CP"
                    />
                ))}
            </Stack>
        );
    },
);
