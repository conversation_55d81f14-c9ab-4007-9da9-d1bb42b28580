import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { PolicyApprovalWorkflowsContent } from './components/policy-approval-workflows-content.component';
import { policyApprovalWorkflowsController } from './controllers/policy-approval-workflows.controller';

export const PolicyWorkflowsView = observer((): React.JSX.Element => {
    const { reviewGroups, isLoading } = policyApprovalWorkflowsController;

    return (
        <Box data-testid="PolicyWorkflowsView" data-id="policy-workflows-view">
            <Card
                title={t`Approval`}
                body={
                    <PolicyApprovalWorkflowsContent
                        reviewGroups={reviewGroups}
                        isLoading={isLoading}
                    />
                }
                actions={[
                    {
                        actionType: 'button',
                        id: 'button-action-skeleton',
                        typeProps: {
                            label: t`Edit approval settings`,
                            level: 'secondary',
                        },
                    },
                ]}
            />
        </Box>
    );
});
