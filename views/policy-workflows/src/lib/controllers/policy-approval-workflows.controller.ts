import { isEmpty, isNil } from 'lodash-es';
import { policyApprovalsControllerGetConfigurationOptions } from '@globals/api-sdk/queries';
import type { PolicyApprovalConfigurationPaginatedResponseDto } from '@globals/api-sdk/types';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';

class PolicyApprovalWorkflowsController {
    policyId: number | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    policyApprovalConfigurationQuery = new ObservedQuery(
        policyApprovalsControllerGetConfigurationOptions,
    );

    setPolicyId = (id: number): void => {
        this.policyId = id;
    };

    load = (): void => {
        if (isNil(this.policyId)) {
            logger.warn(
                'PolicyApprovalWorkflowsController: load method called before policyId has been set.',
            );

            return;
        }

        this.policyApprovalConfigurationQuery.load({
            path: {
                policyId: this.policyId,
            },
            query: {
                latest: true,
            },
        });
    };

    get policyApprovalConfiguration(): PolicyApprovalConfigurationPaginatedResponseDto | null {
        return this.policyApprovalConfigurationQuery.data ?? null;
    }

    get isPolicyApprovalConfigurationLoading(): boolean {
        return this.policyApprovalConfigurationQuery.isLoading;
    }

    get isLoading(): boolean {
        return (
            sharedPolicyBuilderModel.isPolicyBuilderFirstLoading ||
            (this.isPolicyApprovalConfigurationLoading &&
                isEmpty(this.policyApprovalConfiguration))
        );
    }

    get hasError(): boolean {
        return this.policyApprovalConfigurationQuery.hasError;
    }

    get error(): Error | null {
        return this.policyApprovalConfigurationQuery.error;
    }

    get reviewGroups() {
        const data = this.policyApprovalConfiguration?.data;

        if (!data || isEmpty(data)) {
            return [];
        }

        return data[0]?.reviewGroups ?? [];
    }

    get needsWorkflowConfiguration(): boolean {
        return isEmpty(this.reviewGroups);
    }
}

export const policyApprovalWorkflowsController =
    new PolicyApprovalWorkflowsController();
