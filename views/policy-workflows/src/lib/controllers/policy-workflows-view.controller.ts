import { isNil } from 'lodash-es';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { logger } from '@globals/logger';
import { makeAutoObservable } from '@globals/mobx';
import { policyApprovalWorkflowsController } from './policy-approval-workflows.controller';

class PolicyWorkflowsViewController {
    constructor() {
        makeAutoObservable(this);
    }

    get policyId(): number | null {
        return sharedPolicyBuilderController.policyId;
    }

    initializeViewData = (): void => {
        if (isNil(this.policyId)) {
            logger.warn(
                'PolicyWorkflowsViewController: initializeViewData method called before policyId has been set.',
            );

            return;
        }

        policyApprovalWorkflowsController.setPolicyId(this.policyId);
        policyApprovalWorkflowsController.load();
    };
}

export const policyWorkflowsViewController =
    new PolicyWorkflowsViewController();
