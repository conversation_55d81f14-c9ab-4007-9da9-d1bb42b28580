import { describe, expect, test } from 'vitest';
import { getConsensusRuleLabel } from './get-consensus-rule-label.helper';

describe('getConsensusRuleLabel', () => {
    test('should return "All" for ALL consensus rule', () => {
        const result = getConsensusRuleLabel('ALL');

        expect(result).toBe('All');
    });

    test('should return "Any" for ANY consensus rule', () => {
        const result = getConsensusRuleLabel('ANY');

        expect(result).toBe('Any');
    });

    test('should return "Subset" for SUBSET consensus rule', () => {
        const result = getConsensusRuleLabel('SUBSET');

        expect(result).toBe('Subset');
    });
});
