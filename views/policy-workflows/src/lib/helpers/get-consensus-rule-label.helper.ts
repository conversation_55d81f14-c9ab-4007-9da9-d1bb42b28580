import type { PolicyApprovalReviewGroupConfigurationDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getConsensusRuleLabel = (
    consensusRule: PolicyApprovalReviewGroupConfigurationDto['consensusRule'],
): string => {
    switch (consensusRule) {
        case 'ALL': {
            return t`All`;
        }
        case 'ANY': {
            return t`Any`;
        }
        case 'SUBSET': {
            return t`Subset`;
        }
        default: {
            return t`Any`;
        }
    }
};
