import { describe, expect, test } from 'vitest';
import type { PolicyApprovalUserCardResponseDto } from '@globals/api-sdk/types';
import {
    formatAvatarStackData,
    formatSingleApproverData,
} from './format-avatar-stack-data.helpers';

describe('formatSingleApproverData', () => {
    test('should format user data for single approver display', () => {
        const user: PolicyApprovalUserCardResponseDto = {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            avatarUrl: 'https://example.com/avatar.jpg',
            id: 1,
            entryId: '1',
        };

        const result = formatSingleApproverData(user);

        expect(result).toStrictEqual({
            primaryLabel: '<PERSON>',
            fallbackText: 'JD',
            imgSrc: 'https://example.com/avatar.jpg',
        });
    });

    test('should handle user without avatar', () => {
        const user: PolicyApprovalUserCardResponseDto = {
            firstName: 'Jane',
            lastName: '<PERSON>',
            email: '<EMAIL>',
            avatarUrl: null,
            id: 2,
            entryId: '2',
        };

        const result = formatSingleApproverData(user);

        expect(result).toStrictEqual({
            primaryLabel: 'Jane Smith',
            fallbackText: 'JS',
            imgSrc: undefined,
        });
    });
});

describe('formatAvatarStackData', () => {
    test('should format multiple users for avatar stack display', () => {
        const users: PolicyApprovalUserCardResponseDto[] = [
            {
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
                avatarUrl: 'https://example.com/john.jpg',
                id: 1,
                entryId: '1',
            },
            {
                firstName: 'Jane',
                lastName: 'Smith',
                email: '<EMAIL>',
                avatarUrl: null,
                id: 2,
                entryId: '2',
            },
        ];

        const result = formatAvatarStackData(users);

        expect(result).toStrictEqual([
            {
                fallbackText: 'JD',
                primaryLabel: 'John Doe',
                secondaryLabel: '<EMAIL>',
                imgSrc: 'https://example.com/john.jpg',
            },
            {
                fallbackText: 'JS',
                primaryLabel: 'Jane Smith',
                secondaryLabel: '<EMAIL>',
                imgSrc: undefined,
            },
        ]);
    });

    test('should handle empty users array', () => {
        const result = formatAvatarStackData([]);

        expect(result).toStrictEqual([]);
    });
});
