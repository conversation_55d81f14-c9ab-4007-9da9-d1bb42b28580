import type { AvatarStackProps } from '@cosmos-lab/components/avatar-stack';
import type { PolicyApprovalUserCardResponseDto } from '@globals/api-sdk/types';
import { getFullName, getInitials } from '@helpers/formatters';

/**
 * Formats a single policy approval user for AvatarIdentity display.
 */
export const formatSingleApproverData = (
    user: PolicyApprovalUserCardResponseDto,
): {
    primaryLabel: string;
    fallbackText: string;
    imgSrc: string | undefined;
} => {
    const fullName = getFullName(user.firstName, user.lastName);

    return {
        primaryLabel: fullName,
        fallbackText: getInitials(fullName),
        imgSrc: user.avatarUrl ?? undefined,
    };
};

/**
 * Formats policy approval users for display in AvatarStack.
 * Maps PolicyApprovalUserCardResponseDto to AvatarStack avatarData format.
 */
export const formatAvatarStackData = (
    users: PolicyApprovalUserCardResponseDto[],
): NonNullable<AvatarStackProps['avatarData']> => {
    return users.map((user) => {
        const fullName = getFullName(user.firstName, user.lastName);

        return {
            fallbackText: getInitials(fullName),
            primaryLabel: fullName,
            secondaryLabel: user.email,
            imgSrc: user.avatarUrl ?? undefined,
        };
    });
};
