import { isEmpty, isNil } from 'lodash-es';
import type { ComponentProps } from 'react';
import {
    COLUMN_NAMES_TO_SORT_IDS_MAP,
    formatVendorsCurrentFilters,
    sharedVendorsReportDownloadController,
} from '@controllers/vendors';
import type {
    Datatable,
    FetchDataResponseParams,
} from '@cosmos/components/datatable';
import type { SchemaDropdownItems } from '@cosmos/components/schema-dropdown';
import type { VendorsControllerListVendorsData } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';

// Type for formatted filters based on the helper function return type
interface FormattedVendorsCurrentFilters {
    type: string | undefined;
    securityReviewStatus: string | undefined;
    status: string | undefined;
    impactLevel: string | undefined;
    risk: string | undefined;
    nextReviewDeadlineStatus: string | undefined;
    category: string | undefined;
    isSubProcessor: boolean | undefined;
    scheduledQuestionnaireStatus: string | undefined;
}

interface VendorsReportDownloadControllerType {
    downloadVendorReport: (params?: unknown) => void;
}

type FormatVendorsCurrentFiltersFunction = (
    filters: unknown,
) => FormattedVendorsCurrentFilters;

/**
 * Checks if there are any filters applied to enable the "Filtered view" download.
 * Returns true if there's at least one filter (search or filter) applied.
 */
const hasFiltersApplied = (
    tableParams: FetchDataResponseParams | undefined,
): boolean => {
    if (!tableParams) {
        return false;
    }

    const { globalFilter } = tableParams;
    const { filters, search } = globalFilter;

    // Check if search is applied
    if (!isEmpty(search?.trim())) {
        return true;
    }

    // Check if any filter has a value
    return Object.values(filters).some((filterData) => {
        const { value } = filterData;

        // Check if filter has a meaningful value
        if (Array.isArray(value)) {
            return !isEmpty(value);
        }

        return !isNil(value) && value !== '' && value !== 'ALL';
    });
};

export const getVendorsCurrentTableSettings = (): ComponentProps<
    typeof Datatable
>['tableSettingsTriggerProps'] => ({
    actionType: 'button',
    id: 'vendors-current-table-action-settings',
    typeProps: {
        colorScheme: 'neutral',
        label: t`Settings`,
        isIconOnly: true,
        startIconName: 'Settings',
        level: 'tertiary',
    },
});

const buildVendorQuery = (
    search: string | undefined,
    formattedFilters: FormattedVendorsCurrentFilters,
    sorting: FetchDataResponseParams['sorting'],
): VendorsControllerListVendorsData['query'] =>
    ({
        q: search,
        ...formattedFilters,
        ...(!isEmpty(sorting) && sorting[0].id in COLUMN_NAMES_TO_SORT_IDS_MAP
            ? {
                  sort: COLUMN_NAMES_TO_SORT_IDS_MAP[
                      sorting[0].id as keyof typeof COLUMN_NAMES_TO_SORT_IDS_MAP
                  ] as NonNullable<
                      VendorsControllerListVendorsData['query']
                  >['sort'],
                  sortDir: sorting[0].desc ? 'DESC' : 'ASC',
              }
            : {}),
    }) as VendorsControllerListVendorsData['query'];

export const getVendorsCurrentTableActions = (
    tableParams?: FetchDataResponseParams,
): ComponentProps<typeof Datatable>['tableActions'] => {
    const hasFilters = hasFiltersApplied(tableParams);

    const items: SchemaDropdownItems = [
        {
            id: 'vendors-current-download-all-vendors',
            label: t`All vendors`,
            type: 'item' as const,
            onClick: action(() => {
                (
                    sharedVendorsReportDownloadController as VendorsReportDownloadControllerType
                ).downloadVendorReport();
            }),
        },
        {
            id: 'vendors-current-filtered-view',
            label: t`Filtered view`,
            isReadOnly: !hasFilters,
            readOnlyTooltip: hasFilters
                ? ''
                : t`Apply filters to enable filtered download`,
            type: 'item' as const,
            onClick: action(() => {
                if (!hasFilters || !tableParams) {
                    return;
                }

                const { globalFilter, sorting } = tableParams;
                const { filters, search } = globalFilter;

                const formattedFilters = (
                    formatVendorsCurrentFilters as FormatVendorsCurrentFiltersFunction
                )(filters);

                (
                    sharedVendorsReportDownloadController as VendorsReportDownloadControllerType
                ).downloadVendorReport({
                    query: buildVendorQuery(search, formattedFilters, sorting),
                });
            }),
        },
    ];

    return [
        {
            actionType: 'dropdown',
            id: 'vendors-current-table-action-download',
            typeProps: {
                isIconOnly: false,
                label: t`Download`,
                items,
                level: 'tertiary',
                startIconName: 'Download',
            },
        },
    ];
};

export const VENDORS_CURRENT_DEFAULT_SORTING = [
    {
        id: 'VENDOR',
        desc: false,
    },
];
