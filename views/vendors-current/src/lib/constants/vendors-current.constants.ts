import type { ComponentProps } from 'react';
import { routeController } from '@controllers/route';
import { sharedVendorsCreateCurrentVendorController } from '@controllers/vendors';
import type { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { NavigateFunction } from '@remix-run/react';

export const VENDORS_CURRENT_PAGE_HEADER_KEY = 'vendors-current-header';

export const getVendorsCurrentPageHeaderActions = (
    navigate: NavigateFunction,
): NonNullable<ComponentProps<typeof ActionStack>['actions']> => [
    {
        actionType: 'dropdown',
        id: `${VENDORS_CURRENT_PAGE_HEADER_KEY}-add-action`,
        typeProps: {
            label: t`Add vendor`,
            endIconName: 'ChevronDown',
            align: 'end',
            items: [
                {
                    id: `${VENDORS_CURRENT_PAGE_HEADER_KEY}-dropdown-add-single`,
                    label: t`Add single vendor`,
                    value: 'ADD_SINGLE',
                    onClick: action(() => {
                        const workspaceId =
                            sharedWorkspacesController.currentWorkspace?.id;

                        if (workspaceId) {
                            sharedVendorsCreateCurrentVendorController.cleanVendorValues();
                            navigate(
                                `${routeController.userPartOfUrl}/vendors/current/add-vendor`,
                            );
                        }
                    }),
                },
                {
                    id: `${VENDORS_CURRENT_PAGE_HEADER_KEY}-dropdown-add-bulk`,
                    label: t`Add/update in bulk`,
                    value: 'ADD_BULK',
                    onClick: action(() => {
                        const workspaceId =
                            sharedWorkspacesController.currentWorkspace?.id;

                        if (workspaceId) {
                            navigate(
                                `${routeController.userPartOfUrl}/vendors/current/bulk-add-vendors`,
                            );
                        }
                    }),
                },
            ],
        },
    },
];
