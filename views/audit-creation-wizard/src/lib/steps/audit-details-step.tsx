import { isNil } from 'lodash-es';
import { forwardRef } from 'react';
import { sharedAuditCreationWizardController } from '@controllers/audit-creation-wizard';
import { Banner } from '@cosmos/components/banner';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { FrameworkForNewAuditResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedAuditCreationDetailsModel } from '@models/audit-creation-wizard';
import { Form, type FormValues } from '@ui/forms';

const BaseAuditDetailsStep = (
    { formRef }: { formRef: React.RefObject<HTMLFormElement> },
    ref: React.ForwardedRef<HTMLDivElement>,
) => {
    const formId = 'set-audit-details';

    const { data, addAuditDetails, isSoc2Type2, isIso27001, hasControls } =
        sharedAuditCreationWizardController;

    const { formSchema } = sharedAuditCreationDetailsModel;

    const handleSubmit = (values: FormValues) => {
        const framework = values.framework as FrameworkForNewAuditResponseDto;
        const date = values.date as string;
        const dateRange = values.dateRange as {
            start: string;
            end: string;
        };

        let startingDate = '';
        let endingDate: string | undefined;

        if (!isNil(date)) {
            // SOC2 Type 1 audit - single date field
            startingDate = date;
        } else if (!isNil(dateRange)) {
            // Other audit types - date range field
            startingDate = dateRange.start;
            endingDate = dateRange.end;
        }

        addAuditDetails({
            framework,
            date: {
                startingDate,
                endingDate,
            },
        });
    };

    return (
        <Stack
            ref={ref}
            direction="column"
            gap="5xl"
            data-id="audit-details-step"
            data-testid="BaseAuditDetailsStep"
        >
            <Stack direction="column" align="center" gap="md">
                <Text as="h1" type="headline" size="600">
                    {t`Audit Details`}
                </Text>
                <Text as="h2" type="subheadline" size="100" align="center">
                    {t`Select the framework for this audit and the period covered.`}
                </Text>
            </Stack>

            {!isNil(data.auditDetails?.framework) && !hasControls && (
                <Banner
                    severity="warning"
                    title={t`The selected framework has no mapped controls. The control evidence package will be empty.`}
                />
            )}

            <Stack direction="column" gap="lg">
                <Form
                    hasExternalSubmitButton
                    ref={formRef}
                    key={`audit-details-${data.auditDetails?.framework?.pill}`}
                    formId={formId}
                    data-testid="SetAuditSamples"
                    data-id="gvqSzqOA"
                    schema={formSchema}
                    onSubmit={handleSubmit}
                />
            </Stack>

            {isSoc2Type2 && (
                <Text as="h2" type="subheadline" size="100" align="center">
                    {t`For a SOC2 Type 2 audit, the start and end dates should be the same as your SOC 2 Type 2 audit period.`}
                </Text>
            )}

            {isIso27001 && (
                <Text as="h2" type="subheadline" size="100" align="center">
                    {t`For an ISO 27001 audit, we recommend reaching out to your ISO 27001 certification body to determine the appropriate audit period, prior to sending out invites.`}
                </Text>
            )}
        </Stack>
    );
};

export const AuditDetailsStep = observer(forwardRef(BaseAuditDetailsStep));
