import { forwardRef } from 'react';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';

const BaseAssignAuditorsStep = (
    _props: Record<string, never>,
    ref: React.ForwardedRef<HTMLDivElement>,
) => {
    return (
        <Stack
            ref={ref}
            direction="column"
            gap="5xl"
            data-id="assign-auditors-step"
            data-testid="BaseAssignAuditorsStep"
        >
            <Stack direction="column" align="center" gap="md">
                <Text as="h1" type="headline" size="600">
                    {t`Assign Auditors`}
                </Text>
                <Text as="h2" type="subheadline" size="100" align="center">
                    {t`Configure auditor information`}
                </Text>
            </Stack>

            {/* TODO: Add assign auditors form  in https://drata.atlassian.net/browse/ENG-72181*/}
            <Stack direction="column" gap="lg">
                <Text type="body" size="200" colorScheme="neutral">
                    {t`Assign auditors form will be implemented here`}
                </Text>
            </Stack>
        </Stack>
    );
};

export const AssignAuditorsStep = forwardRef(BaseAssignAuditorsStep);
