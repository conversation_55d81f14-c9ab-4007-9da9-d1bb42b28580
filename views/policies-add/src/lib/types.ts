/**
 * Form data types for the policy creation wizard steps.
 */

import type { ExternalFileTypeResponseDto } from '@globals/api-sdk/types';
import type { SLAConfiguration } from '@models/policies';

export interface PolicySourceFormData {
    sourceType: 'BUILDER' | 'UPLOADED' | 'EXTERNAL';
    uploadedFile?: File[];
}

export interface DetailsFormData {
    name: string;
    description: string;
    renewalDate: {
        renewalFrequency: {
            id: string;
            label: string;
            value: string;
        };
        renewalDate: string;
    };
    owner: { id: string; label: string; value: string } | undefined;
    disclaimer: string | undefined;
}

export interface PersonnelGroupsFormData {
    assignedTo: 'ALL' | 'NONE' | 'GROUP';
    selectedGroups?: { id: string; label: string; value: string }[];
    notifyNewMembers?: boolean;
}

export interface ReplacePoliciesFormData {
    shouldReplacePolicies?: 'yes' | 'no';
    policiesToReplace?: { id: number; name: string }[];
    slaConfiguration?: SLAConfiguration;
}

export interface ExternalSourceFormData {
    providerType: string;
    externalFile?: ExternalFileTypeResponseDto;
}
