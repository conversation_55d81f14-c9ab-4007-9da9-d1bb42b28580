import { useCallback, useEffect, useMemo } from 'react';
import { sharedCreatePolicyController } from '@controllers/policies';
import { routeController } from '@controllers/route';
import { Stack } from '@cosmos/components/stack';
import {
    Wizard,
    type WizardStepDataProps,
} from '@cosmos-lab/components/wizard';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';
import {
    DetailsStep,
    PersonnelGroupsStep,
    PolicySourceStep,
    ReplacePoliciesStep,
} from './components';
import { ExternalSourceStep } from './components/external-source-step.component';

const WizardWithValidation = observer(
    ({
        onCancel,
        onComplete,
        isExternal = false,
    }: {
        onCancel: () => void;
        onComplete: () => Promise<void>;
        isExternal?: boolean;
    }): React.JSX.Element => {
        // Create form refs for each step
        const policySourceForm = useFormSubmit();
        const detailsForm = useFormSubmit();
        const personnelGroupsForm = useFormSubmit();
        const replacePoliciesForm = useFormSubmit();
        const externalSourceForm = useFormSubmit();
        const { isCreatingPolicy: isLoading } = sharedCreatePolicyController;

        const steps: WizardStepDataProps[] = useMemo(() => {
            const stepsTemp: WizardStepDataProps[] = [];

            if (!isExternal) {
                stepsTemp.push({
                    stepTitle: t`Source`,
                    isStepSkippable: false,
                    component: () => (
                        <PolicySourceStep
                            formRef={policySourceForm.formRef}
                            data-id="SIGYpXND"
                            data-testid="PolicySourceStepComponent"
                        />
                    ),
                    onStepChange: async () => {
                        return policySourceForm.triggerSubmit();
                    },
                });
            }
            stepsTemp.push(
                {
                    stepTitle: t`Details`,
                    isStepSkippable: false,
                    component: () => (
                        <DetailsStep
                            formRef={detailsForm.formRef}
                            data-id="6itg1i0U"
                            data-testid="DetailsStepComponent"
                        />
                    ),
                    onStepChange: async () => {
                        return detailsForm.triggerSubmit();
                    },
                },
                {
                    stepTitle: t`Personnel Groups`,
                    isStepSkippable: false,
                    component: () => (
                        <PersonnelGroupsStep
                            formRef={personnelGroupsForm.formRef}
                            data-id="ZJqTGpbM"
                            data-testid="PersonnelGroupsStepComponent"
                        />
                    ),
                    onStepChange: async () => {
                        return personnelGroupsForm.triggerSubmit();
                    },
                },
            );
            if (isExternal) {
                stepsTemp.push({
                    stepTitle: t`External Source`,
                    isStepSkippable: false,
                    component: () => (
                        <ExternalSourceStep
                            formRef={externalSourceForm.formRef}
                            data-id="external-source-step"
                            data-testid="ExternalSourceStepComponent"
                        />
                    ),
                    onStepChange: async () => {
                        return externalSourceForm.triggerSubmit();
                    },
                });
            }
            stepsTemp.push({
                stepTitle: t`Replace Policies`,
                isStepSkippable: false,
                component: () => (
                    <ReplacePoliciesStep
                        formRef={replacePoliciesForm.formRef}
                        data-id="XPZH_64M"
                        data-testid="ReplacePoliciesStepComponent"
                    />
                ),
                // Note: onStepChange is not called for the last step in Wizard
                // Form submission is handled in onComplete
            });

            return stepsTemp;
        }, [
            detailsForm,
            externalSourceForm,
            isExternal,
            personnelGroupsForm,
            policySourceForm,
            replacePoliciesForm.formRef,
        ]);

        return (
            <Stack direction="column" align="center" data-id="x9imC7OV">
                <Wizard
                    steps={steps}
                    nextButtonLabel={t`Continue`}
                    data-id="policies-add-wizard"
                    data-testid="PoliciesAddWizardView"
                    completeButtonLabel={t`Finish`}
                    isLoading={isLoading}
                    onCancel={onCancel}
                    onComplete={async () => {
                        // Submit the last step's form before completing
                        const isValid =
                            await replacePoliciesForm.triggerSubmit();

                        if (isValid) {
                            await onComplete();
                        }
                    }}
                />
            </Stack>
        );
    },
);

export const PoliciesAddView = observer(
    ({ isExternal = false }: { isExternal?: boolean }): React.JSX.Element => {
        const navigate = useNavigate();

        const handleCancel = useCallback(() => {
            navigate(`${routeController.userPartOfUrl}/governance/policies`);
        }, [navigate]);

        const handleComplete = useCallback(async () => {
            runInAction(() => {
                sharedCreatePolicyController.createPolicyWorkflow(navigate);
            });

            return Promise.resolve();
        }, [navigate]);

        useEffect(() => {
            // Initialize controller
            runInAction(() => {
                sharedCreatePolicyController.initialize();
            });

            /**
             * Cleanup when component unmounts.
             */
            return () => {
                runInAction(() => {
                    sharedCreatePolicyController.resetForm();
                });
            };
        }, []);

        return (
            <WizardWithValidation
                isExternal={isExternal}
                data-id="1lVb95aJ"
                onCancel={handleCancel}
                onComplete={handleComplete}
            />
        );
    },
);
