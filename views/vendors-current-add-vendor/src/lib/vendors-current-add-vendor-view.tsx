import { useCallback } from 'react';
import {
    VendorDetailsFinishFormComponent,
    VendorDetailsFormComponent,
    VendorImpactAssessmentFormComponent,
    VendorInternalDetailsFormComponent,
} from '@components/vendors-current-add-vendor';
import {
    sharedVendorCustomFieldsController,
    sharedVendorsCreateCurrentVendorController,
    sharedVendorsDetailsController,
} from '@controllers/vendors';
import { Loader } from '@cosmos/components/loader';
import { Wizard } from '@cosmos-lab/components/wizard';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { useLocation, useNavigate } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';

const ADD_VENDORS_CURRENT_TEST_ID = 'add-vendors-current';

export const VendorsCurrentAddVendorView = observer((): React.JSX.Element => {
    const { isLoading } = sharedVendorsDetailsController;
    const { isVendorRiskManagementProEnabled } = sharedFeatureAccessModel;
    const {
        saveVendorDetails,
        saveVendorInternalDetails,
        saveImpactAssessmentDetails,
        vendorId,
    } = sharedVendorsCreateCurrentVendorController;

    const navigate = useNavigate();
    const { formRef: formDetailsRef, triggerSubmit: triggerSubmitDetails } =
        useFormSubmit();
    const {
        formRef: formInternalDetailsRef,
        triggerSubmit: triggerSubmitInternalDetails,
    } = useFormSubmit();
    const { formRef: formImpactRef, triggerSubmit: triggerSubmitImpact } =
        useFormSubmit();

    const location = useLocation();
    const parentRoute = getParentRoute(location.pathname);

    const navigateToCurrentVendorsPage = useCallback(() => {
        navigate(parentRoute);
    }, [navigate, parentRoute]);

    const navigateToCurrentVendorsProfile = useCallback(() => {
        if (vendorId) {
            navigate(`${parentRoute}/${vendorId}/overview`);
        } else {
            navigate(parentRoute);
        }
    }, [navigate, parentRoute, vendorId]);

    const createDetailsComponent = () => {
        return (
            <VendorDetailsFormComponent
                isAddingVendor
                ref={formDetailsRef}
                formId="vendor-details-form"
                data-id="vendorDetailsStep"
                data-testid="createDetailsComponent"
                handleOnSubmit={(values) => {
                    saveVendorDetails(values);

                    return undefined;
                }}
            />
        );
    };

    const createInternalDetailsComponent = () => {
        return (
            <VendorInternalDetailsFormComponent
                isAddingVendor
                ref={formInternalDetailsRef}
                formId="vendor-internal-details-form"
                data-id="vendorInternalDetailsStep"
                data-testid="createInternalDetailsComponent"
                handleOnSubmit={(values) => {
                    saveVendorInternalDetails(values);
                }}
            />
        );
    };

    const createImpactComponent = () => {
        return (
            <VendorImpactAssessmentFormComponent
                formRef={formImpactRef}
                data-id="vendorImpactStep"
                data-testid="createImpactComponent"
                onSubmit={(values) => {
                    saveImpactAssessmentDetails(values);
                }}
            />
        );
    };

    if (isLoading || sharedVendorCustomFieldsController.isLoadingByVendorId) {
        return <Loader isSpinnerOnly label={t`Loading...`} />;
    }

    return (
        <Wizard
            data-id={ADD_VENDORS_CURRENT_TEST_ID}
            data-testid="VendorsCurrentAddVendorView"
            steps={[
                {
                    isStepSkippable: false,
                    stepTitle: t`Vendor details`,
                    component: createDetailsComponent,
                    onStepChange: triggerSubmitDetails,
                },
                {
                    isStepSkippable: false,
                    stepTitle: t`Internal details`,
                    component: createInternalDetailsComponent,
                    backButtonLabelOverride: t`Back`,
                    onStepChange: triggerSubmitInternalDetails,
                },
                ...(isVendorRiskManagementProEnabled
                    ? [
                          {
                              isStepSkippable: false,
                              stepTitle: t`Impact assessment`,
                              component: createImpactComponent,
                              backButtonLabelOverride: t`Back`,
                              onStepChange: triggerSubmitImpact,
                          },
                      ]
                    : []),
                {
                    isStepSkippable: false,
                    stepTitle: t`Finish`,
                    component: VendorDetailsFinishFormComponent,
                    canGoBack: false,
                },
            ]}
            onCancel={navigateToCurrentVendorsPage}
            onComplete={navigateToCurrentVendorsProfile}
        />
    );
});
