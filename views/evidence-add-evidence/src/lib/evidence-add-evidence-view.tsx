import { useCallback } from 'react';
import { MapControlsStep } from '@components/map-controls-step';
import { sharedEvidenceMutationController } from '@controllers/evidence-library';
import { sharedUsersInfiniteController } from '@controllers/users';
import { Box } from '@cosmos/components/box';
import { Wizard } from '@cosmos-lab/components/wizard';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { useLocation, useNavigate } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';
import { EvidenceDetailsStep } from './components/evidence-details-step';
import { EvidenceSourceStep } from './components/evidence-source-step';
import {
    ADD_EVIDENCE_TEST_ID,
    FORM_ID,
} from './constants/evidence-add-evidence.constants';
import { getSourceOptions } from './helpers/get-source-options';
import { openImpactOnControlReadinessModal } from './helpers/impact-on-control-readiness-modal.helper';
import { sharedEvidenceLibraryAddEvidenceFormModel } from './models/evidence-library-add-evidence-form.model';

export const EvidenceAddEvidenceView = observer((): React.JSX.Element => {
    const { isCreating } = sharedEvidenceMutationController;
    const SOURCE_OPTIONS = getSourceOptions();
    const navigate = useNavigate();
    const location = useLocation();
    const goToEvidencePage = useCallback(() => {
        navigate(getParentRoute(location.pathname));
    }, [location.pathname, navigate]);

    const detailsStepForm = useFormSubmit();
    const EvidenceDetailsStepForm = useCallback(
        () => (
            <EvidenceDetailsStep
                data-id="add-evidence-details-step"
                formId={FORM_ID}
                formRef={detailsStepForm.formRef}
            />
        ),
        [detailsStepForm.formRef],
    );

    const sourceStepForm = useFormSubmit();
    const EvidenceSourceStepForm = useCallback(
        () => (
            <EvidenceSourceStep
                data-id="add-evidence-source-step"
                formId={FORM_ID}
                formRef={sourceStepForm.formRef}
            />
        ),
        [sourceStepForm.formRef],
    );
    const sourceOnStepChange = useCallback(() => {
        const { source } = sharedEvidenceLibraryAddEvidenceFormModel;

        if (source.value === SOURCE_OPTIONS.NONE.value) {
            return Promise.resolve(true);
        }

        return sourceStepForm.triggerSubmit();
    }, [SOURCE_OPTIONS.NONE.value, sourceStepForm]);

    const MapControlsStepForm = useCallback(
        () => (
            <MapControlsStep
                objectType="evidence"
                data-id="add-evidence-map-controls-step"
            />
        ),
        [],
    );

    const cleanUpFormAndRedirectToEvidence = useCallback(() => {
        sharedEvidenceLibraryAddEvidenceFormModel.resetFormValues();
        goToEvidencePage();
    }, [goToEvidencePage]);

    const handleCompleteAddEvidenceWizard = useCallback(() => {
        const { shouldDisplayImpactOnControlReadinessModal, getFormValues } =
            sharedEvidenceLibraryAddEvidenceFormModel;

        if (shouldDisplayImpactOnControlReadinessModal) {
            openImpactOnControlReadinessModal(cleanUpFormAndRedirectToEvidence);

            return;
        }

        const addEvidenceFormValues = getFormValues();

        sharedEvidenceMutationController.createEvidence(
            addEvidenceFormValues,
            cleanUpFormAndRedirectToEvidence,
        );
    }, [cleanUpFormAndRedirectToEvidence]);

    const evidenceDetailsOnStepChange = useCallback(() => {
        sharedUsersInfiniteController.loadUsers({
            roles: [
                'ADMIN',
                'TECHGOV',
                'WORKSPACE_ADMINISTRATOR',
                'CONTROL_MANAGER',
            ],
        });

        return detailsStepForm.triggerSubmit();
    }, [detailsStepForm]);

    return (
        <Box
            pb="2xl"
            data-id={ADD_EVIDENCE_TEST_ID}
            data-testid="EvidenceAddEvidenceView"
        >
            <Wizard
                isLoading={isCreating}
                steps={[
                    {
                        isStepSkippable: false,
                        stepTitle: t`Add artifact`,
                        component: EvidenceDetailsStepForm,
                        onStepChange: evidenceDetailsOnStepChange,
                    },
                    {
                        isStepSkippable: false,
                        stepTitle: t`Select source`,
                        component: EvidenceSourceStepForm,
                        onStepChange: sourceOnStepChange,
                        backButtonLabelOverride: t`Back`,
                    },
                    {
                        isStepSkippable: false,
                        stepTitle: t`Map controls`,
                        component: MapControlsStepForm,
                        backButtonLabelOverride: t`Back`,
                        forwardButtonLabelOverride: t`Finish`,
                    },
                ]}
                onCancel={goToEvidencePage}
                onComplete={handleCompleteAddEvidenceWizard}
            />
        </Box>
    );
});
