import { useState } from 'react';
import { z } from 'zod';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { Box } from '@cosmos/components/box';
import { But<PERSON> } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { ComboboxField } from '@cosmos/components/combobox-field';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { dimension120x, dimension170x } from '@cosmos/constants/tokens';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import type {
    CustomerRequestDetailsWithFrameworkResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getAvatarFallbackText } from '@helpers/get-avatar-fallback-text';
import { Form, type FormSchema, useFormSubmit } from '@ui/forms';
import {
    transformOwnersListToOptions,
    transformOwnerToListBoxItem,
} from './helpers/evidence-request-overview.helper';

const MAX_OWNERS = 5;
const MAX_TITLE_LENGTH = 191;
const MAX_DESCRIPTION_LENGTH = 30000;

const formSchema = (
    initialValues: CustomerRequestDetailsWithFrameworkResponseDto,
    ownersList: UserResponseDto[],
    hasCustomRequestOwnersNextPage: boolean,
    customRequestOwnersIsLoading: boolean,
    loadCustomRequestOwnersNextPage: ({
        search,
        auditId,
    }: {
        search?: string;
        auditId: string;
    }) => void,
    auditId: string,
    isFromAuditHub: boolean,
): FormSchema => {
    return {
        owners: {
            type: 'custom',
            label: t`Owners`,
            isOptional: true,
            initialValue: initialValues.owners.map(transformOwnerToListBoxItem),
            searchDebounce: 500,
            validator: z.union([
                z.undefined(),
                z
                    .array(z.any())
                    .length(0), // Allow empty arrays
                z
                    .array(
                        z.object({
                            id: z.string(),
                            label: z.string(),
                            value: z.string().optional(),
                        }),
                    )
                    .min(1) // At least one item if not empty
                    .max(MAX_OWNERS, {
                        message: t`Owners field must have at most ${MAX_OWNERS} items`,
                    }),
            ]),
            render: (props) => (
                <ComboboxField
                    {...props}
                    isMultiSelect
                    defaultSelectedOptions={props.value || []}
                    options={transformOwnersListToOptions(ownersList)}
                    placeholder={t`Search for owners`}
                    loaderLabel={t`Loading owners...`}
                    getSearchEmptyState={() => t`No owners found`}
                    hasMore={hasCustomRequestOwnersNextPage}
                    isLoading={customRequestOwnersIsLoading}
                    removeAllSelectedItemsLabel={t`Remove all owners`}
                    data-id="zVwu1mAt"
                    onFetchOptions={({ search }: { search?: string }) => {
                        loadCustomRequestOwnersNextPage({
                            search,
                            auditId,
                        });
                    }}
                />
            ),
        },
        title: {
            type: 'text',
            initialValue: initialValues.title,
            label: t`Title`,
            validator: z.string().max(MAX_TITLE_LENGTH, {
                message: t`Title must contain at most ${MAX_TITLE_LENGTH} characters`,
            }),
            disabled: !isFromAuditHub,
        },
        description: {
            type: 'textarea',
            initialValue: initialValues.description,
            label: t`Description`,
            isOptional: true,
            validator: z
                .string()
                .max(MAX_DESCRIPTION_LENGTH, {
                    message: t`Description must contain at most ${MAX_DESCRIPTION_LENGTH} characters`,
                })
                .optional(),
            disabled: !isFromAuditHub,
        },
    };
};

export const EvidenceRequestOverview = observer(
    ({ isFromAuditHub = true }): JSX.Element => {
        const { customerRequestDetails, isLoading, auditId, requestId } =
            sharedCustomerRequestDetailsController;
        const {
            customRequestOwners: ownersList,
            loadCustomRequestOwnersNextPage,
            hasCustomRequestOwnersNextPage,
            customRequestOwnersIsLoading,
        } = sharedAuditHubController;
        const [isEditorMode, setIsEditorMode] = useState(false);
        const { formRef, triggerSubmit } = useFormSubmit();

        if (isLoading || !customerRequestDetails) {
            return <Skeleton />;
        }

        const handleSave = (data: {
            title: string;
            description: string;
            owners?: ListBoxItemData[];
        }) => {
            const updateData = {
                title: data.title,
                description: data.description,
                ownerIds: data.owners
                    ? data.owners.map((owner) => Number(owner.id))
                    : [],
            };

            sharedCustomerRequestDetailsController.updateCustomerRequestDetails(
                Number(requestId),
                updateData,
            );

            setIsEditorMode(false);
        };

        return (
            <Card
                title={t`Overview`}
                data-testid="EvidenceRequestOverview"
                data-id="kJyQA3ss"
                isEditMode={isEditorMode}
                actions={
                    isEditorMode
                        ? []
                        : [
                              {
                                  actionType: 'button',
                                  id: 'edit-button',
                                  typeProps: {
                                      label: t`Edit`,
                                      level: 'secondary',
                                      onClick: () => {
                                          setIsEditorMode(true);
                                      },
                                  },
                              },
                          ]
                }
                body={
                    <Stack direction="column" gap="xl">
                        <KeyValuePair
                            label={t`ID`}
                            type="TEXT"
                            value={customerRequestDetails.code}
                        />

                        {isEditorMode ? (
                            <Box maxHeight={dimension120x}>
                                <Box maxWidth={dimension170x}>
                                    <Form
                                        hasExternalSubmitButton
                                        ref={formRef}
                                        formId="evidence-request-overview-form"
                                        data-id={
                                            'evidence-request-overview-form'
                                        }
                                        schema={formSchema(
                                            customerRequestDetails,
                                            ownersList,
                                            hasCustomRequestOwnersNextPage,
                                            customRequestOwnersIsLoading,
                                            loadCustomRequestOwnersNextPage,
                                            auditId as string,
                                            isFromAuditHub,
                                        )}
                                        onSubmit={(data) => {
                                            const {
                                                title,
                                                description,
                                                owners,
                                            } = data;

                                            handleSave({
                                                title: title as string,
                                                description:
                                                    description as string,
                                                owners: owners as
                                                    | ListBoxItemData[]
                                                    | undefined,
                                            });
                                        }}
                                    />
                                </Box>
                                <Stack
                                    align="start"
                                    justify="start"
                                    gap="4x"
                                    pt={'xl'}
                                >
                                    <Button
                                        label={t`Save`}
                                        colorScheme="primary"
                                        onClick={() => triggerSubmit()}
                                    />
                                    <Button
                                        label={t`Cancel`}
                                        colorScheme="neutral"
                                        onClick={() => {
                                            setIsEditorMode(false);
                                        }}
                                    />
                                </Stack>
                            </Box>
                        ) : (
                            <>
                                <KeyValuePair
                                    label={t`Owners`}
                                    type="REACT_NODE"
                                    value={
                                        <AvatarStack
                                            avatarData={customerRequestDetails.owners.map(
                                                (owner) => ({
                                                    fallbackText:
                                                        getAvatarFallbackText(
                                                            `${owner.firstName} ${owner.lastName}`,
                                                        ),
                                                    primaryLabel: `${owner.firstName} ${owner.lastName}`,
                                                    secondaryLabel: owner.email,
                                                    imgSrc:
                                                        owner.avatarUrl ??
                                                        undefined,
                                                }),
                                            )}
                                        />
                                    }
                                />
                                <KeyValuePair
                                    label={t`Title`}
                                    type="TEXT"
                                    value={customerRequestDetails.title}
                                />
                                {customerRequestDetails.description && (
                                    <KeyValuePair
                                        label={t`Description`}
                                        type="TEXT"
                                        value={
                                            customerRequestDetails.description
                                        }
                                    />
                                )}
                            </>
                        )}
                    </Stack>
                }
            />
        );
    },
);
