import { isEmpty } from 'lodash-es';
import { useState } from 'react';
import { sharedNotHumanReasonModalController } from '@controllers/personnel';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { TextareaField } from '@cosmos/components/textarea-field';
import type { EmploymentStatusEnum } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    getHelpText,
    getReasonLabel,
    getStatusLabel,
} from '../helpers/not-human-reason.helper';

const FORM_ID = 'not-human-reason-form';
// Database field limit for notHumanReason column
const MAX_CHARACTERS = 191;

interface NotHumanReasonModalProps {
    personnelId: number;
    userFullName: string;
    employmentStatus: EmploymentStatusEnum;
    onClose: () => void;
}

export const NotHumanReasonModal = observer(
    ({
        personnelId,
        userFullName,
        employmentStatus,
        onClose,
    }: NotHumanReasonModalProps): React.JSX.Element => {
        const [reason, setReason] = useState<string>('');
        const [feedback, setFeedback] = useState<
            { type: 'error' | 'success'; message: string } | undefined
        >(undefined);

        const { updateEmploymentStatusWithReason } =
            sharedNotHumanReasonModalController;

        const handleOnChangeReason = (
            event: React.ChangeEvent<HTMLTextAreaElement>,
        ) => {
            setReason(event.target.value);
            setFeedback(undefined);
        };

        const handleSave = () => {
            if (isEmpty(reason)) {
                setFeedback({
                    type: 'error',
                    message: t`Reason is required.`,
                });

                return;
            }

            if (reason.length > MAX_CHARACTERS) {
                setFeedback({
                    type: 'error',
                    message: t`Reason cannot be longer than 191 characters.`,
                });

                return;
            }

            updateEmploymentStatusWithReason(
                personnelId,
                employmentStatus,
                reason,
            );

            onClose();
        };

        const statusLabel = getStatusLabel(employmentStatus);

        return (
            <Modal size="lg" data-id="not-human-reason-modal">
                <Modal.Header
                    size="md"
                    title={t`Change status to ${statusLabel} for ${userFullName}`}
                    closeButtonAriaLabel={t`Close reason modal`}
                    onClose={onClose}
                />
                <Modal.Body size="md">
                    <Stack gap="4x" direction="column">
                        <Text size="200">
                            <Trans>
                                Business Rationale: Enter management rationale
                                for why this account is out of scope for your
                                automated control tests.
                                {statusLabel}.
                            </Trans>
                        </Text>
                        <TextareaField
                            feedback={feedback}
                            formId={FORM_ID}
                            helpText={getHelpText(employmentStatus)}
                            name="reason"
                            label={getReasonLabel(employmentStatus)}
                            labelStyleOverrides={{ size: 'md' }}
                            value={reason}
                            maxCharacters={MAX_CHARACTERS}
                            onChange={handleOnChangeReason}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    size="md"
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: onClose,
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            onClick: handleSave,
                        },
                    ]}
                />
            </Modal>
        );
    },
);
