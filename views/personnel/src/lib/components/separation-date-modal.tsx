import { z } from 'zod';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import { Modal } from '@cosmos/components/modal';
import type { EmploymentStatusEnum } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { compareDayIsFuture, convertToISO8601String } from '@helpers/date-time';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';

interface SeparationDateModalProps {
    personnelId: number;
    employmentStatus: EmploymentStatusEnum;
    currentStartDate: string | null;
    onClose: () => void;
    onSubmit: (data: {
        startDate: string;
        separationDate: string;
        employmentStatus: EmploymentStatusEnum;
    }) => void;
    isLoading?: boolean;
}

const FORM_ID = 'separation-date-form';

export const SeparationDateModal = ({
    employmentStatus,
    currentStartDate,
    onClose,
    onSubmit,
    isLoading = false,
}: SeparationDateModalProps): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();

    const formattedStartDate: TDateISODate | undefined = currentStartDate
        ? (convertToISO8601String(new Date(currentStartDate)) as TDateISODate)
        : undefined;

    const handleSubmit = (values: {
        startDate: string;
        separationDate: string;
    }) => {
        if (values.startDate && values.separationDate) {
            onSubmit({
                startDate: values.startDate,
                separationDate: values.separationDate,
                employmentStatus,
            });
        }
    };

    return (
        <>
            <Modal.Header
                title={t`Set Employment Dates`}
                description={t`Please confirm the hire date and enter the separation date for this former employee.`}
                closeButtonAriaLabel={t`Close employment dates modal`}
                onClose={onClose}
            />
            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    formId={FORM_ID}
                    ref={formRef}
                    data-id="separation-date-modal-form"
                    data-testid="SeparationDateModalForm"
                    schema={{
                        startDate: {
                            type: 'date',
                            label: t`Hire Date`,
                            isOptional: false,
                            initialValue: formattedStartDate,
                            locale: 'en-US',
                        },
                        separationDate: {
                            type: 'date',
                            label: t`Separation Date`,
                            isOptional: false,
                            initialValue: convertToISO8601String(
                                new Date(),
                            ) as TDateISODate, // Explicitly set to prevent uncontrolled to controlled warning
                            locale: 'en-US',
                            validator: z
                                .string({
                                    message: t`Separation date is required`,
                                })
                                .date()
                                .refine(
                                    (date: string) => {
                                        // Prevent selecting future dates (today is allowed)
                                        return !compareDayIsFuture(date);
                                    },
                                    {
                                        message: t`Future dates are not available for separation date`,
                                    },
                                ),
                        },
                    }}
                    onSubmit={(values: FormValues) => {
                        handleSubmit(
                            values as {
                                startDate: string;
                                separationDate: string;
                            },
                        );
                    }}
                />
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'tertiary',
                        onClick: onClose,
                        isLoading,
                    },
                    {
                        label: t`Submit`,
                        level: 'primary',
                        onClick: triggerSubmit,
                        isLoading,
                    },
                ]}
            />
        </>
    );
};
