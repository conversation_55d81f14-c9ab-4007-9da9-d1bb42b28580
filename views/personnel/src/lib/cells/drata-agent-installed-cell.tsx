import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getSpecificComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface DrataAgentInstalledCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const DrataAgentInstalledCell = ({
    row,
}: DrataAgentInstalledCellProps): React.JSX.Element => {
    const status = getSpecificComplianceStatus(
        row.original,
        'DRATA_AGENT_INSTALLED',
    );

    return (
        <Box
            pt="2x"
            data-id="drata-agent-installed-box"
            data-testid="DrataAgentInstalledCell"
        >
            <ComplianceStatus
                treatFailureAsPending
                status={status}
                data-testid="DrataAgentInstalledCell"
                data-id="drata-agent-installed-status"
                customText={{
                    success: t`Installed`,
                    failure: t`Not Installed`,
                    pending: t`Not Installed`,
                }}
            />
        </Box>
    );
};
