import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getSecurityTrainingStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface SecurityTrainingCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const SecurityTrainingCell = ({
    row,
}: SecurityTrainingCellProps): React.JSX.Element => {
    const status = getSecurityTrainingStatus(row.original);

    return (
        <Box
            pt="2x"
            data-id="security-training-box"
            data-testid="SecurityTrainingCell"
        >
            <ComplianceStatus
                status={status}
                data-testid="SecurityTrainingCell"
                data-id="security-training-status"
            />
        </Box>
    );
};
