import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getSpecificComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface DiskEncryptedCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const DiskEncryptedCell = ({
    row,
}: DiskEncryptedCellProps): React.JSX.Element => {
    const status = getSpecificComplianceStatus(row.original, 'HDD_ENCRYPTION');

    return (
        <Box
            pt="2x"
            data-id="disk-encrypted-box"
            data-testid="DiskEncryptedCell"
        >
            <ComplianceStatus
                status={status}
                data-testid="DiskEncryptedCell"
                data-id="disk-encrypted-status"
            />
        </Box>
    );
};
