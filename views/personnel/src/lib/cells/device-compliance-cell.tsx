import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getDeviceComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface DeviceComplianceCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const DeviceComplianceCell = ({
    row,
}: DeviceComplianceCellProps): React.JSX.Element => {
    const status = getDeviceComplianceStatus(row.original);

    return (
        <Box
            pt="2x"
            data-id="device-compliance-box"
            data-testid="DeviceComplianceCell"
        >
            <ComplianceStatus
                status={status}
                data-testid="DeviceComplianceCell"
                data-id="device-compliance-status"
            />
        </Box>
    );
};
