import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getSpecificComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface LockScreenCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const LockScreenCell = ({
    row,
}: LockScreenCellProps): React.JSX.Element => {
    const status = getSpecificComplianceStatus(row.original, 'LOCK_SCREEN');

    return (
        <Box pt="2x" data-id="lock-screen-box" data-testid="LockScreenCell">
            <ComplianceStatus
                status={status}
                data-testid="LockScreenCell"
                data-id="lock-screen-status"
            />
        </Box>
    );
};
