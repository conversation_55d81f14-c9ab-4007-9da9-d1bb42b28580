import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getSpecificComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface IdentityMfaCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const IdentityMfaCell = ({
    row,
}: IdentityMfaCellProps): React.JSX.Element => {
    const status = getSpecificComplianceStatus(row.original, 'IDENTITY_MFA');

    return (
        <Box pt="2x" data-id="identity-mfa-box" data-testid="IdentityMfaCell">
            <ComplianceStatus
                status={status}
                data-testid="IdentityMfaCell"
                data-id="identity-mfa-status"
            />
        </Box>
    );
};
