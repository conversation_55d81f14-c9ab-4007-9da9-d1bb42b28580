import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getSyncStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface SyncStatusCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const SyncStatusCell = ({
    row,
}: SyncStatusCellProps): React.JSX.Element => {
    const status = getSyncStatus(row.original);

    return (
        <Box pt="2x" data-id="sync-status-box" data-testid="SyncStatusCell">
            <ComplianceStatus
                treatFailureAsPending
                status={status}
                data-testid="SyncStatusCell"
                data-id="sync-status"
                customText={{
                    success: t`Found in HRIS/IDP`,
                    failure: t`Not found in HRIS/IDP`,
                    pending: t`Not found in HRIS/IDP`,
                }}
            />
        </Box>
    );
};
