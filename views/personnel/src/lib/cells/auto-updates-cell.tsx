import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getSpecificComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface AutoUpdatesCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const AutoUpdatesCell = ({
    row,
}: AutoUpdatesCellProps): React.JSX.Element => {
    const status = getSpecificComplianceStatus(row.original, 'AUTO_UPDATES');

    return (
        <Box pt="2x" data-id="auto-updates-box" data-testid="AutoUpdatesCell">
            <ComplianceStatus
                status={status}
                data-testid="AutoUpdatesCell"
                data-id="auto-updates-status"
            />
        </Box>
    );
};
