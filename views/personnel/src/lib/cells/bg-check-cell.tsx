import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getSpecificComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface BgCheckCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const BgCheckCell = ({ row }: BgCheckCellProps): React.JSX.Element => {
    const status = getSpecificComplianceStatus(row.original, 'BG_CHECK');

    return (
        <Box pt="2x" data-id="bg-check-box" data-testid="BgCheckCell">
            <ComplianceStatus
                status={status}
                data-testid="BgCheckCell"
                data-id="bg-check-status"
            />
        </Box>
    );
};
