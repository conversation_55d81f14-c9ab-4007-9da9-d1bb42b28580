import { Box } from '@cosmos/components/box';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getSpecificComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface OffboardingEvidenceCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const OffboardingEvidenceCell = ({
    row,
}: OffboardingEvidenceCellProps): React.JSX.Element => {
    const { separationDate } = row.original;

    // If no separation date, show consistent empty value
    if (!separationDate) {
        return <EmptyValue label={t`No separation date`} />;
    }

    // If separation date exists, show compliance status
    const status = getSpecificComplianceStatus(row.original, 'OFFBOARDING');

    return (
        <Box
            pt="2x"
            data-id="offboarding-evidence-box"
            data-testid="OffboardingEvidenceCell"
        >
            <ComplianceStatus
                status={status}
                data-testid="OffboardingEvidenceCell"
                data-id="offboarding-evidence-status"
            />
        </Box>
    );
};
