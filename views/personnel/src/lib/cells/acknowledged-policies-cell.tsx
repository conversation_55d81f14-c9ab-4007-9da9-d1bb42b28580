import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getSpecificComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface AcknowledgedPoliciesCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const AcknowledgedPoliciesCell = ({
    row,
}: AcknowledgedPoliciesCellProps): React.JSX.Element => {
    const status = getSpecificComplianceStatus(
        row.original,
        'ACCEPTED_POLICIES',
    );

    return (
        <Box
            pt="2x"
            data-id="acknowledged-policies-box"
            data-testid="AcknowledgedPoliciesCell"
        >
            <ComplianceStatus
                status={status}
                data-testid="AcknowledgedPoliciesCell"
                data-id="acknowledged-policies-status"
            />
        </Box>
    );
};
