import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getSpecificComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface PasswordManagerCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const PasswordManagerCell = ({
    row,
}: PasswordManagerCellProps): React.JSX.Element => {
    const status = getSpecificComplianceStatus(
        row.original,
        'PASSWORD_MANAGER',
    );

    return (
        <Box
            pt="2x"
            data-id="password-manager-box"
            data-testid="PasswordManagerCell"
        >
            <ComplianceStatus
                status={status}
                data-testid="PasswordManagerCell"
                data-id="password-manager-status"
            />
        </Box>
    );
};
