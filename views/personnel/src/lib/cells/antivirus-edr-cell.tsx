import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getSpecificComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface AntivirusEdrCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const AntivirusEdrCell = ({
    row,
}: AntivirusEdrCellProps): React.JSX.Element => {
    const status = getSpecificComplianceStatus(row.original, 'ANTIVIRUS');

    return (
        <Box pt="2x" data-id="antivirus-edr-box" data-testid="AntivirusEdrCell">
            <ComplianceStatus
                status={status}
                data-testid="AntivirusEdrCell"
                data-id="antivirus-edr-status"
            />
        </Box>
    );
};
