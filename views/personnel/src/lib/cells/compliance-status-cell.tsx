import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getOverallComplianceStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface ComplianceStatusCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const ComplianceStatusCell = ({
    row,
}: ComplianceStatusCellProps): React.JSX.Element => {
    const status = getOverallComplianceStatus(row.original);

    return (
        <Box
            pt="2x"
            data-id="compliance-status-box"
            data-testid="ComplianceStatusCell"
        >
            <ComplianceStatus
                status={status}
                data-testid="ComplianceStatusCell"
                data-id="compliance-status"
            />
        </Box>
    );
};
