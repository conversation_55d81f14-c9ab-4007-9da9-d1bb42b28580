import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getHipaaTrainingStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface HipaaTrainingCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const HipaaTrainingCell = ({
    row,
}: HipaaTrainingCellProps): React.JSX.Element => {
    const status = getHipaaTrainingStatus(row.original);

    return (
        <Box
            pt="2x"
            data-id="hipaa-training-box"
            data-testid="HipaaTrainingCell"
        >
            <ComplianceStatus
                status={status}
                data-testid="HipaaTrainingCell"
                data-id="hipaa-training-status"
            />
        </Box>
    );
};
