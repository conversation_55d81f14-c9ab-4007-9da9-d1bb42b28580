import type {
    EmploymentStatusEnum,
    PersonnelDetailsResponseDto,
} from '@globals/api-sdk/types';
import { getEmploymentStatusLabel } from '../helpers/employment-status.helper';

interface EmploymentStatusCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const EmploymentStatusCell = ({
    row,
}: EmploymentStatusCellProps): string => {
    const status = row.original.employmentStatus as EmploymentStatusEnum;

    return getEmploymentStatusLabel(status);
};
