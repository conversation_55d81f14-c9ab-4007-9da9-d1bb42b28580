import { noop } from 'lodash-es';
import { useMemo } from 'react';
import { EmploymentStatusCellController } from '@controllers/personnel';
import { SelectField } from '@cosmos/components/select-field';
import type {
    EmploymentStatusEnum,
    PersonnelDetailsResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form } from '@ui/forms';
import { SeparationDateModal } from '../components/separation-date-modal';
import { SEPARATION_DATE_MODAL_ID } from '../constants/separation-date-modal.constants';
import { getEmploymentStatusOptions } from '../helpers/employment-status.helper';
import { openNotHumanReasonModal } from '../helpers/open-not-human-reason-modal';

interface InlineEmploymentStatusCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

type EmploymentStatus = EmploymentStatusEnum;

export const InlineEmploymentStatusCell = observer(
    ({
        row: { original },
    }: InlineEmploymentStatusCellProps): React.JSX.Element => {
        const { employmentStatus, id, startDate, user } = original;

        // Create a stable controller instance using useMemo to prevent recreation on every render
        // This ensures MobX reactive context is maintained properly
        const controller = useMemo(
            () => new EmploymentStatusCellController(id, employmentStatus),
            [id, employmentStatus],
        );

        const formId = `employment-status-form-${id}`;
        const options = getEmploymentStatusOptions();

        // Extract MobX observable values and methods in the observer component scope
        // to prevent accessing them outside reactive context in render functions
        const { isUpdating } = controller;
        const { openSeparationDateModal } = controller;
        const { updateEmploymentStatus } = controller;
        const { closeSeparationDateModal } = controller;
        const { handleSeparationDateSubmit } = controller;

        // Use a computed getter to make the selected option reactive to data changes
        const currentOption = options.find(
            (option) => option.value === employmentStatus,
        );

        const handleChange = (selectedValue: string | undefined): void => {
            if (!selectedValue || selectedValue === employmentStatus) {
                return;
            }

            // If selecting "FORMER_EMPLOYEE", "FORMER_CONTRACTOR", "SPECIAL_FORMER_EMPLOYEE", or "SPECIAL_FORMER_CONTRACTOR", open the separation date modal
            if (
                [
                    'FORMER_EMPLOYEE',
                    'FORMER_CONTRACTOR',
                    'SPECIAL_FORMER_EMPLOYEE',
                    'SPECIAL_FORMER_CONTRACTOR',
                ].includes(selectedValue)
            ) {
                const modalContent = () => (
                    <SeparationDateModal
                        personnelId={id}
                        employmentStatus={selectedValue as EmploymentStatus}
                        currentStartDate={startDate}
                        isLoading={isUpdating}
                        data-id="swSzf4nW"
                        data-testid="modalContent"
                        onClose={() => {
                            closeSeparationDateModal(SEPARATION_DATE_MODAL_ID);
                        }}
                        onSubmit={(data) => {
                            handleSeparationDateSubmit(
                                data,
                                SEPARATION_DATE_MODAL_ID,
                            );
                        }}
                    />
                );

                openSeparationDateModal(
                    startDate,
                    selectedValue as EmploymentStatus,
                    modalContent,
                    SEPARATION_DATE_MODAL_ID,
                );

                return;
            }

            // If selecting "OUT_OF_SCOPE" or "SERVICE_ACCOUNT", open the not human reason modal
            if (['OUT_OF_SCOPE', 'SERVICE_ACCOUNT'].includes(selectedValue)) {
                const { firstName, lastName, email } = user;
                const userFullName = `${firstName} ${lastName}`.trim() || email;

                openNotHumanReasonModal(
                    id,
                    userFullName,
                    selectedValue as EmploymentStatus,
                );

                return;
            }

            // For all other statuses, update directly
            updateEmploymentStatus(selectedValue as EmploymentStatus);
        };

        return (
            <Form
                hasExternalSubmitButton
                formId={formId}
                data-id="inline-employment-status-cell"
                data-testid="InlineEmploymentStatusCell"
                // Use key to force form re-render when employment status changes
                key={`${id}-${employmentStatus}`}
                schema={{
                    employmentStatus: {
                        type: 'custom',
                        label: t`Employment Status`,
                        isOptional: false,
                        initialValue: currentOption,
                        render: (fieldProps) => {
                            return (
                                <SelectField
                                    shouldHideLabel
                                    required
                                    readOnly={isUpdating}
                                    formId={fieldProps.formId}
                                    label={fieldProps.label}
                                    name={fieldProps.name}
                                    disabled={isUpdating}
                                    data-id={fieldProps['data-id']}
                                    defaultValue={currentOption}
                                    options={options}
                                    // Use currentOption directly to ensure it reflects the latest data
                                    value={currentOption}
                                    onChange={({ value: selectedValue }) => {
                                        handleChange(selectedValue);
                                        fieldProps.onChange();
                                    }}
                                />
                            );
                        },
                    },
                }}
                onSubmit={noop}
            />
        );
    },
);
