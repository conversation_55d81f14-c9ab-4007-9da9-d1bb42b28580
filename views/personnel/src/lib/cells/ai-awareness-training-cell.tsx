import { Box } from '@cosmos/components/box';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import { getAiTrainingStatus } from '../helpers/compliance-status.helper';
import { ComplianceStatus } from './ComplianceStatus.tsx';

interface AiAwarenessTrainingCellProps {
    row: { original: PersonnelDetailsResponseDto };
}

export const AiAwarenessTrainingCell = ({
    row,
}: AiAwarenessTrainingCellProps): React.JSX.Element => {
    const status = getAiTrainingStatus(row.original);

    return (
        <Box
            pt="2x"
            data-id="ai-awareness-training-box"
            data-testid="AiAwarenessTrainingCell"
        >
            <ComplianceStatus
                status={status}
                data-testid="AiAwarenessTrainingCell"
                data-id="ai-awareness-training-status"
            />
        </Box>
    );
};
