import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { EmploymentStatusEnum } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getEmploymentStatusLabel = (
    status: EmploymentStatusEnum,
): string => {
    switch (status) {
        case 'CURRENT_EMPLOYEE': {
            return t`Current Employee`;
        }
        case 'FORMER_EMPLOYEE': {
            return t`Former Employee`;
        }
        case 'CURRENT_CONTRACTOR': {
            return t`Current Contractor`;
        }
        case 'FORMER_CONTRACTOR': {
            return t`Former Contractor`;
        }
        case 'OUT_OF_SCOPE': {
            return t`Out of Scope`;
        }
        case 'UNKNOWN': {
            return t`Unknown`;
        }
        case 'SPECIAL_FORMER_EMPLOYEE': {
            return t`Special Former Employee`;
        }
        case 'SPECIAL_FORMER_CONTRACTOR': {
            return t`Special Former Contractor`;
        }
        case 'FUTURE_HIRE': {
            return t`Future Hire`;
        }
        case 'SERVICE_ACCOUNT': {
            return t`Service Account`;
        }
    }
};

export const getEmploymentStatusOptions = (): ListBoxItemData[] => {
    const statuses: EmploymentStatusEnum[] = [
        'CURRENT_EMPLOYEE',
        'FORMER_EMPLOYEE',
        'CURRENT_CONTRACTOR',
        'FORMER_CONTRACTOR',
        'OUT_OF_SCOPE',
        'UNKNOWN',
        'SPECIAL_FORMER_EMPLOYEE',
        'SPECIAL_FORMER_CONTRACTOR',
        'FUTURE_HIRE',
        'SERVICE_ACCOUNT',
    ];

    return statuses.map((status) => ({
        id: status,
        value: status,
        label: getEmploymentStatusLabel(status),
    }));
};
