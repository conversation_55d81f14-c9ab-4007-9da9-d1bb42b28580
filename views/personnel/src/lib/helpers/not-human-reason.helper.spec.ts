import { describe, expect, test } from 'vitest';
import type { EmploymentStatusEnum } from '@globals/api-sdk/types';
import {
    getHelpText,
    getReasonLabel,
    getStatusLabel,
} from './not-human-reason.helper';

describe('not-human-reason.helper', () => {
    describe('getStatusLabel', () => {
        test('should return correct label for OUT_OF_SCOPE', () => {
            expect(getStatusLabel('OUT_OF_SCOPE')).toBe('Out of Scope');
        });

        test('should return correct label for SERVICE_ACCOUNT', () => {
            expect(getStatusLabel('SERVICE_ACCOUNT')).toBe('Service Account');
        });

        test('should return the status as-is for other employment statuses', () => {
            const otherStatuses: EmploymentStatusEnum[] = [
                'CURRENT_EMPLOYEE',
                'FORMER_EMPLOYEE',
                'CURRENT_CONTRACTOR',
                'FORMER_CONTRACTOR',
                'UNKNOWN',
                'SPECIAL_FORMER_EMPLOYEE',
                'SPECIAL_FORMER_CONTRACTOR',
                'FUTURE_HIRE',
            ];

            otherStatuses.forEach((status) => {
                expect(getStatusLabel(status)).toBe(status);
            });
        });
    });

    describe('getReasonLabel', () => {
        test('should return correct reason label for OUT_OF_SCOPE', () => {
            expect(getReasonLabel('OUT_OF_SCOPE')).toBe(
                'Reason for out of scope status',
            );
        });

        test('should return correct reason label for SERVICE_ACCOUNT', () => {
            expect(getReasonLabel('SERVICE_ACCOUNT')).toBe(
                'Reason for service account status',
            );
        });

        test('should return generic reason label for other employment statuses', () => {
            const otherStatuses: EmploymentStatusEnum[] = [
                'CURRENT_EMPLOYEE',
                'FORMER_EMPLOYEE',
                'CURRENT_CONTRACTOR',
                'FORMER_CONTRACTOR',
                'UNKNOWN',
                'SPECIAL_FORMER_EMPLOYEE',
                'SPECIAL_FORMER_CONTRACTOR',
                'FUTURE_HIRE',
            ];

            otherStatuses.forEach((status) => {
                expect(getReasonLabel(status)).toBe('Reason');
            });
        });
    });

    describe('getHelpText', () => {
        test('should return correct help text for OUT_OF_SCOPE', () => {
            expect(getHelpText('OUT_OF_SCOPE')).toBe(
                'Please explain why this personnel is marked as out of scope.',
            );
        });

        test('should return correct help text for SERVICE_ACCOUNT', () => {
            expect(getHelpText('SERVICE_ACCOUNT')).toBe(
                'Please explain why this personnel is marked as a service account.',
            );
        });

        test('should return generic help text for other employment statuses', () => {
            const otherStatuses: EmploymentStatusEnum[] = [
                'CURRENT_EMPLOYEE',
                'FORMER_EMPLOYEE',
                'CURRENT_CONTRACTOR',
                'FORMER_CONTRACTOR',
                'UNKNOWN',
                'SPECIAL_FORMER_EMPLOYEE',
                'SPECIAL_FORMER_CONTRACTOR',
                'FUTURE_HIRE',
            ];

            otherStatuses.forEach((status) => {
                expect(getHelpText(status)).toBe(
                    'Please provide a reason for this status change.',
                );
            });
        });
    });

    describe('integration tests', () => {
        test('should provide consistent messaging for OUT_OF_SCOPE status', () => {
            const status: EmploymentStatusEnum = 'OUT_OF_SCOPE';

            expect(getStatusLabel(status)).toBe('Out of Scope');
            expect(getReasonLabel(status)).toBe(
                'Reason for out of scope status',
            );
            expect(getHelpText(status)).toBe(
                'Please explain why this personnel is marked as out of scope.',
            );
        });

        test('should provide consistent messaging for SERVICE_ACCOUNT status', () => {
            const status: EmploymentStatusEnum = 'SERVICE_ACCOUNT';

            expect(getStatusLabel(status)).toBe('Service Account');
            expect(getReasonLabel(status)).toBe(
                'Reason for service account status',
            );
            expect(getHelpText(status)).toBe(
                'Please explain why this personnel is marked as a service account.',
            );
        });

        test('should provide consistent generic messaging for other statuses', () => {
            const status: EmploymentStatusEnum = 'CURRENT_EMPLOYEE';

            expect(getStatusLabel(status)).toBe('CURRENT_EMPLOYEE');
            expect(getReasonLabel(status)).toBe('Reason');
            expect(getHelpText(status)).toBe(
                'Please provide a reason for this status change.',
            );
        });
    });
});
