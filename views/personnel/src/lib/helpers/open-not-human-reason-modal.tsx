import { modalController } from '@controllers/modal';
import type { EmploymentStatusEnum } from '@globals/api-sdk/types';
import { NotHumanReasonModal } from '../components/not-human-reason-modal';

const NOT_HUMAN_REASON_MODAL_ID = 'not-human-reason-modal';

export const openNotHumanReasonModal = (
    personnelId: number,
    userFullName: string,
    employmentStatus: EmploymentStatusEnum,
): void => {
    modalController.openModal({
        id: NOT_HUMAN_REASON_MODAL_ID,
        content: () => (
            <NotHumanReasonModal
                personnelId={personnelId}
                userFullName={userFullName}
                employmentStatus={employmentStatus}
                data-id="O_u9DNTZ"
                onClose={() => {
                    modalController.closeModal(NOT_HUMAN_REASON_MODAL_ID);
                }}
            />
        ),
        centered: true,
        disableClickOutsideToClose: true,
        size: 'md',
    });
};
