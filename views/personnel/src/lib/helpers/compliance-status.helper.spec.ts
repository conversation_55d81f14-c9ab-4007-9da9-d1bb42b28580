import { describe, expect, test } from 'vitest';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import {
    getAiTrainingStatus,
    getComplianceStatus,
    getDeviceComplianceStatus,
    getHipaaTrainingStatus,
    getOverallComplianceStatus,
    getSecurityTrainingStatus,
    getSpecificComplianceStatus,
    getSyncStatus,
} from './compliance-status.helper';

describe('compliance-status.helper', () => {
    describe('getComplianceStatus', () => {
        test('should return PASS for true boolean value', () => {
            expect(getComplianceStatus(true)).toBe('PASS');
        });

        test('should return FAIL for false boolean value', () => {
            expect(getComplianceStatus(false)).toBe('FAIL');
        });

        test('should return PASS for object with isCompliant true', () => {
            expect(getComplianceStatus({ isCompliant: true })).toBe('PASS');
        });

        test('should return FAIL for object with isCompliant false', () => {
            expect(getComplianceStatus({ isCompliant: false })).toBe('FAIL');
        });

        test('should return PASS for object with compliant true', () => {
            expect(getComplianceStatus({ compliant: true })).toBe('PASS');
        });

        test('should return FAIL for object with compliant false', () => {
            expect(getComplianceStatus({ compliant: false })).toBe('FAIL');
        });

        test('should return PASS for PASS status', () => {
            expect(getComplianceStatus({ status: 'PASS' })).toBe('PASS');
        });

        test('should return PASS for COMPLIANT status', () => {
            expect(getComplianceStatus({ status: 'COMPLIANT' })).toBe('PASS');
        });

        test('should return PASS for PASSED status', () => {
            expect(getComplianceStatus({ status: 'PASSED' })).toBe('PASS');
        });

        test('should return PASS for SUCCESS status', () => {
            expect(getComplianceStatus({ status: 'SUCCESS' })).toBe('PASS');
        });

        test('should return FAIL for FAIL status', () => {
            expect(getComplianceStatus({ status: 'FAIL' })).toBe('FAIL');
        });

        test('should return FAIL for NON_COMPLIANT status', () => {
            expect(getComplianceStatus({ status: 'NON_COMPLIANT' })).toBe(
                'FAIL',
            );
        });

        test('should return FAIL for FAILED status', () => {
            expect(getComplianceStatus({ status: 'FAILED' })).toBe('FAIL');
        });

        test('should return FAIL for FAILURE status', () => {
            expect(getComplianceStatus({ status: 'FAILURE' })).toBe('FAIL');
        });

        test('should return MISCONFIGURED for MISCONFIGURED status', () => {
            expect(getComplianceStatus({ status: 'MISCONFIGURED' })).toBe(
                'MISCONFIGURED',
            );
        });

        test('should return EXCLUDED for EXCLUDED status', () => {
            expect(getComplianceStatus({ status: 'EXCLUDED' })).toBe(
                'EXCLUDED',
            );
        });

        test('should return EXCLUDED for OUT_OF_SCOPE status', () => {
            expect(getComplianceStatus({ status: 'OUT_OF_SCOPE' })).toBe(
                'EXCLUDED',
            );
        });

        test('should return EXCLUDED for unknown status values', () => {
            expect(getComplianceStatus({ status: 'UNKNOWN_STATUS' })).toBe(
                'EXCLUDED',
            );
            expect(getComplianceStatus({ status: 'UNABLE_TO_GET_DATA' })).toBe(
                'EXCLUDED',
            );
            expect(getComplianceStatus({ status: 'PENDING' })).toBe('EXCLUDED');
        });

        test('should return fallback status for null value', () => {
            expect(getComplianceStatus(null)).toBe('EXCLUDED');
            expect(getComplianceStatus(null, 'PASS')).toBe('PASS');
        });

        test('should return fallback status for undefined value', () => {
            expect(getComplianceStatus(undefined)).toBe('EXCLUDED');
            expect(getComplianceStatus(undefined, 'FAIL')).toBe('FAIL');
        });

        test('should return fallback status for string value', () => {
            expect(getComplianceStatus('some string')).toBe('EXCLUDED');
        });

        test('should return fallback status for number value', () => {
            expect(getComplianceStatus(123)).toBe('EXCLUDED');
        });

        test('should handle empty array', () => {
            expect(getComplianceStatus([])).toBe('EXCLUDED');
        });

        test('should return FAIL for array with failure items', () => {
            const arrayWithFailure = [
                { isCompliant: true },
                { isCompliant: false },
                { status: 'PASS' },
            ];

            expect(getComplianceStatus(arrayWithFailure)).toBe('FAIL');
        });

        test('should return PASS for array with only success items', () => {
            const arrayWithSuccess = [
                { isCompliant: true },
                { status: 'PASS' },
                { compliant: true },
            ];

            expect(getComplianceStatus(arrayWithSuccess)).toBe('PASS');
        });

        test('should return fallback for array with no clear status', () => {
            const arrayWithoutStatus = [
                { someOtherField: 'value' },
                { anotherField: 123 },
            ];

            expect(getComplianceStatus(arrayWithoutStatus)).toBe('EXCLUDED');
        });

        test('should prioritize failure over success in arrays', () => {
            const mixedArray = [
                { status: 'PASS' },
                { status: 'FAIL' },
                { isCompliant: true },
            ];

            expect(getComplianceStatus(mixedArray)).toBe('FAIL');
        });
    });

    describe('getOverallComplianceStatus', () => {
        test('should return PASS when isFullyCompliant is true', () => {
            const personnel = {
                isFullyCompliant: true,
            } as PersonnelDetailsResponseDto;

            expect(getOverallComplianceStatus(personnel)).toBe('PASS');
        });

        test('should return FAIL when isFullyCompliant is false', () => {
            const personnel = {
                isFullyCompliant: false,
            } as PersonnelDetailsResponseDto;

            expect(getOverallComplianceStatus(personnel)).toBe('FAIL');
        });

        test('should check FULL_COMPLIANCE in complianceChecks when isFullyCompliant not present', () => {
            const personnel = {
                complianceChecks: [{ type: 'FULL_COMPLIANCE', status: 'PASS' }],
            } as PersonnelDetailsResponseDto;

            expect(getOverallComplianceStatus(personnel)).toBe('PASS');
        });

        test('should return EXCLUDED when no compliance data available', () => {
            const personnel = {} as PersonnelDetailsResponseDto;

            expect(getOverallComplianceStatus(personnel)).toBe('EXCLUDED');
        });
    });

    describe('getDeviceComplianceStatus', () => {
        test('should return EXCLUDED when devices array is missing', () => {
            const personnel = {} as PersonnelDetailsResponseDto;

            expect(getDeviceComplianceStatus(personnel)).toBe('EXCLUDED');
        });

        test('should return EXCLUDED when devices array is not an array', () => {
            const personnel = {
                devices: null,
            } as unknown as PersonnelDetailsResponseDto;

            expect(getDeviceComplianceStatus(personnel)).toBe('EXCLUDED');
        });

        test('should return EXCLUDED when devices array is empty', () => {
            const personnel = {
                devices: [],
            } as PersonnelDetailsResponseDto;

            expect(getDeviceComplianceStatus(personnel)).toBe('EXCLUDED');
        });

        test('should return FAIL when any device is not compliant', () => {
            const personnel = {
                devices: [
                    { isDeviceCompliant: true },
                    { isDeviceCompliant: false },
                    { isDeviceCompliant: true },
                ],
            } as PersonnelDetailsResponseDto;

            expect(getDeviceComplianceStatus(personnel)).toBe('FAIL');
        });

        test('should return PASS when all devices are compliant', () => {
            const personnel = {
                devices: [
                    { isDeviceCompliant: true },
                    { isDeviceCompliant: true },
                ],
            } as PersonnelDetailsResponseDto;

            expect(getDeviceComplianceStatus(personnel)).toBe('PASS');
        });

        test('should return EXCLUDED when some devices have no compliance status', () => {
            const personnel = {
                devices: [
                    { isDeviceCompliant: true },
                    { someOtherField: 'value' },
                ],
            } as PersonnelDetailsResponseDto;

            expect(getDeviceComplianceStatus(personnel)).toBe('EXCLUDED');
        });
    });

    describe('getSecurityTrainingStatus', () => {
        test('should return status from SECURITY_TRAINING compliance check', () => {
            const personnel = {
                complianceChecks: [
                    { type: 'SECURITY_TRAINING', status: 'PASS' },
                ],
            } as PersonnelDetailsResponseDto;

            expect(getSecurityTrainingStatus(personnel)).toBe('PASS');
        });

        test('should return pending when no SECURITY_TRAINING check found', () => {
            const personnel = {
                complianceChecks: [{ type: 'OTHER_CHECK', status: 'PASS' }],
            } as PersonnelDetailsResponseDto;

            expect(getSecurityTrainingStatus(personnel)).toBe('EXCLUDED');
        });
    });

    describe('getHipaaTrainingStatus', () => {
        test('should return status from HIPAA_TRAINING compliance check', () => {
            const personnel = {
                complianceChecks: [{ type: 'HIPAA_TRAINING', status: 'FAIL' }],
            } as PersonnelDetailsResponseDto;

            expect(getHipaaTrainingStatus(personnel)).toBe('FAIL');
        });
    });

    describe('getAiTrainingStatus', () => {
        test('should return status from NIST_AI_TRAINING compliance check', () => {
            const personnel = {
                complianceChecks: [
                    { type: 'NIST_AI_TRAINING', status: 'PASS' },
                ],
            } as PersonnelDetailsResponseDto;

            expect(getAiTrainingStatus(personnel)).toBe('PASS');
        });
    });

    describe('getSyncStatus', () => {
        test('should return success when user has external identities', () => {
            const personnel = {
                user: {
                    identities: [{ provider: 'OKTA', id: '123' }],
                },
            } as PersonnelDetailsResponseDto;

            expect(getSyncStatus(personnel)).toBe('PASS');
        });

        test('should return failure when user has no identities', () => {
            const personnel = {
                user: {
                    identities: [],
                },
            } as PersonnelDetailsResponseDto;

            expect(getSyncStatus(personnel)).toBe('FAIL');
        });

        test('should return failure when user identities is not an array', () => {
            const personnel = {
                user: {
                    identities: null,
                },
            } as unknown as PersonnelDetailsResponseDto;

            expect(getSyncStatus(personnel)).toBe('FAIL');
        });

        test('should return failure when user is missing', () => {
            const personnel = {} as PersonnelDetailsResponseDto;

            expect(getSyncStatus(personnel)).toBe('FAIL');
        });
    });

    describe('getSpecificComplianceStatus', () => {
        test('should return status for matching check type', () => {
            const personnel = {
                complianceChecks: [
                    { type: 'BACKGROUND_CHECK', status: 'PASS' },
                    { type: 'MFA_CHECK', status: 'FAIL' },
                ],
            } as PersonnelDetailsResponseDto;

            expect(
                getSpecificComplianceStatus(personnel, 'BACKGROUND_CHECK'),
            ).toBe('PASS');
            expect(getSpecificComplianceStatus(personnel, 'MFA_CHECK')).toBe(
                'FAIL',
            );
        });

        test('should return status for matching type field', () => {
            const personnel = {
                complianceChecks: [
                    { type: 'BACKGROUND_CHECK', status: 'PASS' },
                ],
            } as PersonnelDetailsResponseDto;

            expect(
                getSpecificComplianceStatus(personnel, 'BACKGROUND_CHECK'),
            ).toBe('PASS');
        });

        test('should return pending when check type not found', () => {
            const personnel = {
                complianceChecks: [{ type: 'OTHER_CHECK', status: 'PASS' }],
            } as PersonnelDetailsResponseDto;

            expect(
                getSpecificComplianceStatus(personnel, 'MISSING_CHECK'),
            ).toBe('EXCLUDED');
        });

        test('should return pending when complianceChecks is not an array', () => {
            const personnel = {
                complianceChecks: null,
            } as unknown as PersonnelDetailsResponseDto;

            expect(getSpecificComplianceStatus(personnel, 'ANY_CHECK')).toBe(
                'EXCLUDED',
            );
        });

        test('should return pending when complianceChecks is missing', () => {
            const personnel = {} as PersonnelDetailsResponseDto;

            expect(getSpecificComplianceStatus(personnel, 'ANY_CHECK')).toBe(
                'EXCLUDED',
            );
        });

        test('should return EXCLUDED for out of scope personnel regardless of compliance checks', () => {
            const personnel = {
                employmentStatus: 'OUT_OF_SCOPE',
                complianceChecks: [
                    { type: 'BACKGROUND_CHECK', status: 'PASS' },
                    { type: 'MFA_CHECK', status: 'FAIL' },
                ],
            } as PersonnelDetailsResponseDto;

            expect(
                getSpecificComplianceStatus(personnel, 'BACKGROUND_CHECK'),
            ).toBe('EXCLUDED');
            expect(getSpecificComplianceStatus(personnel, 'MFA_CHECK')).toBe(
                'EXCLUDED',
            );
        });
    });
});
