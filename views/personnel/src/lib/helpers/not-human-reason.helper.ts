import type { EmploymentStatusEnum } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getStatusLabel = (status: EmploymentStatusEnum): string => {
    switch (status) {
        case 'OUT_OF_SCOPE': {
            return t`Out of Scope`;
        }
        case 'SERVICE_ACCOUNT': {
            return t`Service Account`;
        }
        default: {
            return status;
        }
    }
};

export const getReasonLabel = (status: EmploymentStatusEnum): string => {
    switch (status) {
        case 'OUT_OF_SCOPE': {
            return t`Reason for out of scope status`;
        }
        case 'SERVICE_ACCOUNT': {
            return t`Reason for service account status`;
        }
        default: {
            return t`Reason`;
        }
    }
};

export const getHelpText = (status: EmploymentStatusEnum): string => {
    switch (status) {
        case 'OUT_OF_SCOPE': {
            return t`Please explain why this personnel is marked as out of scope.`;
        }
        case 'SERVICE_ACCOUNT': {
            return t`Please explain why this personnel is marked as a service account.`;
        }
        default: {
            return t`Please provide a reason for this status change.`;
        }
    }
};
