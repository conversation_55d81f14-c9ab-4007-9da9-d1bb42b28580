import { isBoolean, isEmpty, isObject } from 'lodash-es';
import type { PersonnelDetailsResponseDto } from '@globals/api-sdk/types';
import type { StatusType } from '../cells/ComplianceStatus.tsx';

/**
 * Determines the compliance status based on the data structure.
 * This is a generic helper that can be used for different compliance checks.
 */
export const getComplianceStatus = (
    value: unknown,
    fallbackStatus: StatusType = 'EXCLUDED',
): StatusType => {
    // Handle boolean values
    if (isBoolean(value)) {
        return value ? 'PASS' : 'FAIL';
    }

    // Handle objects with compliance status properties
    if (value && isObject(value)) {
        const obj = value as Record<string, unknown>;

        // Check for common compliance status fields
        if ('isCompliant' in obj && isBoolean(obj.isCompliant)) {
            return obj.isCompliant ? 'PASS' : 'FAIL';
        }

        if ('compliant' in obj && isBoolean(obj.compliant)) {
            return obj.compliant ? 'PASS' : 'FAIL';
        }

        if ('status' in obj) {
            const { status } = obj;

            // Handle API status values: PASS, FAIL, EXCLUDED, MISCONFIGURED
            if (
                status === 'PASS' ||
                status === 'COMPLIANT' ||
                status === 'PASSED' ||
                status === 'SUCCESS'
            ) {
                return 'PASS';
            }
            if (
                status === 'FAIL' ||
                status === 'NON_COMPLIANT' ||
                status === 'FAILED' ||
                status === 'FAILURE'
            ) {
                return 'FAIL';
            }
            if (status === 'MISCONFIGURED') {
                return 'MISCONFIGURED';
            }

            // EXCLUDED and OUT_OF_SCOPE are treated as excluded
            // For any other status value, treat as excluded
            return 'EXCLUDED';
        }
    }

    // Handle arrays (e.g., compliance checks array)
    if (Array.isArray(value)) {
        if (isEmpty(value)) {
            return fallbackStatus;
        }

        // If any item has FAIL status, return failure
        const hasFailure = value.some((item) => {
            if (isObject(item)) {
                const obj = item as Record<string, unknown>;

                return (
                    obj.isCompliant === false ||
                    obj.compliant === false ||
                    obj.status === 'FAIL' ||
                    obj.status === 'NON_COMPLIANT' ||
                    obj.status === 'FAILED'
                );
            }

            return false;
        });

        if (hasFailure) {
            return 'FAIL';
        }

        // If any item has PASS status, return success
        const hasSuccess = value.some((item) => {
            if (isObject(item)) {
                const obj = item as Record<string, unknown>;

                return (
                    obj.isCompliant === true ||
                    obj.compliant === true ||
                    obj.status === 'PASS' ||
                    obj.status === 'COMPLIANT' ||
                    obj.status === 'PASSED'
                );
            }

            return false;
        });

        return hasSuccess ? 'PASS' : fallbackStatus;
    }

    return fallbackStatus;
};

/**
 * Gets specific compliance check status from the main complianceChecks array.
 */
export const getSpecificComplianceStatus = (
    personnel: PersonnelDetailsResponseDto,
    checkType: string,
): StatusType => {
    // If personnel is out of scope, always return excluded
    if (personnel.employmentStatus === 'OUT_OF_SCOPE') {
        return 'EXCLUDED';
    }

    const { complianceChecks } = personnel;

    if (Array.isArray(complianceChecks)) {
        const specificCheck = complianceChecks.find(
            (check: PersonnelDetailsResponseDto['complianceChecks'][number]) =>
                check.type === checkType,
        );

        if (specificCheck) {
            return getComplianceStatus(specificCheck, 'EXCLUDED');
        }
    }

    // If no specific check found, return excluded
    return 'EXCLUDED';
};

/**
 * Gets the overall compliance status for a personnel record.
 */
export const getOverallComplianceStatus = (
    personnel: PersonnelDetailsResponseDto,
): StatusType => {
    // Check if there's an overall compliance field
    if (
        'isFullyCompliant' in personnel &&
        isBoolean(personnel.isFullyCompliant)
    ) {
        return personnel.isFullyCompliant ? 'PASS' : 'FAIL';
    }

    // Look for FULL_COMPLIANCE check type in complianceChecks array
    return getSpecificComplianceStatus(personnel, 'FULL_COMPLIANCE');
};

/**
 * Gets device compliance status.
 */
export const getDeviceComplianceStatus = (
    personnel: PersonnelDetailsResponseDto,
): StatusType => {
    // If no devices, return excluded
    if (isEmpty(personnel.devices)) {
        return 'EXCLUDED';
    }

    // Check if any device has undefined compliance status first
    const hasUndefinedStatus = personnel.devices.some(
        (device: PersonnelDetailsResponseDto['devices'][number]) => {
            return !('isDeviceCompliant' in device);
        },
    );

    if (hasUndefinedStatus) {
        return 'EXCLUDED';
    }

    // Check device compliance status
    // If any device is not compliant, return failure
    const hasNonCompliantDevice = personnel.devices.some(
        (device: PersonnelDetailsResponseDto['devices'][number]) => {
            return !device.isDeviceCompliant;
        },
    );

    if (hasNonCompliantDevice) {
        return 'FAIL';
    }

    // If all devices have isDeviceCompliant === true, return success
    const allDevicesCompliant = personnel.devices.every(
        (device: PersonnelDetailsResponseDto['devices'][number]) => {
            return device.isDeviceCompliant;
        },
    );

    if (allDevicesCompliant) {
        return 'PASS';
    }

    // If we get here, some devices are explicitly false
    return 'EXCLUDED';
};

/**
 * Gets security training compliance status.
 */
export const getSecurityTrainingStatus = (
    personnel: PersonnelDetailsResponseDto,
): StatusType => {
    return getSpecificComplianceStatus(personnel, 'SECURITY_TRAINING');
};

/**
 * Gets HIPAA training compliance status.
 */
export const getHipaaTrainingStatus = (
    personnel: PersonnelDetailsResponseDto,
): StatusType => {
    return getSpecificComplianceStatus(personnel, 'HIPAA_TRAINING');
};

/**
 * Gets AI awareness training compliance status.
 */
export const getAiTrainingStatus = (
    personnel: PersonnelDetailsResponseDto,
): StatusType => {
    return getSpecificComplianceStatus(personnel, 'NIST_AI_TRAINING');
};

/**
 * Gets sync status - determines if personnel is found in HRIS/IDP systems.
 */
export const getSyncStatus = (
    personnel: PersonnelDetailsResponseDto,
): StatusType => {
    // Check if user has identities from external providers (HRIS/IDP)
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- Runtime safety check for test scenarios
    if (personnel.user) {
        const { identities } = personnel.user;

        if (identities && Array.isArray(identities) && !isEmpty(identities)) {
            // If user has external identities, they are found in HRIS/IDP
            return 'PASS'; // Found in HRIS/IDP
        }
    }

    // If no external identities found, they are not found in HRIS/IDP
    return 'FAIL'; // Not found in HRIS/IDP
};
