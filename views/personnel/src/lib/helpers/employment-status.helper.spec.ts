import { describe, expect, test } from 'vitest';
import type { EmploymentStatusEnum } from '@globals/api-sdk/types';
import {
    getEmploymentStatusLabel,
    getEmploymentStatusOptions,
} from './employment-status.helper';

describe('employment-status.helper', () => {
    describe('getEmploymentStatusLabel', () => {
        test('should return correct label for CURRENT_EMPLOYEE', () => {
            expect(getEmploymentStatusLabel('CURRENT_EMPLOYEE')).toBe(
                'Current Employee',
            );
        });

        test('should return correct label for FORMER_EMPLOYEE', () => {
            expect(getEmploymentStatusLabel('FORMER_EMPLOYEE')).toBe(
                'Former Employee',
            );
        });

        test('should return correct label for CURRENT_CONTRACTOR', () => {
            expect(getEmploymentStatusLabel('CURRENT_CONTRACTOR')).toBe(
                'Current Contractor',
            );
        });

        test('should return correct label for FORMER_CONTRACTOR', () => {
            expect(getEmploymentStatusLabel('FORMER_CONTRACTOR')).toBe(
                'Former Contractor',
            );
        });

        test('should return correct label for OUT_OF_SCOPE', () => {
            expect(getEmploymentStatusLabel('OUT_OF_SCOPE')).toBe(
                'Out of Scope',
            );
        });

        test('should return correct label for UNKNOWN', () => {
            expect(getEmploymentStatusLabel('UNKNOWN')).toBe('Unknown');
        });

        test('should return correct label for SPECIAL_FORMER_EMPLOYEE', () => {
            expect(getEmploymentStatusLabel('SPECIAL_FORMER_EMPLOYEE')).toBe(
                'Special Former Employee',
            );
        });

        test('should return correct label for SPECIAL_FORMER_CONTRACTOR', () => {
            expect(getEmploymentStatusLabel('SPECIAL_FORMER_CONTRACTOR')).toBe(
                'Special Former Contractor',
            );
        });

        test('should return correct label for FUTURE_HIRE', () => {
            expect(getEmploymentStatusLabel('FUTURE_HIRE')).toBe('Future Hire');
        });

        test('should return correct label for SERVICE_ACCOUNT', () => {
            expect(getEmploymentStatusLabel('SERVICE_ACCOUNT')).toBe(
                'Service Account',
            );
        });
    });

    describe('getEmploymentStatusOptions', () => {
        test('should return array of ListBoxItemData with all employment statuses', () => {
            const options = getEmploymentStatusOptions();

            expect(options).toHaveLength(10);
            expect(options).toStrictEqual([
                {
                    id: 'CURRENT_EMPLOYEE',
                    value: 'CURRENT_EMPLOYEE',
                    label: 'Current Employee',
                },
                {
                    id: 'FORMER_EMPLOYEE',
                    value: 'FORMER_EMPLOYEE',
                    label: 'Former Employee',
                },
                {
                    id: 'CURRENT_CONTRACTOR',
                    value: 'CURRENT_CONTRACTOR',
                    label: 'Current Contractor',
                },
                {
                    id: 'FORMER_CONTRACTOR',
                    value: 'FORMER_CONTRACTOR',
                    label: 'Former Contractor',
                },
                {
                    id: 'OUT_OF_SCOPE',
                    value: 'OUT_OF_SCOPE',
                    label: 'Out of Scope',
                },
                {
                    id: 'UNKNOWN',
                    value: 'UNKNOWN',
                    label: 'Unknown',
                },
                {
                    id: 'SPECIAL_FORMER_EMPLOYEE',
                    value: 'SPECIAL_FORMER_EMPLOYEE',
                    label: 'Special Former Employee',
                },
                {
                    id: 'SPECIAL_FORMER_CONTRACTOR',
                    value: 'SPECIAL_FORMER_CONTRACTOR',
                    label: 'Special Former Contractor',
                },
                {
                    id: 'FUTURE_HIRE',
                    value: 'FUTURE_HIRE',
                    label: 'Future Hire',
                },
                {
                    id: 'SERVICE_ACCOUNT',
                    value: 'SERVICE_ACCOUNT',
                    label: 'Service Account',
                },
            ]);
        });

        test('should have consistent id, value, and label mapping', () => {
            const options = getEmploymentStatusOptions();

            options.forEach((option) => {
                expect(option.id).toBe(option.value);
                expect(option.label).toBe(
                    getEmploymentStatusLabel(
                        option.value as EmploymentStatusEnum,
                    ),
                );
            });
        });

        test('should include all employment status enum values', () => {
            const options = getEmploymentStatusOptions();
            const expectedStatuses: EmploymentStatusEnum[] = [
                'CURRENT_EMPLOYEE',
                'FORMER_EMPLOYEE',
                'CURRENT_CONTRACTOR',
                'FORMER_CONTRACTOR',
                'OUT_OF_SCOPE',
                'UNKNOWN',
                'SPECIAL_FORMER_EMPLOYEE',
                'SPECIAL_FORMER_CONTRACTOR',
                'FUTURE_HIRE',
                'SERVICE_ACCOUNT',
            ];

            const optionValues = options.map((option) => option.value);

            expect(optionValues).toStrictEqual(expectedStatuses);
        });
    });
});
