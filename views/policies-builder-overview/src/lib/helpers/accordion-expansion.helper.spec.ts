import { describe, expect, test } from 'vitest';
import type { PolicyApprovalReviewGroupResponseDto } from '@globals/api-sdk/types';
import { shouldExpandAccordion } from './accordion-expansion.helper';

const getBaseReviewGroup = (): PolicyApprovalReviewGroupResponseDto => ({
    id: 1,
    consensusDecision: undefined,
    consensusReached: false,
    reviews: [],
    deadline: '',
    tier: 1,
    consensusRule: 'ALL',
    name: 'Test Group',
});

describe('shouldExpandAccordion', () => {
    describe('given a review group with consensusDecision', () => {
        describe('when consensusDecision is null', () => {
            test('then should return false', () => {
                const reviewGroup: PolicyApprovalReviewGroupResponseDto = {
                    id: 1,
                    consensusDecision: undefined,
                    consensusReached: false,
                    reviews: [],
                    deadline: '',
                    tier: 1,
                    consensusRule: 'ALL',
                    name: 'Test Group',
                };

                const result = shouldExpandAccordion(reviewGroup);

                expect(result).toBeFalsy();
            });
        });

        describe('when consensusDecision is APPROVED', () => {
            test('then should return false', () => {
                const reviewGroup = {
                    ...getBaseReviewGroup(),
                    consensusDecision: 'APPROVED' as const,
                };

                const result = shouldExpandAccordion(reviewGroup);

                expect(result).toBeFalsy();
            });
        });

        describe('when consensusDecision is CHANGES_REQUESTED', () => {
            test('then should return true', () => {
                const reviewGroup = {
                    ...getBaseReviewGroup(),
                    consensusDecision: 'CHANGES_REQUESTED' as const,
                };

                const result = shouldExpandAccordion(reviewGroup);

                expect(result).toBeTruthy();
            });
        });

        describe('when consensusDecision is READY_FOR_REVIEW', () => {
            test('then should return true', () => {
                const reviewGroup = {
                    ...getBaseReviewGroup(),
                    consensusDecision: 'READY_FOR_REVIEW' as const,
                };

                const result = shouldExpandAccordion(reviewGroup);

                expect(result).toBeTruthy();
            });
        });

        describe('when consensusDecision is DEADLINE_EXCEEDED', () => {
            test('then should return true', () => {
                const reviewGroup = {
                    ...getBaseReviewGroup(),
                    consensusDecision: 'DEADLINE_EXCEEDED' as const,
                };

                const result = shouldExpandAccordion(reviewGroup);

                expect(result).toBeTruthy();
            });
        });
    });
});
