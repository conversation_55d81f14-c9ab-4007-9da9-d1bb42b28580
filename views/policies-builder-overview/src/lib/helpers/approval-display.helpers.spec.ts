import { describe, expect, test, vi } from 'vitest';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import {
    getApprovalNotRequiredMessage,
    shouldHideApprovalCardActions,
    shouldShowNoApprovalMessage,
} from './approval-display.helpers';

vi.mock('@models/policy-builder', () => ({
    sharedPolicyBuilderModel: {
        isBambooHRProvider: false,
    },
}));

describe('shouldShowNoApprovalMessage', () => {
    describe('given isApprovedOrPublished and approvedAt', () => {
        test('when not approved or published, then should return false', () => {
            const result = shouldShowNoApprovalMessage(false, null);

            expect(result).toBeFalsy();
        });

        test('when approved with approvedAt date, then should return false', () => {
            const result = shouldShowNoApprovalMessage(true, '2024-01-01');

            expect(result).toBeFalsy();
        });

        test('when approved without approvedAt date, then should return true', () => {
            const result = shouldShowNoApprovalMessage(true, null);

            expect(result).toBeTruthy();
        });
    });
});

describe('getApprovalNotRequiredMessage', () => {
    describe('given clientType', () => {
        test('when BAMBOO_HR, then should return BambooHR message', () => {
            const result = getApprovalNotRequiredMessage('BAMBOO_HR');

            expect(result).toBe(
                'Please refer to BambooHR for approval details.',
            );
        });

        test('when MERGEDEV_BAMBOO_HR, then should return BambooHR message', () => {
            const result = getApprovalNotRequiredMessage('MERGEDEV_BAMBOO_HR');

            expect(result).toBe(
                'Please refer to BambooHR for approval details.',
            );
        });

        test('when other provider, then should return default message', () => {
            const result = getApprovalNotRequiredMessage('CUSTOM');

            expect(result).toBe('No approval was required for this version.');
        });

        test('when undefined, then should return default message', () => {
            const result = getApprovalNotRequiredMessage();

            expect(result).toBe('No approval was required for this version.');
        });
    });
});

describe('shouldHideApprovalCardActions', () => {
    describe('given approval parameters', () => {
        test('when has BambooHR provider, then should return true', () => {
            vi.mocked(sharedPolicyBuilderModel).isBambooHRProvider = true;

            const result = shouldHideApprovalCardActions(false, false, null);

            expect(result).toBeTruthy();
        });

        test('when approved without approvedAt date, then should return true', () => {
            vi.mocked(sharedPolicyBuilderModel).isBambooHRProvider = false;

            const result = shouldHideApprovalCardActions(true, false, null);

            expect(result).toBeTruthy();
        });

        test('when published without approvedAt date, then should return true', () => {
            vi.mocked(sharedPolicyBuilderModel).isBambooHRProvider = false;

            const result = shouldHideApprovalCardActions(false, true, null);

            expect(result).toBeTruthy();
        });

        test('when normal case, then should return false', () => {
            vi.mocked(sharedPolicyBuilderModel).isBambooHRProvider = false;

            const result = shouldHideApprovalCardActions(false, false, null);

            expect(result).toBeFalsy();
        });

        test('when approved with approvedAt date, then should return false', () => {
            vi.mocked(sharedPolicyBuilderModel).isBambooHRProvider = false;

            const result = shouldHideApprovalCardActions(
                true,
                false,
                '2024-01-01',
            );

            expect(result).toBeFalsy();
        });
    });
});
