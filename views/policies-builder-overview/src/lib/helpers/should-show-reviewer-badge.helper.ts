import type {
    PolicyApprovalReviewGroupResponseDto,
    PolicyReviewerResponseDto,
} from '@globals/api-sdk/types';

export const shouldShowReviewerBadge = (
    reviewStatus: PolicyReviewerResponseDto['reviewStatus'] | null,
    consensusReached?: boolean,
    consensusRule?: PolicyApprovalReviewGroupResponseDto['consensusRule'],
    isOverridden?: boolean,
): boolean => {
    if (consensusReached) {
        if (isOverridden) {
            return reviewStatus === 'APPROVED';
        }

        return (
            reviewStatus === 'APPROVED' || reviewStatus === 'CHANGES_REQUESTED'
        );
    }

    return consensusRule === 'ALL' || consensusRule === 'ANY';
};
