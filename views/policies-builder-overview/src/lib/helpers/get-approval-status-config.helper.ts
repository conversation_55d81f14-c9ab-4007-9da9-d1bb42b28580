import type { ColorScheme } from '@cosmos/components/metadata';
import type { PolicyApprovalResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

type ApprovalStatus = PolicyApprovalResponseDto['status'];

export interface ApprovalStatusConfig {
    label: string;
    colorScheme: ColorScheme;
}

export const getApprovalStatusConfig = (
    approvalStatus?: ApprovalStatus,
    hasChangeRequests?: boolean,
    isPastDue?: boolean,
    hasReviewersPending?: boolean,
): ApprovalStatusConfig => {
    if (hasChangeRequests) {
        return {
            label: t`Changes requested`,
            colorScheme: 'critical',
        };
    }

    if (isPastDue) {
        return {
            label: t`Past due`,
            colorScheme: 'critical',
        };
    }

    switch (approvalStatus) {
        case 'READY_FOR_REVIEWS': {
            if (hasReviewersPending) {
                return {
                    label: t`Needs approval`,
                    colorScheme: 'critical',
                };
            }

            return {
                label: t`Not started`,
                colorScheme: 'neutral',
            };
        }
        case 'APPROVED': {
            return {
                label: t`Approved`,
                colorScheme: 'warning',
            };
        }
        case 'CHANGES_REQUESTED': {
            return {
                label: t`Changes requested`,
                colorScheme: 'critical',
            };
        }
        case 'INITIALIZED':
        case 'PREPARE_FOR_REVIEWS':
        case 'CANCELLED':
        default: {
            return {
                label: t`Not started`,
                colorScheme: 'neutral',
            };
        }
    }
};
