import type { PolicyVersionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { sharedPolicyBuilderModel } from '@models/policy-builder';

export const shouldShowNoApprovalMessage = (
    isApprovedOrPublished: boolean,
    approvedAt?: PolicyVersionResponseDto['approvedAt'],
): boolean => {
    return isApprovedOrPublished && !approvedAt;
};

export const getApprovalNotRequiredMessage = (
    clientType?: PolicyVersionResponseDto['clientType'],
): string => {
    switch (clientType) {
        case 'BAMBOO_HR':
        case 'MERGEDEV_BAMBOO_HR': {
            return t`Please refer to BambooHR for approval details.`;
        }
        default: {
            return t`No approval was required for this version.`;
        }
    }
};

export const shouldHideApprovalCardActions = (
    isApproved?: boolean,
    isPublished?: boolean,
    approvedAt?: PolicyVersionResponseDto['approvedAt'],
): boolean => {
    const shouldHideForProvider = sharedPolicyBuilderModel.isBambooHRProvider;

    const isApprovedOrPublished = Boolean(isApproved || isPublished);
    const shouldShowNoApproval = shouldShowNoApprovalMessage(
        isApprovedOrPublished,
        approvedAt,
    );

    return shouldHideForProvider || shouldShowNoApproval;
};
