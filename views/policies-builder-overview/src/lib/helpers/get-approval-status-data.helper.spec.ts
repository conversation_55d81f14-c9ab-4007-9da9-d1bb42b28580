import { describe, expect, test } from 'vitest';
import type { PolicyApprovalResponseDto } from '@globals/api-sdk/types';
import { getApprovalStatusData } from './get-approval-status-data.helper';

describe('getApprovalStatusData', () => {
    describe('when approval is undefined', () => {
        test('should return all false values', () => {
            const approval = undefined;

            const result = getApprovalStatusData(approval);

            expect(result).toStrictEqual({
                hasChangeRequests: false,
                isPastDue: false,
                hasReviewersPending: false,
            });
        });
    });

    describe('when approval has change requests', () => {
        test('should return hasChangeRequests as true', () => {
            const approval = {
                reviewGroups: [
                    {
                        consensusDecision: 'CHANGES_REQUESTED',
                        reviews: [
                            {
                                reviewStatus: 'CHANGES_REQUESTED',
                            },
                        ],
                    },
                ],
            };

            const result = getApprovalStatusData(
                approval as unknown as PolicyApprovalResponseDto,
            );

            expect(result.hasChangeRequests).toBeTruthy();
        });
    });

    describe('when approval is past due', () => {
        test('should return isPastDue as true', () => {
            const approval = {
                status: 'READY_FOR_REVIEWS',
                reviewGroups: [
                    {
                        consensusDecision: 'DEADLINE_EXCEEDED',
                        reviews: [],
                    },
                ],
            };

            const result = getApprovalStatusData(
                approval as unknown as PolicyApprovalResponseDto,
            );

            expect(result.isPastDue).toBeTruthy();
        });
    });

    describe('when approval has reviewers pending', () => {
        test('should return hasReviewersPending as true', () => {
            const approval = {
                reviewGroups: [
                    {
                        consensusDecision: 'READY_FOR_REVIEW',
                        reviews: [
                            {
                                reviewStatus: 'READY_FOR_REVIEW',
                            },
                        ],
                    },
                ],
            };

            const result = getApprovalStatusData(
                approval as unknown as PolicyApprovalResponseDto,
            );

            expect(result.hasReviewersPending).toBeTruthy();
        });
    });

    describe('when approval has no special conditions', () => {
        test('should return all false values', () => {
            const approval = {
                reviewGroups: [
                    {
                        consensusDecision: 'APPROVED',
                        reviews: [
                            {
                                reviewStatus: 'APPROVED',
                            },
                        ],
                    },
                ],
            };

            const result = getApprovalStatusData(
                approval as unknown as PolicyApprovalResponseDto,
            );

            expect(result).toStrictEqual({
                hasChangeRequests: false,
                isPastDue: false,
                hasReviewersPending: false,
            });
        });
    });
});
