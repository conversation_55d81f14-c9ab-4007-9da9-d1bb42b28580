import { describe, expect, test } from 'vitest';
import { shouldShowReviewerBadge } from './should-show-reviewer-badge.helper';

describe('shouldShowReviewerBadge', () => {
    describe('given consensus is reached', () => {
        describe('when consensus is reached with override', () => {
            describe('when reviewStatus is APPROVED', () => {
                test('then should return true', () => {
                    const result = shouldShowReviewerBadge(
                        'APPROVED',
                        true,
                        'ALL',
                        true,
                    );

                    expect(result).toBeTruthy();
                });
            });

            describe('when reviewStatus is not APPROVED', () => {
                test('then should return false', () => {
                    const result = shouldShowReviewerBadge(
                        'CHANGES_REQUESTED',
                        true,
                        'ALL',
                        true,
                    );

                    expect(result).toBeFalsy();
                });
            });
        });

        describe('when consensus is reached without override', () => {
            describe('when reviewStatus is APPROVED', () => {
                test('then should return true', () => {
                    const result = shouldShowReviewerBadge(
                        'APPROVED',
                        true,
                        'ALL',
                        false,
                    );

                    expect(result).toBeTruthy();
                });
            });

            describe('when reviewStatus is CHANGES_REQUESTED', () => {
                test('then should return true', () => {
                    const result = shouldShowReviewerBadge(
                        'CHANGES_REQUESTED',
                        true,
                        'ALL',
                        false,
                    );

                    expect(result).toBeTruthy();
                });
            });

            describe('when reviewStatus is other', () => {
                test('then should return false', () => {
                    const result = shouldShowReviewerBadge(
                        'READY_FOR_REVIEW',
                        true,
                        'ALL',
                        false,
                    );

                    expect(result).toBeFalsy();
                });
            });
        });
    });

    describe('given consensus is not reached', () => {
        describe('when consensusRule is ALL', () => {
            test('then should return true', () => {
                const result = shouldShowReviewerBadge(null, false, 'ALL');

                expect(result).toBeTruthy();
            });
        });

        describe('when consensusRule is ANY', () => {
            describe('when reviewStatus is not null', () => {
                test('then should return true', () => {
                    const result = shouldShowReviewerBadge(
                        'APPROVED',
                        false,
                        'ANY',
                    );

                    expect(result).toBeTruthy();
                });
            });

            describe('when reviewStatus is null', () => {
                test('then should return false', () => {
                    const result = shouldShowReviewerBadge(null, false, 'ANY');

                    expect(result).toBeTruthy();
                });
            });
        });
    });
});
