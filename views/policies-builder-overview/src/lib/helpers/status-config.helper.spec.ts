import { describe, expect, test } from 'vitest';
import { getStatusConfig } from './status-config.helper';

describe('getStatusConfig', () => {
    describe('given a review status', () => {
        describe('when status is APPROVED', () => {
            test('then should return approved config with warning color', () => {
                const result = getStatusConfig('APPROVED');

                expect(result).toStrictEqual({
                    label: 'Approved',
                    colorScheme: 'warning',
                });
            });
        });

        describe('when status is CHANGES_REQUESTED', () => {
            test('then should return changes requested config with critical color', () => {
                const result = getStatusConfig('CHANGES_REQUESTED');

                expect(result).toStrictEqual({
                    label: 'Changes requested',
                    colorScheme: 'critical',
                });
            });
        });

        describe('when status is READY_FOR_REVIEW', () => {
            test('then should return needs approval config with critical color', () => {
                const result = getStatusConfig('READY_FOR_REVIEW');

                expect(result).toStrictEqual({
                    label: 'Needs approval',
                    colorScheme: 'critical',
                });
            });
        });

        describe('when status is DEADLINE_EXCEEDED', () => {
            test('then should return deadline exceeded config with neutral color', () => {
                const result = getStatusConfig('DEADLINE_EXCEEDED');

                expect(result).toStrictEqual({
                    label: 'Deadline exceeded',
                    colorScheme: 'neutral',
                });
            });
        });

        describe('when status is unknown or null', () => {
            test('then should return not started config with neutral color', () => {
                const result = getStatusConfig(null);

                expect(result).toStrictEqual({
                    label: 'Not started',
                    colorScheme: 'neutral',
                });
            });
        });
    });
});
