import { describe, expect, test } from 'vitest';
import { getApprovalStatusConfig } from './get-approval-status-config.helper';

describe('getApprovalStatusConfig', () => {
    describe('when hasChangeRequests is true', () => {
        test('should return changes requested config with critical color', () => {
            const approvalStatus = 'READY_FOR_REVIEWS';
            const hasChangeRequests = true;
            const isPastDue = false;
            const hasReviewersPending = false;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Changes requested',
                colorScheme: 'critical',
            });
        });
    });

    describe('when isPastDue is true', () => {
        test('should return past due config with critical color', () => {
            const approvalStatus = 'READY_FOR_REVIEWS';
            const hasChangeRequests = false;
            const isPastDue = true;
            const hasReviewersPending = false;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Past due',
                colorScheme: 'critical',
            });
        });
    });

    describe('when status is READY_FOR_REVIEWS and has reviewers pending', () => {
        test('should return needs approval config with critical color', () => {
            const approvalStatus = 'READY_FOR_REVIEWS';
            const hasChangeRequests = false;
            const isPastDue = false;
            const hasReviewersPending = true;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Needs approval',
                colorScheme: 'critical',
            });
        });
    });

    describe('when status is READY_FOR_REVIEWS but no reviewers pending', () => {
        test('should return not started config with neutral color', () => {
            const approvalStatus = 'READY_FOR_REVIEWS';
            const hasChangeRequests = false;
            const isPastDue = false;
            const hasReviewersPending = false;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Not started',
                colorScheme: 'neutral',
            });
        });
    });

    describe('when status is APPROVED', () => {
        test('should return approved config with warning color', () => {
            const approvalStatus = 'APPROVED';
            const hasChangeRequests = false;
            const isPastDue = false;
            const hasReviewersPending = false;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Approved',
                colorScheme: 'warning',
            });
        });
    });

    describe('when status is CHANGES_REQUESTED', () => {
        test('should return changes requested config with critical color', () => {
            const approvalStatus = 'CHANGES_REQUESTED';
            const hasChangeRequests = false;
            const isPastDue = false;
            const hasReviewersPending = false;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Changes requested',
                colorScheme: 'critical',
            });
        });
    });

    describe('when status is INITIALIZED', () => {
        test('should return not started config with neutral color', () => {
            const approvalStatus = 'INITIALIZED';
            const hasChangeRequests = false;
            const isPastDue = false;
            const hasReviewersPending = false;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Not started',
                colorScheme: 'neutral',
            });
        });
    });

    describe('when status is PREPARE_FOR_REVIEWS', () => {
        test('should return not started config with neutral color', () => {
            const approvalStatus = 'PREPARE_FOR_REVIEWS';
            const hasChangeRequests = false;
            const isPastDue = false;
            const hasReviewersPending = false;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Not started',
                colorScheme: 'neutral',
            });
        });
    });

    describe('when status is CANCELLED', () => {
        test('should return not started config with neutral color', () => {
            const approvalStatus = 'CANCELLED';
            const hasChangeRequests = false;
            const isPastDue = false;
            const hasReviewersPending = false;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Not started',
                colorScheme: 'neutral',
            });
        });
    });

    describe('when status is undefined', () => {
        test('should return not started config with neutral color', () => {
            const approvalStatus = undefined;
            const hasChangeRequests = false;
            const isPastDue = false;
            const hasReviewersPending = false;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Not started',
                colorScheme: 'neutral',
            });
        });
    });

    describe('when hasChangeRequests takes precedence over isPastDue', () => {
        test('should return changes requested config', () => {
            const approvalStatus = 'READY_FOR_REVIEWS';
            const hasChangeRequests = true;
            const isPastDue = true;
            const hasReviewersPending = true;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Changes requested',
                colorScheme: 'critical',
            });
        });
    });

    describe('when isPastDue takes precedence over hasReviewersPending', () => {
        test('should return past due config', () => {
            const approvalStatus = 'READY_FOR_REVIEWS';
            const hasChangeRequests = false;
            const isPastDue = true;
            const hasReviewersPending = true;

            const result = getApprovalStatusConfig(
                approvalStatus,
                hasChangeRequests,
                isPastDue,
                hasReviewersPending,
            );

            expect(result).toStrictEqual({
                label: 'Past due',
                colorScheme: 'critical',
            });
        });
    });
});
