import { isEmpty, isNil } from 'lodash-es';
import type { PolicyApprovalResponseDto } from '@globals/api-sdk/types';

export interface ApprovalStatusData {
    hasChangeRequests: boolean;
    isPastDue: boolean;
    hasReviewersPending: boolean;
}

export const getApprovalStatusData = (
    approval?: PolicyApprovalResponseDto,
): ApprovalStatusData => {
    if (isNil(approval) || isEmpty(approval.reviewGroups)) {
        return {
            hasChangeRequests: false,
            isPastDue: false,
            hasReviewersPending: false,
        };
    }

    const hasChangeRequests = approval.reviewGroups.some((reviewGroup) =>
        reviewGroup.reviews.some(
            (review) => review.reviewStatus === 'CHANGES_REQUESTED',
        ),
    );

    const isPastDue =
        approval.reviewGroups.some(
            (reviewGroup) =>
                reviewGroup.consensusDecision === 'DEADLINE_EXCEEDED',
        ) && approval.status === 'READY_FOR_REVIEWS';

    const hasReviewersPending = approval.reviewGroups.some((reviewGroup) =>
        reviewGroup.reviews.some(
            (review) => review.reviewStatus === 'READY_FOR_REVIEW',
        ),
    );

    return {
        hasChangeRequests,
        isPastDue,
        hasReviewersPending,
    };
};
