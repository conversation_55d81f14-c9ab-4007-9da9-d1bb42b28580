import type { ColorScheme } from '@cosmos/components/metadata';
import type {
    PolicyApprovalReviewGroupResponseDto,
    PolicyReviewerResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export interface StatusConfig {
    label: string;
    colorScheme: ColorScheme;
}

export const getStatusConfig = (
    status:
        | PolicyReviewerResponseDto['reviewStatus']
        | PolicyApprovalReviewGroupResponseDto['consensusDecision']
        | null,
): StatusConfig => {
    switch (status) {
        case 'APPROVED': {
            return {
                label: t`Approved`,
                colorScheme: 'warning',
            };
        }
        case 'CHANGES_REQUESTED': {
            return {
                label: t`Changes requested`,
                colorScheme: 'critical',
            };
        }
        case 'READY_FOR_REVIEW': {
            return {
                label: t`Needs approval`,
                colorScheme: 'critical',
            };
        }
        case 'DEADLINE_EXCEEDED': {
            return {
                label: t`Deadline exceeded`,
                colorScheme: 'neutral',
            };
        }
        default: {
            return {
                label: t`Not started`,
                colorScheme: 'neutral',
            };
        }
    }
};
