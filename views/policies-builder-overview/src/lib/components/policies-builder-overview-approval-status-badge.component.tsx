import { Metadata } from '@cosmos/components/metadata';
import type { PolicyApprovalResponseDto } from '@globals/api-sdk/types';
import { getApprovalStatusConfig } from '../helpers/get-approval-status-config.helper';

interface PoliciesBuilderOverviewApprovalStatusBadgeProps {
    approvalStatus?: PolicyApprovalResponseDto['status'];
    hasChangeRequests?: boolean;
    isPastDue?: boolean;
    hasReviewersPending?: boolean;
}

export const PoliciesBuilderOverviewApprovalStatusBadge = ({
    approvalStatus,
    hasChangeRequests,
    isPastDue,
    hasReviewersPending,
}: PoliciesBuilderOverviewApprovalStatusBadgeProps): React.JSX.Element => {
    const { label, colorScheme } = getApprovalStatusConfig(
        approvalStatus,
        hasChangeRequests,
        isPastDue,
        hasReviewersPending,
    );

    return (
        <Metadata
            colorScheme={colorScheme}
            label={label}
            type="status"
            data-testid="PoliciesBuilderOverviewApprovalStatusBadge"
            data-id="approval-status-badge"
        />
    );
};
