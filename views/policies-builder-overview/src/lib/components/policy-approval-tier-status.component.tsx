import { Text } from '@cosmos/components/text';
import type { PolicyApprovalReviewGroupResponseDto } from '@globals/api-sdk/types';
import { getStatusConfig } from '../helpers/status-config.helper';

export const PolicyApprovalTierStatus = ({
    consensusDecision,
}: {
    consensusDecision: PolicyApprovalReviewGroupResponseDto['consensusDecision'];
}): React.JSX.Element => {
    const { label, colorScheme } = getStatusConfig(consensusDecision);

    return (
        <Text
            colorScheme={colorScheme}
            data-testid="PolicyApprovalTierStatus"
            data-id="tier-status-badge"
            size="100"
        >
            {label}
        </Text>
    );
};
