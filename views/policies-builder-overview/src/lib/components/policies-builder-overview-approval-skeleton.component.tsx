import { Skeleton } from '@cosmos/components/skeleton';
/**
 * Policy BUilder Overview Skeleton Component.
 *
 * TODO: <PERSON> Implement skeleton loading state.
 */

export const PoliciesBuilderOverviewApprovalSkeletonComponent =
    (): React.JSX.Element => {
        return (
            <Skeleton
                barCount={3}
                data-testid="PoliciesBuilderOverviewApprovalSkeletonComponent"
                data-id="pZGMuxme"
            />
        );
    };
