import { Avatar } from '@cosmos/components/avatar';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type {
    PolicyApprovalReviewGroupResponseDto,
    PolicyReviewerResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { formatDate } from '@helpers/date-time';
import { getInitials } from '@helpers/formatters';
import { shouldShowReviewerBadge } from '../helpers/should-show-reviewer-badge.helper';
import { getStatusConfig } from '../helpers/status-config.helper';

export const PolicyApprovalReviewer = ({
    review,
    consensusReached,
    consensusRule,
    isOverridden,
}: {
    review: PolicyReviewerResponseDto;
    consensusReached?: boolean;
    consensusRule?: PolicyApprovalReviewGroupResponseDto['consensusRule'];
    isOverridden?: boolean;
}): React.JSX.Element => {
    const { name, reviewStatus, imgUrl, statusUpdatedAt } = review;
    const statusConfig = getStatusConfig(reviewStatus);

    const shouldShowStatus = shouldShowReviewerBadge(
        reviewStatus,
        consensusReached,
        consensusRule,
        isOverridden,
    );

    return (
        <Stack
            direction="row"
            gap="3x"
            align="center"
            justify="between"
            data-testid="PolicyApprovalReviewer"
            data-id="approval-reviewer"
        >
            <Stack direction="row" gap="3x" align="center">
                <Avatar
                    imgSrc={imgUrl}
                    fallbackText={getInitials(name)}
                    size="sm"
                />
                <Text type="body">{name}</Text>
            </Stack>

            {shouldShowStatus && (
                <Stack direction="column" gap="1x" align="end">
                    <Text colorScheme={statusConfig.colorScheme} size="100">
                        {statusConfig.label}
                    </Text>

                    {statusUpdatedAt &&
                        (reviewStatus === 'APPROVED' ||
                            reviewStatus === 'CHANGES_REQUESTED') && (
                            <Text colorScheme="neutral" size="100">
                                {formatDate('sentence', statusUpdatedAt)}
                            </Text>
                        )}

                    {isOverridden && (
                        <Text colorScheme="neutral" size="100">
                            {t`Override`}
                        </Text>
                    )}
                </Stack>
            )}
        </Stack>
    );
};
