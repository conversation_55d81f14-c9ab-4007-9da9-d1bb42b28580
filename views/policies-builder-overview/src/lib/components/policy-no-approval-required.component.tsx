import { Text } from '@cosmos/components/text';
import type { PolicyVersionResponseDto } from '@globals/api-sdk/types';
import { getApprovalNotRequiredMessage } from '../helpers/approval-display.helpers';

export interface NoApprovalRequiredProps {
    clientType?: PolicyVersionResponseDto['clientType'];
}

export const PolicyNoApprovalRequired = ({
    clientType,
}: NoApprovalRequiredProps): React.JSX.Element => {
    return (
        <Text
            colorScheme="neutral"
            size="200"
            data-testid="PolicyNoApprovalRequired"
            data-id="no-approval-required"
        >
            {getApprovalNotRequiredMessage(clientType)}
        </Text>
    );
};
