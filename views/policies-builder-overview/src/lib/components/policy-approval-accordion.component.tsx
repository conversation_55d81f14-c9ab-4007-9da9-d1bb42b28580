import { Accordion } from '@cosmos/components/accordion';
import type { PolicyApprovalReviewGroupResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { shouldExpandAccordion } from '../helpers/accordion-expansion.helper';
import { PolicyApprovalTierContent } from './policy-approval-tier-content.component';
import { PolicyApprovalTierStatus } from './policy-approval-tier-status.component';

export const PolicyApprovalAccordion = ({
    reviewGroup,
}: {
    reviewGroup: PolicyApprovalReviewGroupResponseDto;
}): React.JSX.Element => {
    const { consensusDecision, name, tier } = reviewGroup;
    const isDefaultExpanded = shouldExpandAccordion(reviewGroup);
    const title = name || t`Tier ${tier}`;

    return (
        <Accordion
            title={title}
            isDefaultExpanded={isDefaultExpanded}
            data-testid="PolicyApprovalAccordion"
            data-id="approval-accordion"
            body={<PolicyApprovalTierContent reviewGroup={reviewGroup} />}
            supportingContent={
                <PolicyApprovalTierStatus
                    consensusDecision={consensusDecision}
                />
            }
        />
    );
};
