import type { Action } from '@cosmos/components/action-stack';
import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { shouldHideApprovalCardActions } from '../helpers/approval-display.helpers';
import { PoliciesBuilderOverviewApprovalContent } from './policies-builder-overview-approval-content.component';

export const PoliciesBuilderOverviewReviewComponent = observer(
    (): React.JSX.Element => {
        const { isApproved, isPublished, approvedAt } =
            sharedPolicyBuilderModel;

        const shouldHideActions = shouldHideApprovalCardActions(
            isApproved,
            isPublished,
            approvedAt,
        );

        const actions: Action[] = shouldHideActions
            ? []
            : [
                  {
                      actionType: 'button',
                      id: 'button-action-skeleton',
                      typeProps: {
                          label: t`Edit approval settings`,
                          level: 'secondary',
                      },
                  },
              ];

        return (
            <Card
                size="lg"
                title={t`Approval`}
                body={<PoliciesBuilderOverviewApprovalContent />}
                data-testid="PoliciesBuilderOverviewReviewComponent"
                data-id="j-C6Okgc"
                actions={actions}
            />
        );
    },
);
