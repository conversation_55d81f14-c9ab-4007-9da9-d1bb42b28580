import { PoliciesApprovalEmptyStateComponent } from '@components/policies';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { shouldShowNoApprovalMessage } from '../helpers/approval-display.helpers';
import { getApprovalStatusData } from '../helpers/get-approval-status-data.helper';
import { PoliciesBuilderOverviewApprovalSkeletonComponent } from './policies-builder-overview-approval-skeleton.component';
import { PoliciesBuilderOverviewApprovalStatusBadge } from './policies-builder-overview-approval-status-badge.component';
import { PolicyApprovalAccordion } from './policy-approval-accordion.component';
import { PolicyApprovalTierContent } from './policy-approval-tier-content.component';
import { PolicyNoApprovalRequired } from './policy-no-approval-required.component';

export const PoliciesBuilderOverviewApprovalContent = observer(
    (): React.JSX.Element => {
        const { isApprovalLoading } = sharedPolicyBuilderController;
        const {
            needsApprovalConfiguration,
            currentApproval,
            hasMultipleTiers,
            firstReviewGroup,
            isApprovedOrPublished,
            clientType,
            approvedAt,
        } = sharedPolicyBuilderModel;

        if (isApprovalLoading) {
            return <PoliciesBuilderOverviewApprovalSkeletonComponent />;
        }

        if (shouldShowNoApprovalMessage(isApprovedOrPublished, approvedAt)) {
            return <PolicyNoApprovalRequired clientType={clientType} />;
        }

        if (needsApprovalConfiguration || !currentApproval) {
            return <PoliciesApprovalEmptyStateComponent />;
        }

        const { hasChangeRequests, isPastDue, hasReviewersPending } =
            getApprovalStatusData(currentApproval);

        return (
            <Stack
                direction="column"
                gap="3xl"
                data-testid="PoliciesBuilderOverviewApprovalContent"
                data-id="approval-content"
            >
                <PoliciesBuilderOverviewApprovalStatusBadge
                    approvalStatus={currentApproval.status}
                    hasChangeRequests={hasChangeRequests}
                    isPastDue={isPastDue}
                    hasReviewersPending={hasReviewersPending}
                />

                {hasMultipleTiers
                    ? currentApproval.reviewGroups.map((reviewGroup) => (
                          <PolicyApprovalAccordion
                              key={`accordion-tier-${reviewGroup.tier}-${reviewGroup.id}`}
                              reviewGroup={reviewGroup}
                              data-id="1jJYfGDB"
                          />
                      ))
                    : firstReviewGroup && (
                          <PolicyApprovalTierContent
                              reviewGroup={firstReviewGroup}
                          />
                      )}
            </Stack>
        );
    },
);
