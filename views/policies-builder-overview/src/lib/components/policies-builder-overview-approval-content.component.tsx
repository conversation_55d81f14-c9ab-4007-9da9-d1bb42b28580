import { PoliciesApprovalEmptyStateComponent } from '@components/policies';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { getApprovalStatusData } from '../helpers/get-approval-status-data.helper';
import { PoliciesBuilderOverviewApprovalSkeletonComponent } from './policies-builder-overview-approval-skeleton.component';
import { PoliciesBuilderOverviewApprovalStatusBadge } from './policies-builder-overview-approval-status-badge.component';

export const PoliciesBuilderOverviewApprovalContent = observer(
    (): React.JSX.Element => {
        const { isApprovalLoading } = sharedPolicyBuilderController;
        const { needsApprovalConfiguration, currentApproval } =
            sharedPolicyBuilderModel;

        if (isApprovalLoading) {
            return <PoliciesBuilderOverviewApprovalSkeletonComponent />;
        }

        if (needsApprovalConfiguration || !currentApproval) {
            return <PoliciesApprovalEmptyStateComponent />;
        }

        const { hasChangeRequests, isPastDue, hasReviewersPending } =
            getApprovalStatusData(currentApproval);

        return (
            <Stack
                direction="column"
                gap="md"
                data-testid="PoliciesBuilderOverviewApprovalContent"
                data-id="approval-content"
            >
                <PoliciesBuilderOverviewApprovalStatusBadge
                    approvalStatus={currentApproval.status}
                    hasChangeRequests={hasChangeRequests}
                    isPastDue={isPastDue}
                    hasReviewersPending={hasReviewersPending}
                />
                {/* TODO: Add accordions for review groups here */}
            </Stack>
        );
    },
);
