import { isEmpty, isError, isNumber, isObject } from 'lodash-es';
import type {
    VendorQuestionnaireResponseType,
    VendorQuestionnaireShortAnswerType,
} from '@components/vendor-questionnaires';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import { sharedVendorsQuestionnaireAddController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { runInAction } from '@globals/mobx';
import { downloadBlob } from '@helpers/download-file';
import type {
    ImportCsvFileResult,
    ImportedQuestion,
} from '../types/questionnaire-import.types';
import { parseQuestionnaireCSV } from './csv-parser.helper.js';

/**
 * Interface for the data stored in localStorage during import.
 */
interface ImportStorageData {
    questions: typeof sharedVendorsQuestionnaireAddController.formModel.formData.questions;
    questionsWithIssues: number[];
    message: {
        title: string;
        description: string;
        severity: 'success' | 'warning';
    };
    timestamp: number;
}

/**
 * Generates a descriptive message for import results.
 */
const getImportResultMessage = (
    importedCount: number,
    failedCount: number,
    failedQuestions: number[],
): { title: string; description: string; severity: 'success' | 'warning' } => {
    if (failedCount === 0) {
        return {
            title: t`Questions imported successfully`,
            description: t`${importedCount} questions have been imported.`,
            severity: 'success',
        };
    }

    const failedQuestionsText =
        failedQuestions.length <= 5
            ? failedQuestions.join(', ')
            : `${failedQuestions.slice(0, 5).join(', ')} and ${failedQuestions.length - 5} more`;

    return {
        title: t`${importedCount} questions imported. Some need your attention.`,
        description: t`Please check questions ${failedQuestionsText}.`,
        severity: 'warning',
    };
};

/**
 * Direct mapping from CSV question types to controller format.
 * This matches the implementation in the web project to ensure consistency.
 */
const IMPORT_QUESTIONNAIRE_TYPE_MAPPING: Record<
    string,
    VendorQuestionnaireResponseType
> = {
    'Yes/No': 'YES_NO',
    'Short Answer': 'SHORT_ANSWER',
    'Long Answer': 'LONG_ANSWER',
    'Multiple Choice': 'MULTIPLE_CHOICE',
    Checkboxes: 'CHECKBOXES',
    Date: 'DATE',
    'File Upload': 'FILE_UPLOAD',
};

/**
 * Maps question types from CSV format to controller format.
 * Uses direct mapping to avoid i18n translation issues.
 * Returns undefined for unknown types so they can be marked as errors.
 */
const mapQuestionType = (
    csvType: string,
): VendorQuestionnaireResponseType | undefined => {
    // If empty or undefined, return undefined to mark as error
    if (!csvType || csvType.trim() === '') {
        return undefined;
    }

    const normalizedType = csvType.trim();

    // Try exact match first
    if (IMPORT_QUESTIONNAIRE_TYPE_MAPPING[normalizedType]) {
        return IMPORT_QUESTIONNAIRE_TYPE_MAPPING[normalizedType];
    }

    // Try case-insensitive match
    const lowerCaseType = normalizedType.toLowerCase();
    const matchingKey = Object.keys(IMPORT_QUESTIONNAIRE_TYPE_MAPPING).find(
        (key) => key.toLowerCase() === lowerCaseType,
    );

    if (matchingKey) {
        return IMPORT_QUESTIONNAIRE_TYPE_MAPPING[matchingKey];
    }

    // Return undefined for unknown types so they can be marked as errors
    return undefined;
};

/**
 * Maps short answer type from CSV format to controller format.
 */
const getShortAnswerType = (
    csvShortAnswerType: string,
): VendorQuestionnaireShortAnswerType | undefined => {
    // If empty, return undefined so user sees it needs configuration
    if (!csvShortAnswerType || csvShortAnswerType.trim() === '') {
        return undefined;
    }

    const normalizedType = csvShortAnswerType.toLowerCase().trim();

    switch (normalizedType) {
        case 'email': {
            return 'EMAIL';
        }
        case 'url':
        case 'website url': {
            return 'URL';
        }
        case 'phone':
        case 'phone number': {
            return 'PHONE';
        }
        case 'text': {
            return 'TEXT';
        }
        default: {
            // If not a recognized type, return undefined so user configures it
            return undefined;
        }
    }
};

/**
 * Maps imported questions to the format expected by the questionnaire controller.
 * Filters out questions with invalid types and tracks them as failed questions.
 */
const mapImportedQuestionsToFormData = (
    importedQuestions: ImportedQuestion[],
    failedQuestions: number[],
): typeof sharedVendorsQuestionnaireAddController.formModel.formData.questions => {
    return importedQuestions
        .map((question, index) => {
            const questionNumber = index + 1;
            const mappedType = mapQuestionType(question.type);

            // If type mapping failed, mark as failed question and skip
            if (!mappedType) {
                failedQuestions.push(questionNumber);

                return null;
            }

            // If title is empty, mark as failed question and skip
            if (!question.title || question.title.trim() === '') {
                failedQuestions.push(questionNumber);

                return null;
            }

            return {
                id: question.ref || `imported-question-${Date.now()}-${index}`,
                title: question.title,
                type: mappedType as (typeof sharedVendorsQuestionnaireAddController.formModel.formData.questions)[0]['type'], // DTO types are outdated, but API accepts all types
                required: question.required,
                shortAnswerType: getShortAnswerType(question.shortAnswerType),
                choices: question.choices.map((choice) => ({
                    ref: choice.ref,
                    label: choice.label,
                })),
                followUpQn: question.followUpQn || '',
                allowOtherChoice: question.allowOtherChoice,
                includeFollowUpQn: question.includeFollowUpQn,
                followUpQnTrigger: question.followUpQnTrigger,
            };
        })
        .filter(
            (question): question is NonNullable<typeof question> =>
                question !== null,
        );
};

/**
 * URL for the questionnaire import template CSV file.
 * This URL is hosted on Drata's CDN and contains the template for importing questions.
 */
const QUESTIONNAIRE_IMPORT_DOWNLOAD_URL =
    'https://cdn.drata.com/templates/vendors/questionnaire/Import Questionnaire Template.csv';

/**
 * Downloads the questionnaire import template CSV file.
 * Opens the template in a new tab and shows a success notification.
 */
export const downloadQuestionnaireTemplate = (): void => {
    try {
        window.open(
            QUESTIONNAIRE_IMPORT_DOWNLOAD_URL,
            '_blank',
            // cspell:disable-next-line
            'noopener,noreferrer', // Security features for external links
        );

        snackbarController.addSnackbar({
            id: 'questionnaire-template-download',
            props: {
                title: t`Template download started`,
                description: t`The questionnaire template is being downloaded`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });
    } catch {
        snackbarController.addSnackbar({
            id: 'questionnaire-template-download-error',
            props: {
                title: t`Download failed`,
                description: t`Unable to download the questionnaire template. Please try again.`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    }
};

/**
 * Gets localized headers for questionnaire templates using the i18n system.
 */
const getLocalizedHeaders = () => ({
    questions: t`Questions (Max 500)`,
    responseType: t`Response Type`,
    options: t`Options or Sub-Types (Please use exact formatting if applicable)`,
    required: t`Mark as Required (Yes or No)`,
});

/**
 * Gets example questions using the exact types from the mapping.
 * This ensures consistency between the template and the import logic.
 */
const getExampleQuestions = () => [
    {
        question: t`What is your name?`,
        type: 'Short Answer', // Use exact type from mapping
        subtype: 'text',
        required: t`Yes`,
    },
    {
        question: t`Do you agree to the terms?`,
        type: 'Yes/No', // Use exact type from mapping
        subtype: '',
        required: t`Yes`,
    },
    {
        question: t`Select your department`,
        type: 'Multiple Choice', // Use exact type from mapping
        subtype: 'HR;IT;Finance',
        required: t`No`,
    },
];

/**
 * Generates a localized CSV template content using the i18n system.
 */
export const generateLocalizedTemplate = (): string => {
    const headers = getLocalizedHeaders();
    const exampleQuestions = getExampleQuestions();

    const csvHeaders = [
        headers.questions,
        headers.responseType,
        headers.options,
        headers.required,
    ]
        .map((header) => `"${header}"`)
        .join(',');

    const exampleRows = exampleQuestions.map(
        ({ question, type, subtype, required }) =>
            `"${question}","${type}","${subtype}","${required}"`,
    );

    return [csvHeaders, ...exampleRows].join('\n');
};

/**
 * Downloads a localized questionnaire template CSV file using the current locale.
 */
export const downloadLocalizedQuestionnaireTemplate = (): void => {
    try {
        const templateContent = generateLocalizedTemplate();
        const blob = new Blob([templateContent], {
            type: 'text/csv;charset=utf-8;',
        });

        downloadBlob(blob, 'questionnaire-template.csv');

        snackbarController.addSnackbar({
            id: 'questionnaire-template-download-localized',
            props: {
                title: t`Template download started`,
                description: t`The questionnaire template is being downloaded`,
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
            },
        });
    } catch {
        snackbarController.addSnackbar({
            id: 'questionnaire-template-download-error-localized',
            props: {
                title: t`Download failed`,
                description: t`Unable to download the questionnaire template. Please try again.`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    }
};

/**
 * Handles the upload of a questionnaire CSV file.
 * Parses the CSV and populates the questionnaire form with the questions.
 * Replaces existing questions (same behavior as Web).
 *
 * @param file - The CSV file to upload.
 * @returns Promise that resolves when the import is complete.
 */
export const uploadQuestionnaireFile = async (
    file: File,
): Promise<ImportCsvFileResult> => {
    try {
        // Parse the CSV file
        const result = await parseQuestionnaireCSV(file);

        if (isEmpty(result.importedQuestions)) {
            snackbarController.addSnackbar({
                id: 'questionnaire-upload-no-questions',
                props: {
                    title: t`No questions found`,
                    description: t`The uploaded file contains no valid questions.`,
                    severity: 'warning',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return result;
        }

        // Track failed questions (questions with errors)
        const failedQuestions: number[] = [];

        // Update the form with imported questions
        const mappedQuestions = mapImportedQuestionsToFormData(
            result.importedQuestions,
            failedQuestions,
        );

        // Identify which questions have issues (need attention)
        const questionsWithIssues: number[] = [...failedQuestions];

        runInAction(() => {
            // Replace existing questions (same behavior as Web)
            sharedVendorsQuestionnaireAddController.formModel.updateFormField(
                'questions',
                mappedQuestions,
            );
        });

        // Check for additional issues in successfully mapped questions
        mappedQuestions.forEach((mappedQuestion, index) => {
            const questionNumber = index + 1;

            // Check if this question needs attention (shortAnswerType is undefined for Short Answer)
            if (
                mappedQuestion.type === 'SHORT_ANSWER' &&
                !mappedQuestion.shortAnswerType &&
                !questionsWithIssues.includes(questionNumber)
            ) {
                questionsWithIssues.push(questionNumber);
            }
        });

        // Show appropriate message based on import results
        const message = getImportResultMessage(
            result.importedQuestions.length,
            questionsWithIssues.length,
            questionsWithIssues,
        );

        // Store import data in localStorage for persistence across navigation
        const importData: ImportStorageData = {
            questions: mappedQuestions,
            questionsWithIssues,
            message,
            timestamp: Date.now(),
        };

        localStorage.setItem(
            'questionnaire-import-data',
            JSON.stringify(importData),
        );

        // Close the modal first
        modalController.closeModal('import-questions-modal');

        return result;
    } catch (error) {
        const errorMessage = isError(error) ? error.message : 'Unknown error';

        // Show specific error message based on error type
        let title = t`Import failed`;
        let description = t`Unable to import questions: ${errorMessage}`;

        if (errorMessage.includes('must use the template')) {
            title = t`Invalid file format`;
            description = t`The uploaded file must use the template. Please download the template and try again.`;
        } else if (errorMessage.includes('CSV file is empty')) {
            title = t`Empty file`;
            description = t`The uploaded file is empty. Please upload a completed file.`;
        } else if (errorMessage.includes('Failed to read file')) {
            title = t`File read error`;
            description = t`The file could not be uploaded – try again.`;
        }

        snackbarController.addSnackbar({
            id: 'questionnaire-upload-error',
            props: {
                title,
                description,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });

        throw error;
    }
};

/**
 * Loads imported questions from localStorage and applies them to the form.
 * This is called when the add questionnaire page loads to restore imported questions.
 */
export const loadImportedQuestionsFromStorage = (): void => {
    try {
        const storedData = localStorage.getItem('questionnaire-import-data');

        if (!storedData) {
            return;
        }

        const parsedData: unknown = JSON.parse(storedData);

        /**
         * Type guard to validate the structure of the imported data.
         */
        const isValidImportData = (
            data: unknown,
        ): data is ImportStorageData => {
            return (
                data !== null &&
                isObject(data) &&
                'questions' in data &&
                'message' in data &&
                'questionsWithIssues' in data &&
                'timestamp' in data &&
                Array.isArray((data as ImportStorageData).questions) &&
                isObject((data as ImportStorageData).message) &&
                Array.isArray(
                    (data as ImportStorageData).questionsWithIssues,
                ) &&
                isNumber((data as ImportStorageData).timestamp)
            );
        };

        if (!isValidImportData(parsedData)) {
            // Invalid data structure, remove it
            localStorage.removeItem('questionnaire-import-data');

            return;
        }

        const importData = parsedData;

        // Check if data is recent (within 5 minutes)
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;

        if (importData.timestamp < fiveMinutesAgo) {
            // Data is too old, remove it
            localStorage.removeItem('questionnaire-import-data');

            return;
        }

        // Load questions into the form
        runInAction(() => {
            sharedVendorsQuestionnaireAddController.formModel.updateFormField(
                'questions',
                importData.questions,
            );
        });

        // Show appropriate message
        const { message, questionsWithIssues } = importData;

        if (isEmpty(questionsWithIssues)) {
            // Show success snackbar
            snackbarController.addSnackbar({
                id: 'questionnaire-upload-success',
                props: {
                    title: message.title,
                    description: message.description,
                    severity: message.severity,
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } else {
            // Show warning banner
            sharedVendorsQuestionnaireAddController.showImportBanner(
                message.title,
                {
                    severity: 'warning',
                    body: message.description,
                },
            );
        }

        // Clean up localStorage after loading
        localStorage.removeItem('questionnaire-import-data');
    } catch {
        // If there's an error parsing the data, just remove it
        localStorage.removeItem('questionnaire-import-data');
    }
};
