import { isNil } from 'lodash-es';
import { useEffect } from 'react';
import { PoliciesBuilderEmptyStateComponent } from '@components/policies-builder-empty-state';
import { PoliciesEditorComponent } from '@components/policies-editor';
import {
    sharedPolicyBuilderController,
    sharedPolicyCkEditorController,
} from '@controllers/policy-builder';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { PdfViewer } from '@cosmos-lab/components/pdf-viewer';
import { observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { useBlocker } from '@remix-run/react';

export const PoliciesBuilderPolicyView = observer((): React.JSX.Element => {
    const { policy, policyPdfSignedUrl, isPdfSignedUrlLoading } =
        sharedPolicyBuilderController;
    const { isPolicyBuilderFirstLoading } = sharedPolicyBuilderModel;
    const {
        isAuthoredPolicy,
        isUploadedPolicy,
        isApproved,
        isExternalPolicy,
        isPolicyCkEditorInEditMode,
        hasPolicyCkEditorUnsavedChanges,
        policyId,
    } = sharedPolicyBuilderModel;

    const isFileBasedPolicy = isUploadedPolicy || isExternalPolicy;

    const shouldBlock =
        isApproved &&
        isPolicyCkEditorInEditMode &&
        hasPolicyCkEditorUnsavedChanges;

    const blocker = useBlocker(({ currentLocation, nextLocation }) => {
        return (
            shouldBlock && currentLocation.pathname !== nextLocation.pathname
        );
    });

    useEffect(() => {
        if (blocker.state === 'blocked') {
            sharedPolicyCkEditorController.handleNavigationAttempt(() => {
                blocker.proceed();
            });
        }
    }, [blocker]);

    useEffect(() => {
        if (isFileBasedPolicy && policyId) {
            sharedPolicyBuilderController.loadPolicyPdfDownload(policyId);
        }
    }, [isFileBasedPolicy, policyId]);

    if (isPolicyBuilderFirstLoading || isPdfSignedUrlLoading) {
        return (
            <Stack
                direction="column"
                gap="6x"
                p="6x"
                data-testid="PoliciesBuilderPolicyViewLoader"
                data-id="5DYP_Z0x"
            >
                <Skeleton barHeight="64px" width="100%" />
                <Skeleton barHeight="600px" width="100%" />
            </Stack>
        );
    }

    if (isNil(policy?.latestPolicyVersion)) {
        return <PoliciesBuilderEmptyStateComponent />;
    }

    if (isFileBasedPolicy) {
        return (
            <PdfViewer
                src={policyPdfSignedUrl ?? ''}
                label={policy.name}
                data-id="EUID_Z0b"
            />
        );
    }

    if (isAuthoredPolicy) {
        return <PoliciesEditorComponent />;
    }

    return <PoliciesBuilderEmptyStateComponent data-id="G-EDULpZ" />;
});
