import { sharedMonitoringCodeController } from '@controllers/monitoring';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { MonitorTestInstanceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { MonitoringCodeTableCellActionsModel } from '../models/monitoring-code-table-cell-actions.model';

interface MonitoringTableCellProps {
    row: { original: MonitorTestInstanceResponseDto };
    column: { id: string };
}

export const MonitoringCodeTableCellActionsComponent = observer(
    ({ row: { original } }: MonitoringTableCellProps): React.ReactNode => {
        const { testId, checkStatus, checkResultStatus, testType } = original;

        const model = new MonitoringCodeTableCellActionsModel(
            testId,
            checkStatus,
            checkResultStatus,
            testType,
            sharedMonitoringCodeController,
        );

        // Don't render dropdown if no actions are available
        if (!model.hasActions) {
            return null;
        }

        return (
            <SchemaDropdown
                isIconOnly
                colorScheme="neutral"
                level="tertiary"
                startIconName="HorizontalMenu"
                label={t`More options`}
                data-testid="MonitoringCodeTableCellActionsComponent"
                data-id="CXqNbR4t"
                items={model.actionItems}
            />
        );
    },
);
