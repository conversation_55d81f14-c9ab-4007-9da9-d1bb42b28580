import { isEmpty } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import type { SchemaDropdownProps } from '@cosmos/components/schema-dropdown';
import type { MonitorTestInstanceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

interface MonitoringController {
    handleTestNow: (testId: number) => void;
    handleStopTesting: (testId: number) => void;
    handleViewHelpArticle: () => void;
}

export class MonitoringCodeTableCellActionsModel {
    testId: number;
    checkStatus: MonitorTestInstanceResponseDto['checkStatus'];
    checkResultStatus: MonitorTestInstanceResponseDto['checkResultStatus'];
    testType: MonitorTestInstanceResponseDto['testType'];
    controller: MonitoringController;

    constructor(
        testId: number,
        checkStatus: MonitorTestInstanceResponseDto['checkStatus'],
        checkResultStatus: MonitorTestInstanceResponseDto['checkResultStatus'],
        testType: MonitorTestInstanceResponseDto['testType'],
        controller: MonitoringController,
    ) {
        this.testId = testId;
        this.checkStatus = checkStatus;
        this.checkResultStatus = checkResultStatus;
        this.testType = testType;
        this.controller = controller;
        makeAutoObservable(this);
    }

    get actionItems(): SchemaDropdownProps['items'] {
        const items: SchemaDropdownProps['items'] = [];

        // Test now - Only if test has status of "enabled"
        if (this.checkStatus === 'ENABLED') {
            items.push({
                id: 'test-now',
                label: t`Test now`,
                type: 'item',
                value: 'test-now',
                onSelect: () => {
                    this.controller.handleTestNow(this.testId);
                },
            });
        }

        // Stop testing - Only if test has status of "Testing"
        if (this.checkStatus === 'TESTING') {
            items.push({
                id: 'stop-testing',
                label: t`Stop testing`,
                type: 'item',
                value: 'stop-testing',
                onSelect: () => {
                    this.controller.handleStopTesting(this.testId);
                },
            });
        }

        // View findings - Only if test has status of "failing"
        if (this.checkResultStatus === 'FAILED') {
            items.push({
                id: 'view-findings',
                label: t`View findings`,
                type: 'item',
                value: 'view-findings',
                onSelect: () => {
                    sharedProgrammaticNavigationController.navigateTo(
                        `${this.testId}/findings`,
                    );
                },
            });
        }

        // View help article - Only for Drata tests
        if (this.testType === 'DRATA') {
            items.push({
                id: 'view-help',
                label: t`View help article`,
                type: 'item',
                value: 'view-help',
                endIconName: 'LinkOut',
                onSelect: () => {
                    this.controller.handleViewHelpArticle();
                },
            });
        }

        return items;
    }

    get hasActions(): boolean {
        return !isEmpty(this.actionItems);
    }
}
