import { isEmpty } from 'lodash-es';
import { useEffect, useRef } from 'react';
import {
    sharedRiskInsightsController,
    sharedRiskInsightsDownloadController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { DataHeatmap } from '@cosmos-lab/components/data-heatmap';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import {
    type HeatmapLegendThreshold,
    sharedRiskInsightsHeatmapModel,
} from '@models/risk-insights-heatmap';
import { riskHeatmapAdaptor } from './helper/risk-insights-heatmap.helper';

export const HeatmapCard = observer((): React.JSX.Element | null => {
    const { downloadRiskInsightsHeatmap } =
        sharedRiskInsightsDownloadController;
    const captureAreaRef = useRef<HTMLDivElement>(null);

    const { riskInsights, isLoading } = sharedRiskInsightsController;
    const { riskSettings } = sharedRiskSettingsController;
    const { heatmapLegendThresholds } = sharedRiskInsightsHeatmapModel;

    const { riskHeatmap = [] } = riskInsights ?? {};

    const { thresholds = [], impact = 0, likelihood = 0 } = riskSettings ?? {};

    const onDownloadClick = () => {
        runInAction(async () => {
            await downloadRiskInsightsHeatmap();
        });
    };

    useEffect(() => {
        runInAction(() => {
            sharedRiskInsightsDownloadController.heatmapCardRef =
                captureAreaRef;
        });
    }, [captureAreaRef, isLoading, riskHeatmap, thresholds]);

    if (isEmpty(thresholds) || !impact || !likelihood) {
        return null;
    }

    return (
        <Card
            title={t`Heatmap`}
            tooltipText={t`The risk heatmap shows how many risks you have based on impact and likelihood to help you prioritize.`}
            data-testid="HeatmapCard"
            data-id="BHJlG6TA"
            body={
                isLoading ? (
                    <Skeleton barCount={5} />
                ) : (
                    <Box>
                        {Array.isArray(heatmapLegendThresholds) &&
                            !isEmpty(heatmapLegendThresholds) && (
                                <Stack
                                    direction="row"
                                    gap="md"
                                    data-testid="renderLegend"
                                    data-id="heatmap-legend"
                                    pb="md"
                                    pl="3xl"
                                >
                                    {heatmapLegendThresholds.map(
                                        (threshold: HeatmapLegendThreshold) => (
                                            <Stack
                                                key={`${threshold.label}-${threshold.color}`}
                                                direction="row"
                                                gap="xs"
                                                align="center"
                                                data-id="WmU91Hvn"
                                            >
                                                <Box
                                                    width="3x"
                                                    height="3x"
                                                    backgroundColor={
                                                        threshold.color
                                                    }
                                                />
                                                <Text>{threshold.label}</Text>
                                            </Stack>
                                        ),
                                    )}
                                </Stack>
                            )}
                        <Box ref={captureAreaRef}>
                            <DataHeatmap
                                yAxisSize={impact}
                                xAxisSize={likelihood}
                                data-id="heatmap-max-data-test-id"
                                thresholds={thresholds}
                                values={riskHeatmapAdaptor(
                                    riskHeatmap,
                                    thresholds,
                                )}
                            />
                        </Box>
                    </Box>
                )
            }
            actions={[
                {
                    actionType: 'button',
                    id: 'add-auditor-action-button',
                    typeProps: {
                        label: 'Download',
                        isIconOnly: true,
                        cosmosUseWithCaution_isDisabled:
                            isLoading || isEmpty(riskHeatmap),
                        startIconName: 'Download',
                        level: 'tertiary',
                        colorScheme: 'neutral',
                        onClick: onDownloadClick,
                    },
                },
            ]}
        />
    );
});
