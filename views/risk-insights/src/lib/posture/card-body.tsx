import { isEmpty } from 'lodash-es';
import { useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { sharedRiskInsightsDownloadController } from '@controllers/risk';
import { Box } from '@cosmos/components/box';
import { EmptyState } from '@cosmos/components/empty-state';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import {
    DataPosture,
    type DataPostureBox,
} from '@cosmos-lab/components/data-posture';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';

interface CardBodyInnerProps {
    boxes: DataPostureBox[];
    isLoading: boolean;
}

export const CardBody = observer(
    ({ boxes, isLoading }: CardBodyInnerProps): React.JSX.Element => {
        useEffect(() => {
            // eslint-disable-next-line custom/no-direct-dom-manipulation -- Required for creating temporary DOM element for download
            const el = document.createElement('div');

            el.id = 'posture-card-download';
            document.body.appendChild(el);
            const root = createRoot(el);

            root.render(
                <Box data-id="SU2VzTRx">
                    <DataPosture
                        legend={false}
                        size="lg"
                        boxes={boxes}
                        data-testid="CardBody"
                        data-id="nTFOqq3N"
                    />
                </Box>,
            );

            runInAction(() => {
                sharedRiskInsightsDownloadController.postureCardRef = el;
            });

            return () => {
                // eslint-disable-next-line custom/no-direct-dom-manipulation -- Required for creating temporary DOM element for download
                const renderedPosture = document.querySelector(
                    '#posture-card-download',
                );

                root.unmount();
                renderedPosture?.remove();
            };
        }, [boxes, isLoading]);

        if (isLoading) {
            return <Skeleton barCount={5} />;
        }

        if (isEmpty(boxes)) {
            return (
                <Stack align="start" justify="center">
                    <EmptyState
                        isInline
                        title={t`No risk data available`}
                        description={t`Once risks are assessed, you'll see a breakdown of your organization's risk posture here.`}
                    />
                </Stack>
            );
        }

        return (
            <Box data-id="SU2VzTRx">
                <DataPosture
                    legend
                    size="lg"
                    boxes={boxes}
                    data-testid="CardBody"
                    data-id="nTFOqq3N"
                />
            </Box>
        );
    },
);
