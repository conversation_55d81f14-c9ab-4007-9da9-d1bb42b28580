import { useEffect } from 'react';
import { TreatmentOverviewCard } from '@components/risks';
import { sharedRiskInsightsController } from '@controllers/risk';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { observer } from '@globals/mobx';
import { sharedRiskInsightsCategoriesFilterModel } from './risk-insights.categories-filter.model';
import { AssessmentProgressCard } from './risk-insights-assessment-progress-card';
import { RiskInsightsFilters } from './risk-insights-filters';
import { RiskScoresCard } from './risk-insights-risk-scores-card';

export const RiskInsightsView = observer((): React.JSX.Element => {
    useEffect(() => {
        return (): void => {
            sharedRiskInsightsController.clearAllFilters();
            sharedRiskInsightsCategoriesFilterModel.clearAllFilters();
        };
    }, []);

    return (
        <Grid
            areas='"filters" "content"'
            columns="1"
            gapX="xl"
            gapY="3xl"
            data-testid="RiskInsightsView"
            data-id="0Ushkef6"
        >
            <Box gridArea="filters">
                <RiskInsightsFilters />
            </Box>
            <Box gridArea="content">
                <Grid
                    areas='"assessmentProgress treatmentOverview" "riskScores riskScores"'
                    columns="1fr 2fr"
                    gapX="2xl"
                    gapY="3xl"
                    align="stretch"
                >
                    <Box gridArea="assessmentProgress">
                        <AssessmentProgressCard />
                    </Box>
                    <Box gridArea="treatmentOverview">
                        <TreatmentOverviewCard />
                    </Box>
                    <Box gridArea="riskScores">
                        <RiskScoresCard />
                    </Box>
                </Grid>
            </Box>
        </Grid>
    );
});
