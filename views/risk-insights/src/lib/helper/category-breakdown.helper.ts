import { capitalize, isEmpty } from 'lodash-es';
import {
    DATA_POSTURE_BACKGROUND_COLORS,
    type DataPostureBox,
} from '@cosmos-lab/components/data-posture';
import type {
    DashboardResponseDto,
    RiskThresholdResponseDto,
} from '@globals/api-sdk/types';
import { calculateThresholdLevelsHelper } from '@helpers/calculate-threshold-levels';

export function generateCategoryBreakdownBoxes(
    categoriesBreakdown: DashboardResponseDto['categoryBreakdown'],
    thresholds: RiskThresholdResponseDto[],
    isForDownload?: boolean,
): { category: string; boxes: DataPostureBox[]; categoryId: number }[] {
    return categoriesBreakdown.map((categoryBreakdown) => {
        const { severity = {}, category = {} } = categoryBreakdown;
        const thresholdLevels = calculateThresholdLevelsHelper(thresholds);

        const keys = Object.keys(severity);
        const boxes = isEmpty(severity)
            ? []
            : keys
                  .filter((key: string) => Number(severity[key]) > 0)
                  .map((key: string) => {
                      const splittedKey = key.split('_');
                      const severityName = splittedKey[0];
                      const thresholdId = Number(splittedKey[2]);

                      const value = Number(severity[key] ?? 0);
                      const threshold = thresholdLevels.find(
                          (t) => t.id === thresholdId,
                      );

                      return {
                          id: `${severityName}-${category.id}`,
                          value,
                          valueSize: isForDownload ? '600' : '100',
                          color: threshold?.level
                              ? DATA_POSTURE_BACKGROUND_COLORS[
                                    threshold.level as keyof typeof DATA_POSTURE_BACKGROUND_COLORS
                                ]
                              : '',
                          legendLabel: capitalize(severityName),
                          thresholdId,
                          minThreshold: threshold?.minThreshold ?? 0,
                      } satisfies DataPostureBox & {
                          thresholdId: number;
                          minThreshold: number;
                      };
                  })
                  .sort((a, b) => a.minThreshold - b.minThreshold);

        return {
            category: category.name ?? '',
            boxes,
            categoryId: Number(category.id),
        };
    });
}
