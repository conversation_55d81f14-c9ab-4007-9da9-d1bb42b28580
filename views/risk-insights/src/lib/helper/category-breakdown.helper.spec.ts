import { describe, expect, test } from 'vitest';
import type { DataPostureBox } from '@cosmos-lab/components/data-posture';
import type {
    DashboardResponseDto,
    RiskThresholdResponseDto,
} from '@globals/api-sdk/types';
import { generateCategoryBreakdownBoxes } from './category-breakdown.helper';

describe('category-breakdown.helper', () => {
    describe('generateCategoryBreakdownBoxes', () => {
        const mockThresholds: RiskThresholdResponseDto[] = [
            {
                id: 1,
                color: 'red500',
                name: 'Critical',
            },
            {
                id: 2,
                color: 'orange500',
                name: 'High',
            },
            {
                id: 3,
                color: 'yellow500',
                name: 'Medium',
            },
        ] as RiskThresholdResponseDto[];

        const mockCategoryBreakdown: DashboardResponseDto['categoryBreakdown'] =
            [
                {
                    category: {
                        id: 1,
                        name: 'Category 1',
                    },
                    severity: {
                        CRITICAL_THRESHOLD_1: 5,
                        HIGH_THRESHOLD_2: 3,
                        MODERATE_THRESHOLD_3: 4,
                    },
                },
                {
                    category: {
                        id: 2,
                        name: 'Category 2',
                    },
                    severity: {
                        MODERATE_THRESHOLD_3: 8,
                    },
                },
                {
                    category: {
                        id: 3,
                        name: 'Category 3',
                    },
                    severity: {},
                },
            ] as DashboardResponseDto['categoryBreakdown'];

        test('should generate correct boxes for categories with severity data', () => {
            const result = generateCategoryBreakdownBoxes(
                mockCategoryBreakdown,
                mockThresholds,
            );

            expect(result).toHaveLength(3);

            // Check Category 1
            expect(result[0].category).toBe('Category 1');
            expect(result[0].boxes).toHaveLength(3);
            expect(result[0].boxes).toContainEqual({
                color: 'var(--data-diverge-1-strong)',
                id: 'CRITICAL-1',
                value: 5,
                legendLabel: 'Critical',
                thresholdId: 1,
                minThreshold: 0,
                valueSize: '100',
            } satisfies DataPostureBox & {
                thresholdId: number;
                minThreshold: number;
            });
            expect(result[0].boxes).toContainEqual({
                color: 'var(--data-diverge-5-strong)',
                id: 'HIGH-1',
                value: 3,
                legendLabel: 'High',
                thresholdId: 2,
                minThreshold: 0,
                valueSize: '100',
            } satisfies DataPostureBox & {
                thresholdId: number;
                minThreshold: number;
            });
            expect(result[0].boxes).toContainEqual({
                color: 'var(--data-diverge-9-strong)',
                id: 'MODERATE-1',
                value: 4,
                legendLabel: 'Moderate',
                thresholdId: 3,
                minThreshold: 0,
                valueSize: '100',
            } satisfies DataPostureBox & {
                thresholdId: number;
                minThreshold: number;
            });

            // Check Category 2
            expect(result[1].category).toBe('Category 2');
            expect(result[1].boxes).toHaveLength(1);
            expect(result[1].boxes).toContainEqual({
                color: 'var(--data-diverge-9-strong)',
                id: 'MODERATE-2',
                value: 8,
                legendLabel: 'Moderate',
                thresholdId: 3,
                minThreshold: 0,
                valueSize: '100',
            } satisfies DataPostureBox & {
                thresholdId: number;
                minThreshold: number;
            });

            // Check Category 3 (empty severity)
            expect(result[2].category).toBe('Category 3');
            expect(result[2].boxes).toHaveLength(0);
        });

        test('should handle empty category breakdown', () => {
            const result = generateCategoryBreakdownBoxes([], mockThresholds);

            expect(result).toHaveLength(0);
        });

        test('should handle missing category name', () => {
            const mockCategoryWithoutName = [
                {
                    category: {
                        id: 1,
                    },
                    severity: {
                        CRITICAL: 2,
                    },
                },
            ] as DashboardResponseDto['categoryBreakdown'];

            const result = generateCategoryBreakdownBoxes(
                mockCategoryWithoutName,
                mockThresholds,
            );

            expect(result).toHaveLength(1);
            expect(result[0].category).toBe('');
        });

        test('should handle missing threshold', () => {
            const ******************************** = [
                {
                    category: {
                        id: 5,
                        name: 'Category 5',
                    },
                    severity: {
                        CRITICAL_THRESHOLD_999: 2,
                    },
                },
            ] as DashboardResponseDto['categoryBreakdown'];

            const result = generateCategoryBreakdownBoxes(
                ********************************,
                mockThresholds,
            );

            expect(result).toHaveLength(1);
            expect(result[0].boxes).toHaveLength(1);
            expect(result[0].boxes[0].color).toBe('');
        });

        test('should filter out severity entries with zero value', () => {
            const mockCategoryWithZeroValues = [
                {
                    category: {
                        id: 6,
                        name: 'Category 6',
                    },
                    severity: {
                        CRITICAL_THRESHOLD_1: 0,
                        HIGH_THRESHOLD_2: 0,
                    },
                },
            ] as DashboardResponseDto['categoryBreakdown'];

            const result = generateCategoryBreakdownBoxes(
                mockCategoryWithZeroValues,
                mockThresholds,
            );

            expect(result).toHaveLength(1);
            expect(result[0].boxes).toHaveLength(0);
        });
    });
});
