import { isEmpty, isObject, isString, noop } from 'lodash-es';
import { sharedRiskInsightsController } from '@controllers/risk';
import type { FilterState } from '@cosmos/components/filter-field';
import { Filters } from '@cosmos/components/filters';
import type { RiskManagementControllerGetDashboardData } from '@globals/api-sdk/types';
import { action, observer, toJS } from '@globals/mobx';
import { sharedRiskInsightsScoreTypeModel } from '@models/risk-insights';
import { sharedRiskInsightsCategoriesFilterModel } from './risk-insights.categories-filter.model';

const transformFilterStateToApiParams = (
    filterState: FilterState,
): Partial<RiskManagementControllerGetDashboardData['query']> => {
    const { id, value } = filterState;
    const hasArrayValue = Array.isArray(value) && !isEmpty(value);

    switch (id) {
        case 'categoriesIds': {
            return {
                categoriesIds: hasArrayValue
                    ? (value as { value: string }[]).map((item) =>
                          Number(item.value),
                      )
                    : [],
            };
        }
        case 'ownersIds': {
            return {
                ownersIds:
                    isObject(value) && 'value' in value
                        ? [Number((value as { value: string }).value)]
                        : [],
            };
        }
        case 'riskFilter': {
            return {
                riskFilter: isString(value)
                    ? (value as 'INTERNAL_ONLY' | 'EXTERNAL_ONLY')
                    : undefined,
            };
        }
        case 'status': {
            return {
                'status[]': hasArrayValue
                    ? (value as ('ACTIVE' | 'ARCHIVED' | 'CLOSED')[])
                    : [],
            };
        }
        default: {
            return {};
        }
    }
};

export const RiskInsightsFilters = observer((): React.JSX.Element => {
    const { tableFilters, filterValues, setFilterValue, clearAllFilters } =
        sharedRiskInsightsCategoriesFilterModel;

    const filters = toJS(tableFilters);
    const filterValuesCopy = toJS(filterValues);

    const handleFilterChange = action((filterState: FilterState): void => {
        setFilterValue(filterState);

        const apiParams = transformFilterStateToApiParams(filterState);

        sharedRiskInsightsController.updateFilters(apiParams);
    });

    const handleClearAll = action((): void => {
        clearAllFilters();
        sharedRiskInsightsController.clearAllFilters();
        sharedRiskInsightsScoreTypeModel.scoreType = 'INHERENT';
    });

    return (
        //TODO: PINNED FILTER -> https://drata.atlassian.net/browse/ENG-64250
        <Filters
            showViewModeToggle
            clearAllButtonLabel="Clear all"
            data-id="cosmos-filters"
            filters={filters}
            formId="filters"
            triggerId="cosmos-filters-trigger"
            triggerLabel="Filters"
            data-testid="RiskInsightsFilters"
            filterValues={filterValuesCopy}
            viewModeToggleProps={{
                onChange: noop,
                selectedOption: 'pinned',
                togglePinnedLabel: 'Pin filters to the page',
                toggleUnpinnedLabel: 'Move filters to a dropdown',
            }}
            onChange={handleFilterChange}
            onClearAll={handleClearAll}
        />
    );
});
