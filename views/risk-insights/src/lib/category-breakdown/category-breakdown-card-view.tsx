import { isEmpty } from 'lodash-es';
import { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';
import {
    sharedRiskInsightsController,
    sharedRiskInsightsDownloadController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { type AllowedBackgroundToken, Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import {
    DataPosture,
    type DataPostureBox,
} from '@cosmos-lab/components/data-posture';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, observer, runInAction } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { calculateThresholdLevelsHelper } from '@helpers/calculate-threshold-levels';
import { sharedCategoryBreakdownPaginationModel } from '@models/risk-insights-category-breakdown';
import { generateCategoryBreakdownBoxes } from '../helper/category-breakdown.helper';
import { CategoryBreakdownCardView } from './risk-insights-category-breakdown-card';

export interface Category {
    id: number;
    name: string;
    categoryId: number;
}

export interface CategoryBreakdownRef {
    category: Category;
    uri: HTMLElement | null;
}

const THRESHOLD_LEVEL_TO_BOX_TOKEN = {
    '1': 'dataDiverge1Strong',
    '3': 'dataDiverge3Strong',
    '4': 'dataDiverge3Strong',
    '5': 'dataDiverge6Strong',
    '7': 'dataDiverge7Strong',
    '9': 'dataDiverge10Strong',
} as const satisfies Record<string, AllowedBackgroundToken>;

class CategoryBreakdownCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    get boxesPerCategory(): {
        category: string;
        boxes: DataPostureBox[];
    }[] {
        const { riskSettings } = sharedRiskSettingsController;
        const { paginatedCategories } = sharedCategoryBreakdownPaginationModel;

        return generateCategoryBreakdownBoxes(
            paginatedCategories,
            riskSettings?.thresholds ?? [],
        );
    }

    get allCategories(): {
        category: string;
        boxes: DataPostureBox[];
        categoryId: number;
    }[] {
        const { riskSettings } = sharedRiskSettingsController;
        const { allCategories } = sharedCategoryBreakdownPaginationModel;

        return generateCategoryBreakdownBoxes(
            allCategories,
            riskSettings?.thresholds ?? [],
            true,
        );
    }

    get legendData(): {
        label: string;
        value: string;
        color: string;
    }[] {
        const { riskSettings } = sharedRiskSettingsController;
        const { thresholds = [] } = riskSettings ?? {};
        const thresholdLevels = calculateThresholdLevelsHelper(thresholds);

        if (isEmpty(this.boxesPerCategory)) {
            return [];
        }

        return thresholdLevels.map((threshold) => ({
            label: threshold.name,
            value: '',
            color: threshold.level
                ? THRESHOLD_LEVEL_TO_BOX_TOKEN[
                      threshold.level as keyof typeof THRESHOLD_LEVEL_TO_BOX_TOKEN
                  ]
                : '',
        }));
    }
}

export const CategoryBreakdownCard = observer((): React.JSX.Element => {
    const [isDownloading, setIsDownloading] = useState(false);
    const { isLoading } = sharedRiskInsightsController;
    const currentWorkspace = `/workspaces/${sharedWorkspacesController.currentWorkspace?.id}`;

    const { legendData, allCategories, boxesPerCategory } =
        new CategoryBreakdownCardModel();

    const {
        totalCategories,
        defaultCategoryBreakdownPageSize,
        categoryBreakdownPaginationOnPageChange,
    } = sharedCategoryBreakdownPaginationModel;

    const handleDownloadCategoryBreakdown = () => {
        setIsDownloading(true);

        runInAction(async () => {
            await sharedRiskInsightsDownloadController.downloadRiskInsightsCategoryBreakdown();
        });

        setIsDownloading(false);
    };

    useEffect(() => {
        const roots: ReturnType<typeof createRoot>[] = [];

        const categoryBreakdownRefs: CategoryBreakdownRef[] = [];

        allCategories.forEach(({ category, boxes, categoryId }) => {
            // eslint-disable-next-line custom/no-direct-dom-manipulation -- Required for creating temporary DOM element for download
            const el = document.createElement('div');

            document.body.appendChild(el);

            const root = createRoot(el);

            roots.push(root);

            root.render(
                <Stack
                    key={category}
                    direction="column"
                    gap="sm"
                    data-id="ri-JARNE"
                    data-category={category}
                >
                    <Box>
                        <DataPosture
                            legend={false}
                            size="lg"
                            boxes={boxes}
                            data-id="Cx1ET0qR"
                        />
                    </Box>
                </Stack>,
            );

            categoryBreakdownRefs.push({
                category: {
                    id: categoryId,
                    name: category,
                    categoryId,
                },
                uri: el,
            });
        });

        runInAction(() => {
            sharedRiskInsightsDownloadController.categoryBreakdownRef =
                categoryBreakdownRefs;
        });

        /**
         * Cleanup function to unmount roots when effect re-runs or component unmounts.
         */
        return () => {
            // Defer unmounting to avoid synchronous unmount during React rendering
            Promise.resolve().then(() => {
                roots.forEach((root) => {
                    root.unmount();
                });
            });
        };
    }, [isLoading, allCategories]);

    return (
        <CategoryBreakdownCardView
            title={t`Category breakdown`}
            tooltipText={t`The category breakdown shows how different parts of your business affect your risk posture.`}
            isLoading={isLoading}
            legendData={legendData}
            boxesPerCategory={boxesPerCategory}
            currentWorkspace={currentWorkspace}
            totalCategories={totalCategories}
            pageSize={defaultCategoryBreakdownPageSize}
            isDownloading={isDownloading}
            data-id="qUl7ERX_"
            onPageChange={categoryBreakdownPaginationOnPageChange}
            onDownload={handleDownloadCategoryBreakdown}
        />
    );
});
