import { sharedRiskCategoriesFilterController } from '@controllers/risk';
import { sharedUsersInfiniteController } from '@controllers/users';
import type { CheckboxFieldGroupProps } from '@cosmos/components/checkbox-field-group';
import type { ComboboxFieldProps } from '@cosmos/components/combobox-field';
import type {
    Filter,
    FilterState,
    FilterStateValue,
} from '@cosmos/components/filter-field';
import type { RadioFieldGroupProps } from '@cosmos/components/radio-field-group';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

class RiskInsightsCategoriesFilterModel {
    filterValues: Record<string, FilterStateValue> = {};

    constructor() {
        makeAutoObservable(this);
    }

    setFilterValue = (filterState: FilterState): void => {
        this.filterValues[filterState.id] = filterState.value;
    };

    clearAllFilters = (): void => {
        this.filterValues = {};
    };

    getFilterValue(id: string): FilterStateValue | undefined {
        return this.filterValues[id];
    }

    get tableFilters(): Filter[] {
        const { options, hasNextPage, isFetching, isLoading, loadNextPage } =
            sharedRiskCategoriesFilterController;

        const {
            options: usersOptions,
            hasNextPage: hasMoreUsers,
            isFetching: isFetchingUsers,
            isLoading: isLoadingUsers,
            onFetchUsers,
        } = sharedUsersInfiniteController;

        const { isVendorRiskManagementProEnabled } = sharedFeatureAccessModel;

        const baseFilters: Filter[] = [
            {
                filterType: 'combobox',
                id: 'categoriesIds',
                label: t`Categories`,
                isMultiSelect: true,
                placeholder: t`Search categories...`,
                hasMore: hasNextPage,
                isLoading: isFetching && isLoading,
                options,
                onFetchOptions: loadNextPage,
                clearSelectedItemButtonLabel: t`Clear`,
                removeAllSelectedItemsLabel: t`Remove all`,
                defaultSelectedOptions: (this.filterValues.categoriesIds ??
                    undefined) as ComboboxFieldProps['defaultSelectedOptions'],
            },
            {
                filterType: 'combobox',
                id: 'ownersIds',
                label: t`Owner`,
                isMultiSelect: false,
                placeholder: t`Search by owner`,
                options: usersOptions,
                hasMore: hasMoreUsers,
                isLoading: isFetchingUsers && isLoadingUsers,
                onFetchOptions: (params: {
                    search?: string;
                    increasePage?: boolean;
                }) => {
                    onFetchUsers({
                        ...params,
                        roles: [
                            'ADMIN',
                            'TECHGOV',
                            'RISK_MANAGER',
                            'SERVICE_USER',
                            'WORKSPACE_ADMINISTRATOR',
                        ],
                    });
                },
                clearSelectedItemButtonLabel: t`Clear`,
                defaultValue: (this.filterValues.ownersIds ??
                    undefined) as ComboboxFieldProps['defaultValue'],
            },
        ];

        // Only include Type filter if Vendor Risk Management Pro is enabled
        if (isVendorRiskManagementProEnabled) {
            baseFilters.push({
                filterType: 'radio',
                id: 'riskFilter',
                label: t`Type`,
                options: [
                    {
                        label: t`Internal`,
                        value: 'INTERNAL_ONLY',
                    },
                    {
                        label: t`External`,
                        value: 'EXTERNAL_ONLY',
                    },
                ],
                value: (this.filterValues.riskFilter ??
                    undefined) as RadioFieldGroupProps['value'],
            });
        }

        baseFilters.push({
            filterType: 'checkbox',
            id: 'status',
            label: t`Status`,
            options: [
                {
                    label: t`Active`,
                    value: 'ACTIVE',
                },
                {
                    label: t`Closed`,
                    value: 'CLOSED',
                },
                {
                    label: t`Archived`,
                    value: 'ARCHIVED',
                },
            ],
            value: (this.filterValues.status ??
                undefined) as CheckboxFieldGroupProps['value'],
        });

        return baseFilters;
    }
}

export const sharedRiskInsightsCategoriesFilterModel =
    new RiskInsightsCategoriesFilterModel();
