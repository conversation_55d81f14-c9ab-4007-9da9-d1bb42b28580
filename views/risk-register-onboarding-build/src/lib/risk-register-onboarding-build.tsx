import { useMemo } from 'react';
import { Wizard, type WizardProps } from '@cosmos-lab/components/wizard';
import { useLingui } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedOnboardingBuildFlowModel } from '@models/risk-management';
import { useNavigate } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';
import { ArtificialIntelligenceStep } from '../components/build-steps/artificial-intelligence';
import { CloudEnvironmentStep } from '../components/build-steps/cloud-environment';
import { DeviceDeliveryStep } from '../components/build-steps/device-delivery';
import { PhysicalSiteStep } from '../components/build-steps/physical-site';
import { RegulatoryRequirementsStep } from '../components/build-steps/regulatory-requirements';
import { SoftwareDevelopmentStep } from '../components/build-steps/software-development';
import { UnsecuredDevicesStep } from '../components/build-steps/unsecured-devices';

export const RiskRegisterOnboardingBuildView = observer(
    (): React.JSX.Element => {
        const navigate = useNavigate();
        const { t } = useLingui();
        const { isPending, riskRegisterToRedirectLink } =
            sharedOnboardingBuildFlowModel;

        const { formRef: aiFormRef, triggerSubmit: aiTriggerSubmit } =
            useFormSubmit();

        const {
            formRef: physicalSiteFormRef,
            triggerSubmit: physicalSiteTriggerSubmit,
        } = useFormSubmit();

        const {
            formRef: cloudEnvironmentFormRef,
            triggerSubmit: cloudEnvironmentTriggerSubmit,
        } = useFormSubmit();

        const {
            formRef: regulatoryRequirementsFormRef,
            triggerSubmit: regulatoryRequirementsTriggerSubmit,
        } = useFormSubmit();

        const {
            formRef: softwareDevelopmentFormRef,
            triggerSubmit: softwareDevelopmentTriggerSubmit,
        } = useFormSubmit();

        const {
            formRef: unsecuredDevicesFormRef,
            triggerSubmit: unsecuredDevicesTriggerSubmit,
        } = useFormSubmit();

        const {
            formRef: deviceDeliveryFormRef,
            triggerSubmit: deviceDeliveryTriggerSubmit,
        } = useFormSubmit();

        const wizardProps: WizardProps = useMemo(
            () => ({
                steps: [
                    {
                        component: () => (
                            <ArtificialIntelligenceStep
                                formRef={aiFormRef}
                                data-id="ai-step"
                            />
                        ),
                        stepTitle: t`Artificial intelligence`,
                        isStepSkippable: false,
                        onStepChange: aiTriggerSubmit,
                    },
                    {
                        component: () => (
                            <PhysicalSiteStep
                                formRef={physicalSiteFormRef}
                                data-id="physical-site-step"
                            />
                        ),
                        stepTitle: t`Physical site`,
                        isStepSkippable: false,
                        onStepChange: physicalSiteTriggerSubmit,
                    },
                    {
                        component: () => (
                            <CloudEnvironmentStep
                                formRef={cloudEnvironmentFormRef}
                                data-id="cloud-environment-step"
                            />
                        ),
                        stepTitle: t`Cloud environment`,
                        isStepSkippable: false,
                        onStepChange: cloudEnvironmentTriggerSubmit,
                    },
                    {
                        component: () => (
                            <RegulatoryRequirementsStep
                                formRef={regulatoryRequirementsFormRef}
                                data-id="regulatory-requirements-step"
                            />
                        ),
                        stepTitle: t`Regulatory requirements`,
                        isStepSkippable: false,
                        onStepChange: regulatoryRequirementsTriggerSubmit,
                    },
                    {
                        component: () => (
                            <SoftwareDevelopmentStep
                                formRef={softwareDevelopmentFormRef}
                                data-id="software-development-step"
                            />
                        ),
                        stepTitle: t`Software development`,
                        isStepSkippable: false,
                        onStepChange: softwareDevelopmentTriggerSubmit,
                    },
                    {
                        component: () => (
                            <UnsecuredDevicesStep
                                formRef={unsecuredDevicesFormRef}
                                data-id="unsecured-devices-step"
                            />
                        ),
                        stepTitle: t`Unsecured devices`,
                        isStepSkippable: false,
                        onStepChange: unsecuredDevicesTriggerSubmit,
                    },
                    {
                        component: () => (
                            <DeviceDeliveryStep
                                formRef={deviceDeliveryFormRef}
                                data-id="device-delivery-step"
                            />
                        ),
                        stepTitle: t`Device delivery`,
                        isStepSkippable: false,
                    },
                ],
                onCancel: () => {
                    navigate(riskRegisterToRedirectLink);
                },
                onComplete: () => {
                    deviceDeliveryTriggerSubmit();
                },
            }),
            [
                t,
                navigate,
                riskRegisterToRedirectLink,
                aiTriggerSubmit,
                physicalSiteTriggerSubmit,
                cloudEnvironmentTriggerSubmit,
                regulatoryRequirementsTriggerSubmit,
                softwareDevelopmentTriggerSubmit,
                unsecuredDevicesTriggerSubmit,
                deviceDeliveryTriggerSubmit,
                aiFormRef,
                physicalSiteFormRef,
                cloudEnvironmentFormRef,
                regulatoryRequirementsFormRef,
                softwareDevelopmentFormRef,
                unsecuredDevicesFormRef,
                deviceDeliveryFormRef,
            ],
        );

        return (
            <Wizard
                data-testid="RiskRegisterOnboardingBuildView"
                data-id="risk-register-onboarding-build-view"
                steps={wizardProps.steps}
                completeButtonLabel={t`Finish`}
                nextButtonLabel={t`Continue`}
                isLoading={isPending}
                onCancel={wizardProps.onCancel}
                onComplete={wizardProps.onComplete}
            />
        );
    },
);
