import { sharedOnboardingBuildFlowController } from '@controllers/risk';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedOnboardingBuildFlowModel } from '@models/risk-management';
import { Form, type FormValues } from '@ui/forms';

interface CloudEnvironmentStepProps {
    formRef: React.RefObject<HTMLFormElement>;
}

export const CloudEnvironmentStep = observer(
    ({ formRef }: CloudEnvironmentStepProps): React.JSX.Element => {
        const { handleOnStepSubmit } = sharedOnboardingBuildFlowController;
        const { getStepSchema } = sharedOnboardingBuildFlowModel;

        return (
            <Stack
                data-id="cloud-environment-step"
                data-testid="CloudEnvironmentStep"
                direction="column"
                gap="xl"
            >
                <Text type="subheadline" size="400">
                    <Trans>Identify cloud environment risks</Trans>
                </Text>
                <Text>
                    <Trans>
                        The Cloud has become integral to most businesses’
                        day-to-day operations, but because of its decentralized
                        nature it inherently introduces threats.
                    </Trans>
                </Text>
                <Form
                    hasExternalSubmitButton
                    data-id="cloud-environment-form"
                    formId="cloud-environment-form"
                    ref={formRef}
                    schema={getStepSchema('CLOUD_ENVIRONMENT')}
                    onSubmit={(formData: FormValues) => {
                        handleOnStepSubmit('CLOUD_ENVIRONMENT', formData);
                    }}
                />
            </Stack>
        );
    },
);
