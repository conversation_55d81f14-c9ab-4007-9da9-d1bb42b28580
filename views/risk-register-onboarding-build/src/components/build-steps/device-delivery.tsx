import { sharedOnboardingBuildFlowController } from '@controllers/risk';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedOnboardingBuildFlowModel } from '@models/risk-management';
import { Form, type FormValues } from '@ui/forms';

interface DeviceDeliveryStepProps {
    formRef: React.RefObject<HTMLFormElement>;
}

export const DeviceDeliveryStep = observer(
    ({ formRef }: DeviceDeliveryStepProps): React.JSX.Element => {
        const { handleOnStepSubmit } = sharedOnboardingBuildFlowController;
        const { getStepSchema } = sharedOnboardingBuildFlowModel;

        return (
            <Stack
                data-id="device-delivery-step"
                data-testid="DeviceDeliveryStep"
                direction="column"
                gap="xl"
            >
                <Text type="subheadline" size="400">
                    <Trans>Identify device delivery risks</Trans>
                </Text>
                <Text>
                    <Trans>
                        Many companies ship devices to their distributed
                        workforce, creating an opportunity for risks to arise in
                        transit.
                    </Trans>
                </Text>
                <Form
                    hasExternalSubmitButton
                    data-id="device-delivery-form"
                    formId="device-delivery-form"
                    ref={formRef}
                    schema={getStepSchema('DEVICE_DELIVERY')}
                    onSubmit={(formData: FormValues) => {
                        handleOnStepSubmit('DEVICE_DELIVERY', formData);
                    }}
                />
            </Stack>
        );
    },
);
