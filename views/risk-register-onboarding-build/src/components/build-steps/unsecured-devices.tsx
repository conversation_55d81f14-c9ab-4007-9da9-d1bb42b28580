import { sharedOnboardingBuild<PERSON>lowController } from '@controllers/risk';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedOnboardingBuildFlowModel } from '@models/risk-management';
import { Form, type FormValues } from '@ui/forms';

interface UnsecuredDevicesStepProps {
    formRef: React.RefObject<HTMLFormElement>;
}

export const UnsecuredDevicesStep = observer(
    ({ formRef }: UnsecuredDevicesStepProps): React.JSX.Element => {
        const { handleOnStepSubmit } = sharedOnboardingBuildFlowController;
        const { getStepSchema } = sharedOnboardingBuildFlowModel;

        return (
            <Stack
                data-id="unsecured-devices-step"
                data-testid="UnsecuredDevicesStep"
                direction="column"
                gap="xl"
            >
                <Text type="subheadline" size="400">
                    <Trans>Identify unsecured device risks</Trans>
                </Text>
                <Text>
                    <Trans>
                        An unlocked or unattended laptop or phone can be the
                        perfect target for a malicious actor to infiltrate your
                        business.
                    </Trans>
                </Text>
                <Form
                    hasExternalSubmitButton
                    data-id="unsecured-devices-form"
                    formId="unsecured-devices-form"
                    ref={formRef}
                    schema={getStepSchema('UNSECURE_DEVICES')}
                    onSubmit={(formData: FormValues) => {
                        handleOnStepSubmit('UNSECURE_DEVICES', formData);
                    }}
                />
            </Stack>
        );
    },
);
