import { sharedOnboardingBuildFlowController } from '@controllers/risk';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedOnboardingBuildFlowModel } from '@models/risk-management';
import { Form, type FormValues } from '@ui/forms';

interface PhysicalSiteStepProps {
    formRef: React.RefObject<HTMLFormElement>;
}

export const PhysicalSiteStep = observer(
    ({ formRef }: PhysicalSiteStepProps): React.JSX.Element => {
        const { handleOnStepSubmit } = sharedOnboardingBuildFlowController;
        const { getStepSchema } = sharedOnboardingBuildFlowModel;

        return (
            <Stack
                data-id="physical-site-step"
                data-testid="PhysicalSiteStep"
                direction="column"
                gap="xl"
            >
                <Text type="subheadline" size="400">
                    <Trans>Identify physical site risks</Trans>
                </Text>
                <Text>
                    <Trans>
                        Owning or operating a physical office can introduce a
                        number of unique threats to your organization.
                    </Trans>
                </Text>
                <Form
                    hasExternalSubmitButton
                    data-id="physical-site-form"
                    formId="physical-site-form"
                    ref={formRef}
                    schema={getStepSchema('PHYSICAL_SITE')}
                    onSubmit={(formData: FormValues) => {
                        handleOnStepSubmit('PHYSICAL_SITE', formData);
                    }}
                />
            </Stack>
        );
    },
);
