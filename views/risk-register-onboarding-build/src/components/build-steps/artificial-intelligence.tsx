import { sharedOnboardingBuild<PERSON>lowController } from '@controllers/risk';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedOnboardingBuildFlowModel } from '@models/risk-management';
import { Form, type FormValues } from '@ui/forms';

interface ArtificialIntelligenceStepProps {
    formRef: React.RefObject<HTMLFormElement>;
}

export const ArtificialIntelligenceStep = observer(
    ({ formRef }: ArtificialIntelligenceStepProps): React.JSX.Element => {
        const { handleOnStepSubmit } = sharedOnboardingBuildFlowController;
        const { getStepSchema } = sharedOnboardingBuildFlowModel;

        return (
            <Stack
                data-id="artificial-intelligence-step"
                data-testid="ArtificialIntelligenceStep"
                direction="column"
                gap="xl"
            >
                <Text type="subheadline" size="400">
                    <Trans>Identify artificial intelligence risks</Trans>
                </Text>
                <Text>
                    <Trans>
                        AI provides many exciting new opportunities, but it can
                        also introduce threats and vulnerabilities.
                    </Trans>
                </Text>
                <Form
                    hasExternalSubmitButton
                    data-id="artificial-intelligence-form"
                    formId="artificial-intelligence-form"
                    ref={formRef}
                    schema={getStepSchema('AI_DEVELOPMENT')}
                    onSubmit={(formData: FormValues) => {
                        handleOnStepSubmit('AI_DEVELOPMENT', formData);
                    }}
                />
            </Stack>
        );
    },
);
