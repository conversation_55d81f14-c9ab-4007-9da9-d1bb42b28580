import { sharedOnboardingBuildFlowController } from '@controllers/risk';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedOnboardingBuildFlowModel } from '@models/risk-management';
import { Form, type FormValues } from '@ui/forms';

interface RegulatoryRequirementsStepProps {
    formRef: React.RefObject<HTMLFormElement>;
}

export const RegulatoryRequirementsStep = observer(
    ({ formRef }: RegulatoryRequirementsStepProps): React.JSX.Element => {
        const { handleOnStepSubmit } = sharedOnboardingBuildFlowController;
        const { getStepSchema } = sharedOnboardingBuildFlowModel;

        return (
            <Stack
                data-id="regulatory-requirements-step"
                data-testid="RegulatoryRequirementsStep"
                direction="column"
                gap="xl"
            >
                <Text type="subheadline" size="400">
                    <Trans>Identify regulatory requirement risks</Trans>
                </Text>
                <Text>
                    <Trans>
                        Most companies need to adhere to certain{` `}
                        <Tooltip
                            text={t`Common examples are HIPAA, SOC2, and ISO 27001`}
                        >
                            <Text as="span" colorScheme="primary">
                                industry-specific regulatory guidelines
                            </Text>
                        </Tooltip>
                        . Legal, statutory, regulatory, or contractual
                        requirements should be taken into consideration when
                        evaluating threats and vulnerabilities and identifying
                        risks.
                    </Trans>
                </Text>
                <Form
                    hasExternalSubmitButton
                    data-id="regulatory-requirements-form"
                    formId="regulatory-requirements-form"
                    ref={formRef}
                    schema={getStepSchema('REGULATORY_REQUIREMENTS')}
                    onSubmit={(formData: FormValues) => {
                        handleOnStepSubmit('REGULATORY_REQUIREMENTS', formData);
                    }}
                />
            </Stack>
        );
    },
);
