import { sharedOnboardingBuild<PERSON>lowController } from '@controllers/risk';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedOnboardingBuildFlowModel } from '@models/risk-management';
import { Form, type FormValues } from '@ui/forms';

interface SoftwareDevelopmentStepProps {
    formRef: React.RefObject<HTMLFormElement>;
}

export const SoftwareDevelopmentStep = observer(
    ({ formRef }: SoftwareDevelopmentStepProps): React.JSX.Element => {
        const { handleOnStepSubmit } = sharedOnboardingBuildFlowController;
        const { getStepSchema } = sharedOnboardingBuildFlowModel;

        return (
            <Stack
                data-id="software-development-step"
                data-testid="SoftwareDevelopmentStep"
                direction="column"
                gap="xl"
            >
                <Text type="subheadline" size="400">
                    <Trans>Identify software development risks</Trans>
                </Text>
                <Text>
                    <Trans>
                        The ever-changing landscape of technology and software
                        development can introduce new threats and
                        vulnerabilities.
                    </Trans>
                </Text>
                <Form
                    hasExternalSubmitButton
                    data-id="software-development-form"
                    formId="software-development-form"
                    ref={formRef}
                    schema={getStepSchema('SOFTWARE_DEVELOPMENT')}
                    onSubmit={(formData: FormValues) => {
                        handleOnStepSubmit('SOFTWARE_DEVELOPMENT', formData);
                    }}
                />
            </Stack>
        );
    },
);
