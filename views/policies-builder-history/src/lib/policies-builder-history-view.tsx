import { isEmpty } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import { PoliciesBuilderEmptyStateComponent } from '@components/policies-builder-empty-state';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import type { PolicyTableVersionResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import {
    policiesBuilderHistoryDatatableController,
    type PoliciesBuilderHistoryDatatableQuery,
} from './controllers/policies-builder-history-datatable.controller';

export const PoliciesBuilderHistoryView = observer((): React.JSX.Element => {
    if (
        isEmpty(sharedPolicyBuilderController.policy?.latestPolicyVersion) &&
        !sharedPolicyBuilderModel.isPolicyBuilderFirstLoading
    ) {
        return <PoliciesBuilderEmptyStateComponent />;
    }

    return (
        <AppDatatable<
            PolicyTableVersionResponseDto,
            PoliciesBuilderHistoryDatatableQuery
        >
            controller={policiesBuilderHistoryDatatableController}
            data-testid="PoliciesBuilderHistoryView"
            data-id="8NcGvlR0"
        />
    );
});
