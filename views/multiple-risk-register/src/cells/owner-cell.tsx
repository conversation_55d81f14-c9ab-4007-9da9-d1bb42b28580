import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { getFullName, getInitials } from '@helpers/formatters';
import type { MultipleRiskRegisterStub } from '../types/multiple-risk-register-type';

export const OwnerCell = ({
    row: { original },
}: {
    row: { original: MultipleRiskRegisterStub };
}): React.JSX.Element => {
    const { firstName, lastName, avatarUrl } = original.owner;
    const name = getFullName(firstName, lastName);
    const initials = getInitials(name);

    return (
        <AvatarIdentity
            primaryLabel={name}
            fallbackText={initials}
            data-testid="OwnerCell"
            data-id="2rzyY5l2"
            imgSrc={avatarUrl ?? ''}
        />
    );
};
