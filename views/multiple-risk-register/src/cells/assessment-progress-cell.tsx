import { dataDiverge2Strong } from '@cosmos/constants/tokens';
import { DataMeter } from '@cosmos-lab/components/data-meter';
import type { MultipleRiskRegisterStub } from '../types/multiple-risk-register-type';

export const AssessmentProgressCell = ({
    row: { original },
}: {
    row: { original: MultipleRiskRegisterStub };
}): React.JSX.Element => {
    const max = 100;

    return (
        <DataMeter
            size="lg"
            value={original.assessmentProgress}
            max={max}
            label={`${original.assessmentProgress} of ${max}`}
            data-testid="AssessmentProgressCell"
            overrideColor={dataDiverge2Strong}
            id="assessment-progress-data-meter"
            aria-labelledby="assessment-progress-data-meter"
            data-id="AJu-mhRj"
        />
    );
};
