import { DateTime } from '@cosmos-lab/components/date-time';
import type { MultipleRiskRegisterStub } from '../types/multiple-risk-register-type';

export const RiskRegisterCreatedAtCell = ({
    row: { original },
}: {
    row: { original: MultipleRiskRegisterStub };
}): React.JSX.Element => {
    const { createdAt, id } = original;

    return (
        <DateTime
            date={createdAt}
            format="field"
            data-id={`${id}-created-at`}
            data-testid="RiskRegisterCreatedAtCell"
        />
    );
};
