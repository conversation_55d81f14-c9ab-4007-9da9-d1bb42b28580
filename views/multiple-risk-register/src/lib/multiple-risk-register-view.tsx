import { AppDatatable } from '@components/app-datatable';
import { observer } from '@globals/mobx';
import {
    multipleRiskRegisterDatatableController,
    type MultipleRiskRegisterDatatableQuery,
} from '../controllers/multiple-risk-register-datatable.controller';
import type { MultipleRiskRegisterStub } from '../types/multiple-risk-register-type';

export const MultipleRiskRegisterView = observer((): React.JSX.Element => {
    return (
        <AppDatatable<
            MultipleRiskRegisterStub,
            MultipleRiskRegisterDatatableQuery
        >
            controller={multipleRiskRegisterDatatableController}
            data-id="8GH5a-PE"
        />
    );
});
