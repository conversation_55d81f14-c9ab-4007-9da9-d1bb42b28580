import type { UserResponseDto } from '@globals/api-sdk/types';

// TODO: remove this when API is implemented ENG-72820
export interface MultipleRiskRegisterStub {
    id: number;
    name: string;
    owner: UserResponseDto;
    assessmentProgress: number;
    accepted: number;
    mitigated: number;
    avoided: number;
    transferred: number;
    internal: number;
    external: number;
    createdAt: Date;
}
