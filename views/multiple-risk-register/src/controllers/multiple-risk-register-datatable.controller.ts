import type { ExternalDatatableController } from '@components/app-datatable';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import type {
    DatatableProps,
    FilterProps,
    FilterViewModeProps,
    RowActionItem,
    RowActionsProps,
    TableSearchProps,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { COLUMN_SIZES } from '@helpers/table';
import { AssessmentProgressCell } from '../cells/assessment-progress-cell';
import { OwnerCell } from '../cells/owner-cell';
import { RiskRegisterCreatedAtCell } from '../cells/risk-register-created-at-cell';
import { registerStubs } from '../stubs/multiple-register-stubs';
import type { MultipleRiskRegisterStub } from '../types/multiple-risk-register-type';

// Todo: Replace with actual query type once API is available ENG-72820
export type MultipleRiskRegisterDatatableQuery = Required<{
    page: number;
    limit: number;
    sort: string;
    sortDir: string;
}>;

class MultipleRiskRegisterDatatableController
    implements
        ExternalDatatableController<
            MultipleRiskRegisterStub,
            MultipleRiskRegisterDatatableQuery
        >
{
    constructor() {
        makeAutoObservable(this);
    }

    get filterProps(): FilterProps {
        return {
            clearAllButtonLabel: t`Reset`,
            triggerLabel: t`Filters`,
            filters: [
                {
                    filterType: 'combobox',
                    id: 'userId',
                    label: t`Owner`,
                    options: [
                        {
                            id: '1',
                            label: 'User 1',
                            value: '1',
                        },
                        {
                            id: '2',
                            label: 'User 2',
                            value: '2',
                        },
                    ],
                },
            ],
        };
    }
    get filterViewModeProps(): FilterViewModeProps {
        return {
            viewMode: 'toggleable',
            props: {
                initialSelectedOption: 'pinned',
                togglePinnedLabel: t`Pin filters to page`,
                toggleUnpinnedLabel: t`Move filters to dropdown`,
                selectedOption: 'pinned',
            },
        };
    }

    get rowActionsProps(): RowActionsProps<MultipleRiskRegisterStub> {
        return {
            type: 'dropdown',
            getRowActions: this.getRowActions,
        };
    }

    get tableId(): string {
        return 'multiple-risk-register-datatable';
    }
    get tableSearchProps(): TableSearchProps {
        return {
            hideSearch: false,
            placeholder: t`Search`,
            debounceDelay: 500,
        };
    }

    get data(): MultipleRiskRegisterStub[] {
        return registerStubs;
    }

    get total(): number {
        return registerStubs.length;
    }

    get isLoading(): boolean {
        return false;
    }

    get columns(): DatatableProps<MultipleRiskRegisterStub>['columns'] {
        return [
            {
                id: 'name',
                header: t`Register name`,
                accessorKey: 'name',
                enableSorting: true,
                size: 200,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'user',
                header: t`Owner`,
                accessorKey: 'owner',
                cell: OwnerCell,
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'assessmentProgress',
                header: t`Assessment progress`,
                accessorKey: 'assessmentProgress',
                cell: AssessmentProgressCell,
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'accepted',
                header: t`Accepted`,
                accessorKey: 'accepted',
                enableSorting: true,
                minSize: COLUMN_SIZES.MEDIUM,
            },
            {
                id: 'mitigated',
                header: t`Mitigated`,
                accessorKey: 'mitigated',
                enableSorting: true,
                minSize: COLUMN_SIZES.MEDIUM,
            },
            {
                id: 'avoided',
                header: t`Avoided`,
                accessorKey: 'avoided',
                enableSorting: true,
                minSize: COLUMN_SIZES.MEDIUM,
            },
            {
                id: 'transferred',
                header: t`Transferred`,
                accessorKey: 'transferred',
                enableSorting: true,
                minSize: COLUMN_SIZES.MEDIUM,
            },
            {
                id: 'internal',
                header: t`Internal`,
                accessorKey: 'internal',
                enableSorting: true,
                minSize: COLUMN_SIZES.MEDIUM,
            },
            {
                id: 'external',
                header: t`External`,
                accessorKey: 'external',
                enableSorting: true,
                minSize: COLUMN_SIZES.MEDIUM,
            },
            {
                id: 'createdAt',
                header: t`Created date`,
                accessorKey: 'createdAt',
                cell: RiskRegisterCreatedAtCell,
                enableSorting: true,
                minSize: COLUMN_SIZES.MEDIUM,
            },
        ];
    }

    onRowClick: DatatableProps<MultipleRiskRegisterStub>['onRowClick'] = ({
        row,
    }) => {
        if (!row.id) {
            return;
        }

        sharedProgrammaticNavigationController.navigateTo(
            `${routeController.userPartOfUrl}/risk/registers/${row.id}`,
        );
    };

    getRowActions = (row: MultipleRiskRegisterStub): RowActionItem[] => {
        if (!row.id) {
            return [];
        }

        return [
            {
                id: 'edit-register',
                label: t`Edit`,
                onClick: () => {
                    // TODO: implement edit functionality ENG-72816
                },
            },
        ];
    };

    load = (): void => {
        // TODO: implement API call to fetch data ENG-72820
    };
}

export const multipleRiskRegisterDatatableController =
    new MultipleRiskRegisterDatatableController();
