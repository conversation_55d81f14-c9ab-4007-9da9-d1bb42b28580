import type { UserResponseDto } from '@globals/api-sdk/types';
import type { MultipleRiskRegisterStub } from '../types/multiple-risk-register-type';

export const registerStubs = [
    {
        id: 1,
        name: 'Risk Register 1',
        owner: {
            firstName: 'User',
            lastName: '1',
            avatarUrl: '',
            email: '<EMAIL>',
            entryId: '1',
            jobTitle: 'Job Title 1',
            drataTermsAgreedAt: new Date().toUTCString(),
            createdAt: new Date().toUTCString(),
            updatedAt: new Date().toUTCString(),
            roles: ['EMPLOYEE'],
        } as UserResponseDto,
        assessmentProgress: 90,
        accepted: 50,
        mitigated: 50,
        avoided: 50,
        transferred: 50,
        internal: 100,
        external: 100,
        createdAt: new Date(),
    },
    {
        id: 2,
        name: 'Risk Register 2',
        owner: {
            firstName: 'User',
            lastName: '2',
            avatarUrl: '',
            email: '<EMAIL>',
            entryId: '2',
        } as UserResponseDto,
        assessmentProgress: 90,
        accepted: 50,
        mitigated: 50,
        avoided: 50,
        transferred: 50,
        internal: 100,
        external: 100,
        createdAt: new Date(),
    },
] as MultipleRiskRegisterStub[];
