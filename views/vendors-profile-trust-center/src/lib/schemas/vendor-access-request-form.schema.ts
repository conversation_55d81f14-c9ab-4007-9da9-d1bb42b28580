import { isEmpty, isNil } from 'lodash-es';
import { z } from 'zod';
import type { VendorTrustCenterAccessRequestRequirementsResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import type { FormSchema } from '@ui/forms';

// The unicode ranges cover Latin letters, Latin-1 Supplement, and Latin Extended-A
// Excludes multiplication (×) and division (÷) signs for security
const NAME_REGEX_STRING =
    '^[\u00DF-\u00F6\u00F8-\u017Fa-z0-9 |,./\'’\\-+()"&\\\\]';

// eslint-disable-next-line regexp/no-obscure-range -- this is used to match validation on safebase
const NAME_REGEX = new RegExp(`${NAME_REGEX_STRING}+$`, 'i');

const createNameValidator = (
    fieldName: string,
    minLength?: number,
    maxLength?: number,
    isRequired = true,
) => {
    const unsupportedCharsMessage = t`You have entered unsupported characters for this field`;
    const requiredMessage = t`Enter ${fieldName}`;

    let baseValidator = z.string();

    if (!isNil(minLength)) {
        baseValidator = baseValidator.min(minLength, requiredMessage);
    }

    if (!isNil(maxLength)) {
        baseValidator = baseValidator.max(maxLength);
    }

    if (isRequired) {
        return baseValidator.superRefine((val, ctx) => {
            if (isEmpty(val.trim())) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: requiredMessage,
                });

                return;
            }

            if (!NAME_REGEX.test(val)) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: unsupportedCharsMessage,
                });
            }
        });
    }

    return z.union([
        z.literal(''),
        baseValidator.regex(NAME_REGEX, unsupportedCharsMessage),
    ]);
};

const createEmailValidator = (isRequired = true) => {
    return z.string().superRefine((val, ctx) => {
        if (isRequired && isEmpty(val.trim())) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Enter email',
            });

            return;
        }

        if (!isEmpty(val.trim())) {
            const result = z
                .string()
                .email('This email is invalid')
                .safeParse(val);

            if (!result.success) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'This email is invalid',
                });
            }
        }
    });
};

const createCheckboxValidator = (isRequired = true) => {
    const baseValidator = z.boolean();

    if (isRequired) {
        return baseValidator.refine((val) => val, {
            message: t`Please check this box if you want to proceed`,
        });
    }

    return baseValidator;
};

const createSelectValidator = () => {
    return z.object(
        { id: z.string(), value: z.string(), label: z.string() },
        {
            message: t`Please select an option`,
        },
    );
};

const createMultiSelectValidator = () => {
    return z
        .array(
            z.object({
                id: z.string(),
                value: z.string(),
                label: z.string(),
            }),
        )
        .nonempty('Please select at least one option');
};

export const buildVendorAccessRequestFormSchema = (
    requirements: VendorTrustCenterAccessRequestRequirementsResponseDto,
): FormSchema => {
    const schema: FormSchema = {};

    const nonCheckboxFields = requirements.fields.filter(
        (field) => field.type !== 'checkbox',
    );
    const checkboxFields = requirements.fields.filter(
        (field) => field.type === 'checkbox',
    );

    const orderedFields = [...nonCheckboxFields, ...checkboxFields];

    for (const field of orderedFields) {
        const isRequired = field.required;

        let fieldConfig: FormSchema[string];

        switch (field.type) {
            case 'text': {
                fieldConfig = {
                    type: 'text',
                    label: field.label,
                    initialValue: '',
                    isOptional: !isRequired,
                    validator: createNameValidator(
                        field.label,
                        field.minLength,
                        field.maxLength,
                        isRequired,
                    ),
                };
                break;
            }

            case 'email': {
                const isWorkEmailField = field.name === 'workEmail';
                const currentUserEmail =
                    sharedCurrentUserController.user?.email ?? '';

                fieldConfig = {
                    type: 'text',
                    label: field.label,
                    initialValue: isWorkEmailField ? currentUserEmail : '',
                    isOptional: !isRequired,
                    validator: createEmailValidator(isRequired),
                    readOnly: isWorkEmailField,
                };
                break;
            }

            case 'select': {
                fieldConfig = {
                    type: 'select',
                    label: field.label,
                    isOptional: !isRequired,
                    validator: isRequired ? createSelectValidator() : undefined,
                    options: field.options?.map((option) => ({
                        id: option.value,
                        label: option.label,
                        value: option.value,
                    })),
                };
                break;
            }

            case 'multi-select': {
                fieldConfig = {
                    type: 'combobox',
                    label: field.label,
                    initialValue: [],
                    isOptional: !isRequired,
                    validator: isRequired
                        ? createMultiSelectValidator()
                        : undefined,
                    isMultiSelect: true,
                    loaderLabel: t`Loading options...`,
                    options: field.options?.map((option) => ({
                        id: option.value,
                        label: option.label,
                        value: option.value,
                    })),
                };
                break;
            }

            case 'checkbox': {
                fieldConfig = {
                    type: 'checkbox',
                    label: field.label,
                    initialValue: false,
                    isOptional: !isRequired,
                    validator: createCheckboxValidator(isRequired),
                };
                break;
            }

            default: {
                // Skip unsupported field types
                continue;
            }
        }

        schema[field.name] = fieldConfig;
    }

    return schema;
};
