import { describe, expect, test } from 'vitest';
import type { VendorTrustCenterAccessRequestRequirementsResponseDto } from '@globals/api-sdk/types';
import { buildVendorAccessRequestFormSchema } from './vendor-access-request-form.schema';

describe('vendor-access-request-form.schema', () => {
    describe('buildVendorAccessRequestFormSchema', () => {
        test('should return empty schema for requirements with empty fields', () => {
            const requirements: VendorTrustCenterAccessRequestRequirementsResponseDto =
                {
                    memberLoginStatusCode: 'email_not_recognized',
                    fields: [],
                };

            const schema = buildVendorAccessRequestFormSchema(requirements);

            expect(schema).toStrictEqual({});
        });

        test('should build schema for text field', () => {
            const requirements: VendorTrustCenterAccessRequestRequirementsResponseDto =
                {
                    memberLoginStatusCode: 'email_not_recognized',
                    fields: [
                        {
                            name: 'firstName',
                            type: 'text',
                            label: 'First Name',
                            required: true,
                            minLength: 2,
                            maxLength: 50,
                        },
                    ],
                };

            const schema = buildVendorAccessRequestFormSchema(requirements);

            expect(schema).toHaveProperty('firstName');
            expect(schema.firstName).toMatchObject({
                type: 'text',
                label: 'First Name',
                initialValue: '',
                isOptional: false,
            });
            expect(schema.firstName.validator).toBeDefined();
        });

        test('should build schema for email field', () => {
            const requirements: VendorTrustCenterAccessRequestRequirementsResponseDto =
                {
                    memberLoginStatusCode: 'email_not_recognized',
                    fields: [
                        {
                            name: 'email',
                            type: 'email',
                            label: 'Email Address',
                            required: true,
                        },
                    ],
                };

            const schema = buildVendorAccessRequestFormSchema(requirements);

            expect(schema).toHaveProperty('email');
            expect(schema.email).toMatchObject({
                type: 'text',
                label: 'Email Address',
                initialValue: '',
                isOptional: false,
            });
            expect(schema.email.validator).toBeDefined();
        });

        test('should build schema for select field with options', () => {
            const requirements: VendorTrustCenterAccessRequestRequirementsResponseDto =
                {
                    memberLoginStatusCode: 'email_not_recognized',
                    fields: [
                        {
                            name: 'role',
                            type: 'select',
                            label: 'Role',
                            required: true,
                            options: [
                                { value: 'customer', label: 'Customer' },
                                { value: 'partner', label: 'Partner' },
                            ],
                        },
                    ],
                };

            const schema = buildVendorAccessRequestFormSchema(requirements);

            expect(schema).toHaveProperty('role');
            expect(schema.role).toMatchObject({
                type: 'select',
                label: 'Role',
                isOptional: false,
                options: [
                    { id: 'customer', label: 'Customer', value: 'customer' },
                    { id: 'partner', label: 'Partner', value: 'partner' },
                ],
            });

            expect(schema.role).not.toHaveProperty('initialValue');
            expect(schema.role.validator).toBeDefined();
        });

        test('should build schema for multi-select field', () => {
            const requirements: VendorTrustCenterAccessRequestRequirementsResponseDto =
                {
                    memberLoginStatusCode: 'email_not_recognized',
                    fields: [
                        {
                            name: 'interests',
                            type: 'multi-select',
                            label: 'Interests',
                            required: false,
                            options: [
                                { value: 'security', label: 'Security' },
                                {
                                    value: 'compliance',
                                    label: 'Compliance',
                                },
                            ],
                        },
                    ],
                };

            const schema = buildVendorAccessRequestFormSchema(requirements);

            expect(schema).toHaveProperty('interests');
            expect(schema.interests).toMatchObject({
                type: 'combobox',
                label: 'Interests',
                isOptional: true,
                isMultiSelect: true,
                loaderLabel: 'Loading options...',
                options: [
                    { id: 'security', label: 'Security', value: 'security' },
                    {
                        id: 'compliance',
                        label: 'Compliance',
                        value: 'compliance',
                    },
                ],
            });

            expect(schema.interests).toHaveProperty('initialValue', []);
            expect(schema.interests.validator).toBeUndefined();
        });

        test('should build schema for checkbox field', () => {
            const requirements: VendorTrustCenterAccessRequestRequirementsResponseDto =
                {
                    memberLoginStatusCode: 'email_not_recognized',
                    fields: [
                        {
                            name: 'agreeToTerms',
                            type: 'checkbox',
                            label: "I have read and agree to SafeBase's Terms of Service and Privacy Notice.",
                            required: true,
                        },
                    ],
                };

            const schema = buildVendorAccessRequestFormSchema(requirements);

            expect(schema).toHaveProperty('agreeToTerms');
            expect(schema.agreeToTerms).toMatchObject({
                type: 'checkbox',
                label: "I have read and agree to SafeBase's Terms of Service and Privacy Notice.",
                initialValue: false,
                isOptional: false,
            });
            expect(schema.agreeToTerms.validator).toBeDefined();
        });

        test('should skip unsupported field types', () => {
            const requirements: VendorTrustCenterAccessRequestRequirementsResponseDto =
                {
                    memberLoginStatusCode: 'email_not_recognized',
                    fields: [
                        {
                            name: 'firstName',
                            type: 'text',
                            label: 'First Name',
                            required: true,
                        },
                        {
                            name: 'unsupported',
                            // @ts-expect-error - Testing unsupported type
                            type: 'unsupported-type',
                            label: 'Unsupported Field',
                            required: true,
                        },
                    ],
                };

            const schema = buildVendorAccessRequestFormSchema(requirements);

            expect(schema).not.toHaveProperty('unsupported');
            expect(schema).toHaveProperty('firstName');
        });

        test('should handle optional fields correctly', () => {
            const requirements: VendorTrustCenterAccessRequestRequirementsResponseDto =
                {
                    memberLoginStatusCode: 'email_not_recognized',
                    fields: [
                        {
                            name: 'optionalField',
                            type: 'text',
                            label: 'Optional Field',
                            required: false,
                        },
                    ],
                };

            const schema = buildVendorAccessRequestFormSchema(requirements);

            expect(schema.optionalField).toMatchObject({
                type: 'text',
                label: 'Optional Field',
                initialValue: '',
                isOptional: true,
            });
            expect(schema.optionalField.validator).toBeDefined();
        });

        test('should build schema with multiple field types', () => {
            const requirements: VendorTrustCenterAccessRequestRequirementsResponseDto =
                {
                    memberLoginStatusCode: 'email_not_recognized',
                    fields: [
                        {
                            name: 'firstName',
                            type: 'text',
                            label: 'First Name',
                            required: true,
                        },
                        {
                            name: 'email',
                            type: 'email',
                            label: 'Email',
                            required: true,
                        },
                        {
                            name: 'role',
                            type: 'select',
                            label: 'Role',
                            required: true,
                            options: [{ value: 'customer', label: 'Customer' }],
                        },
                        {
                            name: 'agree',
                            type: 'checkbox',
                            label: 'I agree',
                            required: true,
                        },
                    ],
                };

            const schema = buildVendorAccessRequestFormSchema(requirements);

            expect(Object.keys(schema)).toStrictEqual([
                'firstName',
                'email',
                'role',
                'agree',
            ]);
        });

        test('should place checkbox fields at the end', () => {
            const requirements: VendorTrustCenterAccessRequestRequirementsResponseDto =
                {
                    memberLoginStatusCode: 'email_not_recognized',
                    fields: [
                        {
                            name: 'agreeToTerms',
                            type: 'checkbox',
                            label: 'I agree to terms',
                            required: true,
                        },
                        {
                            name: 'firstName',
                            type: 'text',
                            label: 'First Name',
                            required: true,
                        },
                        {
                            name: 'agreeToPrivacy',
                            type: 'checkbox',
                            label: 'I agree to privacy policy',
                            required: true,
                        },
                        {
                            name: 'email',
                            type: 'email',
                            label: 'Email',
                            required: true,
                        },
                    ],
                };

            const schema = buildVendorAccessRequestFormSchema(requirements);

            // Checkbox fields should be at the end, regardless of their original order
            expect(Object.keys(schema)).toStrictEqual([
                'firstName',
                'email',
                'agreeToTerms',
                'agreeToPrivacy',
            ]);
        });

        test('should handle workEmail field as readonly with current user email', () => {
            const requirements: VendorTrustCenterAccessRequestRequirementsResponseDto =
                {
                    memberLoginStatusCode: 'email_not_recognized',
                    fields: [
                        {
                            name: 'firstName',
                            type: 'text',
                            label: 'First Name',
                            required: true,
                        },
                        {
                            name: 'workEmail',
                            type: 'email',
                            label: 'Work Email',
                            required: true,
                        },
                    ],
                };

            const schema = buildVendorAccessRequestFormSchema(requirements);

            expect(schema).toHaveProperty('workEmail');
            expect(schema.workEmail).toMatchObject({
                type: 'text',
                label: 'Work Email',
                isOptional: false, // Since the field was required in the requirements
                readOnly: true,
            });
            expect(schema.workEmail).toHaveProperty('initialValue');
            expect(typeof schema.workEmail.initialValue).toBe('string');
            expect(schema.workEmail).toHaveProperty('validator');
        });
    });
});
