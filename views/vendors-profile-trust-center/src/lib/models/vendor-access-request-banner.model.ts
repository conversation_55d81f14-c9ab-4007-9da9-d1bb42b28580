import { sharedVendorTrustCenterController } from '@controllers/vendors';
import type { BannerProps } from '@cosmos/components/banner';
import type { ButtonProps } from '@cosmos/components/button';
import type { IconName } from '@cosmos/components/icon';
import type { TextProps } from '@cosmos/components/text';
import { plural, t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { getTimeDiff } from '@helpers/date-time';
import { openVendorAccessRequestModal } from '../helpers/vendor-access-request-modal.helper';

export interface BannerConfig {
    title: string;
    body: {
        text: string;
        colorScheme: TextProps['colorScheme'];
    };
    severity?: BannerProps['severity'];
    button?: {
        label: string;
        level: ButtonProps['level'];
        startIconName?: IconName;
        isLoading?: boolean;
        a11yLoadingLabel?: string;
        onClick?: () => void;
    };
}

export class VendorAccessRequestBannerModel {
    constructor() {
        makeAutoObservable(this);
    }

    get isBannerConfigLoading(): boolean {
        return sharedVendorTrustCenterController.isAccessRequestLoading;
    }

    get bannerConfig(): BannerConfig | null {
        const {
            isAccessRequestLoading,
            isAccessExpired,
            isAccessPending,
            isAccessGranted,
            isAccessDeclined,
        } = sharedVendorTrustCenterController;

        if (isAccessRequestLoading) {
            return null;
        }

        if (isAccessExpired) {
            return this.getExpiredConfig();
        }

        if (isAccessPending) {
            return this.getPendingConfig();
        }

        if (isAccessGranted) {
            return this.getGrantedConfig();
        }

        if (isAccessDeclined) {
            return this.getDeclinedConfig();
        }

        return this.getNotRequestedConfig();
    }

    private get vendorName(): string {
        return sharedVendorTrustCenterController.info?.name ?? '';
    }

    private get userEmail(): string {
        return (
            sharedVendorTrustCenterController.accessRequest?.requestedBy ?? ''
        );
    }

    private get timeToExpire(): { hours: number; days: number } {
        const { accessRequest } = sharedVendorTrustCenterController;
        const hoursToExpire = getTimeDiff(
            new Date(),
            accessRequest?.expiresAt ?? new Date(),
            'hours',
        );
        const daysToExpire = Math.round(hoursToExpire / 24);

        return { hours: hoursToExpire, days: daysToExpire };
    }

    private get grantedAccessTitle(): string {
        const { hours, days } = this.timeToExpire;

        return hours < 24
            ? plural(hours, {
                  one: t`Access granted for ${hours} hour.`,
                  other: t`Access granted for ${hours} hours.`,
              })
            : plural(days, {
                  one: t`Access granted for ${days} day.`,
                  other: t`Access granted for ${days} days.`,
              });
    }

    private getNotRequestedConfig(): BannerConfig {
        const { vendorName } = this;

        return {
            title: t`You are viewing public information that ${vendorName} has made available to everyone.`,
            body: {
                text: t`Make a request to access their documents and other information about their security posture.`,
                colorScheme: 'primary',
            },
            button: {
                label: t`Request access`,
                level: 'primary',
                startIconName: 'Lock',
                isLoading:
                    sharedVendorTrustCenterController.isAccessRequestRequirementsLoading,
                a11yLoadingLabel: t`Loading access request requirements`,
                onClick: openVendorAccessRequestModal,
            },
        };
    }

    private getPendingConfig(): BannerConfig {
        const { userEmail } = this;

        return {
            title: t`Status: Access requested`,
            body: {
                text: t`We've requested access to the vendor's information. Once they review it, you will receive an email at ${userEmail} with the next steps.`,
                colorScheme: 'primary',
            },
        };
    }

    private getGrantedConfig(): BannerConfig {
        return {
            title: this.grantedAccessTitle,
            body: {
                text: t`Add documents to your security review, learn about their controls, subprocessors, and other information related to their security posture.`,
                colorScheme: 'success',
            },
            severity: 'success',
            button: {
                label: t`Start Security Review`,
                level: 'secondary',
            },
        };
    }

    private getDeclinedConfig(): BannerConfig {
        const { vendorName } = this;

        return {
            title: t`You are viewing public information that ${vendorName} has made available to everyone.`,
            body: {
                text: t`The vendor has not provided further access. Please reach out to the contact at ${vendorName} if you have any further questions or concerns.`,
                colorScheme: 'primary',
            },
        };
    }

    private getExpiredConfig(): BannerConfig {
        const { vendorName } = this;

        return {
            title: t`Your access to ${vendorName}'s information has expired.`,
            body: {
                text: t`You are only viewing public information that ${vendorName} has made available to everyone.`,
                colorScheme: 'primary',
            },
            button: {
                label: t`Re-request Access`,
                level: 'primary',
                startIconName: 'Lock',
                isLoading:
                    sharedVendorTrustCenterController.isAccessRequestRequirementsLoading,
                a11yLoadingLabel: t`Loading access request requirements`,
                onClick: openVendorAccessRequestModal,
            },
        };
    }
}

export const sharedVendorAccessRequestBannerModel =
    new VendorAccessRequestBannerModel();
