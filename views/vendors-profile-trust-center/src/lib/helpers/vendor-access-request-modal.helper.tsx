import { modalController } from '@controllers/modal';
import { sharedVendorTrustCenterController } from '@controllers/vendors';
import { when } from '@globals/mobx';
import { VendorAccessRequestModal } from '../components/vendor-access-request-modal.component';

const VENDOR_ACCESS_REQUEST_MODAL_ID = 'vendor-access-request-modal';

export const closeVendorAccessRequestModal = (): void => {
    modalController.closeModal(VENDOR_ACCESS_REQUEST_MODAL_ID);
};

export const openVendorAccessRequestModal = (): void => {
    sharedVendorTrustCenterController.loadAccessRequestRequirements();

    when(
        () =>
            !sharedVendorTrustCenterController.isAccessRequestRequirementsLoading,
        () => {
            modalController.openModal({
                id: VENDOR_ACCESS_REQUEST_MODAL_ID,
                content: () => (
                    <VendorAccessRequestModal
                        data-id="zK9qL-qa"
                        onClose={closeVendorAccessRequestModal}
                    />
                ),
                centered: true,
                disableClickOutsideToClose: true,
                size: 'lg',
            });
        },
    );
};
