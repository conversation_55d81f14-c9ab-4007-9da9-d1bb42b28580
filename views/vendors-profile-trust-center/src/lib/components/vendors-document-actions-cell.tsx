import { Button } from '@cosmos/components/button';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Tooltip } from '@cosmos/components/tooltip';
import { t } from '@globals/i18n/macro';
import { AppButton } from '@ui/app-button';
import type { VendorsTrustCenterDocumentsData } from '../types/vedors-trust-center-documents-data.type';

export const VendorsDocumentActionsCell = ({
    row: { original },
}: {
    row: { original: VendorsTrustCenterDocumentsData };
}): React.JSX.Element => {
    const { id, isViewOnly, isAccessRequired } = original;

    if (isAccessRequired) {
        return (
            <Stack
                data-testid="VendorsDocumentActionsCell"
                data-id="oUFo3PmL"
                justify="end"
            >
                <Tooltip text={t`Access required`} preferredSide="right">
                    <Icon name="Lock" size="200" colorScheme="faded" />
                </Tooltip>
            </Stack>
        );
    }

    return (
        <Stack
            data-testid="VendorsDocumentActionsCell"
            data-id="oUFo3PmL"
            gap="2x"
            justify="end"
        >
            <AppButton
                isIconOnly
                startIconName="ArrowRight"
                label="View"
                level="tertiary"
                href={`documents/${id}`}
                size="sm"
            />
            <Tooltip
                isInteractive={!isViewOnly}
                isDisabled={!isViewOnly}
                preferredSide="right"
                text="You only have access to view this document. You cannot add this document to your Security Review."
            >
                <Button
                    isIconOnly
                    cosmosUseWithCaution_isDisabled={isViewOnly}
                    startIconName="Plus"
                    label="Add"
                    level="tertiary"
                    size="sm"
                />
            </Tooltip>
        </Stack>
    );
};
