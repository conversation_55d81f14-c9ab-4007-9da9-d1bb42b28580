import { isNil } from 'lodash-es';
import { Banner } from '@cosmos/components/banner';
import { Button } from '@cosmos/components/button';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { observer } from '@globals/mobx';
import { sharedVendorAccessRequestBannerModel } from '../models/vendor-access-request-banner.model';

export const VendorAccessRequestBanner = observer(
    (): React.JSX.Element | null => {
        const { isBannerConfigLoading, bannerConfig } =
            sharedVendorAccessRequestBannerModel;

        if (isBannerConfigLoading) {
            return <Skeleton barCount={1} />;
        }

        if (isNil(bannerConfig)) {
            return null;
        }

        return (
            <Banner
                title={bannerConfig.title}
                severity={bannerConfig.severity}
                displayMode="full"
                data-id="fW8pB_Od"
                data-testid="VendorAccessRequestBanner"
                body={
                    <Stack direction="column" align="start" gap="4x">
                        <Text colorScheme={bannerConfig.body.colorScheme}>
                            {bannerConfig.body.text}
                        </Text>
                        {bannerConfig.button && (
                            <Button
                                label={bannerConfig.button.label}
                                level={bannerConfig.button.level}
                                isLoading={bannerConfig.button.isLoading}
                                a11yLoadingLabel={
                                    bannerConfig.button.a11yLoadingLabel
                                }
                                startIconName={
                                    bannerConfig.button.startIconName
                                }
                                onClick={bannerConfig.button.onClick}
                            />
                        )}
                    </Stack>
                }
            />
        );
    },
);
