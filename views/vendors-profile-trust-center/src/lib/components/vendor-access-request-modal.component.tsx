import { isEmpty, noop, uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { sharedVendorTrustCenterController } from '@controllers/vendors';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { Form, useFormSubmit } from '@ui/forms';
import { buildVendorAccessRequestFormSchema } from '../schemas/vendor-access-request-form.schema';

interface VendorAccessRequestModalProps {
    onClose: () => void;
}

export const VendorAccessRequestModal = observer(
    ({ onClose }: VendorAccessRequestModalProps): React.JSX.Element | null => {
        const { formRef, triggerSubmit } = useFormSubmit();
        const {
            accessRequestRequirements,
            accessRequestRequirementsUrls,
            hasAccessRequestRequirementsError,
            info,
        } = sharedVendorTrustCenterController;

        const vendorName = info?.name ?? '';

        if (
            hasAccessRequestRequirementsError ||
            isEmpty(accessRequestRequirements)
        ) {
            // Show error snackbar if requirements failed to load
            snackbarController.addSnackbar({
                id: `vendor-access-request-requirements-modal-error-${uniqueId()}`,
                props: {
                    title: t`Unable to load access request requirements`,
                    description: t`We couldn't retrieve the access request form. Please try again or contact support if the problem persists.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            // Close the modal since we can't show the form
            onClose();

            return null;
        }

        const schema = buildVendorAccessRequestFormSchema(
            accessRequestRequirements,
        );

        const description = isEmpty(schema)
            ? t`No additional information is required. You can request access directly.`
            : t`Provide your information so ${vendorName} can review your access request.`;

        return (
            <>
                <Modal.Header
                    title={t`Request access`}
                    closeButtonAriaLabel={t`Close request access modal`}
                    onClose={onClose}
                />
                <Modal.Body>
                    <Stack gap="2xl" direction="column">
                        <Text type="title">{description}</Text>

                        <Form
                            hasExternalSubmitButton
                            formId="vendor-request-access-form"
                            data-id="K7mN9pQx"
                            ref={formRef}
                            schema={schema}
                            onSubmit={noop}
                        />

                        {!isEmpty(accessRequestRequirementsUrls) && (
                            <Stack gap="md">
                                {accessRequestRequirementsUrls.map((url) => (
                                    <AppLink
                                        isExternal
                                        key={url}
                                        size="sm"
                                        href={url}
                                        data-id="jrxOaHZz"
                                    >
                                        {url}
                                    </AppLink>
                                ))}
                            </Stack>
                        )}
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Back`,
                            level: 'tertiary',
                            onClick: onClose,
                        },
                        {
                            label: t`Submit request`,
                            onClick: () => {
                                triggerSubmit();
                            },
                        },
                    ]}
                />
            </>
        );
    },
);
