import {
    VendorDetailsFormComponent,
    VendorDetailsInfoComponent,
} from '@components/vendors-current-add-vendor';
import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedVendorsDetailsController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { type FormValues, useFormSubmit } from '@ui/forms';

interface VendorDetailsCardComponentProps {
    isEditable?: boolean;
}

export const VendorDetailsCardComponent = observer(
    ({
        isEditable = true,
    }: VendorDetailsCardComponentProps): React.JSX.Element => {
        const {
            vendorDetails,
            updateBaseDetails,
            isUpdatingVendor,
            hasErrorVendorUpdate,
        } = sharedVendorsDetailsController;
        const { formRef, triggerSubmit } = useFormSubmit();

        const handleOnSubmit = (values: FormValues) => {
            vendorDetails && updateBaseDetails(vendorDetails.id, values);
        };

        return (
            <ViewEditCardComponent
                title={t`Vendor details`}
                data-testid="VendorDetailsCardComponent"
                data-id="zsFpkwHU"
                isMutationPending={isUpdatingVendor}
                hasMutationError={hasErrorVendorUpdate}
                readOnlyComponent={
                    <VendorDetailsInfoComponent state={vendorDetails} />
                }
                editComponent={
                    isEditable ? (
                        <VendorDetailsFormComponent
                            formId={`vendor-details-form`}
                            handleOnSubmit={handleOnSubmit}
                            ref={formRef}
                        />
                    ) : null
                }
                onSave={triggerSubmit}
            />
        );
    },
);
