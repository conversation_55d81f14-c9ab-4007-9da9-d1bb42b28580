import { isNil } from 'lodash-es';
import React from 'react';
import { z } from 'zod';
import { createNumericOptionsWithDash } from '@controllers/risk';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type {
    CustomFieldsSubmissionResponseDto,
    RiskResponseDto,
    RiskSettingsResponseDto,
    RoleEnum,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { convertToISO8601String } from '@helpers/date-time';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import type { CustomFieldRenderProps, FormSchema } from '@ui/forms';
import { CategoryFieldWithAddButton } from '../components/forms/risk-category-field-with-add-button.component';
import { mapUserToOption } from './risk-user-form-schemas.helper';

/**
 * Form limits constants for risk forms.
 */
export const RISK_FORM_LIMITS = {
    TITLE_MAX_LENGTH: 191,
    DESCRIPTION_MAX_LENGTH: 30000,
    TREATMENT_DETAILS_MAX_LENGTH: 30000,
    FILE_MAX_SIZE: 25 * 1024 * 1024, // 25MB
} as const;

/**
 * Generate form schema for assessment section.
 */
export const getAssessmentFormSchema = (
    riskDetails: RiskResponseDto | null,
    riskSettings: RiskSettingsResponseDto | null,
    customFields?: Partial<CustomFieldsSubmissionResponseDto>[],
): FormSchema => {
    const impactOptions = riskSettings?.impact
        ? createNumericOptionsWithDash(riskSettings.impact)
        : [];
    const likelihoodOptions = riskSettings?.likelihood
        ? createNumericOptionsWithDash(riskSettings.likelihood)
        : [];

    const defaultImpact = impactOptions.find((option) => {
        if (isNil(riskDetails?.impact)) {
            return option.value === 'null';
        }

        return option.value === String(riskDetails.impact);
    });

    const defaultLikelihood = likelihoodOptions.find((option) => {
        if (isNil(riskDetails?.likelihood)) {
            return option.value === 'null';
        }

        return option.value === String(riskDetails.likelihood);
    });

    const baseSchema: FormSchema = {
        impact: {
            type: 'select',
            label: t`Inherent impact`,
            placeholder: t`Select impact`,
            options: impactOptions,
            initialValue: defaultImpact,
            validator: z
                .object({
                    id: z.string(),
                    label: z.string(),
                    value: z.string(),
                })
                .refine(
                    (val) => {
                        if (val.value === 'null') {
                            return true;
                        }
                        const numValue = Number(val.value);

                        return (
                            !isNaN(numValue) &&
                            numValue >= 1 &&
                            numValue <= (riskSettings?.impact ?? 5)
                        );
                    },
                    {
                        message: t`Please select a valid impact value`,
                    },
                ),
        },
        likelihood: {
            type: 'select',
            label: t`Inherent Likelihood`,
            placeholder: t`Select likelihood`,
            options: likelihoodOptions,
            initialValue: defaultLikelihood,
            validator: z
                .object({
                    id: z.string(),
                    label: z.string(),
                    value: z.string(),
                })
                .refine(
                    (val) => {
                        if (val.value === 'null') {
                            return true;
                        }
                        const numValue = Number(val.value);

                        return (
                            !isNaN(numValue) &&
                            numValue >= 1 &&
                            numValue <= (riskSettings?.likelihood ?? 5)
                        );
                    },
                    {
                        message: t`Please select a valid likelihood value`,
                    },
                ),
        },
    };

    const customFieldsSchema = customFields
        ? sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
              customFields,
          )
        : {};

    return {
        ...baseSchema,
        ...customFieldsSchema,
    };
};

const renderCategoryField = (
    fieldProps: CustomFieldRenderProps,
): React.JSX.Element => {
    return React.createElement(CategoryFieldWithAddButton, fieldProps);
};

/**
 * Generate form schema for details section.
 */
export const getDetailsFormSchema = (
    riskDetails: RiskResponseDto | null,
    customFields?: CustomFieldsSubmissionResponseDto[],
): FormSchema => {
    const titleMaxLength = RISK_FORM_LIMITS.TITLE_MAX_LENGTH;
    const descriptionMaxLength = RISK_FORM_LIMITS.DESCRIPTION_MAX_LENGTH;

    const baseSchema = {
        title: {
            type: 'text',
            label: t`Title`,
            initialValue: riskDetails?.title ?? '',
            validator: z
                .string()
                .min(1, t`Title is required`)
                .max(
                    titleMaxLength,
                    t`Title must be ${titleMaxLength} characters or less`,
                ),
        },

        description: {
            type: 'textarea',
            label: t`Description`,
            placeholder: t`Enter risk description`,
            initialValue: riskDetails?.description ?? '',
            validator: riskDetails?.id
                ? z
                      .string()
                      .max(
                          descriptionMaxLength,
                          t`Description must be ${descriptionMaxLength} characters or less`,
                      )
                      .optional()
                : z.string().min(1, t`Description is required`),
        },
        categories: {
            type: 'custom',
            label: t`Category`,
            render: renderCategoryField,
            isOptional: true,
            initialValue: () =>
                riskDetails?.categories
                    ? riskDetails.categories.map((category) => ({
                          id: String(category.id),
                          label: category.name,
                          value: String(category.id),
                      }))
                    : [],
        },
    };

    const customFieldsSchema = customFields
        ? sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
              customFields,
          )
        : {};

    return {
        ...baseSchema,
        ...customFieldsSchema,
    } as FormSchema;
};

export const getTreatmentOptions = (): {
    id: string;
    label: string;
    value: string;
}[] => [
    { id: 'UNTREATED', label: t`Untreated`, value: 'UNTREATED' },
    { id: 'MITIGATE', label: t`Mitigate`, value: 'MITIGATE' },
    { id: 'ACCEPT', label: t`Accept`, value: 'ACCEPT' },
    { id: 'TRANSFER', label: t`Transfer`, value: 'TRANSFER' },
    { id: 'AVOID', label: t`Avoid`, value: 'AVOID' },
];

export const getTreatmentFormSchema = (
    riskDetails: RiskResponseDto | null,
    riskSettings: RiskSettingsResponseDto | null,
    customFields?: CustomFieldsSubmissionResponseDto[],
    usersList: {
        id: number;
        firstName: string;
        lastName: string;
        avatarUrl?: string | null;
    }[] = [],
    hasNextPage = false,
    isLoading = false,
    isFetching = false,
    onFetchUsers?: ({
        search,
        increasePage,
        ...query
    }: { search?: string; increasePage?: boolean } & Omit<
        {
            page?: number;
            limit?: number;
            q?: string;
            'roles[]'?: RoleEnum[];
            'excludeUserIds[]'?: number[] | null;
            'excludeRoles[]'?: RoleEnum[] | null;
            'includeUserIds[]'?: number[] | null;
            excludeReadOnlyUsers?: boolean | null;
            includeSupportUser?: boolean | null;
            withAllUsers?: boolean | null;
        },
        'roles[]' | 'excludeUserIds[]' | 'excludeRoles[]' | 'includeUserIds[]'
    > & {
            roles?: RoleEnum[];
            excludeUserIds?: number[];
            excludeRoles?: RoleEnum[];
            includeUserIds?: number[];
        }) => void,
): FormSchema => {
    const treatmentDetailsMaxLength =
        RISK_FORM_LIMITS.TREATMENT_DETAILS_MAX_LENGTH;

    const treatmentOptions = getTreatmentOptions();
    const impactOptions = riskSettings?.impact
        ? createNumericOptionsWithDash(riskSettings.impact)
        : [];
    const likelihoodOptions = riskSettings?.likelihood
        ? createNumericOptionsWithDash(riskSettings.likelihood)
        : [];

    const defaultTreatmentPlan = treatmentOptions.find(
        (option) => option.value === riskDetails?.treatmentPlan,
    );
    const defaultResidualImpact = impactOptions.find((option) => {
        if (isNil(riskDetails?.residualImpact)) {
            return option.value === 'null';
        }

        return option.value === String(riskDetails.residualImpact);
    });
    const defaultResidualLikelihood = likelihoodOptions.find((option) => {
        if (isNil(riskDetails?.residualLikelihood)) {
            return option.value === 'null';
        }

        return option.value === String(riskDetails.residualLikelihood);
    });

    const baseSchema = {
        treatmentPlan: {
            type: 'select',
            label: t`Treatment option`,
            placeholder: t`Select treatment plan`,
            options: treatmentOptions,
            initialValue: defaultTreatmentPlan,
            isOptional: true,
            validator: z
                .object({
                    id: z.string(),
                    label: z.string(),
                    value: z.string(),
                })
                .optional(),
        },
        treatmentDetails: {
            type: 'textarea',
            label: t`Treatment plan`,
            placeholder: t`Enter treatment details`,
            initialValue: riskDetails?.treatmentDetails ?? '',
            validator: z
                .string()
                .max(
                    treatmentDetailsMaxLength,
                    t`Treatment details must be ${treatmentDetailsMaxLength} characters or less`,
                )
                .optional(),
            shownIf: {
                fieldName: 'treatmentPlan.value',
                operator: 'notEquals',
                value: 'UNTREATED',
            },
        },
        residualImpact: {
            type: 'select',
            label: t`Residual impact`,
            placeholder: t`Select residual impact`,
            options: impactOptions,
            initialValue: defaultResidualImpact,
            isOptional: true,
            validator: z
                .object({
                    id: z.string(),
                    label: z.string(),
                    value: z.string(),
                })
                .refine(
                    (val) => {
                        if (val.value === 'null') {
                            return true;
                        }
                        const numValue = Number(val.value);

                        return (
                            !isNaN(numValue) &&
                            numValue >= 1 &&
                            numValue <= (riskSettings?.impact ?? 5)
                        );
                    },
                    {
                        message: t`Please select a valid residual impact value`,
                    },
                ),
            shownIf: {
                operator: 'or',
                conditions: [
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'TRANSFER',
                    },
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'MITIGATE',
                    },
                ],
            },
        },
        residualLikelihood: {
            type: 'select',
            label: t`Residual likelihood`,
            placeholder: t`Select residual likelihood`,
            options: likelihoodOptions,
            initialValue: defaultResidualLikelihood,
            isOptional: true,
            validator: z
                .object({
                    id: z.string(),
                    label: z.string(),
                    value: z.string(),
                })
                .refine(
                    (val) => {
                        if (val.value === 'null') {
                            return true;
                        }
                        const numValue = Number(val.value);

                        return (
                            !isNaN(numValue) &&
                            numValue >= 1 &&
                            numValue <= (riskSettings?.likelihood ?? 5)
                        );
                    },
                    {
                        message: t`Please select a valid residual likelihood value`,
                    },
                ),
            shownIf: {
                operator: 'or',
                conditions: [
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'TRANSFER',
                    },
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'MITIGATE',
                    },
                ],
            },
        },
        treatmentDueDate: {
            type: 'date',
            label: t`Anticipated completed date`,
            initialValue: riskDetails?.anticipatedCompletionDate
                ? (convertToISO8601String(
                      new Date(riskDetails.anticipatedCompletionDate),
                  ) as TDateISODate)
                : undefined,
            isOptional: true,
            validator: z
                .string()
                .optional()
                .refine(
                    (val) => {
                        if (!val) {
                            return true;
                        }
                        // Basic date format validation (YYYY-MM-DD)
                        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;

                        if (!dateRegex.test(val)) {
                            return false;
                        }

                        const date = new Date(val);

                        return !isNaN(date.getTime());
                    },
                    {
                        message: t`Please enter a valid date`,
                    },
                ),
            shownIf: {
                operator: 'or',
                conditions: [
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'TRANSFER',
                    },
                    {
                        fieldName: 'treatmentPlan.value',
                        operator: 'equals',
                        value: 'MITIGATE',
                    },
                ],
            },
        },
        treatmentCompletionDate: {
            type: 'date',
            label: t`Completed date`,
            initialValue: riskDetails?.completionDate
                ? (convertToISO8601String(
                      new Date(riskDetails.completionDate),
                  ) as TDateISODate)
                : undefined,
            isOptional: true,
            validator: z
                .string()
                .optional()
                .refine(
                    (val) => {
                        if (!val) {
                            return true;
                        }

                        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;

                        if (!dateRegex.test(val)) {
                            return false;
                        }

                        const date = new Date(val);

                        return !isNaN(date.getTime());
                    },
                    {
                        message: t`Please enter a valid date`,
                    },
                ),
            shownIf: {
                fieldName: 'treatmentPlan.value',
                operator: 'notEquals',
                value: 'UNTREATED',
            },
        },
        reviewers: {
            type: 'combobox',
            label: t`Reviewers`,
            placeholder: t`Select reviewers`,
            loaderLabel: t`Loading users...`,
            options: usersList.map(mapUserToOption),
            initialValue: (riskDetails?.reviewers ?? []).map(mapUserToOption),
            isOptional: false,
            isMultiSelect: true,
            hasMore: hasNextPage,
            isLoading: isLoading || isFetching,
            onFetchOptions: onFetchUsers,
            validator: z
                .array(
                    z.object({
                        id: z.string(),
                        label: z.string(),
                        value: z.string(),
                        avatar: z
                            .object({
                                imgSrc: z.string().nullable().optional(),
                            })
                            .optional(),
                    }),
                )
                .min(1, t`At least one reviewer is required`),
            shownIf: {
                fieldName: 'treatmentPlan.value',
                operator: 'notEquals',
                value: 'UNTREATED',
            },
        },
    };

    const customFieldsSchema = customFields
        ? sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
              customFields,
          )
        : {};

    return {
        ...baseSchema,
        ...customFieldsSchema,
    } as FormSchema;
};
