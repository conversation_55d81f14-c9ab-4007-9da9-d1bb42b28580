import { capitalize, isNil } from 'lodash-es';
import { sharedRiskCustomFieldsSubmissionsController } from '@controllers/risk';
import { Box } from '@cosmos/components/box';
import { Icon } from '@cosmos/components/icon';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { RiskScore } from '@cosmos-lab/components/risk-score';
import type {
    RiskResponseDto,
    RiskSettingsResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import { calculateRiskMetrics } from '../../helpers/risk-assessment.helper';

interface AssessmentReadOnlyContentProps {
    riskDetails: RiskResponseDto | null;
    riskSettings: RiskSettingsResponseDto | null;
}

export const AssessmentReadOnlyContent = observer(
    ({ riskDetails, riskSettings }: AssessmentReadOnlyContentProps) => {
        const {
            riskCustomFieldsAssessmentSection,
            isLoading: isCustomFieldsLoading,
        } = sharedRiskCustomFieldsSubmissionsController;

        const riskMetrics = calculateRiskMetrics(
            riskDetails?.score ?? 0,
            riskSettings,
        );

        return (
            <Stack direction="column" align="start" gap="md" data-id="hAJmIBiD">
                <Stack direction="row" gap="2xl">
                    <KeyValuePair
                        type="REACT_NODE"
                        label={t`Inherent impact`}
                        value={
                            isNil(riskDetails?.impact) ? (
                                <EmptyValue label="Inherent impact" />
                            ) : (
                                <Text>{riskDetails.impact.toString()}</Text>
                            )
                        }
                    />
                    <KeyValuePair
                        type="REACT_NODE"
                        label={t`Inherent likelihood`}
                        value={
                            isNil(riskDetails?.likelihood) ? (
                                <EmptyValue label="Inherent likelihood" />
                            ) : (
                                <Text>{riskDetails.likelihood.toString()}</Text>
                            )
                        }
                    />
                </Stack>
                <KeyValuePair
                    type="REACT_NODE"
                    label={t`Inherent score`}
                    value={
                        <RiskScore
                            intensity="strong"
                            severity={riskMetrics.severity}
                            scoreNumber={riskDetails?.score}
                            label={capitalize(riskMetrics.threshold?.name)}
                        />
                    }
                />
                <Box>
                    <Tooltip
                        isInteractive
                        text={t`This value represents the risk score prior to completing any mitigation efforts calculated by the Inherent Impact times the Inherent Likelihood`}
                        data-id="assessment-score-tooltip"
                    >
                        <Stack direction="row" align="center" gap="xs">
                            <Text type="title" size="100" colorScheme="neutral">
                                {t`What does this score mean?`}
                            </Text>
                            <Icon name="Help" colorScheme="neutral" />
                        </Stack>
                    </Tooltip>
                </Box>

                {isCustomFieldsLoading ? (
                    <Skeleton barCount={3} />
                ) : (
                    sharedCustomFieldsManager.renderReadOnlyCustomFields(
                        riskCustomFieldsAssessmentSection?.customFields ?? [],
                    )
                )}
            </Stack>
        );
    },
);
