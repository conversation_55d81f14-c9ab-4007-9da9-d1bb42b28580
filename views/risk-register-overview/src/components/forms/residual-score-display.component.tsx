import { capitalize, isNil } from 'lodash-es';
import { useMemo } from 'react';
import { useWatch } from 'react-hook-form';
import { sharedRiskSettingsController } from '@controllers/risk';
import { RiskScore } from '@cosmos-lab/components/risk-score';
import { observer } from '@globals/mobx';
import { useFormContext } from '@ui/forms';
import { calculateRiskMetrics } from '../../helpers/risk-assessment.helper';
import { calculateRiskScore } from '../../helpers/risk-calculation.helper';
import { transformSelectToNumber } from '../../helpers/risk-data-transformation.helper';

type SelectOption =
    | {
          id: string;
          label: string;
          value: string;
      }
    | null
    | undefined;

export const ResidualScoreDisplay = observer((): React.JSX.Element => {
    const { riskSettings } = sharedRiskSettingsController;
    const { control } = useFormContext();

    const [residualImpact, residualLikelihood] = useWatch({
        name: ['residualImpact', 'residualLikelihood'],
        control,
    }) as [SelectOption, SelectOption];

    const residualScore = useMemo(() => {
        if (isNil(residualImpact) || isNil(residualLikelihood)) {
            return null;
        }

        const impactValue = transformSelectToNumber(residualImpact);
        const likelihoodValue = transformSelectToNumber(residualLikelihood);

        return calculateRiskScore(impactValue, likelihoodValue);
    }, [residualImpact, residualLikelihood]);

    const residualMetrics = useMemo(
        () => calculateRiskMetrics(residualScore ?? 0, riskSettings),
        [residualScore, riskSettings],
    );

    if (isNil(residualScore)) {
        return (
            <RiskScore
                intensity="strong"
                size="md"
                severity="low"
                scoreNumber={null}
                data-id="residual-score-display"
            />
        );
    }

    return (
        <RiskScore
            intensity="strong"
            size="md"
            severity={residualMetrics.severity}
            scoreNumber={residualScore}
            label={capitalize(residualMetrics.threshold?.name)}
            data-id="residual-score-display"
        />
    );
});
