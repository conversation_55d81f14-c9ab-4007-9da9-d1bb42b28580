import { isEmpty } from 'lodash-es';
import type { EventAiSummaryExecutionData } from '@controllers/monitoring';
import type { AiExecutionResponseDto } from '@globals/api-sdk/types';
import { isExecutionData } from './ai-execution-type-guards.helper';

const formatSingleExecutionAsHtml = (
    data: EventAiSummaryExecutionData,
): string => {
    const resourceLabel =
        data.resourceIds.length > 1 ? 'Resources' : 'Resource';

    return `
        <div>
            <dl>
                <dt><strong>Resource:</strong></dt>
                <dd>${data.resource}</dd>
                <dt><strong>Cause:</strong></dt>
                <dd>${data.cause.title}</dd>
                <dt><strong>Failed ${resourceLabel}:</strong></dt>
                <dd>
                    ${data.resourceIds.length}
                    <ul>
                        <li>${data.cause.explanation}</li>
                    </ul>
                </dd>
            </dl>
        </div>`;
};

export const formatAiExecutionsDataJsonToHtml = (
    executions: AiExecutionResponseDto[],
): string => {
    const validExecutions = executions
        .filter((execution) => isExecutionData(execution.data))
        .map(
            (execution) =>
                execution.data as unknown as EventAiSummaryExecutionData,
        );

    if (isEmpty(validExecutions)) {
        return '';
    }

    return validExecutions.map(formatSingleExecutionAsHtml).join('<br>');
};
