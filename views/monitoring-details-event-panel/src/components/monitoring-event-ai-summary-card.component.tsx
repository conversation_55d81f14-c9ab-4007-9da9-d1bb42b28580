import { sharedEventsDetailsController } from '@controllers/events-details';
import { AICard } from '@cosmos-lab/components/ai-card';
import { observer } from '@globals/mobx';
import { sharedMonitoringEventSummaryCardModel } from '../models/monitoring-event-summary-card.model';

export const MonitoringEventAISummaryCard = observer(
    (): React.JSX.Element | null => {
        const { title, renderContent } = sharedMonitoringEventSummaryCardModel;

        if (!sharedEventsDetailsController.shouldShowAiSummary) {
            return null;
        }

        return (
            <AICard
                title={title}
                data-id="monitoring-event-summary-card"
                body={renderContent()}
            />
        );
    },
);
