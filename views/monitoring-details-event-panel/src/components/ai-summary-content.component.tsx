import { isEmpty } from 'lodash-es';
import { Stack } from '@cosmos/components/stack';
import { AISummaryFooter } from '@cosmos-lab/components/ai-summary-footer';
import { Divider } from '@cosmos-lab/components/divider';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { copyAISummaryToClipboard } from '@helpers/clipboard';
import { AppButton } from '@ui/app-button';
import { sharedMonitoringEventSummaryCardModel } from '../models/monitoring-event-summary-card.model';
import { ExecutionItem } from './execution-item.component';

export const AISummaryContent = observer((): React.JSX.Element => {
    const { executions, canDownload, executionsAsHtml } =
        sharedMonitoringEventSummaryCardModel;

    return (
        <>
            {isEmpty(executions) ? (
                <EmptyValue label={t`No summary data available`} />
            ) : (
                <Stack
                    gap="md"
                    direction="column"
                    data-testid="renderSummaryContent"
                    data-id="KaZK4ayf"
                >
                    {executions.map((execution) => (
                        <ExecutionItem
                            key={execution.id}
                            execution={execution}
                            data-id={`execution-item-${execution.id}`}
                        />
                    ))}
                </Stack>
            )}
            {canDownload && (
                <Stack
                    direction="row"
                    justify="start"
                    align="start"
                    pt="md"
                    data-id="download-summary-stack"
                    data-testid="download-summary-stack"
                >
                    <AppButton
                        label={t`Download summary`}
                        size="sm"
                        data-testid="download-summary-button"
                        data-id="download-summary-button"
                        level="tertiary"
                        startIconName="Download"
                        colorScheme="primary"
                        onClick={() => {
                            sharedMonitoringEventSummaryCardModel.downloadSummary();
                        }}
                    />
                </Stack>
            )}
            <Divider data-id="summary-divider" data-testid="summary-divider" />
            <AISummaryFooter
                data-id="monitoring-event-ai-summary-footer"
                onFeedback={(feedbackStatus) => {
                    sharedMonitoringEventSummaryCardModel.openFeedbackModal(
                        feedbackStatus,
                    );
                }}
                onCopy={() => {
                    copyAISummaryToClipboard(executionsAsHtml ?? null);
                }}
            />
        </>
    );
});
