import { useMemo, useRef } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { routeController } from '@controllers/route';
import type { DatatableRef } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { getAuditorsListTableColumns } from '../constants/audits-details-page.constants';
import { getFilterProps } from '../helpers/audit-details-filter.helper';
import { CustomerRequestBulkActionsModel } from './models/customer-request-bulk-actions.model';

export const AuditDetailsRequestTable = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const {
        customerRequests,
        isLoading,
        total,
        getCustomerRequestList,
        totalUnreadMessages,
    } = sharedCustomerRequestsController;

    const { auditSummaryByIdData } = sharedAuditorController;
    const { customRequestOwners } = sharedAuditHubController;

    const datatableRef = useRef<DatatableRef>(null);

    const { bulkActions, handleRowSelection } = useMemo(
        () => new CustomerRequestBulkActionsModel(datatableRef),
        [],
    );

    const navigateToRequestDetail = (requestId: number) => {
        navigate(
            `${routeController.userPartOfUrl}/compliance/audits/all-audits/${auditSummaryByIdData?.auditorFrameworkId}/requests/${requestId}/overview`,
        );
    };

    return (
        <AppDatatable
            isFullPageTable
            isRowSelectionEnabled
            getRowId={(row) => String(row.id)}
            imperativeHandleRef={datatableRef}
            isLoading={isLoading}
            tableId="datatable-audit-details-requests"
            total={total}
            data={customerRequests}
            columns={getAuditorsListTableColumns()}
            data-testid="AuditDetailsRequestTable"
            data-id="ojxb0h8q"
            bulkActionDropdownItems={bulkActions}
            filterProps={getFilterProps(
                totalUnreadMessages,
                customRequestOwners,
            )}
            tableSearchProps={{
                hideSearch: false,
                placeholder: t`Search`,
                defaultValue: '',
            }}
            filterViewModeProps={{
                props: {
                    selectedOption: 'unpinned',
                    initialSelectedOption: 'unpinned',
                    togglePinnedLabel: t`Pin filters to page`,
                    toggleUnpinnedLabel: t`Move filters to dropdown`,
                },
                viewMode: 'toggleable',
            }}
            emptyStateProps={{
                title: t`No matching requests`,
                description: t`Try adjusting your filters or check your search terms.`,
            }}
            onRowSelection={handleRowSelection}
            onFetchData={getCustomerRequestList}
            onRowClick={({ row }) => {
                navigateToRequestDetail(row.id);
            }}
        />
    );
});
