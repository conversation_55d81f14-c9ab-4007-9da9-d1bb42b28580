import { sharedAuditHubController } from '@controllers/audit-hub';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Skeleton } from '@cosmos/components/skeleton';
import { dimension40x } from '@cosmos/constants/tokens';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getInitials } from '@helpers/formatters';

export const AssignedAuditorsCardBody = observer((): React.JSX.Element => {
    const { auditByIdData, auditByIdIsLoading } = sharedAuditHubController;

    if (auditByIdIsLoading) {
        return (
            <>
                <Skeleton barCount={1} />
                <Skeleton barCount={1} />
            </>
        );
    }

    return (
        <Box
            maxHeight={dimension40x}
            overflowY="auto"
            data-testid="ScrollableAuditorsContainer"
            data-id="Y5NfT31a"
        >
            <StackedList
                id="assigned-auditors-list"
                data-testid="AssignedAuditorsStackedList"
            >
                {(auditByIdData?.auditors ?? []).map((auditor) => {
                    const fullName = `${auditor.firstName} ${auditor.lastName}`;
                    const initials = getInitials(fullName);

                    return (
                        <StackedListItem
                            key={auditor.id}
                            data-id={`auditor-${auditor.id}`}
                            primaryColumn={
                                <AvatarIdentity
                                    primaryLabel={fullName}
                                    secondaryLabel={auditor.email}
                                    fallbackText={initials}
                                    imgSrc={auditor.avatarUrl || undefined}
                                    size="sm"
                                />
                            }
                            action={
                                <Button
                                    isIconOnly
                                    colorScheme="danger"
                                    label={t`Unlink auditor`}
                                    level="tertiary"
                                    size="sm"
                                    startIconName="Trash"
                                />
                            }
                        />
                    );
                })}
            </StackedList>
        </Box>
    );
});
