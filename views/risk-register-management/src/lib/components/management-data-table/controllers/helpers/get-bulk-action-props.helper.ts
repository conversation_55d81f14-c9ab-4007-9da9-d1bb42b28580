import { isNil } from 'lodash-es';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { SchemaDropdownItems } from '@cosmos/components/schema-dropdown';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { sharedManagementDataTableBulkController } from '../management-data-table-bulk.controller';

export function getBulkActionProps(): DatatableProps<RiskWithCustomFieldsResponseDto>['bulkActionDropdownItems'] {
    return [
        {
            id: 'add-risk-owners-bulk-action',
            actionType: 'button',
            typeProps: {
                label: t`Add risk owners`,
                level: 'tertiary',
                onClick: (): void => {
                    sharedManagementDataTableBulkController.openRiskOwnerModal();
                },
            },
        },
        {
            id: 'assign-risk-category-bulk-action',
            actionType: 'button',
            typeProps: {
                'data-id': 'async-datatable-bulk-actions-button',
                label: t`Assign risk category`,
                level: 'tertiary',
                onClick: (): void => {
                    sharedManagementDataTableBulkController.openRiskCategoriesModal();
                },
            },
        },
        {
            id: 'update-status-bulk-action-dropdown',
            actionType: 'dropdown',
            typeProps: {
                'data-id': 'async-datatable-bulk-actions',
                label: t`Update status`,
                level: 'tertiary',
                items: [
                    sharedManagementDataTableBulkController.shouldDisplayActivateAction
                        ? {
                              id: 'activate-bulk-action',
                              label: t`Activate`,
                              onSelect:
                                  sharedManagementDataTableBulkController.handleSetRiskStatusAsActive,
                          }
                        : null,
                    sharedManagementDataTableBulkController.shouldDisplayArchiveAction
                        ? {
                              id: 'archive-bulk-action',
                              label: t`Archive`,
                              onSelect:
                                  sharedManagementDataTableBulkController.handleSetRiskStatusAsArchived,
                          }
                        : null,
                    sharedManagementDataTableBulkController.shouldDisplayCloseAction
                        ? {
                              id: 'close-bulk-action',
                              label: t`Close`,
                              onSelect:
                                  sharedManagementDataTableBulkController.handleSetRiskStatusAsClosed,
                          }
                        : null,
                    sharedFeatureAccessModel.isReleaseClosedStatusForRiskEnabled
                        ? {
                              id: 'delete-bulk-action',
                              label: t`Delete`,
                              onSelect:
                                  sharedManagementDataTableBulkController.handleDeleteRisks,
                              colorScheme: 'critical',
                          }
                        : null,
                ].filter((item) => !isNil(item)) as SchemaDropdownItems,
            },
        },
    ];
}
