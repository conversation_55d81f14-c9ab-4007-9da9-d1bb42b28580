import { sharedRiskManagementMutationController } from '@controllers/risk';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, when } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { managementDatatableController } from '../management-data-table.controller';
import { ManagementDataTableCellController } from '../management-data-table-cell.controller';

export function getRowActionProps(): DatatableProps<RiskWithCustomFieldsResponseDto>['rowActionsProps'] {
    return {
        type: 'dropdown',
        getRowActions: action((row: RiskWithCustomFieldsResponseDto) => {
            const managementDataTableCellController =
                new ManagementDataTableCellController(row);

            const actions: SchemaDropdownItemData[] = [
                {
                    id: 'add-risk-owners-row-action',
                    label: t`Add risk owners`,
                    onSelect:
                        managementDataTableCellController.openRiskOwnerModal,
                },
                {
                    id: 'assign-risk-category-row-action',
                    label: t`Assign risk category`,
                    onSelect:
                        managementDataTableCellController.openRiskCategoriesModal,
                },
            ];

            if (row.status !== 'ACTIVE') {
                actions.push({
                    id: 'activate-row-action',
                    label: t`Activate`,
                    onSelect: () => {
                        openConfirmationModal({
                            title: t`Activate risk?`,
                            body: t`Are you sure you want to activate this risk?`,
                            confirmText: t`Activate`,
                            cancelText: t`Cancel`,
                            type: 'primary',
                            onConfirm: () => {
                                managementDataTableCellController.updateRiskAndUpdateDataTable(
                                    {
                                        status: 'ACTIVE',
                                    },
                                );

                                when(
                                    () =>
                                        !managementDataTableCellController.isMutationPending,
                                    () => {
                                        closeConfirmationModal();
                                    },
                                );
                            },
                            onCancel: closeConfirmationModal,
                            isLoading: () =>
                                managementDataTableCellController.isMutationPending,
                        });
                    },
                });
            }

            if (row.status !== 'ARCHIVED') {
                actions.push({
                    id: 'archive-row-action',
                    label: t`Archive`,
                    onSelect: () => {
                        openConfirmationModal({
                            title: t`Archive risk?`,
                            body: t`Are you sure you want to archive this risk?`,
                            confirmText: t`Archive`,
                            cancelText: t`Cancel`,
                            type: 'primary',
                            onConfirm: () => {
                                managementDataTableCellController.updateRiskAndUpdateDataTable(
                                    {
                                        status: 'ARCHIVED',
                                    },
                                );

                                when(
                                    () =>
                                        !managementDataTableCellController.isMutationPending,
                                    () => {
                                        closeConfirmationModal();
                                    },
                                );
                            },
                            onCancel: closeConfirmationModal,
                            isLoading: () =>
                                managementDataTableCellController.isMutationPending,
                        });
                    },
                });
            }

            if (row.status !== 'CLOSED') {
                actions.push({
                    id: 'close-row-action',
                    label: t`Close`,
                    onSelect: () => {
                        openConfirmationModal({
                            title: t`Close risk?`,
                            body: t`Are you sure you want to close this risk?`,
                            confirmText: t`Close`,
                            cancelText: t`Cancel`,
                            type: 'primary',
                            onConfirm: () => {
                                managementDataTableCellController.updateRiskAndUpdateDataTable(
                                    {
                                        status: 'CLOSED',
                                    },
                                );

                                when(
                                    () =>
                                        !managementDataTableCellController.isMutationPending,
                                    () => {
                                        closeConfirmationModal();
                                    },
                                );
                            },
                            onCancel: closeConfirmationModal,
                            isLoading: () =>
                                managementDataTableCellController.isMutationPending,
                        });
                    },
                });
            }

            if (sharedFeatureAccessModel.isReleaseClosedStatusForRiskEnabled) {
                actions.push({
                    id: 'delete-row-action',
                    label: t`Delete`,
                    colorScheme: 'critical',
                    onSelect: () => {
                        openConfirmationModal({
                            title: t`Delete risk?`,
                            body: t`Are you sure you want to delete this risk?`,
                            confirmText: t`Delete`,
                            cancelText: t`Cancel`,
                            type: 'danger',
                            onConfirm: () => {
                                sharedRiskManagementMutationController.deleteRisk(
                                    row.riskId,
                                );

                                when(
                                    () =>
                                        !sharedRiskManagementMutationController.isDeleteRiskPending,
                                    () => {
                                        managementDatatableController.invalidate();

                                        closeConfirmationModal();
                                    },
                                );
                            },
                            onCancel: closeConfirmationModal,
                            isLoading: () =>
                                sharedRiskManagementMutationController.isDeleteRiskPending,
                        });
                    },
                });
            }

            return actions;
        }),
    };
}
