import { isEmpty } from 'lodash-es';
import type { ExternalDatatableController } from '@components/app-datatable';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { sharedRiskManagementController } from '@controllers/risk';
import { routeController } from '@controllers/route';
import type {
    DatatableProps,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import type { ListBoxItems } from '@cosmos/components/list-box';
import type { riskManagementControllerGetRisksListOptions } from '@globals/api-sdk/queries';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { getBulkActionProps } from './helpers/get-bulk-action-props.helper';
import { getColumns } from './helpers/get-columns.helper';
import { getFilterProps } from './helpers/get-filter-props.helper';
import { getRowActionProps } from './helpers/get-row-action-props.helper';
import { getTableActions } from './helpers/get-table-actions.helper';

export type RiskManagementDatatableQuery = NonNullable<
    Required<
        Parameters<typeof riskManagementControllerGetRisksListOptions>
    >[0]['query']
>;

class ManagementDatatableController
    implements
        ExternalDatatableController<
            RiskWithCustomFieldsResponseDto,
            RiskManagementDatatableQuery
        >
{
    tableId = 'datatable-risk-management';

    selectedRiskIds: string[] = [];

    appliedFilters: RiskManagementDatatableQuery = {};

    constructor() {
        makeAutoObservable(this);
    }

    get selectedRisks(): RiskWithCustomFieldsResponseDto[] {
        return sharedRiskManagementController.risks.filter((risk) => {
            return this.selectedRiskIds.includes(risk.riskId);
        });
    }

    load = (query: RiskManagementDatatableQuery): void => {
        this.appliedFilters = Object.fromEntries(
            Object.entries(query).filter(([, value]) => !isEmpty(value)),
        );

        sharedRiskManagementController.riskManagementListQuery.load({ query });
    };

    invalidate = (): void => {
        sharedRiskManagementController.riskManagementListQuery.invalidate();
    };

    get data(): RiskWithCustomFieldsResponseDto[] {
        return sharedRiskManagementController.risks;
    }

    get isLoading(): boolean {
        return sharedRiskManagementController.isLoading;
    }

    get total(): number {
        return sharedRiskManagementController.total;
    }

    get isFirstUse(): boolean {
        return sharedRiskManagementController.statistics?.totalRisks === 0;
    }

    loadOwnersIdsOptions = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            sharedRiskManagementController.risksOwnersLoadNextPage();
        } else {
            sharedRiskManagementController.risksOwnersLoadFirstPage(search);
        }
    };

    get ownersIdsOptions(): ListBoxItems {
        return sharedRiskManagementController.risksOwners.map((owner) => {
            return {
                id: owner.id.toString(),
                label: getFullName(owner.firstName, owner.lastName),
                value: owner.id.toString(),
            };
        });
    }

    loadCategoriesIdsOptions = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            sharedRiskManagementController.riskCategoriesLoadNextPage();
        } else {
            sharedRiskManagementController.riskCategoriesLoadFirstPage(search);
        }
    };

    get categoriesIdsOptions(): ListBoxItems {
        return sharedRiskManagementController.riskCategories.map((category) => {
            return {
                id: category.id.toString(),
                label: category.name,
                value: category.id.toString(),
            };
        });
    }

    get rowActionsProps(): DatatableProps<RiskWithCustomFieldsResponseDto>['rowActionsProps'] {
        return getRowActionProps();
    }

    get filterProps(): DatatableProps<RiskWithCustomFieldsResponseDto>['filterProps'] {
        return getFilterProps();
    }

    get columns(): DatatableProps<RiskWithCustomFieldsResponseDto>['columns'] {
        return getColumns();
    }

    get tableActions(): DatatableProps<RiskWithCustomFieldsResponseDto>['tableActions'] {
        return getTableActions();
    }

    onRowClick: DatatableProps<RiskWithCustomFieldsResponseDto>['onRowClick'] =
        ({ row }) => {
            sharedProgrammaticNavigationController.navigateTo(
                `${routeController.userPartOfUrl}/risk/management/registers/${routeController.currentParams.registerId}/register-risks/${row.riskId}/overview`,
            );
        };

    emptyStateProps: DatatableProps<RiskWithCustomFieldsResponseDto>['emptyStateProps'] =
        {
            title: t`No results found`,
            description: t`Try adjusting your search terms or filters.`,
        };

    tableSearchProps: DatatableProps<RiskWithCustomFieldsResponseDto>['tableSearchProps'] =
        {
            placeholder: t`Search by name, description, control code, or requirement`,
            debounceDelay: 1000,
            defaultValue: '',
        };

    isRowSelectionEnabled: DatatableProps<RiskWithCustomFieldsResponseDto>['isRowSelectionEnabled'] =
        () => {
            return true;
        };

    getRowId: DatatableProps<RiskWithCustomFieldsResponseDto>['getRowId'] = (
        row,
    ) => row.riskId;

    onRowSelection: DatatableProps<RiskWithCustomFieldsResponseDto>['onRowSelection'] =
        ({ selectedRows }: DatatableRowSelectionState) => {
            this.selectedRiskIds = Object.keys(selectedRows);
        };

    get bulkActionDropdownItems(): DatatableProps<RiskWithCustomFieldsResponseDto>['bulkActionDropdownItems'] {
        return getBulkActionProps();
    }
}

export const managementDatatableController =
    new ManagementDatatableController();
