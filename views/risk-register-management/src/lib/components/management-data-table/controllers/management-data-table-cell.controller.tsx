import type { UseComboboxInputValueChange } from 'downshift';
import { modalController } from '@controllers/modal';
import {
    sharedRiskCategoriesController,
    sharedRiskPartiallyMutationController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { UsersInfiniteController } from '@controllers/users';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    criticalBackgroundStrongInitial,
    successBackgroundModerate,
} from '@cosmos/constants/tokens';
import type { AvatarStackProps } from '@cosmos-lab/components/avatar-stack';
import type { DataDonutSliceData } from '@cosmos-lab/components/data-donut';
import type {
    RiskRequestOptionalDto,
    RiskWithCustomFieldsResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, when } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';
import type { FormValues } from '@ui/forms';
import {
    DEFAULT_SCORING_OPTION,
    scoringValueOptions,
} from '../../../helpers/risk-scoring.helper';
import { AddRiskCategoriesModalContent } from '../components/add-risk-categories-modal-content';
import { AssignRiskOwnerModalContent } from '../components/assign-risk-owner-modal-content';
import { managementDatatableController } from './management-data-table.controller';

export const RISK_OWNERS_MODAL_ID = 'risk-owners-modal';
export const RISK_CATEGORIES_MODAL_ID = 'risk-categories-modal';

export class ManagementDataTableCellController {
    risk: RiskWithCustomFieldsResponseDto;
    usersInfiniteController: UsersInfiniteController;

    constructor(risk: RiskWithCustomFieldsResponseDto) {
        makeAutoObservable(this);

        this.risk = risk;

        this.usersInfiniteController = new UsersInfiniteController();
    }

    /**
     * MitigatingControlsCell.
     */

    get readyControls(): number {
        const { controls } = this.risk;

        return controls.filter((control) => control.isReady).length;
    }

    get notReadyControls(): number {
        const { controls } = this.risk;

        return controls.filter((control) => !control.isReady).length;
    }

    get mitigatingControlsCellValues(): DataDonutSliceData[] {
        return [
            {
                label: t`Ready`,
                value: this.readyControls,
                color: successBackgroundModerate,
            },
            {
                label: t`Not ready`,
                value: this.notReadyControls,
                color: criticalBackgroundStrongInitial,
            },
        ];
    }

    /**
     * RiskTreatmentCell.
     */

    get treatmentOptions(): ListBoxItemData[] {
        return [
            {
                id: 'untreated-option',
                label: t`Untreated`,
                value: 'UNTREATED',
            },
            {
                id: 'accepted-option',
                label: t`Accept`,
                value: 'ACCEPT',
            },
            {
                id: 'transferred-option',
                label: t`Transfer`,
                value: 'TRANSFER',
            },
            {
                id: 'avoided-option',
                label: t`Avoid`,
                value: 'AVOID',
            },
            {
                id: 'mitigated-option',
                label: t`Mitigate`,
                value: 'MITIGATE',
            },
        ];
    }

    get treatmentSelectedOption(): ListBoxItemData | undefined {
        const { treatmentPlan } = this.risk;

        return this.treatmentOptions.find(
            (option) => option.value === treatmentPlan,
        );
    }

    get isMutationPending(): boolean {
        return sharedRiskPartiallyMutationController.isPending;
    }

    updateRiskAndUpdateDataTable = (
        requestBody: RiskRequestOptionalDto,
    ): void => {
        sharedRiskPartiallyMutationController.updateRiskPartially(
            this.risk.riskId,
            requestBody,
        );

        when(
            () => !this.isMutationPending,
            () => {
                managementDatatableController.invalidate();
            },
        );
    };

    handleTreatmentChange = (option: ListBoxItemData): void => {
        this.updateRiskAndUpdateDataTable({
            treatmentPlan:
                option.value as RiskRequestOptionalDto['treatmentPlan'],
        });
    };

    /**
     * InherentImpactCell.
     */

    get inherentImpactOptions(): ListBoxItemData[] {
        const { riskSettings } = sharedRiskSettingsController;

        return scoringValueOptions(riskSettings?.impact ?? 0);
    }

    get inherentImpactValue(): ListBoxItemData {
        const impact = this.risk.impact ?? 0;

        return (
            this.inherentImpactOptions.find(
                (option) => option.value === String(impact),
            ) ?? DEFAULT_SCORING_OPTION
        );
    }

    handleInherentImpactChange = (option: ListBoxItemData): void => {
        const impact = option.value ? Number(option.value) : 0;

        const { likelihood, residualImpact, residualLikelihood } = this.risk;

        const score = impact * (likelihood ?? 0);
        const residualScore = (residualImpact ?? 0) * (residualLikelihood ?? 0);

        this.updateRiskAndUpdateDataTable({
            likelihood,
            residualImpact,
            residualLikelihood,
            impact: impact === 0 ? null : impact,
            score: score === 0 ? null : score,
            residualScore: residualScore === 0 ? null : residualScore,
        });
    };

    /**
     * InherentLikelihoodCell.
     */

    get inherentLikelihoodOptions(): ListBoxItemData[] {
        const { riskSettings } = sharedRiskSettingsController;

        return scoringValueOptions(riskSettings?.likelihood ?? 0);
    }

    get inherentLikelihoodValue(): ListBoxItemData {
        const likelihood = this.risk.likelihood ?? 0;

        return (
            this.inherentLikelihoodOptions.find(
                (option) => option.value === String(likelihood),
            ) ?? DEFAULT_SCORING_OPTION
        );
    }

    handleInherentLikelihoodChange = (option: ListBoxItemData): void => {
        const likelihood = option.value ? Number(option.value) : 0;

        const { impact, residualImpact, residualLikelihood } = this.risk;

        const score = (impact ?? 0) * likelihood;
        const residualScore = (residualImpact ?? 0) * (residualLikelihood ?? 0);

        this.updateRiskAndUpdateDataTable({
            impact,
            residualImpact,
            residualLikelihood,
            likelihood: likelihood === 0 ? null : likelihood,
            score: score === 0 ? null : score,
            residualScore: residualScore === 0 ? null : residualScore,
        });
    };

    /**
     * ResidualImpactCell.
     */

    get residualImpactOptions(): ListBoxItemData[] {
        return this.inherentImpactOptions;
    }

    get residualImpactValue(): ListBoxItemData {
        const residualImpact = this.risk.residualImpact ?? 0;

        return (
            this.residualImpactOptions.find(
                (option) => option.value === String(residualImpact),
            ) ?? DEFAULT_SCORING_OPTION
        );
    }

    handleResidualImpactChange = (option: ListBoxItemData): void => {
        const residualImpact = option.value ? Number(option.value) : 0;

        const { impact, residualLikelihood, likelihood } = this.risk;

        const score = (impact ?? 0) * (likelihood ?? 0);
        const residualScore = residualImpact * (residualLikelihood ?? 0);

        this.updateRiskAndUpdateDataTable({
            impact,
            residualLikelihood,
            likelihood,
            residualImpact: residualImpact === 0 ? null : residualImpact,
            score: score === 0 ? null : score,
            residualScore: residualScore === 0 ? null : residualScore,
        });
    };

    /**
     * ResidualLikelihoodCell.
     */

    get residualLikelihoodOptions(): ListBoxItemData[] {
        return this.inherentLikelihoodOptions;
    }

    get residualLikelihoodValue(): ListBoxItemData {
        const residualLikelihood = this.risk.residualLikelihood ?? 0;

        return (
            this.residualLikelihoodOptions.find(
                (option) => option.value === String(residualLikelihood),
            ) ?? DEFAULT_SCORING_OPTION
        );
    }

    handleResidualLikelihoodChange = (option: ListBoxItemData): void => {
        const residualLikelihood = option.value ? Number(option.value) : 0;

        const { impact, residualImpact, likelihood } = this.risk;

        const score = (impact ?? 0) * (likelihood ?? 0);
        const residualScore = (residualImpact ?? 0) * residualLikelihood;

        this.updateRiskAndUpdateDataTable({
            impact,
            residualImpact,
            likelihood,
            residualLikelihood:
                residualLikelihood === 0 ? null : residualLikelihood,
            score: score === 0 ? null : score,
            residualScore: residualScore === 0 ? null : residualScore,
        });
    };

    /**
     * RiskOwnerCell.
     */

    get owners(): RiskWithCustomFieldsResponseDto['owners'] {
        return this.risk.owners;
    }

    get avatarData(): AvatarStackProps['avatarData'] {
        return this.risk.owners.map((owner) => ({
            fallbackText: getInitials(
                getFullName(owner.firstName, owner.lastName),
            ),
            primaryLabel: getFullName(owner.firstName, owner.lastName),
            secondaryLabel: owner.email,
            imgSrc: owner.avatarUrl,
        }));
    }

    handleFetchOwners = (params: {
        search?: UseComboboxInputValueChange<ListBoxItemData>['inputValue'];
        increasePage?: boolean;
    }): void => {
        this.usersInfiniteController.onFetchUsers(params);
    };

    get hasNextOwnersPage(): boolean {
        return this.usersInfiniteController.hasNextPage;
    }

    get isLoadingOwners(): boolean {
        return this.usersInfiniteController.isLoading;
    }

    get ownerOptions(): ListBoxItemData[] {
        return this.usersInfiniteController.usersList.map(
            ({ id, firstName, lastName, email, avatarUrl }) => {
                const fullName = getFullName(firstName, lastName);

                return {
                    id: String(id),
                    label: `${firstName} ${lastName} - ${email}`,
                    avatar: {
                        imgAlt: t`${fullName} image`,
                        imgSrc: avatarUrl ?? undefined,
                    },
                };
            },
        );
    }

    get ownersInitialValue(): ListBoxItemData[] {
        return this.owners.map((owner) => ({
            id: owner.id.toString(),
            label: getFullName(owner.firstName, owner.lastName),
            value: owner.id.toString(),
        }));
    }

    openRiskOwnerModal = (): void => {
        this.usersInfiniteController.loadUsers({
            roles: [
                'ADMIN',
                'TECHGOV',
                'WORKSPACE_ADMINISTRATOR',
                'CONTROL_MANAGER',
            ],
        });

        when(
            () => !this.usersInfiniteController.isLoading,
            () => {
                modalController.openModal({
                    id: RISK_OWNERS_MODAL_ID,
                    content: () => (
                        <AssignRiskOwnerModalContent
                            data-id="gxAploOy"
                            controller={this}
                        />
                    ),
                    centered: true,
                    disableClickOutsideToClose: true,
                    size: 'lg',
                });
            },
        );
    };

    handleAssignOwnersSubmit = (values: FormValues): void => {
        this.updateRiskAndUpdateDataTable({
            owners: (values.owners as ListBoxItemData[]).map(
                (value: ListBoxItemData) => {
                    return {
                        id: Number(value.id),
                    };
                },
            ),
        });

        when(
            () => !this.isMutationPending,
            () => {
                modalController.closeModal(RISK_OWNERS_MODAL_ID);
            },
        );
    };

    get categoriesOptions(): ListBoxItemData[] {
        return sharedRiskCategoriesController.categories.map((category) => ({
            id: category.id.toString(),
            label: category.name,
        }));
    }

    get categoriesInitialValue(): ListBoxItemData[] {
        return this.risk.categories.map((category) => ({
            id: category.id.toString(),
            label: category.name,
            value: category.id.toString(),
        }));
    }

    openRiskCategoriesModal = (): void => {
        modalController.openModal({
            id: RISK_CATEGORIES_MODAL_ID,
            content: () => (
                <AddRiskCategoriesModalContent
                    data-id="9FCQyvE6"
                    controller={this}
                />
            ),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'lg',
        });
    };

    handleAssignCategoriesSubmit = (values: FormValues): void => {
        this.updateRiskAndUpdateDataTable({
            categories: (values.categories as ListBoxItemData[]).map(
                (value: ListBoxItemData) => {
                    return {
                        id: Number(value.id),
                    };
                },
            ),
        });

        when(
            () => !this.isMutationPending,
            () => {
                modalController.closeModal(RISK_CATEGORIES_MODAL_ID);
            },
        );
    };

    /**
     * RiskReviewersCell.
     */

    get reviewersData(): AvatarStackProps['avatarData'] {
        return this.risk.reviewers.map((reviewer) => {
            const fullName = getFullName(reviewer.firstName, reviewer.lastName);
            const fallbackText = getInitials(fullName);

            return {
                fallbackText,
                primaryLabel: fullName,
                secondaryLabel: reviewer.email,
                imgSrc: reviewer.avatarUrl,
            };
        });
    }
}
