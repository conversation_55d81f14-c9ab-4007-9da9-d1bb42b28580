import type { UseComboboxInputValueChange } from 'downshift';
import { modalController } from '@controllers/modal';
import {
    sharedRiskCategoriesController,
    sharedRiskManagementMutationController,
} from '@controllers/risk';
import { UsersInfiniteController } from '@controllers/users';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { RiskRequestOptionalDto } from '@globals/api-sdk/types';
import { plural, t } from '@globals/i18n/macro';
import { makeAutoObservable, when } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import type { FormValues } from '@ui/forms';
import { AddRiskCategoriesModalContent } from '../components/add-risk-categories-modal-content';
import { AssignRiskOwnerModalContent } from '../components/assign-risk-owner-modal-content';
import { managementDatatableController } from './management-data-table.controller';

export const RISK_OWNERS_MODAL_ID = 'risk-owners-modal';
export const RISK_CATEGORIES_MODAL_ID = 'risk-categories-modal';

export class ManagementDataTableBulkController {
    usersInfiniteController: UsersInfiniteController;

    constructor() {
        makeAutoObservable(this);

        this.usersInfiniteController = new UsersInfiniteController();
    }

    get isMutationPending(): boolean {
        return sharedRiskManagementMutationController.isBulkActionPending;
    }

    updateRiskAndUpdateDataTable = (
        requestBody: RiskRequestOptionalDto,
    ): void => {
        sharedRiskManagementMutationController.addRiskOwnersInBulk(
            managementDatatableController.selectedRiskIds,
            requestBody.owners?.map((owner) => Number(owner.id)) ?? [],
        );

        when(
            () => !this.isMutationPending,
            () => {
                managementDatatableController.invalidate();
            },
        );
    };

    handleFetchOwners = (params: {
        search?: UseComboboxInputValueChange<ListBoxItemData>['inputValue'];
        increasePage?: boolean;
    }): void => {
        this.usersInfiniteController.onFetchUsers(params);
    };

    get hasNextOwnersPage(): boolean {
        return this.usersInfiniteController.hasNextPage;
    }

    get isLoadingOwners(): boolean {
        return this.usersInfiniteController.isLoading;
    }

    get ownerOptions(): ListBoxItemData[] {
        return this.usersInfiniteController.usersList.map(
            ({ id, firstName, lastName, email, avatarUrl }) => {
                const fullName = getFullName(firstName, lastName);

                return {
                    id: String(id),
                    label: `${firstName} ${lastName} - ${email}`,
                    avatar: {
                        imgAlt: t`${fullName} image`,
                        imgSrc: avatarUrl ?? undefined,
                    },
                };
            },
        );
    }

    get ownersInitialValue(): ListBoxItemData[] {
        return [];
    }

    openRiskOwnerModal = (): void => {
        this.usersInfiniteController.loadUsers({
            roles: [
                'ADMIN',
                'TECHGOV',
                'WORKSPACE_ADMINISTRATOR',
                'CONTROL_MANAGER',
            ],
        });

        when(
            () => !this.usersInfiniteController.isLoading,
            () => {
                modalController.openModal({
                    id: RISK_OWNERS_MODAL_ID,
                    content: () => (
                        <AssignRiskOwnerModalContent
                            data-id="gxAploOy"
                            controller={this}
                        />
                    ),
                    centered: true,
                    disableClickOutsideToClose: true,
                    size: 'lg',
                });
            },
        );
    };

    handleAssignOwnersSubmit = (values: FormValues): void => {
        const owners = (values.owners as ListBoxItemData[]).map(
            (value: ListBoxItemData) => {
                return {
                    id: Number(value.id),
                };
            },
        );

        sharedRiskManagementMutationController.addRiskOwnersInBulk(
            managementDatatableController.selectedRiskIds,
            owners.map((owner) => Number(owner.id)),
        );

        when(
            () => !this.isMutationPending,
            () => {
                managementDatatableController.invalidate();

                modalController.closeModal(RISK_OWNERS_MODAL_ID);
            },
        );
    };

    get categoriesOptions(): ListBoxItemData[] {
        return sharedRiskCategoriesController.categories.map((category) => ({
            id: category.id.toString(),
            label: category.name,
        }));
    }

    get categoriesInitialValue(): ListBoxItemData[] {
        return [];
    }

    openRiskCategoriesModal = (): void => {
        modalController.openModal({
            id: RISK_CATEGORIES_MODAL_ID,
            content: () => (
                <AddRiskCategoriesModalContent
                    data-id="9FCQyvE6"
                    controller={this}
                />
            ),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'lg',
        });
    };

    handleAssignCategoriesSubmit = (values: FormValues): void => {
        const categories = (values.categories as ListBoxItemData[]).map(
            (value: ListBoxItemData) => {
                return {
                    id: Number(value.id),
                };
            },
        );

        sharedRiskManagementMutationController.assignRiskCategoryInBulk(
            managementDatatableController.selectedRiskIds,
            categories.map((owner) => Number(owner.id)),
        );

        when(
            () => !this.isMutationPending,
            () => {
                managementDatatableController.invalidate();

                modalController.closeModal(RISK_CATEGORIES_MODAL_ID);
            },
        );
    };

    get shouldDisplayActivateAction(): boolean {
        return managementDatatableController.selectedRisks.some((risk) => {
            return ['ARCHIVED', 'CLOSED'].includes(risk.status);
        });
    }

    get shouldDisplayArchiveAction(): boolean {
        return managementDatatableController.selectedRisks.some((risk) => {
            return ['ACTIVE', 'CLOSED'].includes(risk.status);
        });
    }

    get shouldDisplayCloseAction(): boolean {
        return managementDatatableController.selectedRisks.some((risk) => {
            return ['ACTIVE', 'ARCHIVED'].includes(risk.status);
        });
    }

    handleUpdateRiskStatusInBulk: typeof sharedRiskManagementMutationController.updateRiskStatusInBulk =
        (riskIds, status): void => {
            sharedRiskManagementMutationController.updateRiskStatusInBulk(
                riskIds,
                status,
            );

            when(
                () => !this.isMutationPending,
                () => {
                    managementDatatableController.invalidate();
                },
            );
        };

    handleSetRiskStatusAsActive = (): void => {
        openConfirmationModal({
            title: t`Activate risks?`,
            body: t`Are you sure you want to activate the selected risks?`,
            confirmText: t`Activate`,
            cancelText: t`Cancel`,
            type: 'primary',
            onConfirm: () => {
                sharedRiskManagementMutationController.updateRiskStatusInBulk(
                    managementDatatableController.selectedRiskIds,
                    'ACTIVE',
                );

                when(
                    () => !this.isMutationPending,
                    () => {
                        managementDatatableController.invalidate();
                        closeConfirmationModal();
                    },
                );
            },
            onCancel: closeConfirmationModal,
            isLoading: () => this.isMutationPending,
        });
    };

    handleSetRiskStatusAsArchived = (): void => {
        openConfirmationModal({
            title: t`Archive risks?`,
            body: t`Are you sure you want to archive the selected risks?`,
            confirmText: t`Archive`,
            cancelText: t`Cancel`,
            type: 'primary',
            onConfirm: () => {
                sharedRiskManagementMutationController.updateRiskStatusInBulk(
                    managementDatatableController.selectedRiskIds,
                    'ARCHIVED',
                );

                when(
                    () => !this.isMutationPending,
                    () => {
                        managementDatatableController.invalidate();
                        closeConfirmationModal();
                    },
                );
            },
            onCancel: closeConfirmationModal,
            isLoading: () => this.isMutationPending,
        });
    };

    handleSetRiskStatusAsClosed = (): void => {
        openConfirmationModal({
            title: t`Close risks?`,
            body: t`Are you sure you want to close the selected risks?`,
            confirmText: t`Close`,
            cancelText: t`Cancel`,
            type: 'primary',
            onConfirm: () => {
                sharedRiskManagementMutationController.updateRiskStatusInBulk(
                    managementDatatableController.selectedRiskIds,
                    'CLOSED',
                );

                when(
                    () => !this.isMutationPending,
                    () => {
                        managementDatatableController.invalidate();
                        closeConfirmationModal();
                    },
                );
            },
            onCancel: closeConfirmationModal,
            isLoading: () => this.isMutationPending,
        });
    };

    handleDeleteRisks = (): void => {
        openConfirmationModal({
            title: t`Delete risks?`,
            body: plural(managementDatatableController.selectedRiskIds.length, {
                one: 'You are about to delete # risk. This action will remove the risk data from your account permanently, and this action cannot be undone.',
                other: 'You are about to delete # risks. This action will remove the risks data from your account permanently, and this action cannot be undone.',
            }),
            confirmText: t`Delete`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                sharedRiskManagementMutationController.deleteRiskInBulk(
                    managementDatatableController.selectedRiskIds,
                );

                when(
                    () =>
                        !sharedRiskManagementMutationController.isDeleteRiskInBulkPending,
                    () => {
                        managementDatatableController.invalidate();
                        closeConfirmationModal();
                    },
                );
            },
            onCancel: closeConfirmationModal,
            isLoading: () =>
                sharedRiskManagementMutationController.isDeleteRiskInBulkPending,
        });
    };
}

export const sharedManagementDataTableBulkController =
    new ManagementDataTableBulkController();
