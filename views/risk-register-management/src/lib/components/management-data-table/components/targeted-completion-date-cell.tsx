import { DateTime } from '@cosmos-lab/components/date-time';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const TargetedCompletionDateCell = ({
    row: { original },
}: {
    row: { original: RiskWithCustomFieldsResponseDto };
}): React.JSX.Element => {
    return original.anticipatedCompletionDate ? (
        <DateTime date={original.anticipatedCompletionDate} format="table" />
    ) : (
        <EmptyValue label={t`Risk targeted completion date is not available`} />
    );
};
