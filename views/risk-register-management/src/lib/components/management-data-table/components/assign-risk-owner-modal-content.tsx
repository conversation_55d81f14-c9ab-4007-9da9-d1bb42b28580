import { modalController } from '@controllers/modal';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import { RISK_OWNERS_MODAL_ID } from '../controllers/management-data-table-cell.controller';

interface IAssignRiskOwnerController {
    ownersInitialValue: ListBoxItemData[];
    isLoadingOwners: boolean;
    ownerOptions: ListBoxItemData[];
    hasNextOwnersPage: boolean;
    handleFetchOwners: (params: {
        search?: string;
        increasePage?: boolean;
    }) => void;
    handleAssignOwnersSubmit: (values: FormValues) => void;
    isMutationPending: boolean;
}

interface Props {
    controller: IAssignRiskOwnerController;
}

export const AssignRiskOwnerModalContent = observer(
    ({ controller }: Props): JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();

        const {
            ownerOptions,
            hasNextOwnersPage,
            handleFetchOwners,
            handleAssignOwnersSubmit,
            isLoadingOwners,
            isMutationPending: isAssignOwnersSubmitIsLoading,
            ownersInitialValue,
        } = controller;

        return (
            <>
                <Modal.Header
                    closeButtonAriaLabel={t`Close`}
                    title={t`Risk Owners`}
                    onClose={() => {
                        modalController.closeModal(RISK_OWNERS_MODAL_ID);
                    }}
                />

                <Modal.Body>
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        formId="risk-owners-form"
                        data-id="risk-owners-form"
                        schema={{
                            owners: {
                                type: 'combobox',
                                label: t`Assign owners`,
                                loaderLabel: t`Loading results`,
                                removeAllSelectedItemsLabel: t`Clear all`,
                                getSearchEmptyState: () => t`No users found`,
                                isMultiSelect: true,
                                isLoading: isLoadingOwners,
                                options: ownerOptions,
                                hasMore: hasNextOwnersPage,
                                onFetchOptions: handleFetchOwners,
                                initialValue: ownersInitialValue,
                            },
                        }}
                        onSubmit={handleAssignOwnersSubmit}
                    />
                </Modal.Body>

                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Close`,
                            level: 'secondary',
                            cosmosUseWithCaution_isDisabled:
                                isAssignOwnersSubmitIsLoading,
                            onClick: () => {
                                modalController.closeModal(
                                    RISK_OWNERS_MODAL_ID,
                                );
                            },
                        },
                        {
                            label: t`Confirm`,
                            level: 'primary',
                            colorScheme: 'primary',
                            type: 'submit',
                            isLoading: isAssignOwnersSubmitIsLoading,
                            onClick: () => {
                                triggerSubmit();
                            },
                        },
                    ]}
                />
            </>
        );
    },
);
