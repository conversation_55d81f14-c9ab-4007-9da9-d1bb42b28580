import { DateTime } from '@cosmos-lab/components/date-time';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const CompletionDateCell = ({
    row: { original },
}: {
    row: { original: RiskWithCustomFieldsResponseDto };
}): React.JSX.Element => {
    return original.completionDate ? (
        <DateTime date={original.completionDate} format="table" />
    ) : (
        <EmptyValue label={t`Risk completion date is not available`} />
    );
};
