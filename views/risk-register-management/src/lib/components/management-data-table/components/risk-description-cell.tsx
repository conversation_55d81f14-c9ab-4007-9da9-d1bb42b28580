import { Text } from '@cosmos/components/text';
import { Truncation } from '@cosmos-lab/components/truncation';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';

export const RiskDescriptionCell = ({
    row: { original },
}: {
    row: { original: RiskWithCustomFieldsResponseDto };
}): React.JSX.Element => {
    const { description } = original;

    return (
        <Text data-testid="RiskDescriptionCell" data-id="Wp_IUEqV">
            <Truncation mode="end" maxLength={100}>
                {description}
            </Truncation>
        </Text>
    );
};
