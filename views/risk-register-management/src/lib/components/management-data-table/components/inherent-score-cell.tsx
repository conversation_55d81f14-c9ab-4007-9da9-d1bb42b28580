import { capitalize } from 'lodash-es';
import { useMemo } from 'react';
import { sharedRiskSettingsController } from '@controllers/risk';
import { Box } from '@cosmos/components/box';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { RiskScore } from '@cosmos-lab/components/risk-score';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { calculateRiskMetrics } from '@views/risk-register-overview';

export const InherentScoreCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskWithCustomFieldsResponseDto };
    }): React.JSX.Element => {
        const { score } = original;

        const { riskSettings } = sharedRiskSettingsController;

        const inheritMetrics = useMemo(
            () => calculateRiskMetrics(score ?? 0, riskSettings),
            [riskSettings, score],
        );

        if (!riskSettings) {
            return (
                <EmptyValue label={t`Risk inherent score is not available`} />
            );
        }

        return (
            <Box pt="1x" data-id="cnBNnERx" data-testid="InherentScoreCellBox">
                <RiskScore
                    intensity="strong"
                    severity={inheritMetrics.severity}
                    scoreNumber={score}
                    label={capitalize(inheritMetrics.threshold?.name)}
                    data-id="mYh-WVFE"
                    data-testid="InherentScoreCell"
                />
            </Box>
        );
    },
);
