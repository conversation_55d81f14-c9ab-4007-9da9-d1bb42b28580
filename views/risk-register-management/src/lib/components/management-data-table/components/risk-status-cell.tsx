import { Text } from '@cosmos/components/text';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const RiskStatusCell = ({
    row: { original },
}: {
    row: { original: RiskWithCustomFieldsResponseDto };
}): React.JSX.Element => {
    switch (original.status) {
        case 'ACTIVE': {
            return (
                <Text data-testid="RiskStatusCell" data-id="9wp2EUta">
                    {t`Active`}
                </Text>
            );
        }
        case 'ARCHIVED': {
            return (
                <Text data-testid="RiskStatusCell" data-id="9wp2EUta">
                    {t`Archived`}
                </Text>
            );
        }
        case 'CLOSED': {
            return (
                <Text data-testid="RiskStatusCell" data-id="9wp2EUta">
                    {t`Closed`}
                </Text>
            );
        }
        default: {
            return <EmptyValue label={t`Risk status is not available`} />;
        }
    }
};
