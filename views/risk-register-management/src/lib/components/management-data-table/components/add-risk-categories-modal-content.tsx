import { modalController } from '@controllers/modal';
import { sharedRiskCategoriesController } from '@controllers/risk';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import { RISK_CATEGORIES_MODAL_ID } from '../controllers/management-data-table-cell.controller';

interface IAddRiskCategoriesController {
    handleAssignCategoriesSubmit: (values: FormValues) => void;
    isMutationPending: boolean;
    categoriesOptions: ListBoxItemData[];
    categoriesInitialValue: ListBoxItemData[];
}

interface Props {
    controller: IAddRiskCategoriesController;
}

export const AddRiskCategoriesModalContent = observer(
    ({ controller }: Props): JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();

        const {
            handleAssignCategoriesSubmit,
            isMutationPending,
            categoriesOptions,
            categoriesInitialValue,
        } = controller;

        return (
            <>
                <Modal.Header
                    closeButtonAriaLabel={t`Close`}
                    title={t`Risk Categories`}
                    onClose={() => {
                        modalController.closeModal(RISK_CATEGORIES_MODAL_ID);
                    }}
                />

                <Modal.Body>
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        formId="risk-categories-form"
                        data-id="risk-categories-form"
                        schema={{
                            categories: {
                                type: 'combobox',
                                isMultiSelect: true,
                                getSearchEmptyState: () =>
                                    t`No categories found`,
                                options: categoriesOptions,
                                label: t`Risk categories`,
                                loaderLabel: t`Loading risk categories options`,
                                isOptional: true,
                                hasMore:
                                    sharedRiskCategoriesController.hasNextPage,
                                isLoading:
                                    sharedRiskCategoriesController.isLoading,
                                onFetchOptions:
                                    sharedRiskCategoriesController.onFetchCategories,
                                initialValue: categoriesInitialValue,
                            },
                        }}
                        onSubmit={handleAssignCategoriesSubmit}
                    />
                </Modal.Body>

                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Close`,
                            level: 'secondary',
                            cosmosUseWithCaution_isDisabled: isMutationPending,
                            onClick: () => {
                                modalController.closeModal(
                                    RISK_CATEGORIES_MODAL_ID,
                                );
                            },
                        },
                        {
                            label: t`Confirm`,
                            level: 'primary',
                            colorScheme: 'primary',
                            type: 'submit',
                            isLoading: isMutationPending,
                            onClick: () => {
                                triggerSubmit();
                            },
                        },
                    ]}
                />
            </>
        );
    },
);
