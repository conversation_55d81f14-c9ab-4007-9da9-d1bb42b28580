import { AppDatatable } from '@components/app-datatable';
import {
    sharedMonitoringController,
    sharedMonitoringStatsController,
} from '@controllers/monitoring';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import type { RowActionItem } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { Icon } from '@cosmos/components/icon';
import { Loader } from '@cosmos/components/loader';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import type { MonitorTestInstanceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMonitoringFiltersModel } from '@models/monitoring';
import { useNavigate } from '@remix-run/react';
import { MonitoringModel } from './models/monitoring.model';
import { MonitoringBulkActionsModel } from './models/monitoring-bulk-actions.model';
import { MONITORING_COLUMNS } from './monitoring.constants';

const getMonitoringRowActions = (
    row: MonitorTestInstanceResponseDto,
): RowActionItem[] => {
    const { testId, checkStatus, testType } = row;
    const actions: RowActionItem[] = [];

    // Test now - Only if test has status of "enabled"
    if (checkStatus === 'ENABLED') {
        actions.push({
            id: 'test-now',
            label: t`Test now`,
            onSelect: () => {
                sharedMonitoringController.handleTestNow(testId);
            },
        });
    }

    // Stop testing - Only if test has status of "Testing"
    if (checkStatus === 'TESTING') {
        actions.push({
            id: 'stop-testing',
            label: t`Stop testing`,
            onSelect: () => {
                sharedMonitoringController.handleStopTesting(testId);
            },
        });
    }

    // View help article - Only for Drata tests
    if (testType === 'DRATA') {
        actions.push({
            id: 'view-help',
            label: t`View help article`,
            endSlot: <Icon name="LinkOut" />,
            onSelect: () => {
                sharedMonitoringController.handleViewHelpArticle();
            },
        });
    }

    return actions;
};

export const MonitoringView = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const { tableActions } = new MonitoringModel();
    const { bulkActions, handleRowSelection } =
        new MonitoringBulkActionsModel();

    const {
        monitoringListData,
        monitoringListTotal,
        monitoringListLoad,
        isLoading,
    } = sharedMonitoringController;

    const {
        monitoringPassingPercents,
        monitoringStatsFailedTests,
        monitoringStatsPassedTests,
        isLoadingStats,
    } = sharedMonitoringStatsController;

    const { filters } = sharedMonitoringFiltersModel;

    return (
        <>
            {isLoadingStats ? (
                <Card
                    title={t`Tests passed`}
                    body={<Loader isSpinnerOnly label={t`Loading...`} />}
                />
            ) : (
                <Grid columns="3" gap="4x" pb="4x">
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Tests Passed`}
                            statValue={monitoringPassingPercents}
                            totalText={t`Percentage of tests passed, excludes draft tests`}
                        />
                    </Box>
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Failed tests`}
                            statValue={monitoringStatsFailedTests}
                            statIcon="NotReady"
                            statIconColor="critical"
                            totalText={t`Excludes draft tests`}
                        />
                    </Box>
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Passed Tests`}
                            statValue={monitoringStatsPassedTests}
                            statIcon="CheckCircle"
                            statIconColor="success"
                            totalText={t`Excludes draft tests`}
                        />
                    </Box>
                </Grid>
            )}

            <AppDatatable
                isFullPageTable
                isRowSelectionEnabled
                isLoading={isLoading}
                tableId="datatable-monitoring"
                data-id="datatable-monitoring"
                data={monitoringListData}
                columns={MONITORING_COLUMNS}
                total={monitoringListTotal}
                filterProps={filters}
                bulkActionDropdownItems={bulkActions}
                getRowId={(row) => String(row.testId)}
                tableActions={tableActions}
                rowActionsProps={{
                    type: 'dropdown',
                    getRowActions: getMonitoringRowActions,
                }}
                emptyStateProps={{
                    title: t`No monitoring tests found`,
                    description: t`No monitoring tests found`,
                }}
                tableSearchProps={{
                    hideSearch: false,
                    placeholder: t`Search`,
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t`Pin filters to page`,
                        toggleUnpinnedLabel: t`Move filters to dropdown`,
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={monitoringListLoad}
                onRowSelection={handleRowSelection}
                onRowClick={({ row }) => {
                    navigate(`${row.testId}/overview`);
                }}
            />
        </>
    );
});
