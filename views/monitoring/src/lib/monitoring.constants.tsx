import {
    MonitoringTableCellCategoryComponent,
    MonitoringTableCellConnectionsComponent,
    MonitoringTableCellFindingsComponent,
    MonitoringTableCellResultComponent,
    MonitoringTableCellStatusComponent,
    MonitoringTableCellTestNameComponent,
} from '@components/monitoring';
import type { DatatableProps } from '@cosmos/components/datatable';
import type {
    FailsByCategoryResponseDto,
    MonitorsV2ControllerListProdMonitorsData,
    MonitorTestInstanceResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const MONITORING_COLUMNS: DatatableProps<MonitorTestInstanceResponseDto>['columns'] =
    [
        {
            accessorKey: 'testName',
            header: 'Name',
            id: 'name',
            enableSorting: false,
            minSize: 300,
            cell: MonitoringTableCellTestNameComponent,
        },
        {
            accessorKey: 'connections',
            header: 'Result',
            id: 'checkResultStatus',
            enableSorting: true,
            cell: MonitoringTableCellResultComponent,
        },
        {
            accessorKey: 'findingsCount',
            header: 'Findings',
            id: 'findingsCount',
            enableSorting: true,
            cell: MonitoringTableCellFindingsComponent,
        },
        {
            accessorKey: 'connections',
            header: 'Status',
            id: 'checkStatus',
            enableSorting: true,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: MonitoringTableCellStatusComponent,
        },
        {
            accessorKey: 'connections',
            header: 'Category',
            id: 'category',
            enableSorting: true,
            cell: MonitoringTableCellCategoryComponent,
        },
        {
            accessorKey: 'connections',
            header: 'Active connections',
            id: 'activeConnections',
            enableSorting: false,
            cell: MonitoringTableCellConnectionsComponent,
        },
    ];

export const getCheckStatusOptions = (): {
    label: string;
    value: MonitorTestInstanceResponseDto['checkStatus'];
}[] => [
    { label: t`Unused`, value: 'UNUSED' },
    { label: t`New`, value: 'NEW' },
    { label: t`Enabled`, value: 'ENABLED' },
    { label: t`Disabled`, value: 'DISABLED' },
    { label: t`Testing`, value: 'TESTING' },
];

/**
 * Get monitor check type options using the API type.
 */
export const getMonitorCheckTypeOptions = (): {
    label: string;
    value: FailsByCategoryResponseDto['category'];
}[] => [
    { label: t`Device`, value: 'AGENT' },
    { label: t`Identity Provider`, value: 'IDENTITY' },
    { label: t`In Drata`, value: 'IN_DRATA' },
    { label: t`Infrastructure`, value: 'INFRASTRUCTURE' },
    { label: t`Observability`, value: 'OBSERVABILITY' },
    { label: t`Policy`, value: 'POLICY' },
    { label: t`Version Control`, value: 'VERSION_CONTROL' },
    { label: t`Ticketing`, value: 'TICKETING' },
    { label: t`HRIS`, value: 'HRIS' },
    { label: t`Custom`, value: 'CUSTOM' },
];

/**
 * Get test type options using the API type.
 */
export const getTestTypeOptions = (): {
    label: string;
    value: NonNullable<
        NonNullable<
            MonitorsV2ControllerListProdMonitorsData['query']
        >['allowedTestSources']
    >[number];
}[] => [
    { label: t`Drata`, value: 'DRATA' },
    { label: t`Custom (published)`, value: 'CUSTOM_PUBLISHED' },
    { label: t`Custom (draft)`, value: 'CUSTOM_DRAFT' },
];
