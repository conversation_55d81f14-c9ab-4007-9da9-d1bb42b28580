import { useEffect } from 'react';
import { VendorSecurityReviewsAISummaryComponent } from '@components/vendors-security-reviews';
import {
    SocAddDocumentEmptyState,
    VendorSecurityReviewsSocFormComponent,
} from '@components/vendors-security-reviews-soc';
import { routeController } from '@controllers/route';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { dimension80x, dimension170x } from '@cosmos/constants/tokens';
import { PdfViewer } from '@cosmos-lab/components/pdf-viewer';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { ContentAside } from '@ui/content-aside';

export const VendorsProfileSecurityReviewSocView = observer(
    (): React.JSX.Element => {
        const thousandPx = `${dimension170x} + ${dimension80x}`;

        const { isLoading, pdfDownloadUrl, isPdfDownloadLoading } =
            sharedVendorsSecurityReviewDocumentsController;

        const { securityReviewDetails, isLoading: isSecurityReviewLoading } =
            sharedVendorsSecurityReviewDetailsController;

        const { isVendorsDomainReadEnabled } = sharedFeatureAccessModel;
        const { currentWorkspace } = sharedWorkspacesController;
        const navigate = useNavigate();

        useEffect(() => {
            runInAction(() => {
                const vendorId =
                    sharedVendorsDetailsController.vendorDetails?.id;

                if (
                    !sharedVendorsDetailsController.vendorDetails ||
                    !currentWorkspace?.id ||
                    !vendorId ||
                    !securityReviewDetails?.id
                ) {
                    return;
                }
                const isVendorProspective =
                    sharedVendorsDetailsController.vendorDetails.status ===
                    'PROSPECTIVE';

                if (!isVendorsDomainReadEnabled && currentWorkspace.id) {
                    navigate(
                        `${routeController.userPartOfUrl}/vendors/${isVendorProspective ? 'prospective' : 'current'}`,
                    );
                }

                if (
                    securityReviewDetails.type === 'SECURITY' ||
                    securityReviewDetails.type === 'UPLOAD_REPORT'
                ) {
                    navigate(
                        `${routeController.userPartOfUrl}/vendors/${isVendorProspective ? 'prospective' : 'current'}/${vendorId}/security-reviews/${securityReviewDetails.id}`,
                    );
                }

                // if the soc review is already completed redirect to completed page
                if (securityReviewDetails.status === 'COMPLETED') {
                    navigate(
                        `${routeController.userPartOfUrl}/vendors/${isVendorProspective ? 'prospective' : 'current'}/${vendorId}/security-reviews/soc/${securityReviewDetails.id}/completed`,
                    );
                }
            });
        }, [
            isVendorsDomainReadEnabled,
            currentWorkspace?.id,
            navigate,
            securityReviewDetails?.vendor?.status,
            securityReviewDetails?.type,
            securityReviewDetails?.status,
            securityReviewDetails?.vendor?.id,
            securityReviewDetails?.id,
        ]);

        if (
            !isVendorsDomainReadEnabled ||
            isLoading ||
            isSecurityReviewLoading ||
            securityReviewDetails?.status === 'COMPLETED'
        ) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        return (
            <ContentAside
                data-testid="VendorsProfileSecurityReviewSocView"
                data-id="FmVVf_kM"
                content={<VendorSecurityReviewsSocFormComponent />}
            >
                <Stack direction="column" gap="xl">
                    <Box>
                        <VendorSecurityReviewsAISummaryComponent />
                    </Box>
                    {pdfDownloadUrl && (
                        <Box minHeight={`calc(${thousandPx})`}>
                            <PdfViewer
                                src={pdfDownloadUrl.signedUrl}
                                label={'pdf-viewer'}
                                data-id={'cosmos-pdf-viewer'}
                            />
                        </Box>
                    )}

                    {!pdfDownloadUrl &&
                        (isPdfDownloadLoading ? (
                            <Loader isSpinnerOnly label="Loading..." />
                        ) : (
                            <SocAddDocumentEmptyState data-id="aJuB4Aaz" />
                        ))}
                </Stack>
            </ContentAside>
        );
    },
);
