/* eslint-disable @typescript-eslint/no-unused-vars -- fine for global types */
// eslint-disable-next-line no-restricted-imports -- Only allowed because this is THE tanstack type type def
import type { RowData } from '@tanstack/react-table';
import type { TableDisplayState } from '../cosmos/components/datatable/src/lib/features/display-state.feature';
import type { PinnedColumnInfo } from '../cosmos/components/datatable/src/lib/features/pinning.feature';
import type { DatatableTableMeta } from '../cosmos/components/datatable/src/lib/types/datatable-table-meta.type';

declare module '@tanstack/react-table' {
    interface TableMeta<TData extends RowData>
        extends DatatableTableMeta<TData> {
        cantDetermineTotalCount?: boolean;
    }

    interface ColumnMeta<TData extends RowData, TValue> {
        shouldIgnoreRowClick: boolean;
        isActionColumn?: boolean;
        isSystemColumn?: boolean;
    }

    interface PaginationState {
        pageSizeOptions: number[];
    }

    interface TableState {
        isAllRowsSelected: boolean;
    }

    interface Table<TData extends RowData> {
        getMeta: () => TableMeta<TData>;
        getDisplayState: () => TableDisplayState<TData>;
        getPinnedColumnInfo: (columnId: string) => PinnedColumnInfo | undefined;
        getPinnedColumnsCount: () => number;
        isMaxPinnedColumnsReached: () => boolean;
        getPinnedColumnIds: () => string[];
        getMaxPinnedColumns: () => number;
        getPinnedLeftPosition: (column: Column<TData>) => number;
    }
}
