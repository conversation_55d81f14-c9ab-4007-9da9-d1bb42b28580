import { useState } from 'react';
import { css, keyframes, styled } from 'styled-components';
import {
    BANNER_ENTRY_ANIMATION_MS,
    BANNER_EXIT_ANIMATION_MS,
    BANNER_ONCLOSE_DELAY_MS,
} from '@controllers/banner-service';
import { Banner, type BannerProps } from '@cosmos/components/banner';
import { logger } from '@globals/logger';

/**
 * Animation keyframes for banner entry - scale in effect.
 */
const scaleInKeyframes = keyframes`
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
`;

/**
 * Exit animation - Two-phase approach for smooth banner dismissal.
 *
 * This animation prevents jarring layout shifts by combining:
 * 1. Fade + scale (0% → 65%): Visual dismissal while maintaining layout space
 * 2. Height collapse (65% → 100%): Smooth removal from layout.
 *
 * See README.md "Animation Architecture" section for detailed explanation
 * of why max-height is essential for professional exit animations.
 *
 * NOTE (<PERSON>): This is not the best solution, but it's a good start until
 * someone has more time to focus on this. The max-height approach works
 * but could be improved with more sophisticated animation techniques.
 */
const slideOutKeyframes = keyframes`
    0% {
        opacity: 1;
        transform: scale(1);
        /* Phase 1: Maintain full height during visual fade */
        max-height: 200px; /* Safe upper bound for most banner content */
        padding-top: initial;
        padding-bottom: initial;
        margin-top: initial;
        margin-bottom: initial;
    }
    65% {
        opacity: 0;
        transform: scale(0.95);
        /* Phase 1 continues: Still maintain height while faded out */
        max-height: 200px; /* Keep layout space reserved */
        padding-top: initial;
        padding-bottom: initial;
        margin-top: initial;
        margin-bottom: initial;
    }
    100% {
        opacity: 0;
        transform: scale(0.95);
        /* Phase 2: Collapse height to remove from layout smoothly */
        max-height: 0; /* This creates the smooth collapse effect */
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 0;
        margin-bottom: 0;
    }
`;

/**
 * Styled wrapper that handles banner animations.
 */
const StyledAnimatedDiv = styled.div<{
    $isExiting: boolean;
}>`
    overflow: hidden;
    /* Optimize for animations - see Animation Architecture in README.md */
    will-change: transform, opacity, max-height, margin;

    /* Add bottom margin for spacing between banners */
    margin-bottom: 12px; /* sm gap equivalent */

    /* Remove margin from last child to avoid extra space */
    &:last-child {
        margin-bottom: 0;
    }

    ${({ $isExiting }) => {
        if ($isExiting) {
            return css`
                /* Height-based exit animation - synchronized with constants */
                animation: ${slideOutKeyframes} ${BANNER_EXIT_ANIMATION_MS}ms ease-in forwards;
                animation-fill-mode: both;
            `;
        }

        return css`
            /* Entry animation on mount */
            animation: ${scaleInKeyframes} ${BANNER_ENTRY_ANIMATION_MS}ms ease-out forwards;
            animation-fill-mode: both;
        `;
    }}

    /* Respect reduced motion preferences */
    @media (prefers-reduced-motion: reduce) {
        animation: none !important;
        opacity: 1;
        transform: none;
        max-height: none;
        padding: initial;
        margin: initial;
    }
`;

export interface AnimatedBannerProps extends BannerProps {
    /**
     * Whether the banner is being dismissed (triggers exit animation).
     */
    isExiting?: boolean;

    /**
     * Callback called when exit animation completes.
     */
    onExitComplete?: () => void;

    /**
     * Unique testing ID for the animation wrapper element.
     * The inner Banner component will use the data-id from BannerProps.
     */
    'data-id'?: string;
}

/**
 * AnimatedBanner wraps the Cosmos Banner component with smooth entry and exit animations.
 *
 * Multiple AnimatedBanner instances can exist on the same page simultaneously, which is
 * the typical use case. Each banner animates independently with its own state and timing.
 *
 * Features:
 * - Scale-in entry animation
 * - Smooth exit animation with height collapse
 * - Respects prefers-reduced-motion
 * - Uses consistent timing with other Cosmos animations
 * - Supports multiple instances with independent animations.
 */
export const AnimatedBanner = ({
    isExiting = false,
    onClose,
    'data-id': wrapperDataId,
    // Explicit Banner props - no spreading
    title,
    body,
    severity,
    displayMode,
    action,
    closeButtonAriaLabel,
}: AnimatedBannerProps): JSX.Element => {
    const [isLocalExiting, setIsLocalExiting] = useState(false);

    /**
     * Handle close with animation.
     */
    const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
        logger.info({
            message:
                '[animated-banner.component] handleClose(): Starting exit animation for banner',
            additionalInfo: {
                bannerId: wrapperDataId || 'animated-banner-exit',
                action: 'exit-animation-start',
            },
        });
        setIsLocalExiting(true);
        // Wait for animation to complete, then call the actual onClose
        setTimeout(() => {
            logger.info({
                message:
                    '[animated-banner.component] handleClose(): Exit animation complete, calling onClose',
                additionalInfo: {
                    bannerId: wrapperDataId || 'animated-banner-exit',
                    action: 'exit-animation-complete',
                },
            });
            if (onClose) {
                onClose(event);
            }
        }, BANNER_ONCLOSE_DELAY_MS); // Slightly less than CSS animation to ensure it completes
    };

    const isCurrentlyExiting = isExiting || isLocalExiting;

    return (
        <StyledAnimatedDiv
            $isExiting={isCurrentlyExiting}
            data-testid="AnimatedBanner"
            data-id={wrapperDataId}
        >
            <Banner
                title={title}
                body={body}
                severity={severity}
                displayMode={displayMode}
                action={action}
                closeButtonAriaLabel={closeButtonAriaLabel}
                onClose={handleClose}
            />
        </StyledAnimatedDiv>
    );
};
