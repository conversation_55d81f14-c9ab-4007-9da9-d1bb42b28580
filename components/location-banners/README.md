# LocationBanners Component

The core React component for displaying banners anywhere in the Multiverse application. Drop it in, specify a location, and banners appear automatically.

## 🚀 Quick Start

```tsx
import { LocationBanners } from '@components/location-banners';
import { BannerLocation } from '@controllers/banner-service';

<LocationBanners
    location={BannerLocation.APP_HEADER}
    dataIdPrefix="app-header"
/>
```


> Start here. This component renders whatever the Banner Service provides for the chosen location. Use programmatic `addBanner` for global/app flows, and route `banners: () => [...]` for route-scoped, reactive banners.

## ✨ Features

## ✅ Do / ❌ Don’t
- ✅ Use `LocationBanners` at APP_HEADER, PAGE_HEADER, DOMAIN_CONTENT
- ✅ Use `dataIdPrefix` ending with "-banner" to get container ids like "...-banners"
- ✅ Keep display-only: add banners via the service, not here
- ❌ Don’t pass window state; the service reads route state for you
- ❌ Don’t stack custom spacing; the component handles stacking


- **🎯 Location-based** - Works with any `BannerLocation`
- **⚡ MobX reactive** - Auto-updates when banners change
- **📚 Stacked display** - Multiple banners with consistent spacing
- **🎨 Cosmos styled** - Uses design system components
- **🔧 Dismissible** - Individual banner dismiss functionality

## 🎯 Banner Locations

```
APPLICATION LAYOUT                 USAGE EXAMPLES
──────────────────                 ──────────────

┌─────────────────────────────┐    <LocationBanners
│ APP_HEADER                  │      location={BannerLocation.APP_HEADER}
│ (Full width at top)         │      dataIdPrefix="app-header-banner" />
├─────────────────────────────┤
│ PAGE_HEADER                 │    <LocationBanners
│ (Above page content)        │      location={BannerLocation.PAGE_HEADER}
├─────────────────────────────┤      dataIdPrefix="page-header-banner" />
│ Main Content Area           │
│ ┌─────────────────────────┐ │    <LocationBanners
│ │ DOMAIN_CONTENT          │ │      location={BannerLocation.DOMAIN_CONTENT}
│ │ (In main content area)  │ │      dataIdPrefix="domain-content-banner" />
│ └─────────────────────────┘ │
└─────────────────────────────┘
```

## 📋 API

```typescript
interface LocationBannersProps {
    location: BannerLocation;     // Where to show banners
    dataIdPrefix: string;         // For data-id attributes
}
```

### Common Usage
```tsx
// App Header - system-wide messages
<LocationBanners location={BannerLocation.APP_HEADER} dataIdPrefix="app-header" />

// Page Header - page-specific info
<LocationBanners location={BannerLocation.PAGE_HEADER} dataIdPrefix="page-header" />

// Domain Content - content-related messages
<LocationBanners location={BannerLocation.DOMAIN_CONTENT} dataIdPrefix="domain-content" />
```

## 🛠️ How Banners Get Added

The `LocationBanners` component automatically displays banners from the banner service. You don't add banners to the component directly - instead, add them via:

### 🎯 Route-Based Banners
```tsx
// In your route file
export const clientLoader = action((): ClientLoader => {
    return {
        banners: () => {
            if (someController.shouldShowBanner) {
                return [createBannerMessage({
                    id: 'my-banner',
                    title: 'This banner reacts to state!',
                    severity: 'education',
                    location: BannerLocation.PAGE_HEADER,
                })];
            }
            return [];
        },
    };
});
```

### 🚀 Programmatic Banners
```tsx
import { sharedBannerServiceController } from '@controllers/banner-service';

// ✨ Super simple - just title!
sharedBannerServiceController.addBanner({
    title: 'Data saved!',
});

// 🚨 Error messages stay visible until dismissed
sharedBannerServiceController.addBanner({
    title: 'Something went wrong',
    severity: 'critical',
});

// 🎨 All Cosmos banner features work automatically
sharedBannerServiceController.addBanner({
    title: 'Welcome to the new feature!',
    severity: 'education',
    body: 'Click here to learn more about what changed',
});
```

> **See the [Banner Service README](../../controllers/banner-service/README.md) for complete banner creation documentation.**

## 🎨 Component Behavior

- **Empty state**: Returns `null` when no banners for the location
- **Multiple banners**: Displays in vertical stack with consistent spacing
- **Auto-dismiss**: Banners can auto-dismiss after specified time
- **Manual dismiss**: Each banner has a close button
- **MobX reactive**: Auto-updates when banner state changes

## 📍 Current Integration Points

The component is already integrated in these locations:

- **APP_HEADER** - `components/app-header/src/lib/app-header-component.tsx` (uses dataIdPrefix="app-header")
- **PAGE_HEADER** - `ui/page-header/src/lib/page-header-ui.tsx` (uses dataIdPrefix="page-header")
- **DOMAIN_CONTENT** - `ui/domain-content/src/lib/domain-content.tsx` (uses dataIdPrefix="domain-content")

## 🏷️ Data Attributes

- **Container**: `data-id="{dataIdPrefix}s"` (e.g., "app-header-banners")
- **Individual banners**: `data-id="{dataIdPrefix}-{bannerId}"`

## 🧪 Testing

```bash
# Run component tests
pnpm test components/location-banners
```

**Test Coverage:** 13/13 tests passing ✅
- Unit tests for rendering, dismissal, data attributes
- Integration tests with real banner service controller

## 🏗️ Architecture Benefits

- **🎯 Single source of truth** - One component for all banner rendering
- **🔧 Direct usage** - No wrapper functions or abstraction layers
- **📍 Explicit config** - Location and dataIdPrefix visible at usage site
- **🛠️ Easy maintenance** - Changes apply to all locations automatically
- **✅ Comprehensive testing** - Core logic tested in one place

## 🎬 Animations

Banners include smooth entry and exit animations:

### 🎯 Entry Animation
- **Scale-in effect**: Banners appear with a subtle scale animation
- **Smooth entrance**: Creates professional, polished feel

### 🚪 Exit Animation
- **Two-phase approach**: Fade out first, then collapse height
- **No layout jumps**: Content below slides up smoothly
- **400ms duration**: Fast enough to feel responsive

```
Banner Visible → User Clicks Dismiss → Phase 1: Fade Out → Phase 2: Height Collapse → Banner Removed
                                       (0-65% of animation)   (65-100% of animation)

                                       ┌─ Opacity: 1 → 0
                                       └─ Height: maintained
                                                              ┌─ Opacity: 0 (already faded)
                                                              └─ Height: collapse to 0
```

> **Note**: Animations respect `prefers-reduced-motion` for accessibility.

## 🔧 Adding a New Banner Location

1. **Add location** to `BannerLocation` enum in `@controllers/banner-service`
2. **Use LocationBanners** in your component:
   ```tsx
   <LocationBanners
       location={BannerLocation.MY_NEW_LOCATION}
       dataIdPrefix="my-new-location"
   />
   ```
3. **Update documentation** with the new location

## 📦 Module Exports

```tsx
export { LocationBanners } from './lib/location-banners.component';
export { AnimatedBanner } from './lib/animated-banner.component';
```

**Primary Usage**: Use `LocationBanners` for all banner display needs.
