import type { AiFeedbackStatus } from '@components/ai-feedback';
import { sharedAiFeedbackController } from '@controllers/ai-feedback';
import {
    sharedAIExecutionController,
    sharedMonitoringSummariesController,
} from '@controllers/monitoring';
import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import { Button } from '@cosmos/components/button';
import { Feedback } from '@cosmos/components/feedback';
import { Icon } from '@cosmos/components/icon';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { AISummaryFooter } from '@cosmos-lab/components/ai-summary-footer';
import { Divider } from '@cosmos-lab/components/divider';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { openAiFeedbackModal } from '@helpers/ai-feedback-modal';

class EventsBulkSummaryAICardModel {
    constructor() {
        makeAutoObservable(this);
    }

    get testId(): number {
        return (
            sharedWorkspaceMonitorsController.workspaceMonitorTestOverview
                ?.testId ?? -1
        );
    }

    get title(): string {
        return t`Summary of test failures`;
    }

    get isLoading(): boolean {
        return sharedMonitoringSummariesController.isProcessing;
    }

    get testName(): string {
        return (
            sharedWorkspaceMonitorsController.workspaceMonitorTestOverview
                ?.name ?? 'Test'
        );
    }

    get summaryCsvName(): string {
        return `${this.testName}-summary.csv`;
    }

    get isLoadingState(): boolean {
        return sharedMonitoringSummariesController.isLoadingState;
    }

    get isErrorState(): boolean {
        return sharedMonitoringSummariesController.isErrorState;
    }

    get body() {
        const { generateSummariesCsv } = sharedMonitoringSummariesController;

        // Loading state - show skeleton
        if (this.isLoadingState) {
            return this.renderLoadingState();
        }

        // Error state - show feedback component
        if (this.isErrorState) {
            return this.renderErrorState();
        }

        // Completed state - show CSV file info with download button + footer
        if (this.status === 'COMPLETED') {
            return this.renderCompletedState();
        }

        // Generate state - show description + generate button
        return this.renderGenerateState(generateSummariesCsv);
    }

    get status() {
        return sharedMonitoringSummariesController.aiExecutionController
            .computedSummaryStatus;
    }

    /**
     * Opens the AI feedback modal for collecting user feedback.
     */
    openFeedbackModal(feedbackStatus: AiFeedbackStatus): void {
        const { handleSubmitAiFeedback } = sharedAiFeedbackController;

        openAiFeedbackModal({
            onSubmit: (formValues) => {
                handleSubmitAiFeedback({
                    formValues,
                    executionGroups:
                        sharedAIExecutionController.executionGroups,
                });
            },
            isLoading: false,
            initialFeedbackStatus: feedbackStatus,
        });
    }

    /**
     * Renders the loading state with skeleton loaders.
     */
    renderLoadingState = (): React.JSX.Element => {
        return (
            <Stack gap="md" direction="column" data-id="1OYMHd-K">
                <Skeleton barCount={3} width="100%" />
            </Stack>
        );
    };

    /**
     * Renders the error state with feedback component.
     */
    renderErrorState = (): React.JSX.Element => {
        return (
            <Feedback
                severity="critical"
                title={t`Drata AI couldn't generate a summary.`}
                data-id="wYOfhG9_"
            />
        );
    };

    /**
     * Renders the generate state with description and generate button.
     */
    renderGenerateState = (
        generateSummariesCsv: (testId: number) => void,
    ): React.JSX.Element => {
        return (
            <Stack gap="lg" direction="column" data-id="kseHBzg6">
                <Text>{t`List each finding with associated account details, resource type, ID, and explanation of the failure.`}</Text>
                <Stack>
                    <Button
                        label={t`Generate summary`}
                        level="secondary"
                        size="md"
                        colorScheme="ai"
                        onClick={() => {
                            generateSummariesCsv(this.testId);
                        }}
                    />
                </Stack>
            </Stack>
        );
    };

    /**
     * Renders the completed state with CSV file info and download button.
     */
    renderCompletedState = (): React.JSX.Element => {
        const { downloadCsv, canDownload } =
            sharedMonitoringSummariesController;

        return (
            <Stack gap="lg" direction="column" data-id="SHl0oZIV">
                <Stack justify="between" align="center">
                    <Stack gap="sm" direction="row">
                        <Icon
                            name="PolicyCenter"
                            colorScheme="neutral"
                            size="100"
                        />
                        <Text type="title" size="100" colorScheme="neutral">
                            {this.summaryCsvName}
                        </Text>
                    </Stack>
                    {canDownload ? (
                        <Button
                            level="tertiary"
                            size="sm"
                            colorScheme="primary"
                            label={t`Download summary`}
                            startIconName="Download"
                            onClick={downloadCsv}
                        />
                    ) : (
                        <Tooltip
                            isInteractive
                            text={t`CSV is not ready for download yet`}
                        >
                            <Button
                                level="tertiary"
                                size="sm"
                                colorScheme="neutral"
                                label={t`Download summary`}
                                startIconName="Download"
                                onClick={() => {
                                    // No-op when not ready
                                }}
                            />
                        </Tooltip>
                    )}
                </Stack>

                <Divider />

                <AISummaryFooter
                    data-id="events-bulk-summary-ai-footer"
                    onFeedback={(feedbackStatus) => {
                        this.openFeedbackModal(feedbackStatus);
                    }}
                />
            </Stack>
        );
    };
}

export const sharedEventsBulkSummaryAICardModel =
    new EventsBulkSummaryAICardModel();
