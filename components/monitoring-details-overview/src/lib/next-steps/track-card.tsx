import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { observer } from '@globals/mobx';
import { sharedTrackCardModel } from '@models/monitoring-details';
import { TrackCardReadySection } from './track-card-ready-section';

export const TrackCard = observer((): React.JSX.Element => {
    const {
        title,
        trackCardStatusMessage,
        sinceDateText,
        trackCardDays,
        trackCardColorScheme,
        trackCardIcon,
        isReadyStatus,
    } = sharedTrackCardModel;

    return (
        <Box
            data-id="q6JvnF-e"
            borderRadius="borderRadiusLg"
            borderColor="neutralBorderFaded"
            borderWidth="borderWidth1"
            p="lg"
            width="100%"
        >
            <Stack direction="column" gap="lg">
                <Text type="title" size="200">
                    {title}
                </Text>
                <StatBlock
                    title={trackCardStatusMessage}
                    statValue={trackCardDays}
                    statValueColor={trackCardColorScheme}
                    state="static"
                    statIcon={trackCardIcon}
                    statIconColor={trackCardColorScheme}
                    trendDirection="down"
                    trendSentiment="negative"
                    totalText={sinceDateText}
                />
                {isReadyStatus && <TrackCardReadySection />}
            </Stack>
        </Box>
    );
});
