import { Box } from '@cosmos/components/box';
import { But<PERSON> } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Divider } from '@cosmos-lab/components/divider';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedTrackCardModel } from '@models/monitoring-details';

export const TrackCardReadySection = observer((): React.JSX.Element => {
    const { handleTestNow } = sharedTrackCardModel;

    return (
        <>
            <Divider />
            <Stack direction="column" gap="md">
                <Text type="body" colorScheme="neutral">
                    {t`This test has not been run.`}
                </Text>
                <Box width="fit-content">
                    <Button
                        label={t`Test now`}
                        level="tertiary"
                        colorScheme="primary"
                        data-id="test-now-button"
                        onClick={handleTestNow}
                    />
                </Box>
            </Stack>
        </>
    );
});
