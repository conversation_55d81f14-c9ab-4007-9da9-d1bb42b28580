import { isNil } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedMonitorFindingsController } from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { DisabledCard } from './disabled-card';
import { FixCard } from './fix-card/fix-card';
import { ManageCard } from './manage-card';
import { TrackCard } from './track-card';
import { UnusedCard } from './unused-card';

export const NextStepsBody = observer((): React.JSX.Element => {
    const { isMonitorFailinWithgNoFindingsAndWithFixNowPath, status } =
        sharedMonitoringTestDetailsController;
    const { failingResources } = sharedMonitorFindingsController;
    const { checkResultStatus } = sharedMonitoringTestDetailsController;
    const { hasTicketingConnectionWithWriteAccessForWorkspace } =
        sharedConnectionsController;

    if (status === 'DISABLED') {
        return <DisabledCard />;
    }

    if (status === 'UNUSED') {
        return <UnusedCard />;
    }

    if (isMonitorFailinWithgNoFindingsAndWithFixNowPath) {
        return (
            <Stack gap="lg" direction="column">
                <FixCard />
                <Stack gap="lg" direction="row">
                    <TrackCard />
                    {hasTicketingConnectionWithWriteAccessForWorkspace && (
                        <ManageCard />
                    )}
                </Stack>
            </Stack>
        );
    }

    return (
        <Stack gap="lg" direction="row" data-id="wI0fCYig">
            <TrackCard />
            {(isNil(failingResources) ||
                checkResultStatus === 'FAILED' ||
                checkResultStatus === 'ERROR') &&
                checkResultStatus !== 'PASSED' && <FixCard />}
            {hasTicketingConnectionWithWriteAccessForWorkspace && (
                <ManageCard />
            )}
        </Stack>
    );
});
