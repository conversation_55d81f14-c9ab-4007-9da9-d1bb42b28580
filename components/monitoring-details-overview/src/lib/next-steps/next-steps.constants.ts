export interface MonitorFixNowDestination {
    [key: string]: {
        tests: string[];
        destination: string;
    };
}

export const MONITOR_FIX_NOW_DESTINATION: MonitorFixNowDestination = {
    '/governance/policies': {
        tests: [
            '13',
            '16',
            '18',
            '27',
            '28',
            '32',
            '33',
            '34',
            '35',
            '36',
            '37',
            '39',
            '44',
            '52',
            '54',
            '56',
            '67',
            '89',
            '106',
            '128',
            '137',
        ],
        destination: 'Policy Center',
    },
    '/settings/organization/personnel-compliance/human-resources': {
        tests: ['17', '46', '58', '59', '60'],
        destination: 'Human Resources',
    },
    '/settings/organization/details/key-personnel': {
        tests: ['40', '51'],
        destination: 'Key Personnel Info',
    },
    '/settings/organization/details/information': {
        tests: ['84', '85', '145', '146', '83'],
        destination: 'Company Info',
    },
    '/connections/manage-accounts/version-control': {
        tests: ['6', '7', '9', '87', '93', '94', '97'],
        destination: 'Version Control',
    },
    '/connections/manage-accounts/infrastructure': {
        tests: ['88', '91', '92', '95', '98'],
        destination: 'Infrastructure',
    },
    '/connections/manage-accounts/observability': {
        tests: ['110'],
        destination: 'Observability',
    },
    '/connections/all': {
        tests: ['5'],
        destination: 'Connections',
    },
    '/governance/personnel': {
        tests: ['38', '43', '50'],
        destination: 'Personnel',
    },
    '/governance/personnel?identityMfaCompliance=false': {
        tests: ['86'],
        destination: 'Personnel',
    },
    '/governance/personnel?employmentStatuses%5B0%5D=UNKNOWN': {
        tests: ['96'],
        destination: 'Personnel',
    },
    '/governance/personnel?employmentStatuses%5B0%5D=CURRENT_CONTRACTOR': {
        tests: ['48', '49'],
        destination: 'Personnel',
    },
    '/governance/personnel?employmentStatuses%5B0%5D=CURRENT_EMPLOYEE': {
        tests: ['45', '55', '57'],
        destination: 'Personnel',
    },
    '/governance/personnel?bgCheckCompliance=false': {
        tests: ['47'],
        destination: 'Personnel',
    },
    '/governance/personnel?fullCompliance=false': {
        tests: ['63', '66', '64', '65'],
        destination: 'Personnel',
    },
    '/governance/personnel?employmentStatuses%5B0%5D=ALL_PERSONNEL&complianceType=failedOffboardingEvidence':
        {
            tests: ['199'],
            destination: 'Personnel',
        },
};
