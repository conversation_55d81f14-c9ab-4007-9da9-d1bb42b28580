import { isEmpty, isNil } from 'lodash-es';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Divider } from '@cosmos-lab/components/divider';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMonitoringScrollController } from '../../controllers/monitoring-scroll.controller';

export const UnusedCard = observer((): React.JSX.Element => {
    const { testDetails } = sharedMonitoringTestDetailsController;

    const supportedConnectionsCount =
        isNil(testDetails?.availableConnections) ||
        isEmpty(testDetails.availableConnections)
            ? '-'
            : testDetails.availableConnections.length.toString();

    return (
        <Box
            data-id="UnusedCard"
            borderRadius="borderRadiusLg"
            borderColor="neutralBorderFaded"
            borderWidth="borderWidth1"
            p="lg"
        >
            <Stack direction="column" gap="lg">
                <Text type="title">{t`Connect`}</Text>

                <Stack direction="column" gap="lg">
                    <StatBlock
                        title={t`Supported connections`}
                        statIcon="Connections"
                        statIconColor="education"
                        statValueColor="neutral"
                        data-id="SupportedConnectionsStatBlock"
                        statValue={supportedConnectionsCount}
                    />

                    <Divider />

                    <Stack direction="column" gap="md" align="start">
                        <Text type="body" colorScheme="neutral">
                            {t`Connect to one or more of the supported connections to start using this test.`}
                        </Text>

                        <Button
                            label={t`View connections`}
                            level="tertiary"
                            colorScheme="primary"
                            data-id="ViewConnectionsLink"
                            onClick={
                                sharedMonitoringScrollController.triggerScrollToConnections
                            }
                        />
                    </Stack>
                </Stack>
            </Stack>
        </Box>
    );
});
