import { findKey } from 'lodash-es';
import { routeController } from '@controllers/route';
import { MONITOR_FIX_NOW_DESTINATION } from '../next-steps/next-steps.constants';

export interface MonitorFixNowDestinationResult {
    tests: string[];
    destination: string;
    route: string;
}

export const buildPolicyGroupsRoute = (
    policyGroups: { id: string }[],
): string => {
    const groupsUrlParams = policyGroups.map(
        (group, index) => `groupIds%5B${index}%5D=${group.id}`,
    );

    return `${routeController.userPartOfUrl}/governance/personnel/?${groupsUrlParams.join(
        '&',
    )}&sort=ACCEPTED_POLICIES&sortDir=ASC`;
};

export interface MonitorFixNowDestination {
    [key: string]: {
        tests: string[];
        destination: string;
    };
}

export type MonitorFixNowDestinationOutput =
    | MonitorFixNowDestinationResult
    | null
    | string;

export const getMonitorFixNowDestination = ({
    testId,
    policyGroups,
    monitorHasPolicyGroups = false,
    isPolicyScopeNone = false,
}: {
    testId: string;
    policyGroups: { id: string }[];
    monitorHasPolicyGroups: boolean;
    isPolicyScopeNone: boolean;
}): MonitorFixNowDestinationOutput => {
    if (monitorHasPolicyGroups) {
        return buildPolicyGroupsRoute(policyGroups);
    }

    const route = findKey(MONITOR_FIX_NOW_DESTINATION, (destination) =>
        destination.tests.includes(testId),
    );

    if (!route || isPolicyScopeNone) {
        return null;
    }

    return {
        ...MONITOR_FIX_NOW_DESTINATION[route],
        route: `${routeController.userPartOfUrl}${route}`,
    } as MonitorFixNowDestinationResult;
};
