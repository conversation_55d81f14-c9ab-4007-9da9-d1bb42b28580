import { beforeEach, describe, expect, test, vi } from 'vitest';
import { routeController } from '@controllers/route';
import {
    buildPolicyGroupsRoute,
    getMonitorFixNowDestination,
} from './next-steps.helpers';

// Mock the routeController module
vi.mock('@controllers/route', () => ({
    routeController: {
        userPartOfUrl: '/test-workspace',
    },
}));

describe('next steps card helpers', () => {
    beforeEach(() => {
        // Reset the mock before each test
        vi.mocked(routeController).userPartOfUrl = '/test-workspace';
    });

    describe('buildPolicyGroupsRoute', () => {
        test('should return correct route for policy groups', () => {
            const policyGroups = [{ id: 'group1' }, { id: 'group2' }];
            const result = buildPolicyGroupsRoute(policyGroups);

            expect(result).toBe(
                `${routeController.userPartOfUrl}/governance/personnel/?groupIds%5B0%5D=group1&groupIds%5B1%5D=group2&sort=ACCEPTED_POLICIES&sortDir=ASC`,
            );
        });

        test('should return correct route for single policy group', () => {
            const policyGroups = [{ id: 'group1' }];
            const result = buildPolicyGroupsRoute(policyGroups);

            expect(result).toBe(
                `${routeController.userPartOfUrl}/governance/personnel/?groupIds%5B0%5D=group1&sort=ACCEPTED_POLICIES&sortDir=ASC`,
            );
        });
    });

    describe('getMonitorFixNowDestination', () => {
        test('should return a string when monitorHasPolicyGroups is true', () => {
            const result = getMonitorFixNowDestination({
                testId: '1',
                policyGroups: [{ id: 'group1' }],
                monitorHasPolicyGroups: true,
                isPolicyScopeNone: false,
            });

            expect(result).toBe(
                `${routeController.userPartOfUrl}/governance/personnel/?groupIds%5B0%5D=group1&sort=ACCEPTED_POLICIES&sortDir=ASC`,
            );
        });

        test('should return null when isPolicyScopeNone is true', () => {
            const result = getMonitorFixNowDestination({
                testId: '1',
                policyGroups: [],
                monitorHasPolicyGroups: false,
                isPolicyScopeNone: true,
            });

            expect(result).toBeNull();
        });

        test('should return MonitorFixNowDestinationResult when a valid route is found', () => {
            const result = getMonitorFixNowDestination({
                testId: '13',
                policyGroups: [],
                monitorHasPolicyGroups: false,
                isPolicyScopeNone: false,
            });

            expect(result).toStrictEqual({
                tests: expect.arrayContaining(['13']),
                destination: 'Policy Center',
                route: `${routeController.userPartOfUrl}/governance/policies`,
            });
        });

        test('should return correct workspace-aware route for infrastructure tests', () => {
            const result = getMonitorFixNowDestination({
                testId: '88',
                policyGroups: [],
                monitorHasPolicyGroups: false,
                isPolicyScopeNone: false,
            });

            expect(result).toStrictEqual({
                tests: expect.arrayContaining(['88']),
                destination: 'Infrastructure',
                route: `${routeController.userPartOfUrl}/connections/manage-accounts/infrastructure`,
            });
        });

        test('should return correct workspace-aware route for human resources tests', () => {
            const result = getMonitorFixNowDestination({
                testId: '17',
                policyGroups: [],
                monitorHasPolicyGroups: false,
                isPolicyScopeNone: false,
            });

            expect(result).toStrictEqual({
                tests: expect.arrayContaining(['17']),
                destination: 'Human Resources',
                route: `${routeController.userPartOfUrl}/settings/organization/personnel-compliance/human-resources`,
            });
        });
    });
});
