import { isString } from 'lodash-es';
import { t } from '@globals/i18n/macro';
import { getMonthLabel } from '@helpers/date-time';

export const getLegendLabels = (): Record<
    'passed' | 'failed' | 'error',
    string
> => ({
    passed: t`Passed`,
    failed: t`Failed`,
    error: t`Error`,
});

export const getHistoricalResultsDownloadOptions = (): {
    id: string;
    label: string;
}[] => [
    { id: 'png', label: t`Download as PNG` },
    { id: 'svg', label: t`Download as SVG` },
    { id: 'csv', label: t`Download as CSV` },
];

/**
 * Convert API label (e.g., "MONTH_9") to readable month name (e.g., "Sep").
 */
export const convertApiLabelToMonthName = (apiLabel: string): string => {
    if (!apiLabel || !isString(apiLabel)) {
        return 'Unknown';
    }

    if (apiLabel.startsWith('MONTH_')) {
        const monthNumber = parseInt(apiLabel.replace('MONTH_', ''), 10);

        if (isNaN(monthNumber) || monthNumber < 1 || monthNumber > 12) {
            return apiLabel;
        }

        try {
            return getMonthLabel(monthNumber, 'short');
        } catch {
            return apiLabel;
        }
    }

    return apiLabel;
};

/**
 * Create a canvas element with specified dimensions.
 * This is a controlled DOM manipulation for image processing.
 */
export const createCanvas = (
    width: number,
    height: number,
): HTMLCanvasElement => {
    // eslint-disable-next-line custom/no-direct-dom-manipulation -- Canvas creation is required for image processing
    const canvas = document.createElement('canvas');

    canvas.width = width;

    canvas.height = height;

    return canvas;
};

/**
 * Convert snapdom PNG image to blob using canvas.
 */
export const convertPngImageToBlob = async (pngImage: {
    width: number;
    height: number;
    src: string;
}): Promise<Blob> => {
    const canvas = createCanvas(pngImage.width, pngImage.height);
    const ctx = canvas.getContext('2d');

    if (!ctx) {
        throw new Error('Could not get canvas context');
    }

    await new Promise<void>((resolve, reject) => {
        const img = new Image();

        img.onload = () => {
            ctx.drawImage(img, 0, 0);
            resolve();
        };
        img.onerror = reject;
        img.src = pngImage.src;
    });

    return new Promise<Blob>((resolve, reject) => {
        canvas.toBlob(
            (blob) => {
                if (blob) {
                    resolve(blob);
                } else {
                    reject(new Error('Failed to create blob from canvas'));
                }
            },
            'image/png',
            0.95,
        );
    });
};

/**
 * Generate CSV content from historical data.
 */
export const generateHistoricalDataCsv = (historicalData: {
    labels: string[];
    passed: number[];
    failed: number[];
    errored: number[];
}): string => {
    const csvHeaders = [
        'category',
        'Total Passed',
        'Total Failed',
        'Total Errored',
    ];
    const csvRows = [csvHeaders.join(',')];

    historicalData.labels.forEach((label: string, index: number) => {
        const passed = historicalData.passed[index] || 0;
        const failed = historicalData.failed[index] || 0;
        const errored = historicalData.errored[index] || 0;
        const readableMonth = convertApiLabelToMonthName(label);

        csvRows.push(
            [
                readableMonth,
                passed.toString(),
                failed.toString(),
                errored.toString(),
            ].join(','),
        );
    });

    return csvRows.join('\n');
};
