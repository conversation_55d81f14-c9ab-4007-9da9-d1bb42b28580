import { isError, isNil } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import { monitorsControllerGetControlTestHistoryOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedQuery,
    runInAction,
    when,
} from '@globals/mobx';
import { sharedSnapdomController, type SnapdomOptions } from '@globals/snapdom';
import { sharedWorkspacesController } from '@globals/workspaces';
import { downloadBlob } from '@helpers/download-file';
import {
    convertPngImageToBlob,
    generateHistoricalDataCsv,
} from '../helpers/historical-results.helper';

class HistoricalResultsController {
    constructor() {
        makeAutoObservable(this);
    }

    historicalResultsQuery = new ObservedQuery(
        monitorsControllerGetControlTestHistoryOptions,
    );

    loadHistoricalResults = (testId: number): void => {
        if (isNil(testId)) {
            throw new Error('Test ID is required');
        }

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    throw new Error('Workspace not found');
                }

                this.historicalResultsQuery.load({
                    path: {
                        xProductId: currentWorkspace.id,
                    },
                    query: {
                        testId,
                        reportInterval: 'MONTHLY',
                    },
                });

                when(
                    () => !this.historicalResultsQuery.isLoading,
                    () => {
                        if (!this.historicalResultsQuery.hasError) {
                            return;
                        }

                        const { error } = this.historicalResultsQuery;

                        logger.error({
                            message: 'Failed to load historical results',
                            additionalInfo: {
                                action: 'loadHistoricalResults',
                                testId,
                            },
                            errorObject: {
                                message: isError(error)
                                    ? error.message
                                    : 'Unknown error',
                                statusCode: 'unknown',
                            },
                        });

                        let errorDescription = t`Unable to load historical results. Please try again.`;

                        if (isError(error)) {
                            const statusCode =
                                'statusCode' in error
                                    ? error.statusCode
                                    : undefined;

                            switch (statusCode) {
                                case 404: {
                                    errorDescription = t`No historical results found for this test.`;
                                    break;
                                }
                                case 403: {
                                    errorDescription = t`You don't have permission to view these results.`;
                                    break;
                                }
                                case 500: {
                                    errorDescription = t`Historical data is temporarily unavailable. Please try again later.`;
                                    break;
                                }
                            }
                        }

                        snackbarController.addSnackbar({
                            id: `historical-results-load-error-${testId}-${Date.now()}`,
                            props: {
                                title: t`Failed to load data`,
                                description: errorDescription,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    },
                );
            },
        );
    };

    downloadHistoricalResults = (
        testId: number,
        format: 'CSV' | 'PNG' | 'SVG',
    ): void => {
        if (isNil(testId)) {
            throw new Error('Test ID is required');
        }

        switch (format) {
            case 'CSV': {
                this.downloadHistoricalDataAsCsv(
                    this.historicalResultsQuery.data,
                    testId,
                );
                break;
            }
            case 'PNG':
            case 'SVG': {
                // eslint-disable-next-line custom/no-direct-dom-manipulation -- Required for chart download functionality
                const chartElement = document.querySelector<HTMLDivElement>(
                    `#historical-results-chart-${testId}`,
                );

                if (!chartElement) {
                    logger.info({
                        message: 'Chart element not found in DOM',
                        additionalInfo: {
                            action: 'downloadHistoricalResultsChart',
                            format,
                            testId,
                            expectedId: `historical-results-chart-${testId}`,
                        },
                    });

                    snackbarController.addSnackbar({
                        id: `historical-results-chart-element-error-${testId}-${Date.now()}`,
                        props: {
                            title: t`Download failed`,
                            description: t`Chart not ready for download. Please wait a moment and try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                this.downloadChartAsImage(chartElement, format, testId).catch(
                    (error) => {
                        logger.error({
                            message: 'Unhandled error in chart download',
                            additionalInfo: {
                                action: 'downloadHistoricalResults',
                                format,
                                testId,
                            },
                            errorObject: {
                                message: isError(error)
                                    ? error.message
                                    : 'Unknown error',
                                statusCode: 'unknown',
                            },
                        });
                    },
                );
                break;
            }
            default: {
                throw new Error(
                    `Unsupported download format: ${format as string}`,
                );
            }
        }
    };

    navigateToHistory = (testId: number): void => {
        if (isNil(testId)) {
            throw new Error('Test ID is required');
        }

        sharedProgrammaticNavigationController.navigateTo(
            `${routeController.userPartOfUrl}/compliance/monitoring/production/${testId}/history`,
        );
    };

    downloadHistoricalDataAsCsv = (
        historicalData: {
            labels: string[];
            passed: number[];
            failed: number[];
            errored: number[];
        } | null,
        testId: number,
    ): void => {
        if (!historicalData) {
            return;
        }

        try {
            const csvContent = generateHistoricalDataCsv(historicalData);
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const timestamp = new Date().toISOString().split('T')[0];
            const filename = `production-test-historical-results-${timestamp}.csv`;

            downloadBlob(blob, filename);

            snackbarController.addSnackbar({
                id: `historical-results-csv-download-success-${testId}-${Date.now()}`,
                props: {
                    title: t`Download completed`,
                    description: t`Historical results downloaded as CSV successfully.`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } catch (error) {
            logger.error({
                message: 'Failed to generate CSV from historical data',
                additionalInfo: { testId },
                errorObject: {
                    message: isError(error) ? error.message : 'Unknown error',
                    statusCode: 'unknown',
                },
            });

            snackbarController.addSnackbar({
                id: `historical-results-csv-download-error-${testId}-${Date.now()}`,
                props: {
                    title: t`Download failed`,
                    description: t`Unable to download historical results as CSV. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    };

    downloadChartAsImage = async (
        chartElement: HTMLDivElement,
        format: 'PNG' | 'SVG',
        testId: number,
    ): Promise<void> => {
        try {
            const options: SnapdomOptions = {
                type: format.toLowerCase() as 'png' | 'svg',
                scale: format === 'SVG' ? 1 : 2,
                backgroundColor: '#ffffff',
                quality: format === 'PNG' ? 0.95 : undefined,
            };

            const validChartRef = { current: chartElement };

            await sharedSnapdomController.captureElement(
                validChartRef,
                options,
            );

            const { error, result } = runInAction(() => ({
                error: sharedSnapdomController.error,
                result: sharedSnapdomController.result,
            }));

            if (error) {
                throw error;
            }

            if (result) {
                const timestamp = new Date().toISOString().split('T')[0];
                const fileExtension = format.toLowerCase();
                const filename = `production-test-historical-results-${timestamp}.${fileExtension}`;

                if (format === 'SVG') {
                    try {
                        const pngImage = await runInAction(() =>
                            result.toPng(),
                        );

                        // Create SVG wrapper with embedded PNG image
                        /* cspell:disable */
                        const xlinkNamespace = 'http://www.w3.org/1999/xlink';
                        const svgContent = [
                            '<?xml version="1.0" encoding="UTF-8"?>',
                            `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="${xlinkNamespace}"`,
                            `     width="${pngImage.width}" height="${pngImage.height}" viewBox="0 0 ${pngImage.width} ${pngImage.height}">`,
                            '  <title>Historical Results Chart</title>',
                            `  <image x="0" y="0" width="${pngImage.width}" height="${pngImage.height}" xlink:href="${pngImage.src}"/>`,
                            '</svg>',
                        ].join('\n');
                        /* cspell:enable */

                        const blob = new Blob([svgContent], {
                            type: 'image/svg+xml',
                        });

                        downloadBlob(blob, filename);

                        snackbarController.addSnackbar({
                            id: `historical-results-chart-download-success-${testId}-${Date.now()}`,
                            props: {
                                title: t`Download completed`,
                                description: t`Historical results chart downloaded as ${format} successfully.`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        runInAction(() => {
                            sharedSnapdomController.reset();
                        });

                        return;
                    } catch {
                        // SVG fallback to PNG
                        const pngFilename = filename.replace('.svg', '.png');
                        const pngImage = await runInAction(() =>
                            result.toPng(),
                        );

                        const blob = await convertPngImageToBlob(pngImage);

                        downloadBlob(blob, pngFilename);

                        snackbarController.addSnackbar({
                            id: `historical-results-svg-fallback-${testId}-${Date.now()}`,
                            props: {
                                title: t`Download completed`,
                                description: t`SVG not supported for this chart type. Downloaded as PNG instead.`,
                                severity: 'warning',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        runInAction(() => {
                            sharedSnapdomController.reset();
                        });

                        return;
                    }
                } else {
                    const pngImage = await runInAction(() => result.toPng());
                    const blob = await convertPngImageToBlob(pngImage);

                    downloadBlob(blob, filename);
                }

                snackbarController.addSnackbar({
                    id: `historical-results-chart-download-success-${testId}-${Date.now()}`,
                    props: {
                        title: t`Download completed`,
                        description: t`Historical results chart downloaded as ${format} successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                runInAction(() => {
                    sharedSnapdomController.reset();
                });
            }
        } catch (error) {
            logger.error({
                message: 'Failed to download chart',
                additionalInfo: { format, testId },
                errorObject: {
                    message: isError(error) ? error.message : 'Unknown error',
                    statusCode: 'unknown',
                },
            });

            snackbarController.addSnackbar({
                id: `historical-results-chart-download-error-${testId}-${Date.now()}`,
                props: {
                    title: t`Download failed`,
                    description: t`Unable to download chart as ${format}. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            runInAction(() => {
                sharedSnapdomController.reset();
            });
        }
    };
}

export const sharedHistoricalResultsController =
    new HistoricalResultsController();
