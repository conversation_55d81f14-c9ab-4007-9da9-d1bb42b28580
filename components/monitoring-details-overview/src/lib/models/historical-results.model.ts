import { isEmpty, isNumber } from 'lodash-es';
import { activeMonitoringDetailsController } from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import type { Action } from '@cosmos/components/action-stack';
import type { ControlTestInstanceHistoryResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, runInAction } from '@globals/mobx';
import { HISTORICAL_RESULTS_LEGEND_ITEMS } from '../constants/historical-results.constants';
import { sharedHistoricalResultsController } from '../controllers/historical-results.controller';
import {
    convertApiLabelToMonthName,
    getHistoricalResultsDownloadOptions,
    getLegendLabels,
} from '../helpers/historical-results.helper';

export interface HistoricalChartData {
    month: string;
    passed: number;
    failed: number;
    error: number;
}

export class HistoricalResultsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get testId(): number | undefined {
        return activeMonitoringDetailsController.monitorDetailsData?.testId;
    }

    get historicalData(): ControlTestInstanceHistoryResponseDto | null {
        return (
            sharedHistoricalResultsController.historicalResultsQuery.data ??
            null
        );
    }

    get isLoading(): boolean {
        return sharedHistoricalResultsController.historicalResultsQuery
            .isLoading;
    }

    get error(): Error | null {
        return sharedHistoricalResultsController.historicalResultsQuery.error;
    }

    get hasData(): boolean {
        const data = this.historicalData;

        if (!data?.labels || isEmpty(data.labels)) {
            return false;
        }

        // Check if there's any actual numeric data in the arrays (not just labels/months)
        const { passed, failed, errored } = data;
        const hasPassedData = passed.some(
            (value) => isNumber(value) && value > 0,
        );
        const hasFailedData = failed.some(
            (value) => isNumber(value) && value > 0,
        );
        const hasErroredData = errored.some(
            (value) => isNumber(value) && value > 0,
        );

        return hasPassedData || hasFailedData || hasErroredData;
    }

    get feedbackMessage(): string | null {
        const { testDetails } = sharedMonitoringTestDetailsController;

        if (!testDetails?.draft) {
            return null;
        }

        const isDisabledOrUnused =
            testDetails.checkStatus === 'UNUSED' ||
            testDetails.checkStatus === 'DISABLED';

        if (isDisabledOrUnused && !this.hasData) {
            return t`There is no historical data for this test.`;
        }
        if (isDisabledOrUnused && this.hasData) {
            return t`This is historical data from when this test was enabled.`;
        }

        return t`This data will reset when you publish this test.`;
    }

    get chartDescription(): string {
        return t`The number of times a test ran, and the result of that run.`;
    }

    get chartData(): HistoricalChartData[] {
        if (!this.historicalData || !this.hasData) {
            return [];
        }

        const { labels, passed, failed, errored } = this.historicalData;

        return labels.map((label: string, index: number) => ({
            month: convertApiLabelToMonthName(label),
            passed: passed[index] ?? 0,
            failed: failed[index] ?? 0,
            error: errored[index] ?? 0,
        }));
    }

    get legendData(): { label: string; value: number; color: string }[] {
        const totals = { passed: 0, failed: 0, error: 0 };

        for (const data of this.chartData) {
            totals.passed = totals.passed + data.passed;
            totals.failed = totals.failed + data.failed;
            totals.error = totals.error + data.error;
        }

        const labels = getLegendLabels();

        return HISTORICAL_RESULTS_LEGEND_ITEMS.map((item) => ({
            label: labels[item.key],
            value: totals[item.key],
            color: item.color,
        }));
    }

    get downloadOptions(): { id: string; label: string }[] {
        return getHistoricalResultsDownloadOptions();
    }

    get shouldShowEmptyState(): boolean {
        return Boolean(this.error) || (!this.hasData && !this.isLoading);
    }

    get shouldShowErrorState(): boolean {
        return Boolean(this.error);
    }

    get shouldShowNoDataState(): boolean {
        return !this.hasData && !this.isLoading && !this.error;
    }

    handleDownload = (
        format: 'CSV' | 'PNG' | 'SVG',
        testType: 'production' | 'codebase' = 'production',
    ): void => {
        if (
            !this.testId ||
            !Number.isInteger(this.testId) ||
            this.testId <= 0
        ) {
            logger.error('Invalid testId for download operation');

            return;
        }

        logger.info({
            message: 'Download initiated',
            additionalInfo: {
                format,
                testId: this.testId,
                testType,
            },
        });

        sharedHistoricalResultsController.downloadHistoricalResults(
            this.testId,
            format,
            testType,
        );
    };

    handleViewHistory = (): void => {
        if (this.testId && !isNaN(this.testId)) {
            sharedHistoricalResultsController.navigateToHistory(this.testId);
        }
    };

    get cardActions(): Action[] {
        const { hasData, downloadOptions, handleViewHistory } = this;

        return [
            {
                id: 'download-dropdown',
                actionType: 'dropdown',
                typeProps: {
                    label: t`Download`,
                    level: 'tertiary',
                    size: 'sm',
                    startIconName: 'Download',
                    isIconOnly: true,
                    items: downloadOptions.map((option) => ({
                        id: option.id,
                        type: 'item',
                        label: option.label,
                        disabled: !hasData,
                        onSelect: () => {
                            runInAction(() => {
                                const { source } =
                                    sharedMonitoringTestDetailsController;
                                const testType =
                                    source === 'ACORN'
                                        ? 'codebase'
                                        : 'production';

                                this.handleDownload(
                                    option.id.toUpperCase() as
                                        | 'CSV'
                                        | 'PNG'
                                        | 'SVG',
                                    testType,
                                );
                            });
                        },
                    })),
                },
            },
            {
                id: 'view-history-button',
                actionType: 'button',
                typeProps: {
                    label: t`View history`,
                    level: 'secondary',
                    size: 'sm',
                    onClick: handleViewHistory,
                },
            },
        ];
    }
}

export const sharedHistoricalResultsModel = new HistoricalResultsModel();
