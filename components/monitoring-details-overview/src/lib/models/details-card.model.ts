import { isError } from 'lodash-es';
import { sharedMonitoringCodeController } from '@controllers/monitoring';
import {
    activeMonitoringController,
    sharedMonitoringDetailsUpdateMutationController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import type { Action } from '@cosmos/components/action-stack';
import type {
    MonitorV2ControlTestInstanceDetailsResponseDto,
    MonitorV2ControlTestInstanceOverviewResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, reaction, runInAction, when } from '@globals/mobx';
import { getSourceLabel } from '../helpers/source-label';

// cspell:ignore Toggleable
type ToggleableStatus = 'ENABLED' | 'DISABLED';

export class DetailsCardModel {
    _pendingStatusChange: ToggleableStatus | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    get monitorDetailsData(): MonitorV2ControlTestInstanceDetailsResponseDto | null {
        return activeMonitoringController.monitorDetailsData;
    }

    get isMonitorLoading(): boolean {
        return activeMonitoringController.isMonitorLoading;
    }

    get testDetails(): MonitorV2ControlTestInstanceOverviewResponseDto | null {
        return sharedMonitoringTestDetailsController.testDetails;
    }

    get isTestDetailsLoading(): boolean {
        return sharedMonitoringTestDetailsController.isLoading;
    }

    get isLoading(): boolean {
        return this.isMonitorLoading || this.isTestDetailsLoading;
    }

    get hasData(): boolean {
        return Boolean(this.monitorDetailsData) || Boolean(this.testDetails);
    }

    get description(): string {
        return (
            this.testDetails?.monitorInstances[0]
                ?.evidenceCollectionDescription || ''
        );
    }

    get sourceValue(): string {
        return (
            this.testDetails?.source || this.monitorDetailsData?.source || ''
        );
    }

    get source(): string {
        return getSourceLabel(this.sourceValue);
    }

    get checkStatus(): MonitorV2ControlTestInstanceOverviewResponseDto['checkStatus'] {
        const status =
            this.testDetails?.checkStatus ||
            this.monitorDetailsData?.checkStatus;

        // Ensure we return a valid status type
        if (
            status === 'ENABLED' ||
            status === 'DISABLED' ||
            status === 'TESTING' ||
            status === 'UNUSED' ||
            status === 'NEW'
        ) {
            return status;
        }

        return 'ENABLED';
    }

    get name(): string {
        return this.testDetails?.name || this.monitorDetailsData?.name || '';
    }

    get isCustomTest(): boolean {
        return this.sourceValue === 'CUSTOM';
    }

    get isCustomDraftOrPublished(): boolean {
        return this.isCustomTest;
    }

    get showHelpArticle(): boolean {
        return this.sourceValue === 'DRATA';
    }

    get testId(): number | undefined {
        return this.testDetails?.testId || this.monitorDetailsData?.testId;
    }

    getCardActions = (
        isEditing: boolean,
        triggerSubmit: () => Promise<boolean>,
        handleEdit: () => void,
        handleCancel: () => void,
    ): Action[] => {
        if (!this.isCustomDraftOrPublished) {
            return [];
        }

        if (isEditing) {
            const actions: Action[] = [
                {
                    id: 'save',
                    actionType: 'button',
                    typeProps: {
                        label: t`Save`,
                        level: 'primary',
                        isLoading:
                            sharedMonitoringDetailsUpdateMutationController.isUpdating,
                        onClick: (): void => {
                            triggerSubmit().catch((error: unknown) => {
                                const errorMessage = isError(error)
                                    ? error.message
                                    : 'Unknown error occurred';

                                logger.error(
                                    `Failed to submit form: ${errorMessage}`,
                                );
                            });
                        },
                    },
                },
            ];

            // Only add cancel button if not updating
            if (!sharedMonitoringDetailsUpdateMutationController.isUpdating) {
                actions.push({
                    id: 'cancel',
                    actionType: 'button',
                    typeProps: {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: handleCancel,
                    },
                });
            }

            return actions;
        }

        return [
            {
                id: 'edit',
                actionType: 'button',
                typeProps: {
                    label: t`Edit`,
                    level: 'secondary',
                    onClick: handleEdit,
                },
            },
        ];
    };

    handleSubmit = (
        values: { name: string; description: string | undefined },
        onSuccess: () => void,
    ): void => {
        if (!this.testId) {
            logger.error('No test ID available for update');

            return;
        }

        sharedMonitoringDetailsUpdateMutationController.updateMonitorDetails(
            this.testId,
            {
                name: values.name,
                description: values.description,
            },
            onSuccess,
        );
    };

    get isTestingStatus(): boolean {
        return this.checkStatus === 'TESTING';
    }

    get isUnusedStatus(): boolean {
        return this.checkStatus === 'UNUSED';
    }

    get isToggleableStatus(): boolean {
        return (
            this.checkStatus === 'ENABLED' || this.checkStatus === 'DISABLED'
        );
    }

    get displayStatus(): string {
        return this._pendingStatusChange ?? this.checkStatus;
    }

    get isUpdatingStatus(): boolean {
        return sharedMonitoringCodeController.updateStatusMutation.isPending;
    }

    toggleStatus = (newStatus: ToggleableStatus): void => {
        if (!this.testId) {
            return;
        }

        const originalStatus = this.checkStatus;

        runInAction(() => {
            if (newStatus === 'ENABLED') {
                this._pendingStatusChange = newStatus;
            } else {
                this._pendingStatusChange = null;
            }
        });

        sharedMonitoringCodeController.handleMonitorStatusToggle(
            this.testId,
            newStatus,
        );

        // Watch for mutation completion to handle errors
        when(
            () =>
                !sharedMonitoringCodeController.updateStatusMutation.isPending,
            () => {
                if (
                    sharedMonitoringCodeController.updateStatusMutation.hasError
                ) {
                    // Revert to original status on error
                    runInAction(() => {
                        if (
                            originalStatus === 'ENABLED' ||
                            originalStatus === 'DISABLED'
                        ) {
                            this._pendingStatusChange =
                                originalStatus as ToggleableStatus;
                        } else {
                            this._pendingStatusChange = null;
                        }
                    });
                }
            },
        );
    };

    /**
     * Get props for the status toggle component.
     */
    get statusToggleProps(): {
        'data-testid': string;
        'data-id': string;
        orientation: 'horizontal';
        selectedOption: string;
        options: {
            label: string;
            value: 'ENABLED' | 'DISABLED';
            disabled: boolean;
        }[];
        onChange: (value: string | undefined) => void;
    } {
        return {
            'data-testid': 'renderStatusToggle',
            'data-id': 'status-toggle',
            orientation: 'horizontal' as const,
            selectedOption: this.displayStatus,
            options: [
                {
                    label: t`Enabled`,
                    value: 'ENABLED' as const,
                    disabled: this.isUpdatingStatus,
                },
                {
                    label: t`Disabled`,
                    value: 'DISABLED' as const,
                    disabled: this.isUpdatingStatus,
                },
            ],
            onChange: (value: string | undefined) => {
                if (value === 'ENABLED' || value === 'DISABLED') {
                    this.toggleStatus(value as ToggleableStatus);
                }
            },
        };
    }

    /**
     * Get props for the status metadata badge.
     */
    get statusMetadataProps(): {
        'data-id': string;
        colorScheme: 'neutral';
        type: 'status';
        label: string;
    } {
        return {
            'data-id': 'status-badge',
            colorScheme: 'neutral' as const,
            type: 'status' as const,
            label: t`Testing...`,
        };
    }

    /**
     * Get props for the unused status text.
     */
    get unusedStatusTextProps(): {
        id: string;
        size: '100';
        children: string;
    } {
        return {
            id: 'toggle-label-unused',
            size: '100' as const,
            children: t`Unused`,
        };
    }

    clearPendingChange = (): void => {
        runInAction(() => {
            this._pendingStatusChange = null;
        });
    };
}

export const sharedDetailsCardModel = new DetailsCardModel();

// Clear pending changes when the actual status changes
reaction(
    () => sharedDetailsCardModel.checkStatus,
    () => {
        sharedDetailsCardModel.clearPendingChange();
    },
);
