import type { JSX } from 'react';
import { Card } from '@cosmos/components/card';
import { Feedback } from '@cosmos/components/feedback';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { HistoricalResultsChart } from './components/historical-results-chart.component';
import { createEmptyStateCard } from './helpers/historical-results-card.helpers';
import { sharedHistoricalResultsModel } from './models/historical-results.model';

export const HistoricalResultsCard = observer((): JSX.Element | null => {
    const {
        error,
        hasData,
        isLoading,
        feedbackMessage,
        chartDescription,
        chartData,
        legendData,
        cardActions,
        testId,
    } = sharedHistoricalResultsModel;

    if (!testId) {
        return null;
    }

    if (error) {
        return createEmptyStateCard(
            t`Unable to load historical results`,
            t`There was an error loading the historical data. Please try again.`,
            'historical-results-error',
        );
    }

    if (!hasData && !isLoading) {
        return createEmptyStateCard(
            t`No historical data available`,
            t`Historical results will appear here once the test has run at least once.`,
            'historical-results-empty',
        );
    }

    return (
        <Card
            data-id="overview-historical-results-card"
            title={t`Historical results`}
            data-testid="HistoricalResultsCardView"
            isLoading={isLoading}
            actions={cardActions}
            body={
                <Stack gap="lg" direction="column">
                    {feedbackMessage && (
                        <Feedback
                            severity="primary"
                            title={feedbackMessage}
                            data-id="feedback-message"
                        />
                    )}

                    <Text type="body" size="100" colorScheme="neutral">
                        {chartDescription}
                    </Text>

                    <HistoricalResultsChart
                        showLegend
                        testId={testId}
                        chartData={chartData}
                        legendData={legendData}
                    />
                </Stack>
            }
        />
    );
});
