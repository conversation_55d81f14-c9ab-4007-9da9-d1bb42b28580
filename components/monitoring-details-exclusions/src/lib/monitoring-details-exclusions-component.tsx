import { useCallback } from 'react';
import { AppDatatable } from '@components/app-datatable';
import {
    activeMonitoringController,
    sharedMonitoringDetailsExclusionsController,
} from '@controllers/monitoring-details';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { EmptyState } from '@cosmos/components/empty-state';
import { Stack } from '@cosmos/components/stack';
import { breakpointMd } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMonitoringDetailsExclusionsTableModel } from './models/monitoring-details-exclusions-table.model';

export const MonitoringDetailsExclusionsComponent = observer(
    (): React.JSX.Element => {
        const { isLoading, exclusionsTotal, load, exclusionsData } =
            sharedMonitoringDetailsExclusionsController;

        const { lastTestResult } = activeMonitoringController;

        const loadExclusions = useCallback(
            (params: FetchDataResponseParams) => {
                load(params);
            },
            [load],
        );

        const preventSelectAllAcrossPages = useCallback(() => true, []);

        if (lastTestResult === 'ERROR') {
            return (
                <Stack height="100%" justify="center" align="center">
                    <Box width={breakpointMd}>
                        <EmptyState
                            illustrationName="Warning"
                            title={t`Cannot display exclusions due to error state.`}
                            description={t`This test was unable to generate findings due to an error that prevented it from running. Please review the details of the error to resolve it and try again.`}
                            rightAction={
                                <Button
                                    label={t`View error details`}
                                    level="secondary"
                                />
                            }
                        />
                    </Box>
                </Stack>
            );
        }

        return (
            <AppDatatable
                isRowSelectionEnabled={preventSelectAllAcrossPages}
                data-testid="MonitoringPersonnelExclusionsComponent"
                isLoading={isLoading}
                tableId="datatable-monitoring-exclusions"
                data-id="datatable-monitoring-exclusions"
                data={exclusionsData}
                total={exclusionsTotal}
                columns={sharedMonitoringDetailsExclusionsTableModel.columns}
                getRowId={(row) => row.id.toString()}
                filterProps={
                    sharedMonitoringDetailsExclusionsTableModel.filterProps
                }
                imperativeHandleRef={
                    sharedMonitoringDetailsExclusionsTableModel.datatableRef
                }
                bulkActionDropdownItems={
                    sharedMonitoringDetailsExclusionsTableModel.bulkActions
                }
                filterViewModeProps={
                    sharedMonitoringDetailsExclusionsTableModel.filterViewModeProps
                }
                emptyStateProps={
                    sharedMonitoringDetailsExclusionsTableModel.emptyStateProps
                }
                onFetchData={loadExclusions}
                onRowSelection={
                    sharedMonitoringDetailsExclusionsTableModel.handleRowSelection
                }
            />
        );
    },
);
