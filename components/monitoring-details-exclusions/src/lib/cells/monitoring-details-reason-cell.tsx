import { useRef, useState } from 'react';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Popover } from '@cosmos/components/popover';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Truncation } from '@cosmos-lab/components/truncation';
import type { MonitorExclusionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

const MAX_LENGTH_TRUNCATION = 50;

export const MonitoringDetailsReasonCell = ({
    row: { original },
}: {
    row: { original: MonitorExclusionResponseDto };
}): React.JSX.Element | null => {
    const { reason, id } = original;
    const buttonRef = useRef<HTMLButtonElement>(null);
    const [isOpen, setIsOpen] = useState(false);

    const handleClick = () => {
        setIsOpen(!isOpen);
    };

    if (!reason) {
        return null;
    }

    return (
        <Stack
            direction="column"
            align="start"
            gap="xs"
            data-id={`monitoring-exclusion-reason-${id}`}
            data-testid="MonitoringDetailsReasonCell"
        >
            <Text
                data-testid="MonitoringDetailsReasonText"
                data-id={`monitoring-exclusion-reason-text-${id}`}
            >
                <Truncation mode="end" maxLength={MAX_LENGTH_TRUNCATION}>
                    {reason}
                </Truncation>
            </Text>
            {reason.length > MAX_LENGTH_TRUNCATION && (
                <>
                    <Button
                        ref={buttonRef}
                        colorScheme="neutral"
                        data-id={`monitoring-exclusion-reason-${id}-show-more`}
                        label={t`Show more`}
                        level="tertiary"
                        size="sm"
                        type="button"
                        onClick={handleClick}
                    />
                    <Popover
                        anchor={buttonRef.current}
                        isOpen={isOpen}
                        placement="bottom-start"
                        padding="xl"
                        maxHeight={350}
                        content={
                            <Box
                                maxWidth="200px"
                                data-id={`monitoring-exclusion-reason-${id}-box`}
                            >
                                <Text
                                    data-id={`monitoring-exclusion-reason-${id}-full`}
                                >
                                    {reason}
                                </Text>
                            </Box>
                        }
                        onDismiss={() => {
                            setIsOpen(false);
                        }}
                    />
                </>
            )}
        </Stack>
    );
};
