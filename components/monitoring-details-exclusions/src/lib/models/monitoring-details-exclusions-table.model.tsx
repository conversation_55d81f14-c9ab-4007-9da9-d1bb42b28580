import { isEmpty, isString } from 'lodash-es';
import React from 'react';
import {
    closeMonitorUpdateExclusionReasonBulkModal,
    openMonitorUpdateExclusionReasonBulkModal,
} from '@components/monitor-update-exclusion-reason-modal';
import { openMonitoringRemoveExclusionConfirmationModal } from '@components/monitoring-remove-exclusions-confirmation-modal';
import {
    sharedFindingExclusionMutationController,
    sharedMonitoringDetailsExclusionsController,
    sharedMonitoringFindingsExclusionReasonController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import type {
    BulkAction,
    DatatableProps,
    DatatableRef,
    DatatableRowSelectionState,
    ExtendedDataTableColumnDef,
    FilterProps,
} from '@cosmos/components/datatable';
import type { MonitorExclusionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import { COLUMN_SIZES } from '@helpers/table';
import { MonitoringDetailsAccountNameCell } from '../cells/monitoring-details-account-name-cell';
import { MonitoringDetailsActionsCell } from '../cells/monitoring-details-actions-cell';
import { MonitoringDetailsConnectionAliasCell } from '../cells/monitoring-details-connection-alias-cell';
import { MonitoringDetailsConnectionCell } from '../cells/monitoring-details-connection-cell';
import { MonitoringDetailsDateExcludedCell } from '../cells/monitoring-details-date-excluded-cell';
import { MonitoringDetailsExcludedByCell } from '../cells/monitoring-details-excluded-by-cell';
import { MonitoringDetailsGroupCell } from '../cells/monitoring-details-group-cell';
import { MonitoringDetailsReasonCell } from '../cells/monitoring-details-reason-cell';
import { MonitoringDetailsRegionCell } from '../cells/monitoring-details-region-cell';

class MonitoringDetailsExclusionsTableModel {
    selectedTargetIds: number[] = [];
    isAllRowsSelected = false;
    datatableRef: React.RefObject<DatatableRef> =
        React.createRef<DatatableRef>();

    constructor() {
        makeAutoObservable(this);
    }

    get bulkActions(): BulkAction[] {
        return [
            {
                actionType: 'button',
                id: 'bulk-actions-update-exclusion',
                typeProps: {
                    label: t`Update reason`,
                    level: 'tertiary',
                    onClick: action(() => {
                        openMonitorUpdateExclusionReasonBulkModal({
                            onConfirm: (reason: string) => {
                                const { testId } =
                                    sharedMonitoringDetailsExclusionsController;

                                if (!testId) {
                                    return;
                                }

                                sharedMonitoringFindingsExclusionReasonController.updateExclusionReasonBulk(
                                    testId,
                                    this.selectedTargetIds,
                                    reason,
                                    () => {
                                        closeMonitorUpdateExclusionReasonBulkModal();
                                        this.datatableRef.current?.resetRowSelection();
                                    },
                                );
                            },
                        });
                    }),
                },
            },
            {
                actionType: 'button',
                id: 'bulk-actions-remove-exclusions',
                typeProps: {
                    label: t`Remove exclusions`,
                    level: 'tertiary',
                    onClick: action(() => {
                        openMonitoringRemoveExclusionConfirmationModal({
                            onConfirm: this.handleRemoveExclusionsConfirm,
                        });
                    }),
                },
            },
        ];
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;
        const selectedIds = Object.keys(selectedRows);

        this.selectedTargetIds = selectedIds.map(Number);
        this.isAllRowsSelected = isAllRowsSelected;
    };

    handleRemoveExclusionsConfirm = (): void => {
        const { testId } = sharedMonitoringDetailsExclusionsController;

        if (!testId) {
            return;
        }

        sharedFindingExclusionMutationController.removeExclusionsBulk({
            testId,
            targetIds: this.selectedTargetIds,
            onSuccess: () => {
                this.datatableRef.current?.resetRowSelection();
            },
        });
    };

    get isRemovingExclusions(): boolean {
        return sharedFindingExclusionMutationController.isRemovingExclusionsBulk;
    }

    private getTargetNameHeader(): string {
        const { category } = sharedMonitoringTestDetailsController;

        switch (category) {
            case 'VULNERABILITY': {
                return t`Vulnerability title`;
            }
            case 'TICKETING': {
                return t`Ticket title`;
            }
            case 'IDENTITY': {
                return t`Applicable personnel`;
            }
            default: {
                return t`Resource name`;
            }
        }
    }

    get targetNameFilterLabel(): string {
        const targetColumn = this.columns.find(
            (column) => column.id === 'TARGET_NAME',
        );

        if (isString(targetColumn?.header)) {
            return targetColumn.header;
        }

        return t`Resource name`;
    }

    get columns(): ExtendedDataTableColumnDef<MonitorExclusionResponseDto>[] {
        return [
            {
                id: 'ACTIONS',
                header: '',
                enableSorting: false,
                isActionColumn: true,
                enablePinning: false,
                cell: MonitoringDetailsActionsCell,
            },
            {
                id: 'TARGET_NAME',
                header: this.getTargetNameHeader(),
                accessorKey: 'displayName',
                enableSorting: true,
                isActionColumn: true,
            },
            {
                id: 'REGION',
                header: t`Region`,
                enableSorting: false,
                isActionColumn: true,
                cell: MonitoringDetailsRegionCell,
            },
            {
                id: 'CONNECTION',
                header: t`Connection`,
                enableSorting: false,
                isActionColumn: true,
                cell: MonitoringDetailsConnectionCell,
            },
            {
                id: 'CONNECTION_CLIENT_ALIAS',
                header: t`Connection ID / alias`,
                enableSorting: true,
                isActionColumn: true,
                cell: MonitoringDetailsConnectionAliasCell,
                accessorKey: 'connectionClientAlias',
            },
            {
                id: 'ACCOUNT_NAME_ID',
                header: t`Account name / ID`,
                enableSorting: false,
                isActionColumn: true,
                cell: MonitoringDetailsAccountNameCell,
            },
            {
                id: 'GROUP',
                header: t`Group`,
                enableSorting: true,
                isActionColumn: true,
                cell: MonitoringDetailsGroupCell,
            },
            {
                id: 'CREATED_BY',
                header: t`Excluded by`,
                enableSorting: true,
                isActionColumn: true,
                cell: MonitoringDetailsExcludedByCell,
                accessorKey: 'createdBy',
            },
            {
                id: 'START_DATE',
                header: t`Date excluded`,
                enableSorting: true,
                isActionColumn: true,
                cell: MonitoringDetailsDateExcludedCell,
                accessorKey: 'startDate',
            },
            {
                id: 'EXCLUSION_REASON',
                header: t`Reason`,
                accessorKey: 'reason',
                enableSorting: true,
                isActionColumn: true,
                cell: MonitoringDetailsReasonCell,
                minSize: COLUMN_SIZES.LARGE,
            },
        ];
    }

    private get connectionFilters(): FilterProps['filters'] {
        const availableConnections =
            sharedMonitoringTestDetailsController.testDetails
                ?.availableConnections ?? [];

        if (isEmpty(availableConnections)) {
            return [];
        }

        const filters: FilterProps['filters'] = [];

        const connectionAliasColumn = this.columns.find(
            (col) => col.id === 'CONNECTION_CLIENT_ALIAS',
        );

        if (connectionAliasColumn) {
            filters.push({
                filterType: 'combobox',
                id: 'connections',
                label: t`Connection by ID / alias`,
                placeholder: t`Select all that apply`,
                isMultiSelect: true,
                searchDebounce: 500,
                isLoading:
                    sharedMonitoringDetailsExclusionsController.isConnectionsLoading,
                hasMore:
                    sharedMonitoringDetailsExclusionsController.hasMoreConnections,
                options:
                    sharedMonitoringDetailsExclusionsController.connectionsData
                        .map((connection) => {
                            const { clientAlias, clientId } = connection;
                            const connectionId =
                                clientId || 'Unknown Connection';
                            const alias = clientAlias || undefined;

                            return {
                                id: connectionId,
                                label: connectionId,
                                description: alias,
                                value: JSON.stringify(connection),
                            };
                        })
                        .filter(
                            (option, index, self) =>
                                self.findIndex(
                                    (o) => o.value === option.value,
                                ) === index,
                        ),
                onFetchOptions: action(({ search, increasePage }) => {
                    sharedMonitoringDetailsExclusionsController.onFetchConnections(
                        {
                            search,
                            increasePage,
                        },
                    );
                }),
            });
        }

        return filters;
    }

    get filterProps(): FilterProps {
        const baseFilters: FilterProps['filters'] = [
            {
                filterType: 'combobox',
                id: 'targetName',
                label: this.targetNameFilterLabel,
                placeholder: t`Select all that apply`,
                isMultiSelect: true,
                searchDebounce: 500,
                isLoading:
                    sharedMonitoringDetailsExclusionsController.isResourcesLoading,
                hasMore:
                    sharedMonitoringDetailsExclusionsController.hasMoreResources,
                options:
                    sharedMonitoringDetailsExclusionsController.resourcesData
                        .map((resource) => {
                            const { resourceName } = resource;

                            return {
                                id: String(resourceName),
                                label: String(resourceName),
                                value: String(resourceName),
                            };
                        })
                        .filter(
                            (option) => option.label !== 'Unknown Resource',
                        ),
                onFetchOptions: action(({ search, increasePage }) => {
                    sharedMonitoringDetailsExclusionsController.onFetchResources(
                        {
                            search,
                            increasePage,
                        },
                    );
                }),
            },
        ];

        const remainingFilters: FilterProps['filters'] = [
            {
                filterType: 'combobox',
                id: 'createdBy',
                label: t`Excluded by`,
                placeholder: t`Select all that apply`,
                searchDebounce: 500,
                isMultiSelect: false,
                isLoading:
                    sharedMonitoringDetailsExclusionsController.isDesignatorsLoading,
                hasMore:
                    sharedMonitoringDetailsExclusionsController.hasMoreDesignators,
                options:
                    sharedMonitoringDetailsExclusionsController.designatorsData
                        .map((designator) => {
                            const { fullName, id } = designator;
                            const displayName = fullName || `User ${id}`;

                            return {
                                id: id.toString(),
                                label: displayName,
                                value: id.toString(),
                            };
                        })
                        .filter(
                            (option, index, self) =>
                                self.findIndex(
                                    (o) => o.value === option.value,
                                ) === index,
                        ),
                onFetchOptions: action(({ search, increasePage }) => {
                    sharedMonitoringDetailsExclusionsController.onFetchDesignators(
                        {
                            search,
                            increasePage,
                        },
                    );
                }),
            },
            {
                filterType: 'combobox',
                id: 'dateExcluded',
                label: t`Date excluded`,
                placeholder: t`All dates`,
                isMultiSelect: false,
                options: [
                    {
                        id: 'ALL_DATES',
                        label: t`All dates`,
                        value: 'ALL_DATES',
                    },
                    {
                        id: 'LAST_SEVEN_DAYS',
                        label: t`Last 7 days`,
                        value: 'LAST_SEVEN_DAYS',
                    },
                    {
                        id: 'LAST_THIRTY_DAYS',
                        label: t`Last 30 days`,
                        value: 'LAST_THIRTY_DAYS',
                    },
                    {
                        id: 'LAST_SIX_MONTHS',
                        label: t`Last 6 months`,
                        value: 'LAST_SIX_MONTHS',
                    },
                    {
                        id: 'LAST_TWELVE_MONTHS',
                        label: t`Last 12 months`,
                        value: 'LAST_TWELVE_MONTHS',
                    },
                ],
            },
        ];

        return {
            clearAllButtonLabel: t`Clear all`,
            triggerLabel: t`Filters`,
            filters: [
                ...baseFilters,
                ...this.connectionFilters,
                ...remainingFilters,
            ],
        };
    }

    get filterViewModeProps(): DatatableProps<MonitorExclusionResponseDto>['filterViewModeProps'] {
        return {
            props: {
                selectedOption: 'unpinned',
                initialSelectedOption: 'unpinned',
                togglePinnedLabel: t`Pin filters to page`,
                toggleUnpinnedLabel: t`Move filters to dropdown`,
            },
            viewMode: 'toggleable',
        };
    }

    get emptyStateProps(): DatatableProps<MonitorExclusionResponseDto>['emptyStateProps'] {
        return {
            illustrationName: 'AddApproval',
            title: t`No exclusions found`,
            description: t`Please update your filters or refine your search.`,
        };
    }
}

export const sharedMonitoringDetailsExclusionsTableModel =
    new MonitoringDetailsExclusionsTableModel();
