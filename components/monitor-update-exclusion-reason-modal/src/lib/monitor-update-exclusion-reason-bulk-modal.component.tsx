import { useCallback } from 'react';
import {
    sharedCodebaseFindingExclusionMutationController,
    sharedMonitoringFindingsExclusionReasonController,
} from '@controllers/monitoring-details';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { action, observer } from '@globals/mobx';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import { closeMonitorUpdateExclusionReasonBulkModal } from './helpers/monitor-update-exclusion-reason-modal.helper';
import { monitorUpdateExclusionReasonModalModel } from './models/monitor-update-exclusion-reason-modal.model';

interface MonitorUpdateExclusionReasonBulkModalProps {
    onConfirm: (reason: string) => void;
    'data-id'?: string;
}

export const MonitorUpdateExclusionReasonBulkModal = observer(
    ({
        onConfirm,
        'data-id': dataId = 'MonitorUpdateExclusionReasonModal',
    }: MonitorUpdateExclusionReasonBulkModalProps): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();

        const handleFormSubmit = useCallback(
            (values: FormValues) => {
                const reason = values.reason as string;

                if (!reason.trim()) {
                    return;
                }

                onConfirm(reason.trim());
            },
            [onConfirm],
        );

        return (
            <>
                <Modal.Header
                    title={t`Update exclusion reason`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={closeMonitorUpdateExclusionReasonBulkModal}
                />
                <Modal.Body>
                    <Stack direction="column" gap="lg">
                        <Form
                            hasExternalSubmitButton
                            ref={formRef}
                            formId="monitoring-exclusion-form"
                            data-id={`${dataId}-form`}
                            schema={monitorUpdateExclusionReasonModalModel.getFormSchema()}
                            onSubmit={action((values) => {
                                handleFormSubmit(values);
                            })}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: closeMonitorUpdateExclusionReasonBulkModal,
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            colorScheme: 'primary',
                            onClick: action(() => {
                                triggerSubmit().catch((error) => {
                                    logger.error({
                                        message:
                                            'Failed to update monitoring exclusion reason bulk modal form',
                                        additionalInfo: { error },
                                    });
                                });
                            }),
                            isLoading:
                                sharedMonitoringFindingsExclusionReasonController.isUpdatingBulk ||
                                sharedCodebaseFindingExclusionMutationController.isUpdatingReasonBulk,
                        },
                    ]}
                />
            </>
        );
    },
);
