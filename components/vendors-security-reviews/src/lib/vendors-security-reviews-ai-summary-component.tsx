import { isEmpty, isNil } from 'lodash-es';
import { sharedVendorsProfileQuestionnaireAISummaryController } from '@controllers/vendors';
import { Stack } from '@cosmos/components/stack';
import { AICard } from '@cosmos-lab/components/ai-card';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { copyAISummaryToClipboard } from '@helpers/clipboard';
import { VendorSecurityReviewsAISummaryErrorComponent } from './vendors-security-reviews-ai-summary-error-component';
import { VendorsSecurityReviewsAISummaryFeedbackComponent } from './vendors-security-reviews-ai-summary-feedback-component';
import { VendorSecurityReviewsAISummarySectionsListComponent } from './vendors-security-reviews-ai-summary-sections-list-component';

export const VendorSecurityReviewsAISummaryComponent = observer(
    (): React.JSX.Element => {
        const { isVendorAISummaryEnabled } = sharedFeatureAccessModel;

        const { summary, summarySelectedType, summaryError, isProcessing } =
            sharedVendorsProfileQuestionnaireAISummaryController;

        if (!isVendorAISummaryEnabled) {
            return <>{null}</>;
        }

        if (summaryError) {
            return <VendorSecurityReviewsAISummaryErrorComponent />;
        }

        const isLoadingState = isProcessing;

        const title =
            summarySelectedType === 'questionnaire'
                ? t`Questionnaire summary`
                : t`Report summary`;

        if (isLoadingState && (isNil(summary) || isEmpty(summary))) {
            return (
                <AICard
                    isLoading
                    data-id="security-review-file-ai-summary"
                    data-testid="VendorSecurityReviewsAISummaryComponent"
                    title={t`Generating AI summary...`}
                />
            );
        }

        return (
            <>
                {!isNil(summary) && !isEmpty(summary) && (
                    <AICard
                        data-id="security-review-file-ai-summary"
                        data-testid="VendorSecurityReviewsAISummaryComponent"
                        title={title}
                        body={
                            <Stack gap="lg" direction="column">
                                {summary.map((summarySection) => {
                                    return (
                                        <VendorSecurityReviewsAISummarySectionsListComponent
                                            key={summarySection.title}
                                            summarySection={summarySection}
                                            data-id="7rZrw42J"
                                            summarySelectedType={
                                                summarySelectedType
                                            }
                                        />
                                    );
                                })}
                            </Stack>
                        }
                        toolbar={
                            <VendorsSecurityReviewsAISummaryFeedbackComponent
                                onCopy={() => {
                                    copyAISummaryToClipboard(summary);
                                }}
                            />
                        }
                    />
                )}
            </>
        );
    },
);
