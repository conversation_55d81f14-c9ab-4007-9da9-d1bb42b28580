import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { SocFinalizeReviewModal } from '../components/soc-finalize-review-modal.component';

const FINALIZE_REVIEW_MODAL_ID = 'soc-finalize-review-modal';

export interface SocFinalizeReviewModalOptions {
    onComplete: () => void;
    onDownloadAndComplete: () => void;
}

export const closeSocFinalizeReviewModal = action((): void => {
    modalController.closeModal(FINALIZE_REVIEW_MODAL_ID);
});

export const openSocFinalizeReviewModal = action(
    ({
        onComplete,
        onDownloadAndComplete,
    }: SocFinalizeReviewModalOptions): void => {
        modalController.openModal({
            id: FINALIZE_REVIEW_MODAL_ID,
            content: () => (
                <SocFinalizeReviewModal
                    data-id="soc-finalize-review-modal"
                    onCancel={closeSocFinalizeReviewModal}
                    onComplete={onComplete}
                    onDownloadAndComplete={onDownloadAndComplete}
                />
            ),
            size: 'lg',
            centered: true,
            disableClickOutsideToClose: false,
        });
    },
);
