import { isEmpty, isNil } from 'lodash-es';
import { z } from 'zod';
import {
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
    sharedVendorsSocReviewersInfiniteController,
} from '@controllers/vendors';
import type { CheckboxOption } from '@cosmos/components/checkbox-field-group';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { CosmosFileObject } from '@cosmos/components/file-upload';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { RadioOption } from '@cosmos/components/radio-field-group';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { CustomFieldRenderProps, FormSchema } from '@ui/forms';
import {
    BRIDGE_LETTER_FILE_UPLOAD_ACCEPTED_FORMATS,
    CERTIFICATION_TYPE_PREFIX,
    COMPLIANCE_REPORT_SCOPE_CERTIFICATION_OPTIONS,
    COMPLIANCE_REPORT_SCOPE_TYPE_OPTIONS,
    CONTROLS_ENCOMPASS_BUSINESS_NEEDS_OPTIONS,
    DEFAULT_END_USER_CONTROL,
    DEFAULT_FINDING,
    END_USER_CONTROL_DOCUMENTED_IN_PLACE_OPTIONS,
    END_USER_CONTROL_IN_PLACE_OPTIONS,
    REPORT_FINDINGS_MATERIAL_IMPACT_OPTIONS,
    REPORT_OPINION_OPTIONS,
    SUBSERVICE_ORGS_USE_INCLUSIVE_METHODS_OPTIONS,
    TRUST_SERVICE_CRITERIA_OPTIONS,
} from '../constants/vendors-security-reviews-soc.constants';
import { ComplianceScopeAccordionField } from '../custom-fields/compliance-scope-accordion-field.component';
import { CpaFirmAccordionField } from '../custom-fields/cpa-firm-accordion-field.component';
import { EndUserControlsAccordionField } from '../custom-fields/end-user-controls-accordion-field.component';
import { ReportFindingsAccordionField } from '../custom-fields/report-findings-accordion-field.component';
import { ReportOpinionAccordionField } from '../custom-fields/report-opinion-accordion-field.component';
import { ReviewerInfoAccordionField } from '../custom-fields/reviewer-info-accordion-field.component';
import { ServicesListedAccordionField } from '../custom-fields/services-listed-accordion-field.component';
import { SocEndUserControlsField } from '../custom-fields/soc-end-user-controls-field.component';
import { SocFindingsField } from '../custom-fields/soc-findings-field.component';
import { SocLocationsField } from '../custom-fields/soc-locations-field.component';
import { SocServicesField } from '../custom-fields/soc-services-field.component';
import { SubserviceOrgsAccordionField } from '../custom-fields/subservice-orgs-accordion-field.component';
import { getInitialAuditPeriod } from '../helpers/get-initial-audit-period.helper';
import {
    getInitialAllControlsInPlace,
    getInitialScopeType,
} from '../helpers/soc-form-initial-values.helper';
import { userToListBoxItemDataAdapter } from '../helpers/user-to-list-box-item-data-adapter.helper';

/**
 * SOC Review Form Schema Model
 * Each accordion is represented by a single custom field with its own render component
 * Uses existing controller for initial values.
 */
class SocReviewFormSchemaModel {
    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Get the requester user from security review details and format as ListBoxItemData.
     * Returns the formatted user data for use as initial value in combobox.
     */
    getRequesterUserAsListBoxItem(): ListBoxItemData | undefined {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;
        const requesterUser = securityReviewDetails?.requesterUser;

        if (!requesterUser) {
            return undefined;
        }

        return userToListBoxItemDataAdapter(requesterUser);
    }

    getInitialBridgeLetterDocuments(): CosmosFileObject[] {
        const { bridgeLetterDocuments } =
            sharedVendorsSecurityReviewDocumentsController;

        if (isEmpty(bridgeLetterDocuments)) {
            return [];
        }

        return bridgeLetterDocuments.map((document) => ({
            file: new File([''], document.name, {
                type: 'application/pdf',
            }),
            errors: [],
        }));
    }

    getInitialScopeType(): string | undefined {
        const { vendorReview } =
            sharedVendorsSecurityReviewDocumentsController.reviewDocument ?? {};

        return getInitialScopeType(vendorReview);
    }

    /**
     * Complete SOC Review Form Schema
     * Each accordion is a single custom field that handles all its internal inputs.
     */
    getCompleteFormSchema(): FormSchema {
        const { vendorReview } =
            sharedVendorsSecurityReviewDocumentsController.reviewDocument ?? {};

        let initialSubserviceOrganizationUsingInclusiveMethod = 'NA';

        if (!isNil(vendorReview?.subserviceOrganizationUsingInclusiveMethod)) {
            initialSubserviceOrganizationUsingInclusiveMethod =
                vendorReview.subserviceOrganizationUsingInclusiveMethod
                    ? 'YES'
                    : 'NO';
        }

        return {
            reviewerInfo: {
                type: 'custom',
                label: t`Reviewer Information`,
                render: (fieldProps: CustomFieldRenderProps) => (
                    <ReviewerInfoAccordionField
                        name={fieldProps.name}
                        formId={fieldProps.formId}
                        reviewDate={vendorReview?.reviewDate}
                        data-id="RSlSL7bK"
                    />
                ),
                customType: 'object',
                fields: {
                    reviewer: {
                        type: 'combobox',
                        label: t`Reviewer`,
                        isMultiSelect: false,
                        validator: z.object(
                            {
                                id: z.string(),
                                label: z.string(),
                                value: z.string(),
                            },
                            {
                                message: t`Reviewer is required`,
                            },
                        ),
                        initialValue: this.getRequesterUserAsListBoxItem(),
                        options:
                            sharedVendorsSocReviewersInfiniteController.options.map(
                                userToListBoxItemDataAdapter,
                            ),
                        hasMore:
                            sharedVendorsSocReviewersInfiniteController.hasNextPage ||
                            false,
                        isLoading:
                            (sharedVendorsSocReviewersInfiniteController.isFetching &&
                                sharedVendorsSocReviewersInfiniteController.isLoading) ||
                            false,
                        onFetchOptions:
                            sharedVendorsSocReviewersInfiniteController.onFetchReviewers,
                        loaderLabel: t`Loading reviewers...`,
                        placeholder: t`Search for reviewers`,
                        getSearchEmptyState: () => t`No reviewers found`,
                    },
                    reportIssueDate: {
                        type: 'date',
                        label: t`SOC report issue date`,
                        validator: z
                            .string({
                                message: t`Report issue date is required`,
                            })
                            .date(t`Report issue date is required`),
                        isMulti: false,
                        initialValue:
                            vendorReview?.reportIssueDate as TDateISODate,
                        /**
                         * Only today or past dates allowed in the locale date.
                         */
                        getIsDateUnavailable: (date) => {
                            return (
                                new Date(date).toISOString().split('T')[0] >
                                new Date().toISOString().split('T')[0]
                            );
                        },
                        dateUnavailableText: t`SOC report issue date not available`,
                    },
                },
            },

            complianceScope: {
                type: 'custom',
                label: t`Compliance Report Scope`,
                render: ComplianceScopeAccordionField,
                customType: 'object',
                fields: {
                    certification: {
                        type: 'select',
                        label: t`Certification`,
                        options: COMPLIANCE_REPORT_SCOPE_CERTIFICATION_OPTIONS,
                        initialValue:
                            COMPLIANCE_REPORT_SCOPE_CERTIFICATION_OPTIONS.find(
                                (option) =>
                                    option.value ===
                                    `${CERTIFICATION_TYPE_PREFIX}${vendorReview?.socReport}`,
                            ),
                        validator: z.object(
                            {
                                id: z.string(),
                                label: z.string(),
                                value: z.string(),
                            },
                            {
                                message: t`Certification is required`,
                            },
                        ),
                    },
                    scopeType: {
                        type: 'radioGroup',
                        label: t`Type`,
                        options:
                            COMPLIANCE_REPORT_SCOPE_TYPE_OPTIONS as RadioOption[],
                        initialValue: this.getInitialScopeType(),
                        cosmosUseWithCaution_forceOptionOrientation:
                            'horizontal',
                        shownIf: {
                            fieldName: 'complianceScope.certification',
                            operator: 'notEquals',
                            value: 'SOC_3',
                        },
                    },
                    trustServiceCriteria: {
                        type: 'checkboxGroup',
                        label: t`Trust service criteria`,
                        options:
                            TRUST_SERVICE_CRITERIA_OPTIONS as CheckboxOption[],
                        initialValue:
                            vendorReview?.trustServiceCategories?.map(
                                ({ category }) => String(category),
                            ) ?? [],
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                        shownIf: {
                            fieldName: 'complianceScope.certification',
                            operator: 'notEquals',
                            value: 'SOC_1',
                        },
                    },
                    auditPeriod: {
                        type: 'date',
                        label: t`Audit period`,
                        initialValue: vendorReview
                            ? (getInitialAuditPeriod(
                                  vendorReview.socReportType1 || false,
                                  vendorReview,
                              ).start as TDateISODate)
                            : undefined,
                        locale: 'en-US',
                        shownIf: {
                            operator: 'and',
                            conditions: [
                                {
                                    operator: 'notEquals',
                                    fieldName: 'complianceScope.certification',
                                    value: 'SOC_3',
                                },
                                {
                                    operator: 'equals',
                                    fieldName: 'complianceScope.scopeType',
                                    value: 'type1',
                                },
                            ],
                        },
                    },
                    auditPeriodRange: {
                        type: 'dateRange',
                        initialValue: vendorReview
                            ? getInitialAuditPeriod(false, vendorReview)
                            : { start: null, end: null },
                        label: t`Audit period`,
                        locale: 'en-US',
                        shownIf: {
                            operator: 'and',
                            conditions: [
                                {
                                    operator: 'notEquals',
                                    fieldName: 'complianceScope.certification',
                                    value: 'SOC_3',
                                },
                                {
                                    operator: 'equals',
                                    fieldName: 'complianceScope.scopeType',
                                    value: 'type2',
                                },
                            ],
                        },
                    },
                    bridgeLetter: {
                        type: 'file',
                        label: t`Bridge letter`,
                        acceptedFormats:
                            BRIDGE_LETTER_FILE_UPLOAD_ACCEPTED_FORMATS,
                        maxFileSizeInBytes: 10 * 1024 * 1024, // 10MB
                        isMulti: false,
                        oneFileOnly: true,
                        initialValue: this.getInitialBridgeLetterDocuments(),
                        isOptional: true,
                    },
                },
            },

            reportOpinion: {
                type: 'custom',
                label: t`Report Opinion`,
                render: ReportOpinionAccordionField,
                customType: 'object',
                fields: {
                    reportOpinion: {
                        type: 'select',
                        label: t`Report opinion`,
                        options: REPORT_OPINION_OPTIONS,
                        initialValue: REPORT_OPINION_OPTIONS.find(
                            (option) =>
                                option.value ===
                                String(vendorReview?.reportOpinion),
                        ),
                        validator: z.object(
                            {
                                id: z.string(),
                                label: z.string(),
                                value: z.string(),
                            },
                            {
                                message: t`Report opinion is required`,
                            },
                        ),
                    },
                    encompassBusinessNeeds: {
                        type: 'radioGroup',
                        label: t`Do control objectives or trust principles encompass business needs?`,
                        options: CONTROLS_ENCOMPASS_BUSINESS_NEEDS_OPTIONS,
                        initialValue: vendorReview?.encompassBusinessNeeds
                            ? 'YES'
                            : 'NO',
                        cosmosUseWithCaution_forceOptionOrientation:
                            'horizontal',
                        isOptional: true,
                    },
                    followUpActivity: {
                        type: 'textarea',
                        label: t`Follow up activity if opinion is qualified`,
                        isOptional: true,
                        initialValue: vendorReview?.followUpActivity ?? '',
                        rows: 4,
                        validator: z.string().max(30000, {
                            message: t`Follow Up Activity Details must be at most 30000 characters`,
                        }),
                        maxCharacters: 30000,
                    },
                },
            },

            reportFindings: {
                type: 'custom',
                label: t`Report Findings`,
                render: ReportFindingsAccordionField,
                customType: 'object',
                fields: {
                    noFindingsChecked: {
                        type: 'checkbox',
                        label: t`No findings identified in the report`,
                        initialValue: !(
                            vendorReview?.findings &&
                            !isEmpty(vendorReview.findings)
                        ),
                    },
                    findings: {
                        type: 'custom',
                        label: t`Findings`,
                        render: SocFindingsField,
                        initialValue:
                            vendorReview?.findings &&
                            !isEmpty(vendorReview.findings)
                                ? vendorReview.findings.filter((finding) =>
                                      finding.description.trim(),
                                  )
                                : [{ ...DEFAULT_FINDING }],
                        isOptional: true,
                        shownIf: {
                            fieldName: 'reportFindings.noFindingsChecked',
                            operator: 'equals',
                            value: false,
                        },
                        fields: {
                            description: {
                                type: 'textarea',
                                label: t`Finding`,
                                initialValue: '',
                                validator: z
                                    .string()
                                    .min(1, t`Finding description is required`)
                                    .max(30000),
                                maxCharacters: 30000,
                            },
                        },
                        customType: 'arrayOfObjects',
                    },
                    hasMaterialImpact: {
                        type: 'radioGroup',
                        label: t`Do the findings have any material impact on your control environment?`,
                        options: REPORT_FINDINGS_MATERIAL_IMPACT_OPTIONS,
                        initialValue: vendorReview?.hasMaterialImpact
                            ? 'YES'
                            : 'NO',
                        cosmosUseWithCaution_forceOptionOrientation:
                            'horizontal',
                        shownIf: {
                            fieldName: 'reportFindings.noFindingsChecked',
                            operator: 'equals',
                            value: false,
                        },
                    },
                },
            },

            endUserControls: {
                type: 'custom',
                label: t`End User Controls`,
                render: EndUserControlsAccordionField,
                customType: 'object',
                fields: {
                    noControlsChecked: {
                        type: 'checkbox',
                        label: t`No end-user controls identified in the report`,
                        initialValue: !(
                            vendorReview?.userControls &&
                            !isEmpty(vendorReview.userControls)
                        ),
                    },
                    allControlsInPlace: {
                        type: 'radioGroup',
                        label: t`For applicable end-user controls documented, do you have controls in place?`,
                        options: END_USER_CONTROL_DOCUMENTED_IN_PLACE_OPTIONS,
                        initialValue:
                            getInitialAllControlsInPlace(vendorReview),
                        cosmosUseWithCaution_forceOptionOrientation:
                            'horizontal',
                        isOptional: true,
                        shownIf: {
                            fieldName: 'endUserControls.noControlsChecked',
                            operator: 'equals',
                            value: false,
                        },
                    },
                    userControls: {
                        type: 'custom',
                        label: t`End User Controls`,
                        render: SocEndUserControlsField,
                        initialValue:
                            vendorReview?.userControls &&
                            !isEmpty(vendorReview.userControls)
                                ? vendorReview.userControls.map((control) => ({
                                      id: control.id,
                                      control: control.name,
                                      inPlace: control.inPlace ? 'YES' : 'NO',
                                  }))
                                : [{ ...DEFAULT_END_USER_CONTROL }],
                        isOptional: true,
                        shownIf: {
                            fieldName: 'endUserControls.noControlsChecked',
                            operator: 'equals',
                            value: false,
                        },
                        fields: {
                            control: {
                                type: 'textarea',
                                label: t`Control`,
                                validator: z
                                    .string()
                                    .min(1, t`Control name is required`)
                                    .max(
                                        191,
                                        t`Control name must be at most 191 characters`,
                                    ),
                                maxCharacters: 191,
                            },
                            inPlace: {
                                type: 'radioGroup',
                                label: t`Is this control in place?`,
                                options: END_USER_CONTROL_IN_PLACE_OPTIONS,
                                cosmosUseWithCaution_forceOptionOrientation:
                                    'horizontal',
                                validator: z
                                    .string()
                                    .min(1, 'Control in place is required'),
                            },
                        },
                        customType: 'arrayOfObjects',
                    },
                },
            },

            servicesListed: {
                type: 'custom',
                label: t`Services Listed`,
                render: ServicesListedAccordionField,
                customType: 'object',
                fields: {
                    services: {
                        type: 'custom',
                        label: t`Services`,
                        render: SocServicesField,
                        initialValue:
                            vendorReview?.services?.map((service) => ({
                                ...service,
                                service: service.name,
                            })) ?? [],
                        fields: {
                            service: {
                                type: 'textarea',
                                label: t`Service`,
                                validator: z
                                    .string()
                                    .min(1, t`Service name is required`)
                                    .max(
                                        191,
                                        t`Service input must be at most 191 characters`,
                                    ),
                                maxCharacters: 191,
                            },
                        },
                        customType: 'arrayOfObjects',
                    },
                    locations: {
                        type: 'custom',
                        label: t`Locations`,
                        render: SocLocationsField,
                        initialValue: vendorReview?.locations ?? [],
                        fields: {
                            city: {
                                type: 'text',
                                label: t`City`,
                                validator: z
                                    .string()
                                    .min(1, t`City is required`)
                                    .max(
                                        191,
                                        t`City input must be at most 191 characters`,
                                    ),
                            },
                            stateCountry: {
                                type: 'text',
                                label: t`State/Country`,
                                validator: z
                                    .string()
                                    .min(1, t`State/Country is required`)
                                    .max(
                                        191,
                                        t`State/Country input must be at most 191 characters`,
                                    ),
                            },
                        },
                        customType: 'arrayOfObjects',
                    },
                },
            },

            cpaFirm: {
                type: 'custom',
                label: t`CPA Firm`,
                render: CpaFirmAccordionField,
                customType: 'object',
                fields: {
                    cpaFirm: {
                        type: 'text',
                        label: t`CPA firm that performed the audit`,
                        initialValue: vendorReview?.cpaFirm ?? '',
                        isOptional: true,
                        validator: z.string().max(191, {
                            message: t`Maximum 191 characters allowed`,
                        }),
                    },
                    cpaProcedurePerformed: {
                        type: 'textarea',
                        label: t`Procedures performed to assess reputation of CPA firm`,
                        initialValue: vendorReview?.cpaProcedurePerformed ?? '',
                        rows: 4,
                        maxCharacters: 30000,
                        isOptional: true,
                    },
                },
            },

            subserviceOrgs: {
                type: 'custom',
                label: t`Subservice Organizations`,
                render: SubserviceOrgsAccordionField,
                customType: 'object',
                initialValue: {
                    subserviceOrganization:
                        vendorReview?.subserviceOrganization ?? '',
                    subserviceOrganizationUsingInclusiveMethod:
                        vendorReview?.subserviceOrganizationUsingInclusiveMethod
                            ? 'YES'
                            : 'NO',
                    subserviceOrganizationProcedurePerformed:
                        vendorReview?.subserviceOrganizationProcedurePerformed ??
                        '',
                },
                fields: {
                    subserviceOrganization: {
                        type: 'textarea',
                        label: t`Subservice organizations in report`,
                        initialValue:
                            vendorReview?.subserviceOrganization ?? '',
                        rows: 4,
                        maxCharacters: 30000,
                        isOptional: true,
                    },
                    subserviceOrganizationUsingInclusiveMethod: {
                        type: 'radioGroup',
                        label: t`Are subservice organizations presented in the report using the inclusive method?`,
                        options: SUBSERVICE_ORGS_USE_INCLUSIVE_METHODS_OPTIONS,
                        initialValue:
                            initialSubserviceOrganizationUsingInclusiveMethod,
                        cosmosUseWithCaution_forceOptionOrientation:
                            'horizontal',
                        isOptional: true,
                    },
                    subserviceOrganizationProcedurePerformed: {
                        type: 'textarea',
                        label: t`Subservice organization procedure performed`,
                        initialValue:
                            vendorReview?.subserviceOrganizationProcedurePerformed ??
                            '',
                        rows: 4,
                        maxCharacters: 30000,
                        isOptional: true,
                    },
                },
            },
        };
    }
}

export const socReviewFormSchemaModel = new SocReviewFormSchemaModel();
