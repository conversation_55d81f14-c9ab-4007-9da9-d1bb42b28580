import { useCallback, useState } from 'react';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
} from '@controllers/vendors';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, observer, runInAction, toJS } from '@globals/mobx';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import { sharedSOCReviewFinalizeReviewController } from './controllers/soc-review-finalize-review-controller';
import { sharedSOCReviewSaveProgressController } from './controllers/soc-review-save-progress-controller';
import {
    closeSocFinalizeReviewModal,
    openSocFinalizeReviewModal,
} from './helpers/soc-finalize-review-modal.helper';
import {
    getSocFormActionHandlers,
    getSocFormActionLabels,
} from './helpers/soc-form-actions.helper';
import { socReviewFormSchemaModel } from './models/soc-review-form-schema.model';
import type { SocReviewFormValuesType } from './types/soc-review-form-values.type';

export const VendorSecurityReviewsSocFormComponent = observer(
    (): React.JSX.Element => {
        const { formRef, triggerResetForm, triggerSubmit } = useFormSubmit();
        const [formResetKey, setFormResetKey] = useState(0);

        const triggerFormUpdate = useCallback(() => {
            setFormResetKey((prev) => prev + 1); // Force re-render
        }, []);

        const actionLabels = getSocFormActionLabels();
        const actionHandlers = getSocFormActionHandlers(triggerResetForm);

        const isReadOnly = sharedFeatureAccessModel.isVendorAccessReadOnly;

        const securityReviewId = toJS(
            sharedVendorsSecurityReviewDetailsController.securityReviewDetails
                ?.id,
        );
        const vendorId = toJS(sharedVendorsDetailsController.vendorDetails?.id);

        const handleFormSubmit = useCallback(
            (values: FormValues) => {
                runInAction(() => {
                    openSocFinalizeReviewModal({
                        onComplete: action(() => {
                            sharedSOCReviewFinalizeReviewController
                                .finalizeReviewAsync(
                                    values as SocReviewFormValuesType,
                                    'APPROVED',
                                )
                                .finally(() => {
                                    closeSocFinalizeReviewModal();
                                });
                        }),
                        onDownloadAndComplete: action(() => {
                            sharedSOCReviewFinalizeReviewController
                                .finalizeReviewAsync(
                                    values as SocReviewFormValuesType,
                                    'APPROVED',
                                )
                                .then(() => {
                                    sharedSOCReviewFinalizeReviewController.downloadReportAsync(
                                        securityReviewId,
                                        vendorId,
                                    );
                                })
                                .finally(() => {
                                    closeSocFinalizeReviewModal();
                                });
                        }),
                    });
                });
            },
            [securityReviewId, vendorId],
        );

        const handleSaveProgress = useCallback(() => {
            runInAction(() => {
                const formValues: SocReviewFormValuesType | undefined =
                    formRef.current?.getValues();

                if (formValues) {
                    sharedSOCReviewSaveProgressController.saveProgressAsync(
                        formValues,
                    );
                }
            });
        }, [formRef]);

        return (
            <Stack
                pt="2xl"
                gap="xl"
                direction="column"
                data-testid="VendorSecurityReviewsSocFormComponent"
                data-id="soc-form-component"
            >
                <Text type="title" size="300">
                    {t`Report review`}
                </Text>

                <Form
                    hasExternalSubmitButton
                    key={formResetKey}
                    ref={formRef}
                    formId="soc-review-form"
                    schema={socReviewFormSchemaModel.getCompleteFormSchema()}
                    data-testid="SocReviewForm"
                    data-id="soc-review-form"
                    isReadOnly={isReadOnly}
                    onSubmit={handleFormSubmit}
                />

                <Stack direction="row" align="center" justify="end" gap="md">
                    <Button
                        label={actionLabels.clearForm()}
                        level="tertiary"
                        cosmosUseWithCaution_isDisabled={isReadOnly}
                        onClick={() => {
                            actionHandlers.handleClearForm();
                            triggerFormUpdate();
                        }}
                    />
                    <Button
                        label={actionLabels.saveProgress()}
                        level="secondary"
                        colorScheme="primary"
                        // TODO add to config
                        a11yLoadingLabel={t`Saving progress...`}
                        cosmosUseWithCaution_isDisabled={isReadOnly}
                        isLoading={
                            sharedSOCReviewSaveProgressController.isSaving
                        }
                        onClick={handleSaveProgress}
                    />
                    <Button
                        label={actionLabels.finalizeReview()}
                        colorScheme="primary"
                        // TODO add to config
                        a11yLoadingLabel={t`Finalizing review...`}
                        cosmosUseWithCaution_isDisabled={isReadOnly}
                        isLoading={
                            sharedSOCReviewFinalizeReviewController.isFinalizing
                        }
                        onClick={triggerSubmit}
                    />
                </Stack>
            </Stack>
        );
    },
);
