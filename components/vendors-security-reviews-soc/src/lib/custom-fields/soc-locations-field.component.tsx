import { useFieldArray } from 'react-hook-form';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { action, runInAction } from '@globals/mobx';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';
import { DEFAULT_LOCATION } from '../constants/vendors-security-reviews-soc.constants';
import { openSocDeleteConfirmation } from '../helpers/soc-delete-confirmation.helper';

export const SocLocationsField = ({
    'data-id': dataId,
    name,
    formId,
}: Omit<CustomFieldRenderProps, 'value' | 'onChange'>): React.JSX.Element => {
    const { fields, append, remove } = useFieldArray({
        name,
    });

    const addLocation = () => {
        runInAction(() => {
            append(DEFAULT_LOCATION);
        });
    };

    const removeLocation = (index: number) => {
        openSocDeleteConfirmation({
            itemType: 'location',
            onConfirm: action(() => {
                remove(index);
            }),
        });
    };

    return (
        <FormField
            shouldHideLabel
            formId={formId}
            name={name}
            label={t`Locations`}
            data-testid="SocLocationsField"
            data-id={dataId}
            renderInput={() => (
                <Stack gap="lg" direction="column" data-id={`${dataId}-inputs`}>
                    {fields.map((field, index) => (
                        <Stack
                            key={field.id}
                            gap="md"
                            align="start"
                            data-id={`${dataId}-location-${index}`}
                        >
                            <UniversalFormField
                                name={`${name}[${index}].city`}
                                formId={formId}
                                data-id={`${dataId}-city-field`}
                            />

                            <UniversalFormField
                                name={`${name}[${index}].stateCountry`}
                                formId={formId}
                                data-id={`${dataId}-state-country-field`}
                            />

                            <Box pt="6x">
                                <Button
                                    isIconOnly
                                    label={t`Remove location`}
                                    startIconName="Trash"
                                    level="tertiary"
                                    colorScheme="danger"
                                    type="button"
                                    onClick={() => {
                                        removeLocation(index);
                                    }}
                                />
                            </Box>
                        </Stack>
                    ))}

                    <Stack direction="row" align="start">
                        <Button
                            label={t`Add location`}
                            level="tertiary"
                            type="button"
                            onClick={addLocation}
                        />
                    </Stack>
                </Stack>
            )}
        />
    );
};
