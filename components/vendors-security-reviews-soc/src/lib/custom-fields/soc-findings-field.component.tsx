import { isEmpty } from 'lodash-es';
import { useFieldArray } from 'react-hook-form';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { action, runInAction } from '@globals/mobx';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';
import { DEFAULT_FINDING } from '../constants/vendors-security-reviews-soc.constants';
import { openSocDeleteConfirmation } from '../helpers/soc-delete-confirmation.helper';

export const SocFindingsField = ({
    'data-id': dataId,
    name,
    formId,
}: Omit<CustomFieldRenderProps, 'value' | 'onChange'>): React.JSX.Element => {
    const { fields, append, remove } = useFieldArray({
        name,
    });

    const addFinding = () => {
        runInAction(() => {
            append(DEFAULT_FINDING);
        });
    };

    const removeFinding = (index: number) => {
        openSocDeleteConfirmation({
            itemType: 'finding',
            onConfirm: action(() => {
                remove(index);
            }),
        });
    };

    return (
        <FormField
            shouldHideLabel
            formId={formId}
            name={name}
            label={t`Findings`}
            data-testid="SocFindingsField"
            data-id={dataId}
            renderInput={() => (
                <Stack gap="lg" direction="column" data-id={`${dataId}-inputs`}>
                    {fields.map((field, index) => (
                        <Stack
                            key={field.id}
                            gap="md"
                            align="start"
                            data-id={`${dataId}-finding-${index}`}
                        >
                            <UniversalFormField
                                name={`${name}[${index}].description`}
                                formId={formId}
                                data-id={`${dataId}-description-field`}
                            />

                            {!isEmpty(fields) && (
                                <Box pt="6x">
                                    <Button
                                        isIconOnly
                                        label={t`Remove finding`}
                                        startIconName="Trash"
                                        level="tertiary"
                                        colorScheme="danger"
                                        type="button"
                                        onClick={() => {
                                            removeFinding(index);
                                        }}
                                    />
                                </Box>
                            )}
                        </Stack>
                    ))}

                    <Stack direction="row" align="start">
                        <Button
                            label={t`Add finding`}
                            level="tertiary"
                            type="button"
                            onClick={addFinding}
                        />
                    </Stack>
                </Stack>
            )}
        />
    );
};
