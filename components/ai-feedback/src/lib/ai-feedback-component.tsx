import { noop } from 'lodash-es';
import type { default as React } from 'react';
import { Icon } from '@cosmos/components/icon';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import type { AiFeedbackStatus } from './types/ai-feedback.types';

interface AiFeedbackModalProps {
    /** Callback function to submit the feedback. */
    onSubmit: (values: FormValues) => void;
    /** Whether the form is currently submitting. */
    isLoading: boolean;
    /** Initial feedback status to pre-select. Defaults to 'USEFUL'. */
    initialFeedbackStatus?: AiFeedbackStatus;
}

const buildFormSchema = (
    initialFeedbackStatus: AiFeedbackStatus = 'USEFUL',
): FormSchema => ({
    feedbackStatus: {
        type: 'choiceCardGroup',
        label: t`Please rate your experience`,
        choiceCardInputType: 'radio',
        initialValue: initialFeedbackStatus,
        showDivider: true,
        options: [
            {
                value: 'USEFUL',
                label: t`positive`,
                slot: (
                    <Icon
                        name="ThumbsUp"
                        size="100"
                        colorScheme="success"
                        backgroundType="round"
                    />
                ),
            },
            {
                value: 'NOT_USEFUL',
                label: t`negative`,
                slot: (
                    <Icon
                        name="ThumbsDown"
                        size="100"
                        colorScheme="critical"
                        backgroundType="round"
                    />
                ),
            },
        ],
    },
    feedbackReason: {
        type: 'checkboxGroup',
        label: t`What best describes the problem with this content?`,
        initialValue: [],
        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
        options: [
            {
                value: 'NOT_ACCURATE',
                label: t`This is not accurate`,
            },
            {
                value: 'NOT_HELPFUL',
                label: t`This does not add value`,
            },
            {
                value: 'OTHER',
                label: t`Other`,
            },
        ],
        shownIf: {
            fieldName: 'feedbackStatus',
            operator: 'equals',
            value: 'NOT_USEFUL',
        },
    },
    feedbackText: {
        type: 'textarea',
        label: t`Feedback`,
        maxCharacters: 30000,
        isOptional: true,
    },
});

/**
 * Modal component for collecting AI feedback on any AI content.
 * Allows users to submit positive or negative feedback with optional text comments.
 */
export const AiFeedbackModal = observer(
    ({
        onSubmit,
        isLoading,
        initialFeedbackStatus = 'USEFUL',
    }: AiFeedbackModalProps): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();

        return (
            <>
                <Modal.Header
                    title={t`Provide feedback`}
                    closeButtonAriaLabel={t`Close AI feedback modal`}
                />
                <Modal.Body>
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        formId="ai-feedback-form"
                        schema={buildFormSchema(initialFeedbackStatus)}
                        data-id="ai-feedback-form"
                        onSubmit={onSubmit}
                    />
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Send feedback`,
                            level: 'primary',
                            onClick: () => {
                                triggerSubmit().catch(noop);
                            },
                            cosmosUseWithCaution_isDisabled: isLoading,
                        },
                    ]}
                />
            </>
        );
    },
);
