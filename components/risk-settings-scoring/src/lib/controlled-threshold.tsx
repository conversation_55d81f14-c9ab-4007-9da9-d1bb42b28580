import { useState } from 'react';
import {
    Threshold,
    type ThresholdProps,
} from '@cosmos-lab/components/threshold';
import { sharedFeatureAccessModel } from '@globals/feature-access';

interface ControlledThresholdProps extends Omit<ThresholdProps, 'value'> {
    value?: number[];
}
export const ControlledThreshold = ({
    value,
    initialValues,
    onValueChange,
    ...otherProps
}: ControlledThresholdProps): React.JSX.Element => {
    const [internalValues, setInternalValues] = useState(
        value ?? initialValues,
    );
    const { hasRiskManagePermission } = sharedFeatureAccessModel;

    const handleValueChange = (newValues: number[]) => {
        setInternalValues(newValues);
        onValueChange?.(newValues);
    };

    return (
        <Threshold
            {...otherProps}
            data-testid="ControlledThreshold"
            data-id="ControlledThreshold"
            initialValues={internalValues}
            disabled={!hasRiskManagePermission}
            onValueChange={handleValueChange}
        />
    );
};
