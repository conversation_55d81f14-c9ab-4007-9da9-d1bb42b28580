import type { CustomFieldRenderProps } from '@ui/forms';
import { ImpactLikelihoodField } from './impact-likelihood-field.component';

export const ImpactLikelihoodFormField = (
    props: CustomFieldRenderProps,
): React.JSX.Element => {
    const { formId, name, 'data-id': dataId } = props;

    return (
        <ImpactLikelihoodField
            formId={formId}
            name={name}
            data-id={`${dataId}-display`}
            data-testid="ImpactLikelihoodFormField"
        />
    );
};
