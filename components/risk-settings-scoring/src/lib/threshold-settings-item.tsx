import type React from 'react';
import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { UniversalFormField } from '@ui/forms';
import { ColoredSquare, type ColoredSquareProps } from './colored-square';

interface ThresholdSettingsItemProps {
    color: ColoredSquareProps['color'];
    formId: string;
    index: number;
    thresholdId: number;
    nameFieldName: string;
    descriptionFieldName: string;
    onThresholdChange: (index: number, field: string, newValue: string) => void;
}

export const ThresholdSettingsItem = ({
    color,
    formId,
    index,
    thresholdId,
    nameFieldName,
    descriptionFieldName,
    onThresholdChange,
}: ThresholdSettingsItemProps): React.JSX.Element => {
    const { hasRiskManagePermission } = sharedFeatureAccessModel;

    return (
        <Stack
            direction="column"
            gap="2xl"
            data-testid="ThresholdSettingsItem"
            data-id={`threshold-settings-item-${thresholdId}`}
        >
            <Grid
                columns={'1fr 11fr'}
                gap="2xl"
                data-testid="ThresholdSettingsItem"
                data-id={`threshold-settings-grid-${thresholdId}`}
            >
                <ColoredSquare
                    color={color}
                    data-id={`threshold-${thresholdId}-color-square`}
                />
                <Stack direction={'column'} gap="4x">
                    <Grid columns={'3fr 4fr'} gap="xs">
                        <UniversalFormField
                            __fromCustomRender
                            key={nameFieldName}
                            formId={formId}
                            name={nameFieldName}
                            data-id={`${nameFieldName}-field`}
                        />
                    </Grid>
                    <Grid columns="1fr auto" gap="xl" align={'end'}>
                        <Stack direction="column" gap="xs">
                            <UniversalFormField
                                __fromCustomRender
                                key={descriptionFieldName}
                                formId={formId}
                                name={descriptionFieldName}
                                data-id={`${descriptionFieldName}-field`}
                            />
                        </Stack>
                        {hasRiskManagePermission && (
                            <Button
                                isIconOnly
                                label={t`Delete threshold`}
                                level="tertiary"
                                colorScheme="danger"
                                startIconName="Trash"
                                data-id={`delete-threshold-button-${thresholdId}`}
                                onClick={() => {
                                    onThresholdChange(index, 'delete', '');
                                }}
                            />
                        )}
                    </Grid>
                </Stack>
            </Grid>

            <Divider />
        </Stack>
    );
};
