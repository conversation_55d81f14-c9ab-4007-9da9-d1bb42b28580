import { type JSX, useMemo } from 'react';
import { Accordion } from '@cosmos/components/accordion';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { RISK_CALCULATION_CONSTANTS } from '@helpers/risk-score';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useUniversalFieldController,
} from '@ui/forms';

export const RiskLevelDefinitionsFormField = (
    props: CustomFieldRenderProps,
): JSX.Element => {
    const { formId, name, 'data-id': dataId } = props;

    const [impactField] = useUniversalFieldController<'select'>(
        'impactLikelihoodDisplay.impact',
    );
    const [likelihoodField] = useUniversalFieldController<'select'>(
        'impactLikelihoodDisplay.likelihood',
    );

    const currentImpactLevel = impactField.value?.value
        ? parseInt(impactField.value.value, 10)
        : RISK_CALCULATION_CONSTANTS.DEFAULT_IMPACT_LEVEL;
    const currentLikelihoodLevel = likelihoodField.value?.value
        ? parseInt(likelihoodField.value.value, 10)
        : RISK_CALCULATION_CONSTANTS.DEFAULT_LIKELIHOOD_LEVEL;

    const validImpactLevel = Math.min(
        Math.max(
            currentImpactLevel,
            RISK_CALCULATION_CONSTANTS.MIN_RISK_LEVELS,
        ),
        RISK_CALCULATION_CONSTANTS.MAX_RISK_LEVELS,
    );
    const validLikelihoodLevel = Math.min(
        Math.max(
            currentLikelihoodLevel,
            RISK_CALCULATION_CONSTANTS.MIN_RISK_LEVELS,
        ),
        RISK_CALCULATION_CONSTANTS.MAX_RISK_LEVELS,
    );

    // Memoize the impact fields to prevent re-creation on every render
    const impactFields = useMemo(
        () =>
            Array.from({ length: validImpactLevel }, (_, i) => (
                <UniversalFormField
                    __fromCustomRender
                    key={`impact-${i + 1}`}
                    formId={formId}
                    name={`${name}.impactDefinition${i + 1}`}
                    data-id={`impact-level-${i + 1}-field`}
                />
            )),
        [validImpactLevel, formId, name],
    );

    // Memoize the likelihood fields to prevent re-creation on every render
    const likelihoodFields = useMemo(
        () =>
            Array.from({ length: validLikelihoodLevel }, (_, i) => (
                <UniversalFormField
                    __fromCustomRender
                    key={`likelihood-${i + 1}`}
                    formId={formId}
                    name={`${name}.likelihoodDefinition${i + 1}`}
                    data-id={`likelihood-level-${i + 1}-field`}
                />
            )),
        [validLikelihoodLevel, formId, name],
    );

    return (
        <Stack
            direction="column"
            gap="4x"
            data-id={dataId}
            data-testid="RiskLevelDefinitionsFormField"
        >
            <Text size="300" type="title">
                <Trans>Level definitions</Trans>
            </Text>
            <Accordion
                title={t`Impact`}
                data-id="impact-definitions"
                isDefaultExpanded={false}
                body={
                    <Stack
                        direction="column"
                        gap="4x"
                        data-testid="LevelDefinitionsGroup-Impact"
                        data-id="impact-level-definitions"
                    >
                        {impactFields}
                    </Stack>
                }
            />
            <Accordion
                title={t`Likelihood`}
                data-id="likelihood-definitions"
                isDefaultExpanded={false}
                body={
                    <Stack
                        direction="column"
                        gap="4x"
                        data-testid="LevelDefinitionsGroup-Likelihood"
                        data-id="likelihood-level-definitions"
                    >
                        {likelihoodFields}
                    </Stack>
                }
            />
        </Stack>
    );
};
