import { isEmpty } from 'lodash-es';
import { sharedRiskSettingsController } from '@controllers/risk-settings';
import {
    RiskScore,
    type RiskScoreSeverity,
} from '@cosmos-lab/components/risk-score';
import { observer } from '@globals/mobx';
import {
    getScoreIntensityByThresholds,
    RISK_CALCULATION_CONSTANTS,
} from '@helpers/risk-score';
import { useUniversalFieldController } from '@ui/forms';

interface ReactiveRiskScoreProps {
    name: string;
}

export const ReactiveRiskScore = observer(
    ({ name }: ReactiveRiskScoreProps): React.JSX.Element => {
        const [impactField] = useUniversalFieldController<'select'>(
            `${name}.impact`,
        );
        const [likelihoodField] = useUniversalFieldController<'select'>(
            `${name}.likelihood`,
        );

        const currentImpact = impactField.value?.value
            ? parseInt(impactField.value.value, 10)
            : RISK_CALCULATION_CONSTANTS.DEFAULT_IMPACT_LEVEL;
        const currentLikelihood = likelihoodField.value?.value
            ? parseInt(likelihoodField.value.value, 10)
            : RISK_CALCULATION_CONSTANTS.DEFAULT_LIKELIHOOD_LEVEL;
        const currentRiskScore = currentImpact * currentLikelihood;

        const { riskSettings } = sharedRiskSettingsController;
        const severity: RiskScoreSeverity = (() => {
            if (!riskSettings?.thresholds || isEmpty(riskSettings.thresholds)) {
                return 'low';
            }

            return getScoreIntensityByThresholds(
                currentRiskScore,
                riskSettings.thresholds,
            );
        })();

        return (
            <RiskScore
                intensity="strong"
                severity={severity}
                scoreNumber={currentRiskScore}
                size="md"
            />
        );
    },
);
