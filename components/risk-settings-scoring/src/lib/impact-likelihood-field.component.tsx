import { Box } from '@cosmos/components/box';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { UniversalFormField } from '@ui/forms';
import { ReactiveRiskScore } from './reactive-risk-score.component';
import type { ImpactLikelihoodFieldProps } from './types';

export const ImpactLikelihoodField = ({
    formId,
    name,
    'data-id': dataId,
}: ImpactLikelihoodFieldProps): React.JSX.Element => {
    return (
        <Stack
            direction="column"
            gap="4x"
            data-id={dataId}
            data-testid="ImpactLikelihoodField"
        >
            <Text size="300" type="title">
                <Trans>Impact and likelihood scale</Trans>
            </Text>
            <Text size="200" type="body">
                <Trans>
                    You can adjust ranges anywhere from 3 to 10 building a graph
                    that&apos;s right for your organization. If you reduce
                    ranges, you lose the scores for the eliminated rows or
                    columns.
                </Trans>
            </Text>

            <Stack direction="row" gap="8x" align="end" pl="4x" py="12x">
                <UniversalFormField
                    __fromCustomRender
                    formId={formId}
                    name={`${name}.impact`}
                    data-id={`${dataId}-impact`}
                    aria-labelledby={`${dataId}-impact-label`}
                />

                <Box pb="2x">
                    <Icon
                        name="Close"
                        size="200"
                        aria-label={t`multiplied by`}
                    />
                </Box>

                <UniversalFormField
                    __fromCustomRender
                    formId={formId}
                    name={`${name}.likelihood`}
                    data-id={`${dataId}-likelihood`}
                    aria-labelledby={`${dataId}-likelihood-label`}
                />

                <Box pb="2x">
                    <Text size="500" aria-label={t`equals`}>
                        =
                    </Text>
                </Box>
                <Box pb="1x">
                    <ReactiveRiskScore name={name} />
                </Box>
            </Stack>
        </Stack>
    );
};
