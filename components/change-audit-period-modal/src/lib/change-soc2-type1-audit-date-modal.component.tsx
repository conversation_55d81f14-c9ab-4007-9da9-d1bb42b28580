import { z } from 'zod';
import type { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedChangeAuditPeriodController } from '@controllers/change-audit-period';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';

interface ChangeSoc2Type1AuditDateModalProps {
    auditFramework: NonNullable<
        typeof sharedAuditHubController.auditByIdData
    >['framework'];
    onClose: () => void;
}

const getFormSchema = (): FormSchema => ({
    auditDate: {
        type: 'date',
        label: t`Update audit period`,
        initialValue: undefined,
        locale: 'en-US',
        validator: z.string().nullable().optional(),
    },
});

export const ChangeSoc2Type1AuditDateModal = observer(
    ({ auditFramework, onClose }: ChangeSoc2Type1AuditDateModalProps) => {
        const { formRef, triggerSubmit } = useFormSubmit();

        const handleSubmit = (values: FormValues) => {
            const auditDate = values.auditDate as TDateISODate;

            sharedChangeAuditPeriodController.updateAuditPeriod(
                auditFramework.id,
                auditDate,
                auditDate,
                onClose,
            );
        };

        return (
            <>
                <Modal.Header
                    title={t`Change audit period`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={onClose}
                />
                <Modal.Body>
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        data-id="change-soc2-type1-audit-date-form"
                        formId="change-soc2-type1-audit-date-form"
                        schema={getFormSchema()}
                        onSubmit={handleSubmit}
                    />
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: onClose,
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            onClick: triggerSubmit,
                        },
                    ]}
                />
            </>
        );
    },
);
