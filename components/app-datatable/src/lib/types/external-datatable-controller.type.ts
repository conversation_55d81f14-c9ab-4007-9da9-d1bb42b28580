import type { DatatableProps, RowData } from '@cosmos/components/datatable';
import type { StandardQueryParams } from './standard-query-params.type';

export interface ExternalDatatableController<
    TData extends RowData,
    TQueryParams = StandardQueryParams,
> {
    readonly load: (query: TQueryParams) => void;
    readonly columns: DatatableProps<TData>['columns'];
    readonly data: DatatableProps<TData>['data'];
    readonly emptyStateProps?: DatatableProps<TData>['emptyStateProps'];
    readonly filterProps?: DatatableProps<TData>['filterProps'];
    readonly filterViewModeProps?: DatatableProps<TData>['filterViewModeProps'];
    readonly hidePagination?: DatatableProps<TData>['hidePagination'];
    readonly isLoading: DatatableProps<TData>['isLoading'];
    readonly rowActionsProps?: DatatableProps<TData>['rowActionsProps'];
    readonly tableId: DatatableProps<TData>['tableId'];
    readonly tableSearchProps?: DatatableProps<TData>['tableSearchProps'];
    readonly tableActions?: DatatableProps<TData>['tableActions'];
    readonly total: DatatableProps<TData>['total'];
    readonly onRowClick?: DatatableProps<TData>['onRowClick'];
    readonly bulkActionDropdownItems?: DatatableProps<TData>['bulkActionDropdownItems'];
    readonly isRowSelectionEnabled?: DatatableProps<TData>['isRowSelectionEnabled'];
    readonly getRowId?: DatatableProps<TData>['getRowId'];
    readonly onRowSelection?: DatatableProps<TData>['onRowSelection'];
}
