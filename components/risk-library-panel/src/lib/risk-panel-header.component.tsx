import { sharedRiskLibraryDetailsController } from '@controllers/risk';
import { Metadata } from '@cosmos/components/metadata';
import { PanelHeader } from '@cosmos/components/panel';
import { observer } from '@globals/mobx';

export const RiskLibraryPanelHeader = observer((): React.JSX.Element => {
    const { riskLibraryDetails } = sharedRiskLibraryDetailsController;
    const { riskId, title } = riskLibraryDetails ?? {};

    return (
        <PanelHeader
            title={title ?? ''}
            data-id="b4-p6GSH"
            slot={
                <Metadata
                    colorScheme="neutral"
                    label={riskId ?? ''}
                    type="tag"
                />
            }
        />
    );
});
