import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { CodeViewer } from '@cosmos-lab/components/code-viewer';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedResourceGuideModel } from '@models/resource-guide';

export const SchemaModal = observer((): React.JSX.Element => {
    const {
        selectedProvider,
        selectedService,
        selectedResource,
        currentSchema,
        closeSchemaModal,
    } = sharedResourceGuideModel;

    return (
        <>
            <Modal.Header
                title={t`Example data structure`}
                closeButtonAriaLabel={t`Close schema modal`}
                onClose={closeSchemaModal}
            />
            <Modal.Body>
                <Stack direction="column" gap="4x">
                    <Stack direction="column" gap="2x">
                        <Text allowBold type="body" size="200">
                            <strong>{t`Provider:`}</strong>{' '}
                            {selectedProvider?.label}
                        </Text>
                        <Text allowBold type="body" size="200">
                            <strong>{t`Service:`}</strong>{' '}
                            {selectedService?.label}
                        </Text>
                        <Text allowBold type="body" size="200">
                            <strong>{t`Resource:`}</strong>{' '}
                            {selectedResource?.label}
                        </Text>
                    </Stack>

                    <CodeViewer
                        data-id="resource-guide-schema-code-viewer"
                        language="json"
                        value={currentSchema || ''}
                        isEditable={false}
                    />
                </Stack>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Done`,
                        level: 'primary',
                        onClick: closeSchemaModal,
                    },
                ]}
            />
        </>
    );
});
