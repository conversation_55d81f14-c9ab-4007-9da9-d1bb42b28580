import { activeLibraryTestController } from '@controllers/library-test';
import type { Action } from '@cosmos/components/action-stack';
import { Card } from '@cosmos/components/card';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { DateTime } from '@cosmos-lab/components/date-time';
import type { FailsByCategoryResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    getLibraryTestRatingLabel,
    getMonitorCheckTypeLabel,
} from '@models/library-test';
import { openLibraryTestDetailsInstructionsModal } from './helpers/library-test-details-open-instructions-modal';

export const LibraryTestDetailsCardComponent = observer(
    (): React.JSX.Element => {
        const actions: Action[] = [];

        if (
            !activeLibraryTestController.isAiGenerated &&
            activeLibraryTestController.monitoringControlInstance
                ?.helpArticleUrl
        ) {
            actions.push({
                actionType: 'button',
                id: 'library-test-learn-more-button',
                typeProps: {
                    label: t`Learn more`,
                    endIconName: 'LinkOut',
                    level: 'secondary',
                    'data-id': 'library-test-learn-more-button',
                    target: '_blank',
                    href: activeLibraryTestController.monitoringControlInstance
                        .helpArticleUrl,
                },
            });
        }

        if (activeLibraryTestController.isAiGenerated) {
            actions.push({
                actionType: 'button',
                id: 'library-test-view-instructions-button',
                typeProps: {
                    label: t`View instructions`,
                    level: 'secondary',
                    'data-id': 'library-test-view-instructions-button',
                    onClick: openLibraryTestDetailsInstructionsModal,
                },
            });
        }
        const testCategories =
            activeLibraryTestController.monitoringControlInstance?.categories ??
            [];
        const testCategoryLabels = testCategories.map((category) => {
            return getMonitorCheckTypeLabel(
                category as FailsByCategoryResponseDto['category'],
            );
        });

        return (
            <Card
                title={t`Info`}
                data-testid="LibraryTestDetailsCardComponent"
                data-id="pec9DjVR"
                actions={actions}
                body={
                    <>
                        <KeyValuePair
                            label={t`Name`}
                            type="TEXT"
                            value={
                                activeLibraryTestController
                                    .monitoringControlInstance?.name ?? ''
                            }
                        />
                        <KeyValuePair
                            label={t`Description`}
                            type="TEXT"
                            value={
                                activeLibraryTestController
                                    .monitoringControlInstance?.description ??
                                ''
                            }
                        />
                        <KeyValuePair
                            label={t`Categories`}
                            type="TEXT"
                            value={testCategoryLabels.join(', ')}
                        />
                        <KeyValuePair
                            label={t`Rating`}
                            type="TEXT"
                            value={getLibraryTestRatingLabel(
                                activeLibraryTestController
                                    .monitoringControlInstance?.rating ?? '',
                            )}
                        />
                        <KeyValuePair
                            label={t`Last updated`}
                            type="REACT_NODE"
                            value={
                                <DateTime
                                    format="field"
                                    data-id="library-test-details-updated-at-datetime"
                                    date={
                                        activeLibraryTestController
                                            .monitoringControlInstance
                                            ?.updatedAt ?? ''
                                    }
                                />
                            }
                        />
                    </>
                }
            />
        );
    },
);
