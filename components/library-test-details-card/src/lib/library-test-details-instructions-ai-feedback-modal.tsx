import type { default as React } from 'react';
import { AiFeedbackModal } from '@components/ai-feedback';
import { sharedAIExecutionGroupFeedbackGroupsController } from '@controllers/ai-execution-group';
import type { SummaryFeedbackResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import type { FormValues } from '@ui/forms';

interface AiFeedbackModalProps {
    /** The ID of the test to submit feedback for. */
    testId: number;
    /** Callback function to close the modal. */
    onClose: () => void;
}

/**
 * Modal component for collecting AI feedback on library test instructions.
 * Allows users to submit positive or negative feedback with optional text comments.
 */
export const LibraryTestDetailsInstructionsAiFeedbackModal = observer(
    ({ onClose, testId }: AiFeedbackModalProps): React.JSX.Element => {
        const { isAnyOperationPending, submitFeedback } =
            sharedAIExecutionGroupFeedbackGroupsController;

        // Use the controller's computed loading state
        const isLoading = isAnyOperationPending;

        const handleSubmitFeedback = (values: FormValues) => {
            if (isLoading) {
                return;
            }

            submitFeedback({
                testId: String(testId),
                feedbackStatus:
                    values.feedbackStatus as SummaryFeedbackResponseDto['feedbackStatusType'],
                feedbackText: (values.feedbackText as string) || '',
                feedbackReason:
                    values.feedbackStatus === 'NOT_USEFUL' &&
                    values.feedbackReason
                        ? ([
                              values.feedbackReason,
                          ] as SummaryFeedbackResponseDto['feedbackTypes'])
                        : [],
                onSuccess: onClose,
                processFeature: 'MONITOR_TEST_TEMPLATE_INSTRUCTIONS',
            });
        };

        return (
            <AiFeedbackModal
                isLoading={isLoading}
                data-id="M9ElcCzX"
                onSubmit={handleSubmitFeedback}
            />
        );
    },
);
