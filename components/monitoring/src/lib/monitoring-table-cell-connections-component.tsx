import { isEmpty } from 'lodash-es';
import { EmptyStateTableCell } from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import { OrganizationStack } from '@cosmos-lab/components/organization-stack';
import { providers } from '@globals/providers';
import type { MonitoringTableCellProps } from './types/monitoring.types';

const MAX_VISIBLE_ITEMS = 3;

export const MonitoringTableCellConnectionsComponent = ({
    row: { original },
}: MonitoringTableCellProps): React.JSX.Element => {
    const { connections } = original;

    // KR note: as of 7/24/25 it is possible to have connections that are not in the providers list. These come from FE or BE only clientTypes.
    // This is a known issue and is the reason we need to filter out the undefined values.
    const relevantProviders = connections
        .map((connection) => providers[connection as keyof typeof providers])
        .filter(Boolean);

    return (
        <Stack
            gap="md"
            data-testid="MonitoringTableCellConnectionsComponent"
            data-id="PC9pC22V"
        >
            {isEmpty(connections) ? (
                <EmptyStateTableCell />
            ) : (
                <OrganizationStack
                    maxVisibleItems={MAX_VISIBLE_ITEMS}
                    organizationData={relevantProviders.map((provider) => ({
                        fallbackText: provider.name,
                        primaryLabel: provider.name,
                        imgSrc: provider.logo,
                    }))}
                />
            )}
        </Stack>
    );
};
