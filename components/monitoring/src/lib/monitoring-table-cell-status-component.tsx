import { sharedMonitoringController } from '@controllers/monitoring';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { ToggleGroup } from '@cosmos/components/toggle-group';
import { t } from '@globals/i18n/macro';
import type { MonitoringTableCellProps } from './types/monitoring.types';

export const MonitoringTableCellStatusComponent = ({
    row: { original },
}: MonitoringTableCellProps): React.JSX.Element => {
    const { checkStatus, testId } = original;

    if (checkStatus === 'TESTING') {
        return (
            <Metadata
                data-id="status-badge"
                colorScheme="neutral"
                type="status"
                label={t`Testing...`}
            />
        );
    }

    const toggleId = `test-status-toggle-${testId}`;

    return (
        <Stack
            gap="md"
            data-testid="MonitoringTableCellStatusComponent"
            data-id="7o3eL0Nm"
        >
            {checkStatus === 'UNUSED' ? (
                <Text id="toggle-label-unused" size="100">
                    {t`Unused`}
                </Text>
            ) : (
                <ToggleGroup
                    id={toggleId}
                    selectedOption={checkStatus}
                    options={[
                        { value: 'ENABLED', label: t`Enabled` },
                        {
                            value: 'DISABLED',
                            label: t`Disabled`,
                        },
                    ]}
                    onChange={(newStatus) => {
                        if (
                            newStatus !== 'ENABLED' &&
                            newStatus !== 'DISABLED'
                        ) {
                            return;
                        }

                        sharedMonitoringController.handleMonitorStatusToggle(
                            testId,
                            newStatus,
                        );
                    }}
                />
            )}
        </Stack>
    );
};
