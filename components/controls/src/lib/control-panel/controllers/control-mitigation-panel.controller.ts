import { isNil } from 'lodash-es';
import {
    sharedControlDetailsController,
    sharedControlDetailsOrchestratorController,
    sharedControlMitigationTabController,
    sharedControlsController,
} from '@controllers/controls';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import type {
    ControlListResponseDto,
    ControlShortResponseDto,
} from '@globals/api-sdk/types';
import { computed, makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export class ControlMitigationPanelController {
    constructor() {
        makeAutoObservable(this);
    }

    get controls(): ControlListResponseDto[] {
        return sharedControlsController.controls;
    }

    get controlDetails(): ControlShortResponseDto | null {
        return sharedControlDetailsController.controlDetails;
    }

    get activeTab(): string {
        return sharedControlMitigationTabController.activeTab;
    }

    get currentWorkspaceId(): number | null {
        return sharedWorkspacesController.currentWorkspaceId;
    }

    get notReadyControls(): ControlListResponseDto[] | undefined {
        return computed(() =>
            this.controls.filter((control) => !control.isReady),
        ).get();
    }

    get currentControlIndex(): number {
        return (
            this.notReadyControls?.findIndex(
                (control) => control.id === this.controlDetails?.id,
            ) ?? -1
        );
    }

    get currentItem(): number {
        return this.currentControlIndex >= 0 ? this.currentControlIndex + 1 : 1;
    }

    get totalItems(): number {
        return this.notReadyControls?.length ?? 0;
    }

    get hasNextControl(): boolean {
        return (
            this.currentControlIndex < (this.notReadyControls?.length ?? 0) - 1
        );
    }

    get hasPrevControl(): boolean {
        return this.currentControlIndex > 0;
    }

    setActiveTab = (tabId: string): void => {
        sharedControlMitigationTabController.setActiveTab(tabId);
    };

    goToControlDetails = (): void => {
        if (
            !isNil(this.controlDetails?.id) &&
            !isNil(this.currentWorkspaceId)
        ) {
            sharedProgrammaticNavigationController.navigateFn?.(
                `/workspaces/${this.currentWorkspaceId}/compliance/controls/${this.controlDetails.id}/overview`,
            );
        }
    };

    handleNextPage = (): void => {
        if (!this.hasNextControl) {
            return;
        }
        const nextControl =
            this.notReadyControls?.[this.currentControlIndex + 1];

        if (!isNil(nextControl?.id)) {
            sharedControlDetailsOrchestratorController.load(nextControl.id);
        }
    };

    handlePrevPage = (): void => {
        if (!this.hasPrevControl) {
            return;
        }
        const prevControl =
            this.notReadyControls?.[this.currentControlIndex - 1];

        if (!isNil(prevControl?.id)) {
            sharedControlDetailsOrchestratorController.load(prevControl.id);
        }
    };
}

export const sharedControlMitigationPanelController =
    new ControlMitigationPanelController();
