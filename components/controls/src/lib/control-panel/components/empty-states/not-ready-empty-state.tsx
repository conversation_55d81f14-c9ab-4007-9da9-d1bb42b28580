import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { List } from '@cosmos-lab/components/list';
import { t } from '@globals/i18n/macro';
import { sharedControlMitigationPanelController } from '../../controllers';

const InformationalText = (): React.JSX.Element => {
    const stepsToMakeControlReady = [
        t`Add evidence that demonstrates the control is implemented.`,
        t`Map a test to validate the control’s effectiveness.`,
        t`Map a policy that outlines how the control is governed.`,
    ];

    return (
        <Stack
            direction={'column'}
            gap="4x"
            data-testid="InformationalText"
            data-id="Jbz3Jmyg"
        >
            <Stack direction="column">
                <Text>
                    {t`This control is currently not ready because it has no mappings associated with it. To start evaluating its readiness, please take one of the following actions:`}
                </Text>
                <List items={stepsToMakeControlReady} />
            </Stack>
            <Text>
                {t`Once one or more of these items are in place, the control will be evaluated for its readiness.`}
            </Text>
        </Stack>
    );
};

export const NotReadyEmptyState = (): React.JSX.Element => {
    const { goToControlDetails } = sharedControlMitigationPanelController;

    return (
        <EmptyState
            illustrationName="AddAi"
            title={t`Not ready empty state`}
            data-testid="NotReadyEmptyState"
            data-id="hMmjDBrn"
            description={InformationalText()}
            leftAction={
                <Button
                    label={t`View control details`}
                    level="primary"
                    onClick={goToControlDetails}
                />
            }
        />
    );
};
