import { capitalize } from 'lodash-es';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type { MetadataProps } from '@cosmos/components/metadata';
import type { MonitorV2ControlTestInstanceOverviewResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { formatDate } from '@helpers/date-time';
import { CheckResultStatus, CheckStatus } from '@helpers/evidence';
import { getStatusColorScheme } from '@models/monitoring-details';

export const createMetadataIfCount = (count: number): MetadataProps => {
    return {
        label: count > 99 ? '99+' : count.toString(),
        type: 'number',
        colorScheme: 'critical',
    };
};

export const getKeyValuePairStructure = (
    checkResultStatus: MonitorV2ControlTestInstanceOverviewResponseDto['checkResultStatus'],
    checkStatus: MonitorV2ControlTestInstanceOverviewResponseDto['checkStatus'],
    name: string,
    lastCheck?: string,
): KeyValuePairProps[] => {
    const testResultMetadata: Pick<KeyValuePairProps, 'value' | 'type'> =
        CheckResultStatus[checkResultStatus] === CheckResultStatus.READY ||
        CheckStatus[checkStatus] === CheckStatus.UNUSED
            ? {
                  value: '—',
                  type: 'TEXT',
              }
            : {
                  value: {
                      label: capitalize(checkResultStatus),
                      colorScheme: getStatusColorScheme(checkResultStatus),
                      type: 'status' as const,
                  },
                  type: 'BADGE',
              };

    return [
        {
            id: 'control-action-panel-header-test-name',
            'data-id': 'control-action-panel-header-test-name',
            label: t`Test name`,
            value: name,
            type: 'TEXT',
        },
        {
            id: 'control-action-panel-header-result',
            'data-id': 'control-action-panel-header-result',
            label: t`Result`,
            ...testResultMetadata,
        },
        {
            id: 'control-action-panel-header-last-run',
            'data-id': 'control-action-panel-header-last-run',
            label: t`Last run`,
            value:
                CheckStatus[checkStatus] === CheckStatus.UNUSED
                    ? '—'
                    : formatDate('field_time', lastCheck || undefined),
            type: 'TEXT',
        },
    ] as KeyValuePairProps[];
};
