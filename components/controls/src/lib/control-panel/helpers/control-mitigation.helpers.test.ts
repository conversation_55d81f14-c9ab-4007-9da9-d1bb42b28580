import { describe, expect, test } from 'vitest';
import {
    createMetadataIfCount,
    getKeyValuePairStructure,
} from './control-mitigation.helpers';

describe('createMetadataIfCount', () => {
    test('should return metadata object when count is greater than 0', () => {
        const result = createMetadataIfCount(5);

        expect(result).toStrictEqual({
            label: '5',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should return metadata object when count is 1', () => {
        const result = createMetadataIfCount(1);

        expect(result).toStrictEqual({
            label: '1',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should convert count to string in label', () => {
        const result = createMetadataIfCount(123);

        expect(result).toStrictEqual({
            label: '99+',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should show "99+" for numbers greater than 99', () => {
        const result = createMetadataIfCount(100);

        expect(result).toStrictEqual({
            label: '99+',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should show "99+" for very large numbers', () => {
        const result = createMetadataIfCount(9999);

        expect(result).toStrictEqual({
            label: '99+',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should show exact number for 99', () => {
        const result = createMetadataIfCount(99);

        expect(result).toStrictEqual({
            label: '99',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should show "99+" for 101', () => {
        const result = createMetadataIfCount(101);

        expect(result).toStrictEqual({
            label: '99+',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should return undefined for negative numbers (edge case)', () => {
        const result = createMetadataIfCount(-1);

        // Based on current logic, negative numbers would return metadata
        // but this might be an edge case to consider
        expect(result).toStrictEqual({
            label: '-1',
            type: 'number',
            colorScheme: 'critical',
        });
    });

    test('should always use "number" type', () => {
        const result = createMetadataIfCount(42);

        expect(result.type).toBe('number');
    });

    test('should always use "critical" color scheme', () => {
        const result = createMetadataIfCount(42);

        expect(result.colorScheme).toBe('critical');
    });
});

describe('getKeyValuePairStructure', () => {
    test('should return correct structure for FAILED status', () => {
        const result = getKeyValuePairStructure(
            'FAILED',
            'ENABLED',
            'Test Monitor Name',
            '2024-01-15T10:30:00Z',
        );

        expect(result).toHaveLength(3);
        expect(result[0]).toStrictEqual({
            id: 'control-action-panel-header-test-name',
            'data-id': 'control-action-panel-header-test-name',
            label: 'Test name',
            value: 'Test Monitor Name',
            type: 'TEXT',
        });
        expect(result[1]).toStrictEqual({
            id: 'control-action-panel-header-result',
            'data-id': 'control-action-panel-header-result',
            label: 'Result',
            value: {
                label: 'Failed',
                colorScheme: 'critical',
                type: 'status',
            },
            type: 'BADGE',
        });
        // Validate last run structure without checking specific date value
        expect(result[2]).toMatchObject({
            id: 'control-action-panel-header-last-run',
            'data-id': 'control-action-panel-header-last-run',
            label: 'Last run',
            type: 'TEXT',
        });
        expect(typeof result[2].value).toBe('string');
        expect(result[2].value).not.toBe('—');
    });

    test('should return correct structure for PASSED status', () => {
        const result = getKeyValuePairStructure(
            'PASSED',
            'ENABLED',
            'Successful Test',
            '2024-02-20T14:45:00Z',
        );

        expect(result).toHaveLength(3);
        expect(result[0]).toMatchObject({
            id: 'control-action-panel-header-test-name',
            'data-id': 'control-action-panel-header-test-name',
            label: 'Test name',
            value: 'Successful Test',
            type: 'TEXT',
        });
        expect(result[1]).toStrictEqual({
            id: 'control-action-panel-header-result',
            'data-id': 'control-action-panel-header-result',
            label: 'Result',
            value: {
                label: 'Passed',
                colorScheme: 'success',
                type: 'status',
            },
            type: 'BADGE',
        });
        // Validate last run structure without checking specific date value
        expect(result[2]).toMatchObject({
            id: 'control-action-panel-header-last-run',
            'data-id': 'control-action-panel-header-last-run',
            label: 'Last run',
            type: 'TEXT',
        });
        expect(typeof result[2].value).toBe('string');
        expect(result[2].value).not.toBe('—');
    });

    test('should handle missing lastCheck date', () => {
        const result = getKeyValuePairStructure(
            'FAILED',
            'UNUSED',
            'Test Without Date',
        );

        expect(result[2].value).toBe('—');
    });

    test('should handle UNUSED status with dash for last run', () => {
        const result = getKeyValuePairStructure(
            'FAILED',
            'UNUSED',
            'Unused Test',
            '2024-01-15T10:30:00Z',
        );

        expect(result[2].value).toBe('—');
    });
});
