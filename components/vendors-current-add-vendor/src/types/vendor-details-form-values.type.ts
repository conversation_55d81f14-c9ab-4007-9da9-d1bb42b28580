import type { ListBoxItemData } from '@cosmos/components/list-box';

export interface VendorDetailsFormValuesType {
    name?: string | null;
    url?: string | null;
    servicesProvided?: string | null;
    passwordPolicyGroup?: {
        passwordPolicy?: ListBoxItemData | string | null;
        passwordRequiresMinLength?: boolean | null;
        passwordMinLength?: ListBoxItemData | number | null;
        passwordRequiresNumber?: boolean | null;
        passwordRequiresSymbol?: boolean | null;
        passwordMfaEnabled?: boolean | null;
    };
    trustCenterUrl?: string | null;
    privacyUrl?: string | null;
    termsUrl?: string | null;
    contactAtVendor?: string | null;
    contactsEmail?: string | null;
    // Custom fields will be added dynamically
    [key: string]: unknown;
}
