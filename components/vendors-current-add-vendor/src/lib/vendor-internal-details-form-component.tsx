import { forwardRef } from 'react';
import { observer } from '@globals/mobx';
import { Form, type FormValues } from '@ui/forms';
import { sharedVendorInternalDetailsFormModel } from './models/vendor-internal-details-form.model';

interface VendorInternalDetailsFormComponentParams {
    formId: string;
    isAddingVendor?: boolean;
    handleOnSubmit: (values: FormValues) => void;
}

const VendorInternalDetailsFormComponentInner = forwardRef<
    HTMLFormElement,
    VendorInternalDetailsFormComponentParams
>(
    (
        { formId, handleOnSubmit, isAddingVendor = false },
        ref,
    ): React.JSX.Element => {
        return (
            <Form
                hasExternalSubmitButton
                formId={formId}
                data-id={'VendorInternalDetailsFormComponent'}
                data-testid="VendorInternalDetailsFormComponent"
                ref={ref}
                schema={sharedVendorInternalDetailsFormModel.getSchema(
                    isAddingVendor,
                )}
                onSubmit={handleOnSubmit}
            />
        );
    },
);

VendorInternalDetailsFormComponentInner.displayName =
    'VendorInternalDetailsFormComponent';

export const VendorInternalDetailsFormComponent = observer(
    VendorInternalDetailsFormComponentInner,
);
