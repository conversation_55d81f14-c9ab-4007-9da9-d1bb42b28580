import { isEmpty, isNil } from 'lodash-es';
import { z } from 'zod';
import {
    sharedVendorCustomFieldsController,
    sharedVendorsCreateCurrentVendorController,
    sharedVendorsDetailsController,
} from '@controllers/vendors';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { validateUrlWithTLDCheck } from '@globals/zod';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import type { FormSchema } from '@ui/forms';
import {
    getPasswordPolicyLabel,
    PASSWORD_POLICY_VALUES,
} from '../../constants/password-policy.constants';
import {
    findInOptions,
    toFieldOptionsWithLabelFunction,
} from '../../helpers/field-option.helpers';
import {
    buildPasswordMinLengthListBoxItemData,
    transformPasswordMimLengthToListBoxItemData,
} from '../../helpers/vendor-add-vendor.helper';
import type { VendorDetailsFormValuesType } from '../../types/vendor-details-form-values.type';
import { overlayCustomFieldsInitialValues } from '../helpers/custom-fields-schema.helper';

class VendorDetailsFormModel {
    constructor() {
        makeAutoObservable(this);
    }

    get storedValues() {
        return isNil(sharedVendorsDetailsController.vendorDetails)
            ? sharedVendorsCreateCurrentVendorController.vendorDetails
            : sharedVendorsDetailsController.vendorDetails;
    }

    getCustomFieldsSchema(isAddingVendor: boolean): FormSchema {
        const {
            vendorDetailsCustomFieldsByVendorId,
            vendorDetailsCustomFieldsList,
        } = sharedVendorCustomFieldsController;

        if (sharedFeatureAccessModel.isCustomFieldsEnabled) {
            const customFieldsSchema =
                sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
                    isAddingVendor
                        ? vendorDetailsCustomFieldsList
                        : vendorDetailsCustomFieldsByVendorId,
                );

            const storedValues = isNil(
                sharedVendorsDetailsController.vendorDetailsTemporalValues,
            )
                ? sharedVendorsCreateCurrentVendorController.vendorDetails
                : sharedVendorsDetailsController.vendorDetailsTemporalValues;

            overlayCustomFieldsInitialValues(customFieldsSchema, storedValues);

            return customFieldsSchema;
        }

        return {};
    }

    getSchema(isAddingVendor = false): FormSchema {
        const storedValues = isNil(
            sharedVendorsDetailsController.vendorDetailsTemporalValues,
        )
            ? sharedVendorsCreateCurrentVendorController.vendorDetails
            : sharedVendorsDetailsController.vendorDetailsTemporalValues;

        const vendorDetails: VendorDetailsFormValuesType = {
            ...storedValues,
        };

        const baseSchema: FormSchema = {
            name: {
                type: 'text',
                initialValue: vendorDetails.name ?? '',
                label: t`Vendor name`,
                validator: z
                    .string()
                    .min(1, {
                        message: t`Vendor name is required`,
                    })
                    .refine((val) => !isEmpty(val.trim()), {
                        message: t`Vendor name is required`,
                    }),
            },
            url: {
                type: 'text',
                initialValue: vendorDetails.url ?? '',
                isOptional: true,
                label: t`Website URL`,
                validator: validateUrlWithTLDCheck(
                    t`Website URL must be a valid URL`,
                ),
            },
            servicesProvided: {
                type: 'textarea',
                initialValue: vendorDetails.servicesProvided ?? '',
                isOptional: true,
                label: t`Provided services`,
            },
            passwordPolicyGroup: {
                type: 'group',
                header: t`Password policy`,
                fields: {
                    passwordPolicy: {
                        type: 'select',
                        options: toFieldOptionsWithLabelFunction(
                            PASSWORD_POLICY_VALUES,
                            getPasswordPolicyLabel,
                        ),
                        initialValue: vendorDetails.passwordPolicyGroup
                            ?.passwordPolicy
                            ? findInOptions(
                                  vendorDetails.passwordPolicyGroup
                                      .passwordPolicy as string,
                                  toFieldOptionsWithLabelFunction(
                                      PASSWORD_POLICY_VALUES,
                                      getPasswordPolicyLabel,
                                  ),
                              )
                            : findInOptions(
                                  'NONE',
                                  toFieldOptionsWithLabelFunction(
                                      PASSWORD_POLICY_VALUES,
                                      getPasswordPolicyLabel,
                                  ),
                              ),
                        label: t`Password policy`,
                        isOptional: true,
                    },
                    passwordRequiresMinLength: {
                        type: 'checkbox',
                        initialValue:
                            vendorDetails.passwordPolicyGroup
                                ?.passwordRequiresMinLength ?? false,
                        label: t`Minimum length`,
                        isOptional: true,
                        shownIf: {
                            fieldName: 'passwordPolicyGroup.passwordPolicy',
                            operator: 'equals',
                            value: 'USERNAME_PASSWORD',
                        },
                    },
                    passwordMinLength: {
                        type: 'select',
                        options: buildPasswordMinLengthListBoxItemData(),
                        initialValue:
                            transformPasswordMimLengthToListBoxItemData(
                                vendorDetails.passwordPolicyGroup
                                    ?.passwordMinLength as number | undefined,
                            ),
                        label: t`Minimum length`,
                        shownIf: {
                            operator: 'and',
                            conditions: [
                                {
                                    fieldName:
                                        'passwordPolicyGroup.passwordPolicy',
                                    operator: 'equals',
                                    value: 'USERNAME_PASSWORD',
                                },
                                {
                                    fieldName:
                                        'passwordPolicyGroup.passwordRequiresMinLength',
                                    operator: 'equals',
                                    value: true,
                                },
                            ],
                        },
                    },
                    passwordRequiresNumber: {
                        type: 'checkbox',
                        initialValue:
                            vendorDetails.passwordPolicyGroup
                                ?.passwordRequiresNumber ?? false,
                        label: t`Requires number`,
                        isOptional: true,
                        shownIf: {
                            fieldName: 'passwordPolicyGroup.passwordPolicy',
                            operator: 'notEquals',
                            value: 'NONE',
                        },
                    },
                    passwordRequiresSymbol: {
                        type: 'checkbox',
                        initialValue:
                            vendorDetails.passwordPolicyGroup
                                ?.passwordRequiresSymbol ?? false,
                        label: t`Requires symbol`,
                        isOptional: true,
                        shownIf: {
                            fieldName: 'passwordPolicyGroup.passwordPolicy',
                            operator: 'notEquals',
                            value: 'NONE',
                        },
                    },
                    passwordMfaEnabled: {
                        type: 'checkbox',
                        initialValue:
                            vendorDetails.passwordPolicyGroup
                                ?.passwordMfaEnabled ?? false,
                        label: t`Two-Factor Authentication Enabled`,
                        isOptional: true,
                        shownIf: {
                            fieldName: 'passwordPolicyGroup.passwordPolicy',
                            operator: 'notEquals',
                            value: 'NONE',
                        },
                    },
                },
            },
            trustCenterUrl: {
                type: 'text',
                initialValue: vendorDetails.trustCenterUrl ?? '',
                isOptional: true,
                label: t`Trust Center URL`,
                validator: validateUrlWithTLDCheck(
                    t`Trust Center URL must be a valid URL`,
                ),
            },
            privacyUrl: {
                type: 'text',
                initialValue: vendorDetails.privacyUrl ?? '',
                isOptional: true,
                label: t`Privacy policy URL`,
                validator: validateUrlWithTLDCheck(
                    t`Privacy policy URL must be a valid URL`,
                ),
            },
            termsUrl: {
                type: 'text',
                initialValue: vendorDetails.termsUrl ?? '',
                isOptional: true,
                label: t`Terms of use URL`,
                validator: validateUrlWithTLDCheck(
                    t`Terms of use URL must be a valid URL`,
                ),
            },
            contactAtVendor: {
                type: 'text',
                initialValue: vendorDetails.contactAtVendor ?? '',
                isOptional: true,
                label: t`Vendor contact name`,
            },
            contactsEmail: {
                type: 'text',
                initialValue: vendorDetails.contactsEmail ?? '',
                isOptional: true,
                label: t`Vendor contact email address`,
                validator: z.string().email().or(z.literal('')),
            },
        };

        const customFieldsSchema = this.getCustomFieldsSchema(isAddingVendor);

        return {
            ...baseSchema,
            ...customFieldsSchema,
        };
    }
}

export const sharedVendorDetailsFormModel = new VendorDetailsFormModel();
