import { sharedVendorCustomFieldsController } from '@controllers/vendors';
import { Grid } from '@cosmos/components/grid';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Text } from '@cosmos/components/text';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { VendorResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { formatCurrencyValue, getFullName } from '@helpers/formatters';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import {
    transformBooleanToLabel,
    transformCategoryToLabel,
    transformImpactLevelToLabel,
    transformIntegrationToLabel,
    transformRiskToLabel,
    transformStatusToLabel,
    transformTypeToLabel,
} from '../helpers/vendor-add-vendor.helper';

interface VendorInternalDetailsInfoComponentProps {
    state: VendorResponseDto | null;
}

export const VendorInternalDetailsInfoComponent = observer(
    ({ state }: VendorInternalDetailsInfoComponentProps): React.JSX.Element => {
        const {
            vendorInternalDetailsCustomFieldsByVendorId,
            isLoadingByVendorId,
        } = sharedVendorCustomFieldsController;
        const customFieldsElements =
            sharedFeatureAccessModel.isCustomFieldsEnabled &&
            !isLoadingByVendorId
                ? sharedCustomFieldsManager.renderReadOnlyCustomFields(
                      vendorInternalDetailsCustomFieldsByVendorId,
                  )
                : [];

        return (
            <Grid
                gap="4x"
                data-testid="VendorInternalDetailsInfoComponent"
                data-id="a2JEtDwl"
            >
                <KeyValuePair
                    label={t`Status`}
                    type="REACT_NODE"
                    value={
                        state?.status ? (
                            <Text>{transformStatusToLabel(state.status)}</Text>
                        ) : (
                            <EmptyValue label={t`Status`} />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Type`}
                    type="REACT_NODE"
                    value={
                        state?.type ? (
                            <Text>{transformTypeToLabel(state.type)}</Text>
                        ) : (
                            <EmptyValue label={t`Type`} />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Business unit`}
                    type="REACT_NODE"
                    value={
                        state?.category ? (
                            <Text>
                                {transformCategoryToLabel(state.category)}
                            </Text>
                        ) : (
                            <EmptyValue label={t`Business unit`} />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Risk`}
                    type="REACT_NODE"
                    value={
                        state?.risk ? (
                            <Text>{transformRiskToLabel(state.risk)}</Text>
                        ) : (
                            <EmptyValue label={t`Risk`} />
                        )
                    }
                />

                {!sharedFeatureAccessModel.isVendorRiskManagementProEnabled && (
                    <KeyValuePair
                        label={t`Impact Level`}
                        type="REACT_NODE"
                        value={
                            state?.impactLevel ? (
                                <Text>
                                    {transformImpactLevelToLabel(
                                        state.impactLevel,
                                    )}
                                </Text>
                            ) : (
                                <EmptyValue label={t`Impact Level`} />
                            )
                        }
                    />
                )}
                <KeyValuePair
                    label={t`Vendor is a subprocessor`}
                    type="REACT_NODE"
                    value={
                        state?.isSubProcessor ? (
                            <Text>
                                {transformBooleanToLabel(state.isSubProcessor)}
                            </Text>
                        ) : (
                            <EmptyValue
                                label={t`Vendor is not a subprocessor`}
                            />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Stored data`}
                    type="REACT_NODE"
                    value={
                        state?.dataStored ? (
                            <Text>{state.dataStored}</Text>
                        ) : (
                            <EmptyValue label={t`Stored data`} />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Stores PII`}
                    type="REACT_NODE"
                    value={
                        state?.hasPii ? (
                            <Text>{transformBooleanToLabel(state.hasPii)}</Text>
                        ) : (
                            <EmptyValue label={t`Stores PII`} />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Data location`}
                    type="REACT_NODE"
                    value={
                        state?.location ? (
                            <Text>{state.location}</Text>
                        ) : (
                            <EmptyValue label={t`Data location`} />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Integrations`}
                    type="REACT_NODE"
                    value={
                        state?.integrations ? (
                            <Text>
                                {transformIntegrationToLabel(
                                    state.integrations,
                                )}
                            </Text>
                        ) : (
                            <EmptyValue label={t`Integrations`} />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Security owner`}
                    type="REACT_NODE"
                    value={
                        state?.user ? (
                            <Text>
                                {getFullName(
                                    state.user.firstName,
                                    state.user.lastName,
                                )}
                            </Text>
                        ) : (
                            <EmptyValue label={t`Security owner`} />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Vendor relationship contact`}
                    type="REACT_NODE"
                    value={
                        state?.contact ? (
                            <Text>
                                {getFullName(
                                    state.contact.firstName,
                                    state.contact.lastName,
                                )}
                            </Text>
                        ) : (
                            <EmptyValue
                                label={t`Vendor relationship contact`}
                            />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Annual contract value`}
                    type="REACT_NODE"
                    value={
                        state?.cost ? (
                            <Text>{formatCurrencyValue(state.cost)}</Text>
                        ) : (
                            <EmptyValue label={t`Cost`} />
                        )
                    }
                />
                <KeyValuePair
                    label={t`Additional notes`}
                    type="REACT_NODE"
                    value={
                        state?.notes ? (
                            <Text>{state.notes}</Text>
                        ) : (
                            <EmptyValue label={t`Additional notes`} />
                        )
                    }
                />
                {customFieldsElements}
            </Grid>
        );
    },
);
