import { describe, expect, test } from 'vitest';
import type { FormSchema } from '@ui/forms';
import { overlayCustomFieldsInitialValues } from './custom-fields-schema.helper';

describe('overlayCustomFieldsInitialValues', () => {
    test('overlays initial values for matching customField_* keys', () => {
        const schema = {
            fieldA: { type: 'text' },
            customField_1: { type: 'text' },
            customField_2: { type: 'text', initialValue: 'old' },
        } as unknown as FormSchema;

        const stored: Record<string, unknown> = {
            customField_1: 'value1',
            customField_2: 'new',
            nonCustom: 'ignored',
        };

        const result = overlayCustomFieldsInitialValues(schema, stored);

        // returns same reference (mutates)
        expect(result === schema).toBeTruthy();

        // overlays only matching custom fields
        expect(schema.customField_1.initialValue).toBe('value1');
        expect(schema.customField_2.initialValue).toBe('new');

        // non-custom keys not affected
        expect(
            (schema.fieldA as { initialValue?: unknown }).initialValue,
        ).toBeUndefined();
    });

    test('ignores undefined stored values (does not set initialValue)', () => {
        const schema = {
            customField_3: { type: 'text' },
        } as unknown as FormSchema;
        const stored: Record<string, unknown> = {
            customField_3: undefined as unknown as string,
        };

        overlayCustomFieldsInitialValues(schema, stored);

        expect(schema.customField_3.initialValue).toBeUndefined();
    });

    test('handles null/undefined storedValues gracefully', () => {
        const schema1 = {
            customField_4: { type: 'text' },
        } as unknown as FormSchema;
        const schema2 = {
            customField_5: { type: 'text' },
        } as unknown as FormSchema;

        const res1 = overlayCustomFieldsInitialValues(schema1, null);
        const res2 = overlayCustomFieldsInitialValues(schema2, undefined);

        expect(res1 === schema1).toBeTruthy();
        expect(res2 === schema2).toBeTruthy();
        expect(schema1.customField_4.initialValue).toBeUndefined();
        expect(schema2.customField_5.initialValue).toBeUndefined();
    });

    test('supports object values (e.g. list-box items) as initialValue', () => {
        const schema: FormSchema = {
            customField_select: { type: 'select' },
        } as unknown as FormSchema;

        const listBoxItem = { id: '1', label: 'One', value: '1' };
        const stored: Record<string, unknown> = {
            customField_select: listBoxItem,
        };

        overlayCustomFieldsInitialValues(schema, stored);

        expect(schema.customField_select.initialValue).toStrictEqual(
            listBoxItem,
        );
    });
});
