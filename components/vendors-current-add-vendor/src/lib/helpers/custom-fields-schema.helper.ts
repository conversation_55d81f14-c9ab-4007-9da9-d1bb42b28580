import type { FormSchema } from '@ui/forms';

/**
 * Overlays controller stored values into the custom fields portion of a schema.
 * It mutates the schema and also returns it for convenience.
 */
export function overlayCustomFieldsInitialValues(
    schema: FormSchema,
    storedValues: unknown,
): FormSchema {
    const stored = (storedValues ?? {}) as Record<string, unknown>;

    Object.keys(schema)
        .filter((key) => key.startsWith('customField_'))
        .forEach((key) => {
            const storedValue = stored[key];

            if (storedValue !== undefined) {
                (schema[key] as { initialValue?: unknown }).initialValue =
                    storedValue;
            }
        });

    return schema;
}
