import { forwardRef } from 'react';
import { observer } from '@globals/mobx';
import { Form, type FormValues } from '@ui/forms';
import { sharedVendorDetailsFormModel } from './models/vendor-details-form.model';

interface VendorDetailsFormComponentParams {
    formId: string;
    isAddingVendor?: boolean;
    handleOnSubmit: (values: FormValues) => void;
}

export const VendorDetailsFormInner = forwardRef<
    HTMLFormElement,
    VendorDetailsFormComponentParams
>(
    (
        { formId, isAddingVendor = false, handleOnSubmit },
        ref,
    ): React.JSX.Element => {
        return (
            <Form
                hasExternalSubmitButton
                formId={formId}
                data-id={'VendorDetailsFormComponent'}
                data-testid="VendorDetailsFormComponent"
                schema={sharedVendorDetailsFormModel.getSchema(isAddingVendor)}
                ref={ref}
                onSubmit={handleOnSubmit}
            />
        );
    },
);

VendorDetailsFormInner.displayName = 'VendorDetailsFormComponent';

export const VendorDetailsFormComponent = observer(VendorDetailsFormInner);
