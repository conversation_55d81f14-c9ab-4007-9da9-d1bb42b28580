import { EmptyState } from '@cosmos/components/empty-state';
import { t } from '@globals/i18n/macro';

export const PoliciesApprovalEmptyStateComponent = (): React.JSX.Element => {
    return (
        <EmptyState
            illustrationName="AddApproval"
            title={t`No approvals configured`}
            description={t`Once a policy owner is selected it will be set as the default approver. You can adjust approvals from the approval settings.`}
            data-testid="PoliciesApprovalEmptyStateComponent"
            data-id="policies-approval-empty-state"
        />
    );
};
