import { isEmpty } from 'lodash-es';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Divider } from '@cosmos-lab/components/divider';
import type { P3MatrixSlaResponseDto } from '@globals/api-sdk/types';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';
import { getSecurityLevel } from '../helpers/get-security-level.helper';

interface FormFieldValues {
    id: number;
    label: string;
    value: string;
    meta: string;
}

interface FieldValue {
    id: number;
    policyP3MatrixSLAId: number;
    severity: P3MatrixSlaResponseDto['severity'];
    timeFrame: FormFieldValues;
    definition: FormFieldValues;
    examples: FormFieldValues;
}

export const P3MatrixSLAFields = ({
    'data-id': dataId,
    name,
    label,
    formId,
    value: fieldValue,
}: Omit<CustomFieldRenderProps, 'onChange'>): React.JSX.Element | null => {
    const fieldValues: FieldValue[] = Array.isArray(fieldValue)
        ? (fieldValue as FieldValue[])
        : [];

    if (isEmpty(fieldValues)) {
        // Render nothing when there's no data
        return null;
    }

    return (
        <Card
            data-testid="P3MatrixSLAFields"
            data-id={dataId}
            title={label}
            body={
                <FormField
                    shouldHideLabel
                    label=""
                    formId={formId}
                    name={name}
                    renderInput={() => (
                        <Stack
                            gap="lg"
                            direction="column"
                            data-id={`${dataId}-inputs`}
                        >
                            {fieldValues.map(({ severity }, index) => {
                                return (
                                    <Stack
                                        key={`${name}-${index.toString()}`}
                                        gap="lg"
                                        direction="column"
                                        data-id={`${dataId}-matrix-${index}`}
                                    >
                                        {index > 0 && (
                                            <Box pb="lg">
                                                <Divider />
                                            </Box>
                                        )}
                                        <Text type="title">
                                            {getSecurityLevel(severity)}
                                        </Text>

                                        <UniversalFormField
                                            name={`${name}[${index}].timeFrame`}
                                            formId={formId}
                                            data-id={`${dataId}-timeFrame`}
                                        />
                                        <UniversalFormField
                                            name={`${name}[${index}].definition`}
                                            formId={formId}
                                            data-id={`${dataId}-definition`}
                                        />
                                        <UniversalFormField
                                            name={`${name}[${index}].examples`}
                                            formId={formId}
                                            data-id={`${dataId}-examples`}
                                        />
                                    </Stack>
                                );
                            })}
                        </Stack>
                    )}
                />
            }
        ></Card>
    );
};
