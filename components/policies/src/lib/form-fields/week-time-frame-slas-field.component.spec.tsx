import { afterEach, beforeEach, describe, expect, test } from 'vitest';
import { cleanup, render, screen } from '@testing-library/react';
import { type FieldSchema, type FormSchema, FormWrapper } from '@ui/forms';
// cspell:ignore slas
import { WeekTimeFrameSLAField } from './week-time-frame-slas-field.component';

const formId = 'test-form-id';

describe('weekTimeFrameSLAField', () => {
    beforeEach(() => {
        cleanup();
    });
    afterEach(() => {
        cleanup();
    });

    test('renders items using labels from meta', () => {
        const schema: FormSchema = {
            field: {
                type: 'custom',
                label: 'System Access Control',
                customType: 'arrayOfObjects',
                fields: {
                    timeFrame: {
                        type: 'select',
                        label: '',
                        options: [
                            { id: 'w1', label: '24 Hours', value: 'ONE_DAY' },
                        ],
                    },
                },
                render: WeekTimeFrameSLAField,
                initialValue: [
                    {
                        timeFrame: {
                            id: 'w1',
                            label: '24 Hours',
                            value: 'ONE_DAY',
                            meta: { labels: ['Reassess Employee Roles'] },
                        },
                    },
                ],
            },
        } as FormSchema;

        render(
            <FormWrapper
                formId={formId}
                schema={schema}
                data-id="wrapper"
                onSubmit={() => {
                    // Submit handler
                }}
            >
                <WeekTimeFrameSLAField
                    isShown
                    ref={() => undefined}
                    data-id="wtf"
                    name="field"
                    label={'System Access Control'}
                    formId={formId}
                    required={false}
                    type="custom"
                    fieldSchemaProps={schema.field as FieldSchema}
                    setValue={() => undefined}
                    disabled={false}
                    invalid={false}
                    isTouched={false}
                    isDirty={false}
                    isValidating={false}
                    feedback={{ type: 'success', message: '' }}
                    optionalText=""
                    value={
                        schema.field.initialValue as {
                            timeFrame: {
                                id: string;
                                label: string;
                                value: string;
                                meta: { labels: string[] };
                            };
                        }[]
                    }
                    onChange={() => undefined}
                    onBlur={() => undefined}
                />
            </FormWrapper>,
        );

        // Card may not forward data-testid; verify label and field exist
        expect(screen.getByText('System Access Control')).toBeDefined();
        const selectElement = screen.getByRole('combobox', {
            name: 'Reassess Employee Roles',
        });

        expect(selectElement).toBeDefined();
        expect(selectElement.getAttribute('name')).toBe('field[0].timeFrame');
        expect(screen.getByText('24 Hours')).toBeDefined();
    });
});
