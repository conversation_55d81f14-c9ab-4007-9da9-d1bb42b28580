import type { ClientTypeEnum, GroupResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getProviderRecord } from './get-provider-record.helper';

/**
 * Maps external policy provider client types to user-friendly display names.
 * This helper provides a centralized mapping for external policy providers
 * to ensure consistent display names across the application.
 */
export function getExternalPolicyProviderLabel(
    clientType: ClientTypeEnum | 'N/A',
): string {
    switch (clientType) {
        case 'BAMBOO_HR':
        case 'MERGEDEV_BAMBOO_HR': {
            return 'BambooHR';
        }
        case 'CONFLUENCE': {
            return 'Confluence';
        }
        case 'NOTION': {
            return 'Notion';
        }
        default: {
            // Fallback to the original client type if no mapping is found
            return String(clientType);
        }
    }
}

/**
 * Gets the translated label for "Other Groups".
 * This helper provides a centralized way to get the translated "Other Groups" label
 * to ensure consistent translation across the application.
 */
export function getOtherGroupsLabel(): string {
    return t`Other Groups`;
}

/**
 * Determines the connection label for a group based on its connection alias or client type.
 * This helper provides a centralized way to get the connection label for groups
 * to ensure consistent labeling across the application.
 */
export function getGroupConnectionLabel(group: GroupResponseDto): string {
    if (group.connectionAlias) {
        return group.connectionAlias;
    }

    if (group.clientType) {
        const providerRecord = getProviderRecord(group.clientType);

        return providerRecord?.name ?? getOtherGroupsLabel();
    }

    return getOtherGroupsLabel();
}

/**
 * Sorts groups by connection label first, then by member count (descending).
 * This helper provides a centralized way to sort groups consistently across the application.
 */
export function sortGroupsByConnectionAndMemberCount(
    groups: GroupResponseDto[],
): GroupResponseDto[] {
    return [...groups].sort((a, b) => {
        // First sort by connection
        const aConnectionLabel = getGroupConnectionLabel(a);
        const bConnectionLabel = getGroupConnectionLabel(b);
        const connectionComparison =
            aConnectionLabel.localeCompare(bConnectionLabel);

        if (connectionComparison !== 0) {
            return connectionComparison;
        }

        // Then sort by member count (descending)
        return b.membersCount - a.membersCount;
    });
}
