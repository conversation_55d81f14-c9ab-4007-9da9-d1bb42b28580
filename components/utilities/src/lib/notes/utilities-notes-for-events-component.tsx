import { sharedEventsNotesController } from '@controllers/events-details';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { UtilitiesNotesBaseComponent } from './utilities-notes-base-component';
import type { NoteCreateDto } from './utilities-notes-create-dto-types';

export const UtilitiesNotesForEventsComponent = observer(
    (): React.JSX.Element => {
        const {
            infiniteNotes,
            isLoading,
            isCreatingNote,
            isReadOnly,
            hasNextPage,
            loadNextPage,
        } = sharedEventsNotesController;

        return (
            <UtilitiesNotesBaseComponent
                isReadOnly={isReadOnly}
                notes={infiniteNotes}
                isLoading={isLoading}
                isCreating={isCreatingNote}
                hasMore={hasNextPage}
                data-id="zBsh_UOr"
                labels={{
                    title: t`Internal notes`,
                    subtitle: t`Add any feedback or questions you want to track for this event. These messages are not shared with auditors`,
                    commentLabel: t`New note`,
                    emptyStateTitle: t`No notes yet`,
                    emptyStateDescription: t`Add notes to track feedback, questions or important details for this event.`,
                    readOnlyEmptyStateDescription: t`Follow any feedback or questions about this event. These messages are not shared with auditors.`,
                }}
                attachments={{
                    showAddAttachment: 'modal',
                    includeAttachmentCreationDate: false,
                    includeAttachmentTitle: true,
                    acceptedFormats: [
                        'pdf',
                        'docx',
                        'odt',
                        'xlsx',
                        'ods',
                        'pptx',
                        'odp',
                        'gif',
                        'jpeg',
                        'png',
                    ],
                }}
                onLoadMore={loadNextPage}
                onCreate={(values: NoteCreateDto, onSuccess?: () => void) => {
                    sharedEventsNotesController.createNote(values, onSuccess);
                }}
                onUpdate={(noteId, values) => {
                    sharedEventsNotesController.updateNote(noteId, values);
                }}
                onDelete={(noteId) => {
                    sharedEventsNotesController.deleteNote(noteId);
                }}
                onDownloadAttachment={(noteFileId: string) => {
                    sharedEventsNotesController.downloadNoteAttachment(
                        noteFileId,
                    );
                }}
            />
        );
    },
);
