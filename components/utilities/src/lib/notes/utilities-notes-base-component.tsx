import { isEmpty, noop } from 'lodash-es';
import { useCallback } from 'react';
import { EmptyState } from '@cosmos/components/empty-state';
import type { SupportedFormat } from '@cosmos/components/file-upload';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import {
    backgroundNeutralSurfaceBaseDefault,
    dimension8x,
    dimension72x,
} from '@cosmos/constants/tokens';
import { zIndex } from '@cosmos/constants/z-index';
import { useInfiniteScroll } from '@cosmos-lab/hooks/use-infinite-scroll';
import type {
    AuditHubEvidenceResponseDto,
    NoteResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { DEFAULT_NOTES_MAX_FILE_SIZE_IN_MB } from './comments/utilities-notes-comment-attachment-form-constant';
import { UtilitiesCommentComponent } from './comments/utilities-notes-comment-component';
import { NoteCommentForm } from './comments/utilities-notes-comment-form-component';
import type { ReferenceDocument } from './comments/utilities-notes-comment-types';
import type {
    MessageNoteResponseDto,
    NoteCreateDto,
    NoteUpdateDto,
} from './utilities-notes-create-dto-types';

export const UtilitiesNotesBaseComponent = observer(
    ({
        notes = [],
        hasSecondaryInput = false,
        labels = {
            title: t`Notes`,
            subtitle: t`Add any feedback or questions you want to track for this. These messages are not shared with auditors.`,
            commentLabel: t`New note`,
            emptyStateTitle: t`No notes yet`,
            emptyStateDescription: t`Add notes to track feedback, questions or important details.`,
            readOnlyEmptyStateDescription: t`Follow any feedback or questions. These messages are not shared with auditors.`,
            addButton: t`Add note`,
            saveButton: t`Save note`,
            editTooltip: t`Edit note`,
            deleteTooltip: t`Delete note`,
            cancelConfirmationBody: t`If you leave now, your note changes will be lost.`,
        },
        maxNoteCharacters = 768,
        isReadOnly = false,
        attachments = {
            showAddAttachment: 'modal',
            acceptedFormats: ['jpeg', 'png'],
            useSimpleAttachments: false,
            includeAttachmentTitle: true,
            includeAttachmentCreationDate: true,
            maxFileSizeInMB: DEFAULT_NOTES_MAX_FILE_SIZE_IN_MB,
        },
        enableAuditorOnlyEditing = false,
        showUnreadIndicators = false,
        enableReadStatusTracking = false,
        isLoading = false,
        isCreating = false,
        documentActive = null,
        hasMore = false,
        onCreate = noop,
        onUpdate = noop,
        onDelete = noop,
        onCancel = noop,
        onDownloadAttachment = noop,
        onUpdateReadStatus = noop,
        onSearchDocument = noop,
        onLoadMore,
    }: {
        notes: NoteResponseDto[];
        hasSecondaryInput?: boolean;
        maxNoteCharacters?: number;
        isLoading?: boolean;
        isCreating?: boolean;
        onCreate?: (values: NoteCreateDto, onSuccess?: () => void) => void;
        onUpdate?: (noteId: string, values: NoteUpdateDto) => void;
        onDelete?: (noteId: string) => void;
        onCancel?: () => void;
        onDownloadAttachment?: (noteFileId: string, noteId?: string) => void;
        onUpdateReadStatus?: (noteId: string, hasBeenRead: boolean) => void;
        onSearchDocument?: (refDoc: ReferenceDocument) => void;
        isReadOnly?: boolean;
        enableAuditorOnlyEditing?: boolean;
        showUnreadIndicators?: boolean;
        enableReadStatusTracking?: boolean;
        documentActive?: AuditHubEvidenceResponseDto | null;
        hasMore?: boolean;
        onLoadMore?: () => void;
        attachments?: {
            showAddAttachment?: 'modal' | 'field';
            acceptedFormats?: SupportedFormat[];
            useSimpleAttachments?: boolean;
            includeAttachmentTitle?: boolean;
            includeAttachmentCreationDate?: boolean;
            allowMultipleFiles?: boolean;
            maxFileSizeInMB?: number;
        };
        labels?: {
            title?: string;
            subtitle?: string;
            commentLabel?: string;
            emptyStateTitle?: string;
            emptyStateDescription?: string;
            readOnlyEmptyStateDescription?: string;
            addButton?: string;
            saveButton?: string;
            editTooltip?: string;
            deleteTooltip?: string;
            cancelConfirmationBody?: string;
        };
    }): React.JSX.Element => {
        const isOwner = (note: NoteResponseDto): boolean => {
            const currentUser = sharedCurrentUserController.user;

            if (!currentUser) {
                return false;
            }

            /**
             * For auditor-only editing mode, check if the author is an auditor AND the current user is the owner.
             */
            if (enableAuditorOnlyEditing) {
                const messageNote = note as MessageNoteResponseDto;

                // Compare using entryId (string UUID) which matches the authorId from the message
                return (
                    messageNote.owner.authorIsAuditor === true &&
                    note.owner.entryId === currentUser.entryId
                );
            }

            // For regular mode, check if the current user is the owner of the note
            return note.owner.id === currentUser.id;
        };

        const emptyStateTitle = labels.emptyStateTitle || t`No notes yet`;
        const readOnlyEmptyStateDescription =
            labels.readOnlyEmptyStateDescription ||
            t`Follow any feedback or questions. These messages are not shared with auditors.`;
        const emptyStateDescription =
            labels.emptyStateDescription ||
            t`Add notes to track feedback, questions or important details.`;
        const getSubtitleText = () => {
            if (isReadOnly && !isEmpty(notes)) {
                return readOnlyEmptyStateDescription;
            }
            if (isReadOnly) {
                return '';
            }

            return labels.subtitle || '';
        };

        const handleLoadMore = useCallback(() => {
            if (onLoadMore && hasMore) {
                onLoadMore();
            }
        }, [onLoadMore, hasMore]);

        const infiniteScrollRef = useInfiniteScroll({
            hasMore,
            onLoadMore: handleLoadMore,
        });

        return (
            <Stack
                direction="column"
                height="100%"
                width="100%"
                data-testid="UtilitiesNotesComponent"
                data-id="9329QCnO"
                pl="xl"
                pr="2xl"
                overflowY="auto"
                overflow="scroll"
            >
                <Stack
                    gap="2x"
                    direction="column"
                    pt="xl"
                    pb="xl"
                    position="sticky"
                    top="0"
                    style={{
                        backgroundColor: backgroundNeutralSurfaceBaseDefault,
                        zIndex: zIndex.sticky,
                    }}
                >
                    <Text size="300" type="title">
                        {labels.title}
                    </Text>
                    <Text size="100" colorScheme="faded">
                        {getSubtitleText()}
                    </Text>
                </Stack>
                <Stack width="100%" direction="column" pt="xl">
                    {isReadOnly || (
                        <NoteCommentForm
                            comment=""
                            attachments={[]}
                            source=""
                            maxNoteCharacters={maxNoteCharacters}
                            hasSource={false}
                            editMode={false}
                            commentLabel={labels.commentLabel}
                            isLoading={isCreating}
                            documentActive={documentActive}
                            labels={{
                                confirmationButton: labels.addButton,
                            }}
                            attachmentConfig={{
                                showAddAttachment:
                                    attachments.showAddAttachment,
                                acceptedFormats: attachments.acceptedFormats,
                                useSimpleAttachments:
                                    attachments.useSimpleAttachments,
                                includeAttachmentTitle:
                                    attachments.includeAttachmentTitle,
                                includeAttachmentCreationDate:
                                    attachments.includeAttachmentCreationDate,
                                allowMultipleFiles:
                                    attachments.allowMultipleFiles,
                                maxFileSizeInMB: attachments.maxFileSizeInMB,
                            }}
                            onCancel={onCancel}
                            onSubmit={(values, onSuccess) => {
                                const fileMetadata = values.files.map(
                                    (fileItem) => ({
                                        name:
                                            fileItem.name ||
                                            fileItem.file.file.name,
                                        originalFile: fileItem.file.file.name,
                                        creationDate:
                                            fileItem.creationDate ||
                                            new Date().toISOString(),
                                    }),
                                );
                                const updatedNote: NoteCreateDto = {
                                    comment: values.comment,
                                    'files[]': values.files.map(
                                        (f) => f.file.file,
                                    ),
                                    fileMetadata,
                                    hasReference: values.hasReference,
                                    documentActive: documentActive
                                        ? {
                                              name: documentActive.name,
                                              type: documentActive.type as string,
                                          }
                                        : null,
                                };

                                onCreate(updatedNote, onSuccess);
                            }}
                        />
                    )}
                </Stack>

                {(() => {
                    if (isEmpty(notes) && isLoading) {
                        // is loading and no notes
                        return (
                            <Stack direction="column" gap="lg">
                                <Skeleton
                                    key={`note-skeleton-loading`}
                                    width="100%"
                                    barHeight={dimension8x}
                                    barCount={2}
                                    data-id={`note-skeleton-unique-id`}
                                />
                            </Stack>
                        );
                    }

                    if (isEmpty(notes) && !isLoading) {
                        return (
                            <Stack pt="xl">
                                <EmptyState
                                    data-id="utilities-notes-empty-state"
                                    title={emptyStateTitle}
                                    description={
                                        isReadOnly
                                            ? readOnlyEmptyStateDescription
                                            : emptyStateDescription
                                    }
                                />
                            </Stack>
                        );
                    }

                    return (
                        <Stack
                            direction="column"
                            minHeight={dimension72x}
                            data-id="y1aOKFuf"
                        >
                            {notes.map((note, index) => {
                                const hasDivider = notes.length - 1 !== index;
                                const canEdit = isOwner(note);

                                return (
                                    <UtilitiesCommentComponent
                                        key={`${note.id}-comment`}
                                        id={note.id}
                                        formId={`${note.id}-note-form-id`}
                                        value={note.comment}
                                        createdAt={note.createdAt}
                                        imgSrc={note.owner.avatarUrl ?? ''}
                                        isReadOnly={isReadOnly || !canEdit}
                                        hasDivider={hasDivider}
                                        data-id={`${note.id}-utility-note-comment`}
                                        hasSecondaryInput={hasSecondaryInput}
                                        attachments={note.noteFiles}
                                        canEdit={canEdit}
                                        commentFieldLabel="Edit Note"
                                        maxNoteCharacters={maxNoteCharacters}
                                        documentActive={documentActive}
                                        defaultEditLabel={labels.editTooltip}
                                        referenceDocuments={
                                            (note as MessageNoteResponseDto)
                                                .referenceDocuments
                                        }
                                        defaultDeleteLabel={
                                            labels.deleteTooltip
                                        }
                                        labels={{
                                            confirmationButton:
                                                labels.saveButton,
                                            editTooltip: labels.editTooltip,
                                            deleteTooltip: labels.deleteTooltip,
                                            cancelConfirmationBody:
                                                labels.cancelConfirmationBody,
                                        }}
                                        attachmentConfig={{
                                            shouldShowAddAttachment:
                                                attachments.showAddAttachment,
                                            acceptedFormats:
                                                attachments.acceptedFormats,
                                            useSimpleAttachments:
                                                attachments.useSimpleAttachments,
                                            includeAttachmentTitle:
                                                attachments.includeAttachmentTitle,
                                            includeAttachmentCreationDate:
                                                attachments.includeAttachmentCreationDate,
                                            allowMultipleFiles:
                                                attachments.allowMultipleFiles,
                                            maxFileSizeInMB:
                                                attachments.maxFileSizeInMB,
                                        }}
                                        enableReadStatusTracking={
                                            showUnreadIndicators
                                        }
                                        identityName={getFullName(
                                            note.owner.firstName,
                                            note.owner.lastName,
                                        )}
                                        hasBeenRead={
                                            enableReadStatusTracking
                                                ? ((
                                                      note as MessageNoteResponseDto
                                                  ).hasBeenRead ?? false)
                                                : true
                                        }
                                        onChange={noop}
                                        onSearchDocument={onSearchDocument}
                                        onDownloadAttachment={
                                            onDownloadAttachment
                                        }
                                        onSave={(updateNote: NoteUpdateDto) => {
                                            onUpdate(note.id, updateNote);
                                        }}
                                        onDelete={() => {
                                            onDelete(note.id);
                                        }}
                                        onUpdateReadStatus={
                                            enableReadStatusTracking &&
                                            (() => {
                                                // In auditor-only editing mode, only show read status controls for messages from opposite user type
                                                if (enableAuditorOnlyEditing) {
                                                    const messageNote =
                                                        note as MessageNoteResponseDto;
                                                    const currentUserIsAuditor =
                                                        sharedCurrentUserController.hasAuditorToken;

                                                    // Don't show mark as read buttons for messages from same user type
                                                    if (
                                                        messageNote.owner
                                                            .authorIsAuditor ===
                                                        currentUserIsAuditor
                                                    ) {
                                                        return false;
                                                    }
                                                }

                                                return true;
                                            })()
                                                ? (
                                                      noteId: string,
                                                      hasBeenRead: boolean,
                                                  ) => {
                                                      onUpdateReadStatus(
                                                          noteId,
                                                          hasBeenRead,
                                                      );
                                                  }
                                                : undefined
                                        }
                                    />
                                );
                            })}

                            {hasMore && (
                                // Invisible sentinel div for infinite scroll detection
                                // IntersectionObserver watches this element to trigger loading more notes
                                <div
                                    ref={infiniteScrollRef}
                                    data-id="infinite-scroll-sentinel-notes-base-component"
                                >
                                    <Stack direction="column" gap="lg" pt="lg">
                                        <Skeleton
                                            width="100%"
                                            barCount={1}
                                            data-id="infinite-scroll-loading-skeleton"
                                        />
                                    </Stack>
                                </div>
                            )}
                        </Stack>
                    );
                })()}
            </Stack>
        );
    },
);
