import { isEmpty } from 'lodash-es';
import { z } from 'zod';
import { modalController } from '@controllers/modal';
import type { SupportedFormat } from '@cosmos/components/file-upload';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { Form, type FormSchema, useFormSubmit } from '@ui/forms';
import {
    DEFAULT_NOTES_MAX_FILE_SIZE_IN_MB,
    UTILITIES_NOTES_COMMENT_ATTACHMENT_FORM_MODAL_ID,
} from './utilities-notes-comment-attachment-form-constant';

/**
 * Maximum number of files that can be uploaded for customer request messages.
 */
const MAX_FILES_LIMIT = 10;

const handleCloseModal = () => {
    modalController.closeModal(
        UTILITIES_NOTES_COMMENT_ATTACHMENT_FORM_MODAL_ID,
    );
};

const getSchema = (
    attachmentConfig: {
        acceptedFormats?: SupportedFormat[];
        useSimpleAttachments?: boolean;
        includeAttachmentTitle?: boolean;
        includeAttachmentCreationDate?: boolean;
        allowMultipleFiles?: boolean;
        maxFileSizeInMB?: number;
    } = {},
): FormSchema => {
    const {
        acceptedFormats = ['jpeg', 'png'],
        useSimpleAttachments = false,
        includeAttachmentTitle = false,
        includeAttachmentCreationDate = false,
        allowMultipleFiles = false,
        maxFileSizeInMB = DEFAULT_NOTES_MAX_FILE_SIZE_IN_MB,
    } = attachmentConfig;

    const maxFileSizeInBytes = maxFileSizeInMB * 1024 * 1024;
    const baseSchema: FormSchema = {};

    // Add conditional fields based on flags - name field first
    if (!useSimpleAttachments && includeAttachmentTitle) {
        baseSchema.name = {
            type: 'text',
            label: t`Name`,
            isOptional: true,
            validator: z
                .string()
                .max(191, t`Name cannot exceed 191 characters`)
                .optional(),
        };
    }

    if (!useSimpleAttachments && includeAttachmentCreationDate) {
        baseSchema.creationDate = {
            type: 'date',
            label: t`Created date`,
            isOptional: true,
            helpText: t`If not entered, today's date will be used.`,
            getIsDateUnavailable: (date: string) => {
                const selectedDate = new Date(date);
                const today = new Date();

                today.setHours(0, 0, 0, 0);

                return selectedDate > today;
            },
        };
    }

    // File upload field second
    baseSchema.file = {
        maxFileSizeInBytes,
        type: 'file',
        label: allowMultipleFiles ? t`Attachments` : t`Attachment`,
        initialValue: [],
        selectButtonText: allowMultipleFiles
            ? t`Upload files`
            : t`Upload a file`,
        removeButtonText: t`Remove file`,
        acceptedFormats,
        oneFileOnly: !allowMultipleFiles,
        validator: z.array(z.any()).min(1, t`An attachment is required`),
        errorCodeMessages: {
            'file-invalid-type': t`Not a valid file type.`,
            'file-too-large': t`File cannot be larger than ${maxFileSizeInMB}MB.`,
            'file-too-small': t`File size is too small.`,
            'too-many-files': t`Contains too many files.`,
        },
        innerLabel: allowMultipleFiles
            ? t`Or drop files here`
            : t`Or drop file here`,
        isMulti: allowMultipleFiles,
    };

    return baseSchema;
};

export const UtilitiesNotesCommentAttachmentFormModal = ({
    onSubmit,
    attachmentConfig = {},
}: {
    onSubmit: (
        attachments: {
            name: string;
            creationDate: string;
            file: File;
        }[],
    ) => void;
    attachmentConfig?: {
        acceptedFormats?: SupportedFormat[];
        useSimpleAttachments?: boolean;
        includeAttachmentTitle?: boolean;
        includeAttachmentCreationDate?: boolean;
        allowMultipleFiles?: boolean;
        maxFileSizeInMB?: number;
    };
}): React.JSX.Element => {
    const {
        useSimpleAttachments = false,
        includeAttachmentTitle = false,
        allowMultipleFiles = false,
    } = attachmentConfig;

    const schema = getSchema(attachmentConfig);
    const { formRef, triggerSubmit } = useFormSubmit();

    return (
        <>
            <Modal.Header
                title={
                    allowMultipleFiles ? t`Add attachments` : t`Add attachment`
                }
                closeButtonAriaLabel={
                    allowMultipleFiles
                        ? t`Close add attachments modal`
                        : t`Close add attachment modal`
                }
                onClose={handleCloseModal}
            />
            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    data-id="modal-form"
                    ref={formRef}
                    schema={schema}
                    formId="modal-form"
                    onSubmit={({ name, creationDate, file }) => {
                        if (isEmpty(file)) {
                            return;
                        }

                        const selectedFiles = file as File[];

                        if (selectedFiles.length > MAX_FILES_LIMIT) {
                            return;
                        }

                        const attachments = selectedFiles.map(
                            (selectedFile) => ({
                                name:
                                    useSimpleAttachments ||
                                    !includeAttachmentTitle
                                        ? selectedFile.name
                                        : (name as string) || selectedFile.name,
                                creationDate:
                                    (creationDate as string) ||
                                    new Date().toISOString(),
                                file: selectedFile,
                            }),
                        );

                        onSubmit(attachments);
                        modalController.closeModal(
                            UTILITIES_NOTES_COMMENT_ATTACHMENT_FORM_MODAL_ID,
                        );
                    }}
                />
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: handleCloseModal,
                    },
                    {
                        label: allowMultipleFiles
                            ? t`Add attachments`
                            : t`Add attachment`,
                        level: 'primary',
                        onClick: () => {
                            triggerSubmit();
                        },
                    },
                ]}
            />
        </>
    );
};
