import { isEmpty } from 'lodash-es';
import {
    type ComponentProps,
    type JSXElementConstructor,
    type ReactElement,
    useRef,
    useState,
} from 'react';
import { styled } from 'styled-components';
import { AppHelpContentComponent } from '@components/app-help-content';
import { UtilitiesDockButton } from '@components/utilities';
import type { UtilitiesName } from '@controllers/route';
import { sharedUtilitiesController } from '@controllers/utilities';
import { Button, type ButtonProps } from '@cosmos/components/button';
import { Popover } from '@cosmos/components/popover';
import { Stack } from '@cosmos/components/stack';
import {
    borderWidthSm,
    neutralBackgroundSurfaceInitial,
    neutralBorderFaded,
} from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { UtilitiesVrmAgentAssessmentComponent } from './agent-workflows/vrm-agent/utilities-vrm-agent-assessment-component';
import { UtilitiesVrmAgentCriteriaComponent } from './agent-workflows/vrm-agent/utilities-vrm-agent-criteria-component';
import { UtilitiesVrmAgentSummaryComponent } from './agent-workflows/vrm-agent/utilities-vrm-agent-summary-component';
import { UtilitiesNotesForActiveApplicationComponent } from './notes/utilities-notes-for-active-application-component';
import { UtilitiesNotesForActivePeriodUserComponent } from './notes/utilities-notes-for-active-period-user-component';
import { UtilitiesNotesForCompletedApplicationOnCompletedReviewComponent } from './notes/utilities-notes-for-completed-application-on-completed-review';
import { UtilitiesNotesForControlsComponent } from './notes/utilities-notes-for-controls-component';
import { UtilitiesNotesForCustomerRequestsComponent } from './notes/utilities-notes-for-customer-requests-component';
import { UtilitiesNotesForEventsComponent } from './notes/utilities-notes-for-events-component';
import { UtilitiesNotesForMonitoringTestsComponent } from './notes/utilities-notes-for-monitoring-tests.component';
import { UtilitiesNotesForRiskManagementComponent } from './notes/utilities-notes-for-risk-management-component';
import { UtilitiesForVendorsSecurityReviewObservationsComponent } from './notes/utilities-notes-for-vendors-security-review-observations';
import { UtilitiesResourceGuideComponent } from './resource-guide/utilities-resource-guide-component';
import { UtilitiesTasksForControls } from './tasks/utilities-tasks-for-controls.component';
import { UtilitiesTasksForRisks } from './tasks/utilities-tasks-for-risks.component';
import { UtilitiesTicketingForAccessReviewActivePeriodUserComponent } from './ticketing/utilities-ticketing-for-access-review-active-period-user-component';
import { UtilitiesTicketingForControlsComponent } from './ticketing/utilities-ticketing-for-controls-component';
import { UtilitiesTicketingForRiskManagementComponent } from './ticketing/utilities-ticketing-for-risk-management-component';
import { UtilitiesDockButtonComponent } from './utilities-dock-button-component';
import { UtilitiesUtilityContent } from './utilities-utility-content';

const APP_HELP_BUTTON_ID = 'app-help-button';

type UtilitiesChildComponent<
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- any comes from the definition of ComponentProps
    T extends keyof JSX.IntrinsicElements | JSXElementConstructor<any>,
> = ReactElement<ComponentProps<T>, T>;

type ContentChild = UtilitiesChildComponent<typeof UtilitiesUtilityContent>;

const notesLabel = () => t`Notes`;
const tasksLabel = () => t`Tasks`;
const ticketingLabel = () => t`Ticketing`;
const observationsLabel = () => t`Observations`;
const messagesLabel = () => t`Messages`;
const vrmAgentLabel = () => t`VRM Agent`;
const resourceGuideLabel = () => t`Resource guide`;

const utilitiesMapping: Record<
    UtilitiesName,
    {
        content: ContentChild;
        iconName: ButtonProps['startIconName'];
        label: string;
    }
> = {
    notes_for_events: {
        content: <UtilitiesNotesForEventsComponent />,
        iconName: 'Messages',
        label: notesLabel(),
    },
    notes_for_access_review_active_period_user: {
        content: <UtilitiesNotesForActivePeriodUserComponent />,
        iconName: 'Messages',
        label: notesLabel(),
    },
    notes_for_access_review_active_application: {
        content: <UtilitiesNotesForActiveApplicationComponent />,
        iconName: 'Messages',
        label: notesLabel(),
    },
    notes_for_monitoring_tests: {
        content: <UtilitiesNotesForMonitoringTestsComponent />,
        iconName: 'Messages',
        label: notesLabel(),
    },
    notes_for_completed_application_on_completed_review: {
        content: (
            <UtilitiesNotesForCompletedApplicationOnCompletedReviewComponent />
        ),
        iconName: 'Messages',
        label: notesLabel(),
    },
    notes_for_controls: {
        content: <UtilitiesNotesForControlsComponent />,
        iconName: 'Messages',
        label: notesLabel(),
    },
    notes_for_customer_requests: {
        content: <UtilitiesNotesForCustomerRequestsComponent />,
        iconName: 'Messages',
        label: messagesLabel(),
    },
    notes_for_risk_management: {
        content: <UtilitiesNotesForRiskManagementComponent />,
        iconName: 'Messages',
        label: notesLabel(),
    },
    tasks: {
        content: <UtilitiesNotesForEventsComponent />,
        iconName: 'Task',
        label: tasksLabel(),
    },
    tickets_for_controls: {
        content: <UtilitiesTicketingForControlsComponent />,
        iconName: 'Ticket',
        label: ticketingLabel(),
    },
    tickets_for_risk_management: {
        content: <UtilitiesTicketingForRiskManagementComponent />,
        iconName: 'Ticket',
        label: ticketingLabel(),
    },
    tasks_for_controls: {
        content: <UtilitiesTasksForControls />,
        iconName: 'Task',
        label: tasksLabel(),
    },
    tasks_for_risks: {
        content: <UtilitiesTasksForRisks />,
        iconName: 'Task',
        label: tasksLabel(),
    },
    tickets_for_access_review_active_period_user: {
        content: <UtilitiesTicketingForAccessReviewActivePeriodUserComponent />,
        iconName: 'Ticket',
        label: ticketingLabel(),
    },
    observations: {
        content: <UtilitiesForVendorsSecurityReviewObservationsComponent />,
        iconName: 'Observations',
        label: observationsLabel(),
    },
    vrm_agent_criteria: {
        content: <UtilitiesVrmAgentCriteriaComponent />,
        iconName: 'AI',
        label: vrmAgentLabel(),
    },
    vrm_agent_summary: {
        content: <UtilitiesVrmAgentSummaryComponent />,
        iconName: 'AI',
        label: vrmAgentLabel(),
    },
    vrm_agent_assessment: {
        content: <UtilitiesVrmAgentAssessmentComponent />,
        iconName: 'AI',
        label: vrmAgentLabel(),
    },
    resource_guide: {
        content: <UtilitiesResourceGuideComponent />,
        iconName: 'Information',
        label: resourceGuideLabel(),
    },
};

const StyledWrapperDiv = styled.div`
    height: 100%;
    width: max-content;
    display: flex;
    align-items: stretch;
    background-color: ${neutralBackgroundSurfaceInitial};
    z-index: 1;
`;

const StyledBarWrapperDiv = styled.div`
    border-left: ${borderWidthSm} solid ${neutralBorderFaded};
    border-right: 1px solid transparent;
`;

export const UtilitiesComponent = observer((): React.JSX.Element => {
    const {
        enabledUtilities,
        handleOpenCloseAllTabsClick,
        closeAllUtilities,
        getUtilityControllerByName,
        isOpen: isUtilitiesOpen,
    } = sharedUtilitiesController;

    const buttonRef = useRef<HTMLButtonElement>(null);
    const [isHelpOpen, setIsHelpOpen] = useState<boolean>(false);
    const handleClick = () => {
        setIsHelpOpen(!isHelpOpen);
    };

    const handleDismiss = () => {
        setIsHelpOpen(false);
    };

    return (
        <StyledWrapperDiv data-id="qWKyKNI2">
            <Stack direction="row" height="100%" width="max-content">
                {enabledUtilities.map((utility) => {
                    const { isOpen } = getUtilityControllerByName(utility);
                    const { content } = utilitiesMapping[utility];

                    return (
                        <UtilitiesUtilityContent
                            data-id="wENCz48s"
                            isOpen={isOpen}
                            controlledBy={utility}
                            key={utility}
                        >
                            <content.type
                                {...content.props}
                                data-id="bVaem_gg"
                            />
                        </UtilitiesUtilityContent>
                    );
                })}

                <StyledBarWrapperDiv>
                    <Stack
                        pt="xl"
                        pb="xl"
                        flexShrink="0"
                        direction="column"
                        height="100%"
                        width="100%"
                    >
                        {!isEmpty(enabledUtilities) && (
                            <Stack
                                mb="xl"
                                align="center"
                                direction="row"
                                width="100%"
                                justify="center"
                            >
                                <Button
                                    isIconOnly
                                    colorScheme="primary"
                                    level="tertiary"
                                    label="Close all tabs"
                                    startIconName={
                                        isUtilitiesOpen
                                            ? 'DoubleArrowRight'
                                            : 'DoubleArrowLeft'
                                    }
                                    onClick={() => {
                                        runInAction(() => {
                                            handleOpenCloseAllTabsClick();
                                        });
                                    }}
                                />
                            </Stack>
                        )}

                        <Stack direction="column" flexGrow="1" gap="sm">
                            {enabledUtilities
                                .map((utility) => ({
                                    utility,
                                    mapping: utilitiesMapping[utility],
                                }))
                                .map(({ utility, mapping }) => {
                                    return (
                                        <UtilitiesDockButtonComponent
                                            buttonId={`utilities- ${utility}-dock-button`}
                                            key={`utilities- ${utility}-dock-button`}
                                            data-id={`utilities- ${utility}-dock-button`}
                                            utilityName={utility}
                                            iconName={mapping.iconName}
                                            label={mapping.label}
                                            internal={{
                                                onClick: closeAllUtilities,
                                            }}
                                        />
                                    );
                                })}
                        </Stack>
                        <>
                            <Popover
                                anchor={buttonRef.current}
                                isOpen={isHelpOpen}
                                placement="left-end"
                                content={
                                    <AppHelpContentComponent
                                        aria-labelledby={APP_HELP_BUTTON_ID}
                                    />
                                }
                                onDismiss={handleDismiss}
                            />

                            <UtilitiesDockButton
                                id={APP_HELP_BUTTON_ID}
                                iconName={'Help'}
                                label="Help"
                                data-id={`utilities-dock-button-help-testid`}
                                internal={{ onClick: handleClick }}
                            />
                        </>
                    </Stack>
                </StyledBarWrapperDiv>
            </Stack>
        </StyledWrapperDiv>
    );
});
