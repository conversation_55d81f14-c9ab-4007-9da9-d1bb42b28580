import type React from 'react';
import { Box } from '@cosmos/components/box';
import { Tabs } from '@cosmos/components/tabs';
import { t } from '@globals/i18n/macro';
import type { UtilitiesVrmAgentMessageData } from '.';
import { UtilitiesVrmAgentAuditTrail } from './utilities-vrm-agent-audit-trail.component';
import { UtilitiesVrmAgentMessagesList } from './utilities-vrm-agent-messages-list.component';

export interface UtilitiesVrmAgentTabsProps {
    messages: UtilitiesVrmAgentMessageData[];
    'data-id'?: string;
}

/**
 * Tabs component for VRM Agent with Process and Results panels.
 */
export const UtilitiesVrmAgentTabs = ({
    messages,
    'data-id': dataId = 'vrm-agent-tabs',
}: UtilitiesVrmAgentTabsProps): React.JSX.Element => {
    const tabs = [
        {
            tabId: 'processPanel',
            label: t`Process panel`,
            content: (
                <UtilitiesVrmAgentMessagesList
                    messages={messages}
                    messageIdPrefix="process-message"
                    data-id="process-messages-list"
                />
            ),
        },
        {
            tabId: 'results',
            label: t`Results`,
            content: (
                <UtilitiesVrmAgentAuditTrail data-id="results-audit-trail" />
            ),
        },
    ];

    return (
        <Box
            height="100%"
            overflowY="hidden"
            data-testid="UtilitiesVrmAgentTabs"
            data-id="St_z_-43"
        >
            <Tabs
                hasPadding
                tabs={tabs}
                overflowLeftLabel={t`Scroll left`}
                overflowRightLabel={t`Scroll right`}
                defaultTabId="processPanel"
                data-id={dataId}
            />
        </Box>
    );
};
