import type React from 'react';
import { sharedVendorsVrmAgentController } from '@controllers/vendors';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import {
    UtilitiesVrmAgentHeader,
    type UtilitiesVrmAgentMessageData,
    UtilitiesVrmAgentMessagesList,
    UtilitiesVrmAgentTabs,
} from '.';

export interface UtilitiesVrmAgentBaseComponentProps {
    messages?: UtilitiesVrmAgentMessageData[];
    'data-id'?: string;
}

export const UtilitiesVrmAgentBaseComponent = observer(
    ({
        messages,
        'data-id': dataId,
    }: UtilitiesVrmAgentBaseComponentProps): React.JSX.Element => {
        const controller = sharedVendorsVrmAgentController;

        // Use messages from props or controller, fallback to mock data
        const displayMessages = messages ?? [];

        // Show loading state when controller is loading
        if (controller.isLoading && !controller.hasAssessmentMessages) {
            return (
                <Stack
                    direction="column"
                    height="100%"
                    width="100%"
                    justify="center"
                    align="center"
                    data-testid="UtilitiesVrmAgentBaseComponent"
                    data-id={dataId || 'vrm-agent-base'}
                >
                    <Loader
                        label="Loading VRM Agent..."
                        size="md"
                        colorScheme="primary"
                        data-id="vrm-agent-loader"
                    />
                </Stack>
            );
        }

        // If there's no active agent workflow, don't display tabs
        if (!controller.hasAgentWorkflow) {
            return (
                <Stack
                    direction="column"
                    height="100%"
                    width="100%"
                    data-testid="UtilitiesVrmAgentBaseComponent"
                    data-id={dataId || 'vrm-agent-base'}
                    overflowY="auto"
                >
                    <UtilitiesVrmAgentHeader data-id="no-workflow-header" />
                    <UtilitiesVrmAgentMessagesList
                        messages={displayMessages}
                        messageIdPrefix="base-message"
                        data-id="no-workflow-messages"
                    />
                </Stack>
            );
        }

        // If there's an active workflow, display tabs
        return (
            <Stack
                direction="column"
                height="100%"
                width="100%"
                data-testid="UtilitiesVrmAgentBaseComponent"
                data-id={dataId || 'vrm-agent-base'}
            >
                <UtilitiesVrmAgentHeader data-id="with-workflow-header" />
                <UtilitiesVrmAgentTabs
                    messages={displayMessages}
                    data-id={`${dataId || 'vrm-agent-base'}-tabs`}
                />
            </Stack>
        );
    },
);
