import type React from 'react';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import {
    UtilitiesVrmAgentChatComponent,
    type UtilitiesVrmAgentMessageData,
} from '.';

export interface UtilitiesVrmAgentMessagesListProps {
    messages: UtilitiesVrmAgentMessageData[];
    messageIdPrefix?: string;
    'data-id'?: string;
}

export const UtilitiesVrmAgentMessagesList = observer(
    ({
        messages,
        'data-id': dataId = 'vrm-agent-messages-list',
    }: UtilitiesVrmAgentMessagesListProps): React.JSX.Element => {
        return (
            <Box
                borderPosition="top"
                borderWidth="borderWidth1"
                borderColor="neutralBorderFaded"
                data-id={dataId}
                data-testid="UtilitiesVrmAgentMessagesList"
            >
                <Stack direction="column" gap="lg" p="xl" overflowY="auto">
                    {messages.map((message) => (
                        <UtilitiesVrmAgentChatComponent
                            key={message.id}
                            message={message}
                            data-id={`message-${message.id}`}
                        />
                    ))}
                </Stack>
            </Box>
        );
    },
);
