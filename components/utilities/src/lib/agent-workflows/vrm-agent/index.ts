// Base component
export { UtilitiesVrmAgentBaseComponent } from './utilities-vrm-agent-base-component';

// Sub-components
export { UtilitiesVrmAgentAuditTrail } from './utilities-vrm-agent-audit-trail.component';
export { UtilitiesVrmAgentHeader } from './utilities-vrm-agent-header.component';
export { UtilitiesVrmAgentMessagesList } from './utilities-vrm-agent-messages-list.component';
export { UtilitiesVrmAgentTabs } from './utilities-vrm-agent-tabs.component';

// Assessment component
export { UtilitiesVrmAgentAssessmentComponent } from './utilities-vrm-agent-assessment-component';

// Criteria component
export { UtilitiesVrmAgentCriteriaComponent } from './utilities-vrm-agent-criteria-component';

// Summary component
export { UtilitiesVrmAgentSummaryComponent } from './utilities-vrm-agent-summary-component';

// Agent Message components
export type {
    UtilitiesVrmAgentMessageAction,
    UtilitiesVrmAgentMessageBodyItem,
    UtilitiesVrmAgentMessageData,
    UtilitiesVrmAgentMessageProps,
    UtilitiesVrmAgentMessageTitleItem,
} from './vrm-messages/types';
export { UtilitiesVrmAgentChatComponent } from './vrm-messages/utilities-vrm-agent-chat-component';
export { UtilitiesVrmAgentMessageActionsComponent } from './vrm-messages/utilities-vrm-agent-message-actions-component';
export { UtilitiesVrmAgentMessageBodyComponent } from './vrm-messages/utilities-vrm-agent-message-body-component';
export { UtilitiesVrmAgentMessageComponent } from './vrm-messages/utilities-vrm-agent-message-component';
export { UtilitiesVrmAgentMessageTitleComponent } from './vrm-messages/utilities-vrm-agent-message-title-component';
export { UtilitiesVrmAgentUserMessageComponent } from './vrm-messages/utilities-vrm-agent-user-message-component';

// Mock data constants
export { VRM_AGENT_MOCK_MESSAGES } from './mocks/vrm-agent-mock-messages';

// Status configuration constants
export { VRM_AGENT_AUDIT_TRAIL_STATUS_CONFIG } from './constants/vrm-agent-audit-trail-status.constants';
export type { VrmAgentAuditTrailStepStatus } from './vrm-messages/types';
