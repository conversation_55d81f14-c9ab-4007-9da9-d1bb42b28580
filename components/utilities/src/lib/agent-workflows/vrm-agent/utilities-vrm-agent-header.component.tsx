import type React from 'react';
import { Box } from '@cosmos/components/box';
import { Text } from '@cosmos/components/text';

export interface UtilitiesVrmAgentHeaderProps {
    'data-id'?: string;
}

/**
 * Header component for VRM Agent with consistent styling.
 */
export const UtilitiesVrmAgentHeader = ({
    'data-id': dataId = 'vrm-agent-header',
}: UtilitiesVrmAgentHeaderProps): React.JSX.Element => {
    return (
        <Box p="xl" data-testid="UtilitiesVrmAgentHeader" data-id={dataId}>
            <Text allowBold size="300" type="title" data-id={`${dataId}-title`}>
                VRM Agent
            </Text>
        </Box>
    );
};
