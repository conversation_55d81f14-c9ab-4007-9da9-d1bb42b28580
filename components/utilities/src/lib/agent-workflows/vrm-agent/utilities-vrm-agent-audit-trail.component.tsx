import { isEmpty } from 'lodash-es';
import type React from 'react';
import { sharedVendorsVrmAgentController } from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { VRM_AGENT_AUDIT_TRAIL_STATUS_CONFIG } from '.';

export interface UtilitiesVrmAgentAuditTrailProps {
    'data-id'?: string;
}
export const UtilitiesVrmAgentAuditTrail = observer(
    ({
        'data-id': dataId = 'vrm-agent-audit-trail',
    }: UtilitiesVrmAgentAuditTrailProps): React.JSX.Element => {
        const controller = sharedVendorsVrmAgentController;

        // Get audit trail steps from controller
        const { auditTrailSteps } = controller;

        if (isEmpty(auditTrailSteps)) {
            return (
                <Stack
                    direction="column"
                    height="100%"
                    width="100%"
                    justify="center"
                    align="center"
                    data-testid="UtilitiesVrmAgentAuditTrail"
                    data-id={dataId}
                >
                    <Text size="200" colorScheme="neutral">
                        {t`No audit trail available yet.`}
                    </Text>
                </Stack>
            );
        }

        return (
            <Box
                borderPosition="top"
                borderWidth="borderWidth1"
                borderColor="neutralBorderFaded"
                data-id={dataId}
            >
                <Stack
                    direction="column"
                    height="100%"
                    width="100%"
                    data-testid="UtilitiesVrmAgentAuditTrail"
                    data-id={dataId}
                    py="xl"
                    pl="xl"
                    pr="2xl"
                    overflowY="auto"
                    gap="6x"
                >
                    {auditTrailSteps.map((step) => (
                        <Stack
                            key={step.id}
                            direction="row"
                            align="start"
                            gap="md"
                            data-id={`${dataId}-step-${step.id}`}
                        >
                            <Icon
                                size="200"
                                data-id={`${dataId}-step-${step.id}-icon`}
                                name={
                                    VRM_AGENT_AUDIT_TRAIL_STATUS_CONFIG[
                                        step.status
                                    ].iconName
                                }
                                colorScheme={
                                    VRM_AGENT_AUDIT_TRAIL_STATUS_CONFIG[
                                        step.status
                                    ].colorScheme
                                }
                            />

                            <Stack
                                direction="row"
                                gap="xs"
                                justify="between"
                                style={{ flex: 1 }}
                            >
                                <Box maxWidth="200px">
                                    {step.href ? (
                                        <AppLink
                                            href={step.href}
                                            colorScheme="primary"
                                            data-id={`${dataId}-step-${step.id}-link`}
                                        >
                                            {step.displayName}
                                        </AppLink>
                                    ) : (
                                        <Text
                                            allowBold
                                            size="200"
                                            data-id={`${dataId}-step-${step.id}-text`}
                                            colorScheme={
                                                step.status === 'COMPLETED'
                                                    ? 'neutral'
                                                    : 'faded'
                                            }
                                        >
                                            {step.displayName}
                                        </Text>
                                    )}
                                </Box>
                                {step.createdAt && (
                                    <Text
                                        size="100"
                                        colorScheme="faded"
                                        data-id={`${dataId}-step-${step.id}-timestamp`}
                                    >
                                        {step.createdAt}
                                    </Text>
                                )}
                            </Stack>
                        </Stack>
                    ))}
                </Stack>
            </Box>
        );
    },
);
