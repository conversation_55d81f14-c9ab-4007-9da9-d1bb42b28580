import { activeLibraryTestController } from '@controllers/library-test';
import { Box } from '@cosmos/components/box';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { FailsByCategoryResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getMonitorCheckTypeLabel } from '@models/library-test';

export const LibraryTestLogicDetailsComponent = observer(
    (): React.JSX.Element => {
        const {
            connectionClientTypeLabels,
            monitoringControlInstance,
            testName,
        } = activeLibraryTestController;
        const { categories = [] } = monitoringControlInstance ?? {};

        const testCategoryLabels = categories.map((category) => {
            return getMonitorCheckTypeLabel(
                category as FailsByCategoryResponseDto['category'],
            );
        });

        return (
            <Box
                data-testid="LibraryTestLogicDetailsComponent"
                py="3x"
                pl="3x"
                data-id="PxvCzKM6"
                backgroundColor="neutralBackgroundMild"
                borderRadius="borderRadius4x"
            >
                <Text type="title">{testName}</Text>

                <Stack direction="row" align="start" gap="10x" py="2x">
                    <KeyValuePair
                        key={t`Category`}
                        label={t`Category`}
                        type="TEXT"
                        value={testCategoryLabels.join(', ')}
                    />
                    <KeyValuePair
                        key={t`Provider`}
                        label={t`Provider`}
                        type="TEXT"
                        value={connectionClientTypeLabels.join(', ')}
                    />
                    <KeyValuePair
                        key={t`Accounts`}
                        label={t`Accounts`}
                        type="TEXT"
                        value={t`All Accounts`}
                    />
                </Stack>
            </Box>
        );
    },
);
