import type {
    ComboboxFetchParams,
    ComboboxProps,
} from '@cosmos/components/combobox';
import { usersControllerListUsersOptions } from '@globals/api-sdk/queries';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';

class PolicyOwnerComboboxController {
    constructor() {
        makeAutoObservable(this);
    }

    usersQuery = new ObservedQuery(usersControllerListUsersOptions);

    load(params: ComboboxFetchParams): void {
        const { search } = params;

        this.usersQuery.load({
            query: {
                'roles[]': [
                    'ADMIN',
                    'TECHGOV',
                    'WORKSPACE_ADMINISTRATOR',
                    'POLICY_MANAGER',
                ],
                excludeReadOnlyUsers: true,
                q: search,
                limit: 100,
            },
        });
    }

    get options(): ComboboxProps['options'] {
        const users = this.usersQuery.data?.data ?? [];

        return users.map((user) => {
            const fullName = getFullName(user.firstName, user.lastName);

            return {
                id: user.id.toString(),
                label: fullName,
                value: user.id.toString(),
                avatar: {
                    imgSrc: user.avatarUrl,
                    imgAlt: fullName,
                    fallbackText: getInitials(fullName),
                },
            };
        });
    }

    get isLoading(): boolean {
        return this.usersQuery.isLoading;
    }
}

export const policyOwnerComboboxController =
    new PolicyOwnerComboboxController();
