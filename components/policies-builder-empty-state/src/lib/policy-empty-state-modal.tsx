import { z } from 'zod';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { type MessageDescriptor, useLingui } from '@globals/i18n';
import { msg, t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type FormSchema } from '@ui/forms';

// TODO: ENG-73174 - Form data interface will be defined in empty state controller

interface PolicyEmptyStateModalProps {
    onClose: () => void;
    mode: 'start-building' | 'upload';
}

const modalTitles: Record<
    PolicyEmptyStateModalProps['mode'],
    MessageDescriptor
> = {
    'start-building': msg`Start Building Policy`,
    upload: msg`Upload Existing Policy`,
};

const modalDescriptions: Record<
    PolicyEmptyStateModalProps['mode'],
    MessageDescriptor
> = {
    'start-building': msg`Select an owner to start building your policy.`,
    upload: msg`Select an owner and upload your existing policy document.`,
};

// Mock data that matches the real policy owners controller format
const mockOwnerOptions = [
    {
        id: '1',
        label: '<PERSON>e',
        value: '1',
        avatar: {
            imgSrc: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
            imgAlt: 'John Doe',
            fallbackText: 'JD',
        },
    },
    {
        id: '2',
        label: 'Jane Smith',
        value: '2',
        avatar: {
            imgSrc: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
            imgAlt: 'Jane Smith',
            fallbackText: 'JS',
        },
    },
    {
        id: '3',
        label: 'Michael Johnson',
        value: '3',
        avatar: {
            imgAlt: 'Michael Johnson',
            fallbackText: 'MJ',
        },
    },
];

/**
 * Form schema for the policy empty state modal.
 * TODO: ENG-73174 - Move this schema into the empty state controller when available.
 */
const getPolicyEmptyStateFormSchema = (
    mode: PolicyEmptyStateModalProps['mode'],
): FormSchema => ({
    ownerId: {
        type: 'combobox',
        label: t`Policy Owner`,
        placeholder: t`Search for a policy owner...`,
        options: mockOwnerOptions, // TODO: ENG-73174 - Replace with policy owners controller options
        isLoading: false, // TODO: ENG-73174 - Connect to policy owners controller
        loaderLabel: t`Loading owners...`,
        validator: z.object(
            {
                id: z.string(),
                label: z.string(),
                value: z.string(),
            },
            { message: t`Please select a policy owner` },
        ),
        onFetchOptions: () => {
            // TODO: ENG-73174 - Replace with real policy owners controller
            console.info('Fetching owners...');
        },
    },
    // File upload field for upload mode
    ...(mode === 'upload' && {
        file: {
            type: 'file',
            label: t`Policy Document`,
            acceptedFormats: ['pdf', 'doc', 'docx', 'txt'],
            maxFileSizeInBytes: 10485760, // 10MB
            isMulti: false,
            oneFileOnly: true,
            innerLabel: t`Or drop file here`,
            selectButtonText: t`Upload file`,
            removeButtonText: t`Remove file`,
            // TODO: ENG-73174 - Verify file validation pattern when hooking up
            // Most examples use z.array(z.instanceof(File)).min(1) even for single files
            // May need to change back to array validation and update form submission
            validator: z.instanceof(File, {
                message: t`Please select a policy document`,
            }),
            errorCodeMessages: {
                'file-invalid-type': t`Please upload a PDF, DOC, DOCX, or TXT file`,
                'file-too-large': t`File size must be less than 10MB`,
                'file-too-small': t`File is too small`,
                'too-many-files': t`Please upload only one file`,
            },
        },
    }),
});

export const PolicyEmptyStateModal = observer(
    ({ onClose, mode }: PolicyEmptyStateModalProps): React.JSX.Element => {
        const { _ } = useLingui();

        // TODO: ENG-73174 - Form submission will be handled by empty state controller

        return (
            <>
                <Modal.Header
                    title={_(modalTitles[mode])}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={onClose}
                />
                <Modal.Body>
                    <Stack direction="column" gap="lg">
                        <Text
                            type="body"
                            size="200"
                            colorScheme="neutral"
                            data-id="modal-description"
                        >
                            {_(modalDescriptions[mode])}
                        </Text>

                        <Form
                            hasExternalSubmitButton
                            formId="policy-empty-state-form"
                            schema={getPolicyEmptyStateFormSchema(mode)}
                            data-id="policy-empty-state-form"
                            onSubmit={() => {
                                // TODO: ENG-73174 - Form submission will be handled by empty state controller
                            }}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            level: 'tertiary',
                            label: t`Cancel`,
                            onClick: onClose,
                            // TODO: ENG-73174 - Add disabled state when controller is submitting
                        },
                        {
                            type: 'submit',
                            form: 'policy-empty-state-form',
                            label: t`Submit`,
                            // TODO: ENG-73174 - Add isLoading from empty state controller
                        },
                    ]}
                />
            </>
        );
    },
);
