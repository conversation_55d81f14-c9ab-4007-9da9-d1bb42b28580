import { useCallback, useEffect } from 'react';
import { MapControlsStep } from '@components/map-controls-step';
import { sharedLinkControlsController } from '@controllers/evidence-library';
import {
    sharedCreateRiskMutationController,
    sharedRiskCustomFieldsSubmissionsController,
} from '@controllers/risk';
import { sharedVendorsRisksMutationController } from '@controllers/vendors';
import { Wizard } from '@cosmos-lab/components/wizard';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getParentRoute } from '@helpers/path';
import { useLocation, useNavigate } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';
import { AssessmentAndTreatmentStep } from './components/assessment-and-treatment-step';
import { DetailsStep } from './components/details-step.component';
import { SourceAndStatusStep } from './components/source-and-status-step.component';
import { sharedRiskDetailsFormModel } from './models/risk-details-form.model';

export const CreateRiskWizard = observer(
    ({
        riskType,
        vendorId,
        backRoute,
    }: {
        riskType: 'VENDOR_RISK' | 'RISK';
        vendorId?: number;
        backRoute?: string;
    }): React.JSX.Element => {
        sharedRiskDetailsFormModel.setRiskType(riskType);
        if (sharedEntitlementFlagController.isCustomFieldsEnabled) {
            const {
                riskCustomFieldsDetailsSection,
                riskCustomFieldsAssessmentSection,
                riskCustomFieldsTreatmentSection,
            } = sharedRiskCustomFieldsSubmissionsController;

            const allCustomFields = [
                ...(riskCustomFieldsDetailsSection?.customFields ?? []),
                ...(riskCustomFieldsAssessmentSection?.customFields ?? []),
                ...(riskCustomFieldsTreatmentSection?.customFields ?? []),
            ];

            if (riskType === 'VENDOR_RISK') {
                sharedVendorsRisksMutationController.setAvailableCustomFields(
                    allCustomFields,
                );
            } else {
                sharedCreateRiskMutationController.setAvailableCustomFields(
                    allCustomFields,
                );
            }
        }
        const {
            formRef: formSourceAndStatusRef,
            triggerSubmit: triggerSubmitSourceAndStatus,
        } = useFormSubmit();
        const { formRef: formDetailsRef, triggerSubmit: triggerSubmitDetails } =
            useFormSubmit();
        const {
            formRef: formAssessmentAndTreatmentRef,
            triggerSubmit: triggerSubmitAssessmentAndTreatment,
        } = useFormSubmit();
        const navigate = useNavigate();
        const location = useLocation();
        const uploadStatus =
            riskType === 'VENDOR_RISK'
                ? sharedVendorsRisksMutationController.uploadStatus
                : sharedCreateRiskMutationController.uploadStatus;

        const SourceAndStatusStepForm = useCallback(
            () => (
                <SourceAndStatusStep
                    formRef={formSourceAndStatusRef}
                    vendorId={vendorId}
                    data-id="source-and-status-step-form"
                />
            ),
            [formSourceAndStatusRef, vendorId],
        );

        const DetailsStepForm = useCallback(
            () => (
                <DetailsStep
                    formRef={formDetailsRef}
                    vendorId={vendorId}
                    data-id="details-step-form"
                />
            ),
            [formDetailsRef, vendorId],
        );

        const AssessmentAndTreatmentStepForm = useCallback(
            () => (
                <AssessmentAndTreatmentStep
                    formRef={formAssessmentAndTreatmentRef}
                    vendorId={vendorId}
                    data-id="assessment-and-treatment-step-form"
                />
            ),
            [formAssessmentAndTreatmentRef, vendorId],
        );

        const MapControlsStepForm = useCallback(
            () => (
                <MapControlsStep
                    objectType="risk"
                    data-id="map-controls-step"
                />
            ),
            [],
        );

        const navigateBack = useCallback(() => {
            sharedLinkControlsController.removeAllControls();
            if (riskType === 'RISK') {
                const { currentWorkspace } = sharedWorkspacesController;
                const { newRiskId } = sharedCreateRiskMutationController;

                if (newRiskId) {
                    navigate(
                        `/workspaces/${currentWorkspace?.id}/risk/management/registers/1/register-risks/${newRiskId}/overview`,
                    );
                }
            } else {
                backRoute
                    ? navigate(backRoute)
                    : navigate(getParentRoute(location.pathname));
            }
        }, [backRoute, location.pathname, navigate, riskType]);

        useEffect(
            () =>
                action(() => {
                    // Make sure we clear the selected controls when we enter the wizard
                    sharedLinkControlsController.clearSelectedItems();
                    sharedLinkControlsController.removeAllControls();
                    sharedVendorsRisksMutationController.resetProcess();
                    sharedCreateRiskMutationController.resetProcess();
                }),
            [],
        );

        useEffect(() => {
            if (uploadStatus === 'SUCCESS') {
                navigateBack();
            }
        }, [navigate, navigateBack, uploadStatus]);

        return (
            <Wizard
                data-testid="CreateRiskWizard"
                data-id="RHMFsvT8"
                isLoading={uploadStatus === 'IN_PROGRESS'}
                steps={[
                    {
                        backButtonLabelOverride: t`Cancel`,
                        component: SourceAndStatusStepForm,
                        forwardButtonLabelOverride: t`Continue`,
                        isStepSkippable: false,
                        stepTitle: t`Source and Status`,
                        onStepChange: triggerSubmitSourceAndStatus,
                    },
                    {
                        backButtonLabelOverride: t`Back`,
                        component: DetailsStepForm,
                        forwardButtonLabelOverride: t`Continue`,
                        isStepSkippable: false,
                        stepTitle: t`Details`,
                        onStepChange: triggerSubmitDetails,
                    },
                    {
                        backButtonLabelOverride: t`Back`,
                        component: AssessmentAndTreatmentStepForm,
                        forwardButtonLabelOverride: t`Continue`,
                        isStepSkippable: true,
                        stepTitle: t`Assessment & Treatment`,
                        onStepChange: triggerSubmitAssessmentAndTreatment,
                    },
                    {
                        backButtonLabelOverride: t`Back`,
                        component: MapControlsStepForm,
                        forwardButtonLabelOverride: t`Finish`,
                        isStepSkippable: false,
                        stepTitle: t`Controls`,
                    },
                ]}
                onCancel={navigateBack}
                onComplete={action(() => {
                    if (riskType === 'VENDOR_RISK') {
                        sharedVendorsRisksMutationController.createVendorRisk(
                            vendorId ?? 0,
                        );
                    } else {
                        sharedCreateRiskMutationController.createNewRisk();
                    }
                })}
            />
        );
    },
);
