import { constant } from 'lodash-es';
import {
    createNumericOptions,
    type CreateRiskCustomMutationType,
    sharedRiskCustomFieldsSubmissionsController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { sharedUsersInfiniteController } from '@controllers/users';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, toJS } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import type { FormSchema } from '@ui/forms';
import { InherentScoreDisplay } from '../components/inherent-score-display.component';
import { ResidualScoreDisplay } from '../components/residual-score-display.component';
import { getTreatmentPlanOptions } from '../helpers/create-risk-options.helpers';
import { validateAssessmentAndTreatmentSchema } from '../helpers/validate-assessment-and-treatment-schema.helper';
import { sharedRiskDetailsFormModel } from './risk-details-form.model';

// Define the paths based on the actual schema structure
export const FORM_FIELDS = {
    RESIDUAL: {
        IMPACT: 'treatmentGroup.residualScoreGroup.residualImpact.value' as const,
        LIKELIHOOD:
            'treatmentGroup.residualScoreGroup.residualLikelihood.value' as const,
    },
    INHERENT: {
        IMPACT: 'inherentScoreGroup.impact.value' as const,
        LIKELIHOOD: 'inherentScoreGroup.likelihood.value' as const,
    },
    TREATMENT: {
        OPTION: 'treatmentGroup.treatment.value' as const,
    },
} as const;

export class RiskAssessmentAndTreatmentFormModel {
    constructor() {
        makeAutoObservable(this);
    }

    get likelihoodOptions(): ListBoxItemData[] {
        const { riskSettings } = sharedRiskSettingsController;

        return createNumericOptions(riskSettings?.likelihood ?? 0);
    }

    get impactOptions(): ListBoxItemData[] {
        const { riskSettings } = sharedRiskSettingsController;

        return createNumericOptions(riskSettings?.impact ?? 0);
    }

    getSchema(
        storedValues?: Partial<CreateRiskCustomMutationType>,
    ): FormSchema {
        const treatmentPlanOptions = getTreatmentPlanOptions();
        const storedInherent = toJS(storedValues?.inherentScoreGroup);
        const storedTreatment = toJS(storedValues?.treatmentGroup);
        const storedCustomFields = toJS(storedValues?.customFields);

        let customFieldsAssessmentSchema: FormSchema = {};
        let customFieldsTreatmentSchema: FormSchema = {};

        if (sharedEntitlementFlagController.isCustomFieldsEnabled) {
            const {
                riskCustomFieldsAssessmentSection,
                riskCustomFieldsTreatmentSection,
            } = sharedRiskCustomFieldsSubmissionsController;

            customFieldsAssessmentSchema =
                sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
                    riskCustomFieldsAssessmentSection?.customFields ?? [],
                );
            customFieldsTreatmentSchema =
                sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
                    riskCustomFieldsTreatmentSection?.customFields ?? [],
                );
        }
        if (storedCustomFields) {
            Object.keys(customFieldsAssessmentSchema).forEach((key) => {
                customFieldsAssessmentSchema[key].initialValue =
                    storedCustomFields[key] ?? '';
            });
            Object.keys(customFieldsTreatmentSchema).forEach((key) => {
                customFieldsTreatmentSchema[key].initialValue =
                    storedCustomFields[key] ?? '';
            });
        }

        const schemaDefinition: FormSchema = {
            inherentScoreGroup: {
                type: 'group',
                direction: 'row',
                header: t`Assessment`,
                showDivider: false,
                alignItems: 'end',
                fields: {
                    impact: {
                        type: 'select',
                        options: this.impactOptions,
                        initialValue: storedInherent?.impact,
                        label: t`Inherent impact`,
                        isOptional: true,
                    },
                    likelihood: {
                        type: 'select',
                        options: this.likelihoodOptions,
                        initialValue: storedInherent?.likelihood,
                        label: t`Inherent likelihood`,
                        isOptional: true,
                    },
                    inherentScoreDisplay: {
                        type: 'custom',
                        render: () => (
                            <InherentScoreDisplay data-id="uUd_zU9u" />
                        ),
                        label: t`Score`,
                        isOptional: true,
                        initialValue: undefined,
                    },
                },
            },
            ...customFieldsAssessmentSchema,
            divider: {
                type: 'group',
                header: '',
                fields: {},
                showDivider: true,
            },
            treatmentGroup: {
                type: 'group',
                header: t`Treatment`,
                showDivider: false,
                fields: {
                    treatment: {
                        type: 'select',
                        options: treatmentPlanOptions,
                        initialValue:
                            toJS(storedTreatment?.treatment) ??
                            treatmentPlanOptions.find(
                                (option) => option.value === 'UNTREATED',
                            ),
                        label: t`Treatment option`,
                        isOptional: true,
                    },
                    treatmentPlan: {
                        type: 'textarea',
                        initialValue: toJS(storedTreatment?.treatmentPlan),
                        label: t`Treatment plan`,
                        isOptional: true,
                        shownIf: {
                            fieldName: 'treatmentGroup.treatment',
                            operator: 'notIn',
                            value: ['UNTREATED'],
                        },
                    },
                    residualScoreGroup: {
                        type: 'group',
                        direction: 'row',
                        header: t`Residual score`,
                        headerLevel: 'h4',
                        showDivider: false,
                        alignItems: 'end',
                        shownIf: {
                            fieldName: 'treatmentGroup.treatment',
                            operator: 'in',
                            value: ['MITIGATE', 'TRANSFER'],
                        },
                        fields: {
                            residualImpact: {
                                type: 'select',
                                options: this.impactOptions,
                                initialValue: toJS(
                                    storedTreatment?.residualScoreGroup
                                        ?.residualImpact,
                                ),
                                label: t`Residual impact`,
                                isOptional: true,
                            },
                            residualLikelihood: {
                                type: 'select',
                                options: this.likelihoodOptions,
                                initialValue: toJS(
                                    storedTreatment?.residualScoreGroup
                                        ?.residualLikelihood,
                                ),
                                label: t`Residual likelihood`,
                                isOptional: true,
                            },
                            residualScoreDisplay: {
                                type: 'custom',
                                render: () => (
                                    <ResidualScoreDisplay data-id="2rXMKicj" />
                                ),
                                label: t`Score`,
                                isOptional: true,
                                initialValue: undefined,
                            },
                        },
                    },
                    anticipatedCompletionDate: {
                        type: 'date',
                        label: t`Anticipated completion date`,
                        isOptional: true,
                        initialValue: toJS(
                            storedTreatment?.anticipatedCompletionDate as
                                | TDateISODate
                                | undefined,
                        ),
                        shownIf: {
                            fieldName: 'treatmentGroup.treatment',
                            operator: 'in',
                            value: ['MITIGATE', 'TRANSFER'],
                        },
                    },
                    completedDate: {
                        type: 'date',
                        label: t`Completed date`,
                        isOptional: true,
                        initialValue: toJS(
                            storedTreatment?.completedDate as
                                | TDateISODate
                                | undefined,
                        ),
                        shownIf: {
                            fieldName: 'treatmentGroup.treatment',
                            operator: 'notIn',
                            value: ['UNTREATED'],
                        },
                    },
                    reviewers: {
                        type: 'combobox',
                        isMultiSelect: true,
                        getSearchEmptyState: constant(t`No users found`),
                        options: sharedRiskDetailsFormModel.usersOptions,
                        label: t`Reviewers`,
                        loaderLabel: t`Loading risk reviewers options`,
                        isOptional: true,
                        hasMore: sharedUsersInfiniteController.hasNextPage,
                        isLoading: sharedUsersInfiniteController.isLoading,
                        onFetchOptions:
                            sharedUsersInfiniteController.onFetchUsers,
                        ...(storedTreatment?.reviewers && {
                            initialValue: toJS(storedTreatment.reviewers),
                        }),
                        shownIf: {
                            fieldName: 'treatmentGroup.treatment',
                            operator: 'notIn',
                            value: ['UNTREATED'],
                        },
                    },
                },
            },
            ...customFieldsTreatmentSchema,
        };

        validateAssessmentAndTreatmentSchema(schemaDefinition);

        return schemaDefinition;
    }
}

export const sharedRiskAssessmentAndTreatmentFormModel =
    new RiskAssessmentAndTreatmentFormModel();
