import { constant, isEmpty } from 'lodash-es';
import React from 'react';
import { z } from 'zod';
import {
    sharedCreateRiskMutationController,
    sharedRiskCategoriesController,
    sharedRiskCustomFieldsSubmissionsController,
} from '@controllers/risk';
import { sharedUsersInfiniteController } from '@controllers/users';
import {
    sharedVendorsIntegrationsInfiniteController,
    sharedVendorsRisksMutationController,
} from '@controllers/vendors';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, toJS } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { UPLOAD_FILES_ERROR_CODE_MESSAGES } from '@helpers/upload-file';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import type { CustomFieldRenderProps, FormSchema } from '@ui/forms';
import { CategoryFieldWithAddButton } from '@views/risk-register-overview';
import { RISK_SUPPORT_DOCUMENTS_ALLOWED_TYPES } from '../constants/create-risk-wizard.constants';

const renderCategoryField = (
    fieldProps: CustomFieldRenderProps,
): React.JSX.Element => {
    return React.createElement(CategoryFieldWithAddButton, fieldProps);
};

export class RiskDetailsFormModel {
    internalRiskType: 'VENDOR_RISK' | 'RISK' | undefined = undefined;

    constructor() {
        makeAutoObservable(this);
    }

    setRiskType = (riskType: 'VENDOR_RISK' | 'RISK'): void => {
        this.internalRiskType = riskType;
    };

    get usersOptions(): ListBoxItemData[] {
        return sharedUsersInfiniteController.usersList.map(
            ({ id, firstName, lastName, avatarUrl }) => ({
                id: String(id),
                label: getFullName(firstName, lastName),
                avatar: {
                    imgAlt: t`${firstName} ${lastName} image`,
                    imgSrc: avatarUrl ?? undefined,
                },
            }),
        );
    }

    get riskType(): 'VENDOR_RISK' | 'RISK' | undefined {
        return this.internalRiskType;
    }

    get categoriesOptions(): ListBoxItemData[] {
        return sharedRiskCategoriesController.categories.map((category) => ({
            id: category.id.toString(),
            label: category.name,
        }));
    }

    get getFormSchema(): FormSchema {
        const storedValues =
            this.riskType === 'VENDOR_RISK'
                ? sharedVendorsRisksMutationController.getMutatedStateForStep(
                      'details',
                  )
                : sharedCreateRiskMutationController.getMutatedStateForStep(
                      'details',
                  );

        const {
            options: integrationsOptions,
            hasNextPage: hasMoreIntegrations,
            isLoading: isLoadingIntegrations,
            onFetchRisks,
        } = sharedVendorsIntegrationsInfiniteController;

        let customFieldsDetailsSchema: FormSchema = {};

        if (sharedEntitlementFlagController.isCustomFieldsEnabled) {
            const { riskCustomFieldsDetailsSection } =
                sharedRiskCustomFieldsSubmissionsController;

            customFieldsDetailsSchema =
                sharedCustomFieldsManager.adapterCustomFieldsToFormSchema(
                    riskCustomFieldsDetailsSection?.customFields ?? [],
                );
        }
        const { riskDetailsGroup, customFields, riskSourceGroup } =
            storedValues;

        const riskSource = toJS(riskSourceGroup)?.riskSource ?? 'INTERNAL';

        if (customFields) {
            Object.keys(customFieldsDetailsSchema).forEach((key) => {
                customFieldsDetailsSchema[key].initialValue =
                    customFields[key] ?? '';
            });
        }

        return {
            riskDetailsGroup: {
                type: 'group',
                header: t`Add your risk details`,
                showDivider: false,
                fields: {
                    ...(riskSource === 'EXTERNAL' &&
                        this.riskType === 'RISK' && {
                            vendor: {
                                type: 'combobox',
                                isMultiSelect: false,
                                getSearchEmptyState:
                                    constant(t`No vendor found`),
                                options: integrationsOptions,
                                label: t`Vendor`,
                                loaderLabel: t`Loading vendor options`,
                                isOptional: false,
                                hasMore: hasMoreIntegrations,
                                isLoading: isLoadingIntegrations,
                                onFetchOptions: onFetchRisks,
                                ...(riskDetailsGroup?.vendor && {
                                    initialValue: toJS(riskDetailsGroup.vendor),
                                }),
                            },
                        }),
                    title: {
                        type: 'text',
                        initialValue: toJS(riskDetailsGroup?.title) ?? '',
                        label: t`Title`,
                        validator: z
                            .string()
                            .min(1, {
                                message: t`Title is required`,
                            })
                            .and(
                                z
                                    .string()
                                    .refine((val) => !isEmpty(val.trim()), {
                                        message: t`Title cannot be just whitespace`,
                                    }),
                            ),
                    },
                    description: {
                        type: 'textarea',
                        maxCharacters: 400,
                        initialValue: toJS(riskDetailsGroup?.description) ?? '',
                        label: t`Description`,
                        validator: z
                            .string()
                            .min(1, {
                                message: t`Description is required`,
                            })
                            .and(
                                z
                                    .string()
                                    .refine((val) => !isEmpty(val.trim()), {
                                        message: t`Description cannot be just whitespace`,
                                    }),
                            ),
                    },
                    identifiedAt: {
                        type: 'date',
                        initialValue: toJS(
                            riskDetailsGroup?.identifiedAt as
                                | TDateISODate
                                | undefined,
                        ),
                        label: t`Risk identified date`,
                        isOptional: true,
                    },
                    categories: {
                        type: 'custom',
                        render: renderCategoryField,
                        label: t`Category`,
                        isOptional: true,
                    },
                    owners: {
                        type: 'combobox',
                        isMultiSelect: true,
                        getSearchEmptyState: constant(t`No users found`),
                        options: this.usersOptions,
                        label: t`Risk owners`,
                        loaderLabel: t`Loading risk owner options`,
                        isOptional: true,
                        hasMore: sharedUsersInfiniteController.hasNextPage,
                        isLoading: sharedUsersInfiniteController.isLoading,
                        onFetchOptions:
                            sharedUsersInfiniteController.onFetchUsers,
                        ...(riskDetailsGroup?.owners && {
                            initialValue: toJS(riskDetailsGroup.owners),
                        }),
                    },
                    documents: {
                        type: 'file',
                        label: t`Documents`,
                        isOptional: true,
                        acceptedFormats: RISK_SUPPORT_DOCUMENTS_ALLOWED_TYPES,
                        errorCodeMessages: UPLOAD_FILES_ERROR_CODE_MESSAGES,
                        innerLabel: t`Or drop files here`,
                        isMulti: true,
                        removeButtonText: t`Remove file`,
                        selectButtonText: t`Upload files`,
                        // Note: Documents are complex objects and may need special handling for initial values
                    },
                },
            },
            ...customFieldsDetailsSchema,
        };
    }
}

export const sharedRiskDetailsFormModel = new RiskDetailsFormModel();
