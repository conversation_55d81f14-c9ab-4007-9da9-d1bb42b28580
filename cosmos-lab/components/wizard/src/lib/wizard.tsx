import { use<PERSON><PERSON>back, useMemo, useRef, useState } from 'react';
import { But<PERSON> } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import {
    WizardStep,
    type WizardStepProps,
} from '@cosmos-lab/components/wizard-step';
import { t } from '@globals/i18n/macro';
import { getWizardDefaultBackButtonLabel } from './helpers/get-wizard-default-back-button-label.helper';
import { getWizardDefaultCancelButtonLabel } from './helpers/get-wizard-default-cancel-button-label.helper';
import { getWizardDefaultCompleteButtonLabel } from './helpers/get-wizard-default-complete-button-label.helper';
import { getWizardDefaultForwardButtonLabel } from './helpers/get-wizard-default-forward-button-label.helper';
import { getWizardDefaultResetButtonLabel } from './helpers/get-wizard-default-reset-button-label.helper';
import { getWizardDefaultSkipButtonLabel } from './helpers/get-wizard-default-skip-button-label.helper';
import type { WizardStepDataProps } from './interfaces/wizard-step-data-props';
import type { WizardChangeStepActionType } from './types/wizard-change-step-action.type';

export interface WizardProps {
    /**
     * Props for each step of the wizard.
     */
    steps: WizardStepDataProps[];

    /**
     * Callback function used when the wizard is cancelled.
     */
    onCancel: () => void;

    /**
     * Callback function when the final step of the wizard is completed.
     */
    onComplete: () => void;

    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;

    /**
     * Custom complete button label.
     */
    completeButtonLabel?: string;

    /**
     * A loading state prop that will affect the navigation elements on the wizard.
     */
    isLoading?: boolean;

    /**
     * Custom next button label.
     */
    nextButtonLabel?: string;
    /**
     * Determines whether the reset button is allowed to be navigated to.
     */
    canReset?: boolean;
    /**
     * Wizard reset button's default label override.
     */
    resetButtonLabelOverride?: string;
    /**
     * Callback function when the wizard is reset.
     */
    onReset?: () => void;
}

/**
 * The Wizard component is used when customers need to create new objects in Drata through a step-by-step flow. Wizards help break complex tasks into
 * manageable chunks with clear progress indicators.
 *
 * [Wizard in Figma](https://www.figma.com/design/LhHaXLLZ2iyNGFprqPmQKp/Cosmos--Modules?m=auto&node-id=2501-128968&t=qLQCBvB1eWFRQgGU-1).
 */
export const Wizard = ({
    steps,
    onCancel,
    onComplete,
    'data-id': dataId,
    completeButtonLabel,
    isLoading = false,
    nextButtonLabel,
    canReset = false,
    resetButtonLabelOverride,
    onReset,
}: WizardProps): React.JSX.Element => {
    const wizardRef = useRef<HTMLDivElement>(null);
    const [currentIndex, setCurrentIndex] = useState<number>(0);
    const [stepsWithState, setStepsWithState] = useState<WizardStepProps[]>(
        steps.map((step, i) => {
            const wizardStep: WizardStepProps = {
                stepTitle: step.stepTitle,
                stepSubtitle: step.stepSubtitle,
                stepNumber: i + 1,
                hasNextStep: i < steps.length - 1,
                stepState: i === 0 ? 'active' : 'upcoming',
            };

            return wizardStep;
        }),
    );

    const WizardComponent = useMemo(
        () => steps[currentIndex].component,
        [currentIndex, steps],
    );

    const updateStepState = useCallback(
        ({ action }: { action: WizardChangeStepActionType }) => {
            const step = stepsWithState[currentIndex];

            if (action === 'reset') {
                setStepsWithState((prevSteps) => {
                    return prevSteps.map((prevStep, i) => {
                        if (i === 0) {
                            return {
                                ...prevStep,
                                stepState: 'active',
                            };
                        }

                        return {
                            ...prevStep,
                            stepState: 'upcoming',
                        };
                    });
                });

                return;
            }

            if (action === 'next' || action === 'skip') {
                step.stepState = 'complete';
                const nextStep = stepsWithState[currentIndex + 1];

                nextStep.stepState = 'active';
            }

            if (action === 'back') {
                step.stepState = 'upcoming';
                const previousStep = stepsWithState[currentIndex - 1];

                previousStep.stepState = 'active';
            }
            setStepsWithState(stepsWithState);
        },
        [currentIndex, stepsWithState],
    );

    const goToNextStep = useCallback(
        ({ action }: { action: WizardChangeStepActionType }) => {
            if (currentIndex === steps.length - 1) {
                return;
            }

            // Forcing scroll to top due to re-rendering does not restart the scroll position
            wizardRef.current?.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
            });

            updateStepState({ action });
            setCurrentIndex(currentIndex + 1);
        },
        [currentIndex, steps.length, updateStepState],
    );

    const goToPreviousStep = useCallback(
        ({ action }: { action: WizardChangeStepActionType }) => {
            if (currentIndex === 0) {
                return;
            }
            wizardRef.current?.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
            });

            updateStepState({ action });
            setCurrentIndex(currentIndex - 1);
        },
        [currentIndex, updateStepState],
    );

    const resetWizard = useCallback(() => {
        wizardRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
        });

        updateStepState({ action: 'reset' });
        setCurrentIndex(0);
    }, [updateStepState]);

    const changeStep = useCallback(
        async ({ action }: { action: WizardChangeStepActionType }) => {
            if (isLoading) {
                return;
            }

            const currentStep = steps[currentIndex];

            switch (action) {
                case 'next': {
                    const canAdvance = currentStep.onStepChange
                        ? await currentStep.onStepChange()
                        : true;

                    if (!canAdvance) {
                        return;
                    }

                    goToNextStep({ action });

                    break;
                }
                case 'skip': {
                    goToNextStep({ action });

                    break;
                }
                case 'reset': {
                    onReset?.();
                    resetWizard();

                    break;
                }
                default: {
                    goToPreviousStep({ action });
                }
            }
        },
        [
            currentIndex,
            steps,
            goToNextStep,
            goToPreviousStep,
            isLoading,
            onReset,
            resetWizard,
        ],
    );

    return (
        <div ref={wizardRef} data-testid="Wizard" data-id="rvD2mhFJ">
            <Stack
                direction="column"
                gap="4x"
                data-id={dataId}
                data-testid="Wizard"
            >
                <Stack direction="row" gap="3xl">
                    <Stack direction="column" minWidth="64x">
                        {stepsWithState.map((step) => (
                            <WizardStep
                                key={`${step.stepTitle}-wizard-step`}
                                stepNumber={step.stepNumber}
                                stepTitle={step.stepTitle}
                                stepState={step.stepState}
                                stepSubtitle={step.stepSubtitle}
                                hasNextStep={step.hasNextStep}
                                data-id="yVlFrKs6"
                            />
                        ))}
                    </Stack>
                    <Stack direction="column" gap="2xl" flexGrow="1">
                        <WizardComponent />
                        <Stack direction="column" gap="2xl" height="100%">
                            <Divider />
                            <Stack direction="row-reverse" justify="between">
                                <Stack gap="4x">
                                    {currentIndex === steps.length - 1 ||
                                        (steps[currentIndex]
                                            .isStepSkippable && (
                                            <Button
                                                level="secondary"
                                                isLoading={isLoading}
                                                a11yLoadingLabel={t`Skipping...`}
                                                label={
                                                    steps[currentIndex]
                                                        .skipButtonLabelOverride ??
                                                    getWizardDefaultSkipButtonLabel()
                                                }
                                                onClick={async () => {
                                                    // onStepChange can have side effects, so we need to await it
                                                    await changeStep({
                                                        action: 'skip',
                                                    });
                                                }}
                                            />
                                        ))}
                                    {currentIndex !== steps.length - 1 &&
                                        steps[currentIndex].canGoForward !==
                                            false && (
                                            <Button
                                                type="submit"
                                                isLoading={isLoading}
                                                a11yLoadingLabel={t`Processing...`}
                                                label={
                                                    steps[currentIndex]
                                                        .forwardButtonLabelOverride ??
                                                    nextButtonLabel ??
                                                    getWizardDefaultForwardButtonLabel()
                                                }
                                                onClick={async () => {
                                                    // onStepChange can have side effects, so we need to await it
                                                    await changeStep({
                                                        action: 'next',
                                                    });
                                                }}
                                            />
                                        )}
                                    {currentIndex === steps.length - 1 &&
                                        canReset && (
                                            <Button
                                                type="button"
                                                level="tertiary"
                                                label={
                                                    resetButtonLabelOverride ??
                                                    getWizardDefaultResetButtonLabel()
                                                }
                                                onClick={async () => {
                                                    // onStepChange can have side effects, so we need to await it
                                                    await changeStep({
                                                        action: 'reset',
                                                    });
                                                }}
                                            />
                                        )}
                                    {currentIndex === steps.length - 1 && (
                                        <Button
                                            type="submit"
                                            isLoading={isLoading}
                                            a11yLoadingLabel={t`Completing...`}
                                            label={
                                                steps[currentIndex]
                                                    .forwardButtonLabelOverride ??
                                                completeButtonLabel ??
                                                getWizardDefaultCompleteButtonLabel()
                                            }
                                            onClick={onComplete}
                                        />
                                    )}
                                </Stack>
                                {currentIndex !== 0 &&
                                    steps[currentIndex].canGoBack !== false && (
                                        <Button
                                            level="secondary"
                                            label={
                                                steps[currentIndex]
                                                    .backButtonLabelOverride ??
                                                getWizardDefaultBackButtonLabel()
                                            }
                                            onClick={async () => {
                                                // onStepChange can have side effects, so we need to await it
                                                await changeStep({
                                                    action: 'back',
                                                });
                                            }}
                                        />
                                    )}
                                {currentIndex === 0 && (
                                    <Button
                                        level="secondary"
                                        label={
                                            steps[currentIndex]
                                                .backButtonLabelOverride ??
                                            getWizardDefaultCancelButtonLabel()
                                        }
                                        onClick={onCancel}
                                    />
                                )}
                            </Stack>
                        </Stack>
                    </Stack>
                </Stack>
            </Stack>
        </div>
    );
};
