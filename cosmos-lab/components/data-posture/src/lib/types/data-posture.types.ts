import type { Size } from '@cosmos/components/text';

export type DataPostureSize = 'sm' | 'lg';

export interface DataPostureBox {
    /** Unique identifier for the box. */
    id: string;

    /** Numeric value representing the count for this box. */
    value: number;

    /** Optional size for the value displayed in the box. */
    valueSize?: Size;

    /** Background color of the box. */
    color: string;

    /** Optional click handler for interactions. */
    onClick?: () => void;

    /** Optional content to display in the box's popover. */
    popoverContent?: React.ReactNode;

    /** Optional label for legend to display (Only works if legend is true on DataPosture). */
    legendLabel?: string;
}
