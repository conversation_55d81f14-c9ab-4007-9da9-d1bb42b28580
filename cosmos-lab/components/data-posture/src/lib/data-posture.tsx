import { useCallback, useMemo, useRef, useState } from 'react';
import { styled } from 'styled-components';
import { Box } from '@cosmos/components/box';
import { Popover } from '@cosmos/components/popover';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimension2x } from '@cosmos/constants/tokens';
import type {
    DataPostureBox,
    DataPostureSize,
} from './types/data-posture.types';

interface DataPostureProps {
    size?: DataPostureSize;
    boxes: DataPostureBox[];
    legend?: boolean;
    isEmptyData?: boolean;
}

const MAX_BOXES = 5;
const MIN_BOXES = 0;

const StyledDataPostureBox = styled.div<{
    $bgColor?: string;
    $widthPercentage: string;
}>`
    width: ${({ $widthPercentage }) => $widthPercentage};
    flex: 0 0 auto;
    background-color: ${({ $bgColor }) => $bgColor};
    cursor: ${({ onClick }) => (onClick ? 'pointer' : 'default')};
`;

const LegendColorSquare = styled.div<{ $bgColor?: string }>`
    width: ${dimension2x};
    height: ${dimension2x};
    background-color: ${({ $bgColor }) => $bgColor || 'transparent'};
`;

/**
 * The DataPosture component displays data as a horizontal grid of proportionally sized colored boxes representing different status levels, with optional legend, for compliance and risk visualization.
 *
 * [DataPosture in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=43525-256260&t=GFojvxS2b2W7WsNi-11).
 */
export const DataPosture = ({
    size = 'sm',
    boxes,
    legend = false,
    isEmptyData,
}: DataPostureProps): React.JSX.Element => {
    const safeBoxes = boxes.slice(MIN_BOXES, MAX_BOXES);

    const height = size === 'lg' ? '5xl' : '2xl';

    const boxRefs = useRef<(HTMLDivElement | null)[]>(
        Array.from<HTMLDivElement | null>({ length: safeBoxes.length }).fill(
            null,
        ),
    );

    const [openPopoverIndex, setOpenPopoverIndex] = useState<number | null>(
        null,
    );

    if (boxes.length > 5) {
        console.warn('DataPosture component supports up to 5 boxes.');
    }

    const adjustedPercentages = useMemo(() => {
        const total = safeBoxes.reduce((sum, box) => sum + box.value, 0);
        const percentages = safeBoxes.map(({ value }) =>
            Math.floor((value / total) * 100),
        );

        // Distribute rounding difference
        const diff = 100 - percentages.reduce((sum, p) => sum + p, 0);
        const lastIndex = percentages.length - 1;

        percentages[lastIndex] = percentages[lastIndex] + diff;

        return percentages;
    }, [safeBoxes]);

    const handleClick = useCallback(
        (
            index: number,
            onClick?: () => void,
            popoverContent?: React.ReactNode,
        ) =>
            popoverContent || onClick
                ? () => {
                      if (popoverContent) {
                          setOpenPopoverIndex((prev) =>
                              prev === index ? null : index,
                          );
                      }
                      if (onClick) {
                          onClick();
                      }
                  }
                : undefined,
        [],
    );

    return (
        <Box data-testid="DataPosture" data-id="qUoCk8CV">
            <Stack gap="4x" direction="column">
                {legend && (
                    <Stack gap="2x" align="center">
                        {safeBoxes.map(({ id, color, legendLabel }) => (
                            <Stack
                                gap="2x"
                                align="center"
                                key={id}
                                data-id="rYlj3xHr"
                            >
                                <LegendColorSquare $bgColor={color} />
                                {legendLabel ? (
                                    <Text align="left" type="body">
                                        {legendLabel}
                                    </Text>
                                ) : null}
                            </Stack>
                        ))}
                    </Stack>
                )}
                <Stack gap="05x" height={height} direction="row">
                    {safeBoxes.map(
                        (
                            { id, value, color, onClick, popoverContent },
                            index,
                        ) => (
                            <StyledDataPostureBox
                                key={id}
                                tabIndex={0}
                                role="button"
                                aria-label={`Posture section with ${value} items`}
                                $widthPercentage={`${adjustedPercentages[index]}%`}
                                data-id="XRFkrdWJ"
                                $bgColor={color}
                                ref={(el) => {
                                    boxRefs.current[index] = el;
                                }}
                                onClick={handleClick(
                                    index,
                                    onClick,
                                    popoverContent,
                                )}
                            >
                                <Popover
                                    anchor={boxRefs.current[index]}
                                    placement="top"
                                    isOpen={openPopoverIndex === index}
                                    padding="xl"
                                    content={popoverContent}
                                    onDismiss={() => {
                                        setOpenPopoverIndex(null);
                                    }}
                                />
                            </StyledDataPostureBox>
                        ),
                    )}
                </Stack>
            </Stack>

            <Stack width="100%" direction="row" gap="05x">
                {safeBoxes.map(({ id, value, valueSize }, index) => (
                    <StyledDataPostureBox
                        key={id}
                        $widthPercentage={`${adjustedPercentages[index]}%`}
                        data-id="e0MQU6vu"
                    >
                        <Text
                            align="center"
                            type="body"
                            size={valueSize || '100'}
                        >
                            {isEmptyData ? '0' : value}
                        </Text>
                    </StyledDataPostureBox>
                ))}
            </Stack>
        </Box>
    );
};
