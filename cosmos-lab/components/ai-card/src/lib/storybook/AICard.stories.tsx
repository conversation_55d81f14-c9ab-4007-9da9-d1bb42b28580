import type { Meta } from '@storybook/react-vite';
import { AICard } from '../ai-card';

const meta: Meta<typeof AICard> = {
    title: 'AI/AICard',
    component: AICard,
    tags: ['Lab'],
    argTypes: {
        isLoading: { control: 'boolean' },
    },
};

export default meta;

// Playground should be first and have all controls enabled so they are accessible on Docs page

export {
    Loading,
    Playground,
} from './stories';
