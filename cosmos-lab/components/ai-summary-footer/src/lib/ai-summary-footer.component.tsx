import type { AiFeedbackStatus } from '@components/ai-feedback';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { AIToolbar } from '@cosmos-lab/components/ai-toolbar';
import { t } from '@globals/i18n/macro';
import { AppLink } from '@ui/app-link';

export const AISummaryFooter = ({
    onFeedback,
    'data-id': dataId,
    onCopy,
}: {
    onFeedback: (feedbackStatus: AiFeedbackStatus) => void;
    'data-id'?: string;
    onCopy?: () => void;
}): React.JSX.Element => {
    return (
        <Stack
            gap="md"
            direction="column"
            data-id={dataId}
            data-testid="AISummaryFooter"
        >
            <AIToolbar
                label={t`Was this useful?`}
                data-id={
                    dataId
                        ? `${dataId}-feedback-toolbar`
                        : 'ai-summary-feedback-toolbar'
                }
                onCopy={onCopy}
                onThumbsUp={() => {
                    onFeedback('USEFUL');
                }}
                onThumbsDown={() => {
                    onFeedback('NOT_USEFUL');
                }}
            />
            <Stack gap="sm" direction="row">
                <Text type="body" size="100" colorScheme="faded">
                    {t`Always check AI responses for accuracy.`}
                </Text>
                <AppLink
                    isExternal
                    href="https://drata.com/blog/our-ai-philosophy"
                    size="sm"
                >
                    {t`Learn more about AI`}
                </AppLink>
            </Stack>
        </Stack>
    );
};
