import { useCallback } from 'react';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { useIsUnavailableEndDate } from './hooks/use-is-unavailable-end-date.hook';
import { useIsUnavailableStartDate } from './hooks/use-is-unavailable-start-date.hook';
import { MemoizedDatePickerField } from './memoized-date-picker-field';
import type { DateRangeFieldProps } from './types';

/**
 * The DateRangeField component provides dual date picker inputs for selecting start and end dates with validation, accessibility features, and unavailable date handling.
 *
 * [DateRangeField in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=44258-480473&t=QHg4TovE4tsvKYCl-4).
 */
export const DateRangeField = ({
    'data-id': dataId,
    a11yHiddenLabel,
    dateUnavailableText,
    feedback,
    formId,
    getIsDateUnavailableEnd,
    getIsDateUnavailableStart,
    helpText,
    label,
    labelStyleOverrides,
    name,
    onChange,
    optionalText = 'optional',
    required = false,
    shouldHideLabel = false,
    value = { start: null, end: null },
    ...restProps
}: DateRangeFieldProps): React.JSX.Element => {
    const getIsUnavailableStartDate = useIsUnavailableStartDate({
        endValue: value.end,
        getIsDateUnavailableStart,
    });

    const getIsUnavailableEndDate = useIsUnavailableEndDate({
        startValue: value.start,
        getIsDateUnavailableEnd,
    });

    const handleStartChange = useCallback(
        (date: TDateISODate | TDateISODate[]) => {
            if (Array.isArray(date)) {
                return;
            }

            onChange({ start: date, end: value.end });
        },
        [onChange, value.end],
    );

    const handleEndChange = useCallback(
        (date: TDateISODate | TDateISODate[]) => {
            if (Array.isArray(date)) {
                return;
            }

            onChange({ start: value.start, end: date });
        },
        [onChange, value.start],
    );

    return (
        <FormField
            cosmosUseWithCaution_hideLabelFromScreenReaders
            data-id={dataId}
            data-testid="DateRangeField"
            feedback={feedback}
            formId={formId}
            helpText={helpText}
            label={label}
            labelStyleOverrides={labelStyleOverrides}
            name={name}
            optionalText={optionalText}
            /**
             * Does this work here?
             */
            required={required}
            role="group"
            shouldHideLabel={shouldHideLabel}
            renderInput={({ describeIds, feedbackType }) => {
                const sharedFeedbackObject = { type: feedbackType };

                return (
                    // eslint-disable-next-line custom/enforce-data-id -- defined in parent
                    <Stack gap="md" align="start" style={{ gridArea: 'input' }}>
                        <MemoizedDatePickerField
                            data-id={`${dataId}-start`}
                            dateUnavailableText={dateUnavailableText.start}
                            describeIds={describeIds}
                            feedback={sharedFeedbackObject}
                            formId={formId}
                            getIsDateUnavailable={getIsUnavailableStartDate}
                            label={a11yHiddenLabel.start}
                            name={`${name}-start`}
                            required={required}
                            value={value.start ?? undefined}
                            onChange={handleStartChange}
                            {...restProps}
                        />
                        <MemoizedDatePickerField
                            data-id={`${dataId}-end`}
                            dateUnavailableText={dateUnavailableText.end}
                            describeIds={describeIds}
                            feedback={sharedFeedbackObject}
                            formId={formId}
                            getIsDateUnavailable={getIsUnavailableEndDate}
                            label={a11yHiddenLabel.end}
                            name={`${name}-end`}
                            required={required}
                            value={value.end ?? undefined}
                            onChange={handleEndChange}
                            {...restProps}
                        />
                    </Stack>
                );
            }}
        />
    );
};
