import styles from '../spot-illustration-cosmos-lab.module.css';

export const Telescope = (): React.JSX.Element => (
    // eslint-disable-next-line custom/enforce-data-id -- none of the other svgs allow for custom data-id being passed and the generated one isn't useful because it isn't unique
    <svg
        fill="none"
        viewBox="0 0 80 81"
        xmlns="http://www.w3.org/2000/svg"
        // eslint-disable-next-line custom/enforce-data-testid -- this data-testid isn't useful as specified because there is at least one more telescope svg avalable
        data-testid="telescopespotIllustration"
    >
        <clipPath id="a">
            <path d="m0 0.55273h80v80h-80z" />
        </clipPath>
        <g clipPath="url(#a)">
            <path
                d="m24.8065 43.2202-8.913 3.2851c-.788.2904-1.6594.2561-2.4219-.0957-.7624-.3518-1.3541-.9924-1.6445-1.7802l-1.0948-2.9717c-.2904-.788-.2561-1.6593.0957-2.4219.3519-.7625.9923-1.3541 1.7803-1.6445l8.9131-3.2852c-.2904-.7879-.256-1.6603.0957-2.4228.3518-.7626.9923-1.3541 1.7803-1.6446l11.8818-4.3798-.081-.2207c-.2753-.7481-.258-1.5726.0478-2.3086.3058-.7359.8775-1.3294 1.6016-1.6621l12.9834-5.9571c.3965-.1818.8265-.2792 1.2627-.2871.4361-.0079.8696.0738 1.2724.2412s.7662.4169 1.0684.7315.5365.6883.6875 1.0976l6.4092 17.3535c.1508.4095.2161.8457.1904 1.2813s-.1419.8611-.3399 1.25-.4743.7327-.8115 1.0098c-.3371.2769-.7277.4817-1.1474.6005l-13.75 3.8985c-.7674.2176-1.5889.137-2.2998-.2246-.7109-.3617-1.2592-.9783-1.5352-1.7266l-.082-.2217-11.8828 4.3809c-.788.2904-1.6594.2551-2.4219-.0967-.762-.3517-1.354-.991-1.6446-1.7783z"
                className={styles.medium}
            />
            <path
                d="m66.0127 48.3877c.0805 0 .1594.0245.2256.0703.0661.0458.1172.1112.1455.1865l3.0205 8.0528c.0195.0527.0501.1008.0898.1406.04.0399.0887.0704.1416.0898l8.0557 3.0244c.0754.0284.1407.0794.1865.1456.0456.066.0702.1443.0703.2246 0 .0804-.0246.1594-.0703.2256-.0458.0661-.1111.1171-.1865.1455l-8.0557 3.0175c-.0527.0199-.1007.051-.1406.0909-.0398.0398-.071.0878-.0908.1406l-3.0205 8.0586c-.0283.0753-.0794.1407-.1455.1865-.0662.0458-.1451.0703-.2256.0703s-.1594-.0245-.2256-.0703c-.0661-.0458-.1162-.1113-.1445-.1865l-3.0215-8.0586c-.0195-.053-.0499-.1017-.0899-.1416-.0397-.0398-.0878-.0704-.1406-.0899l-8.0566-3.0175c-.0753-.0284-.1398-.0794-.1856-.1455-.0458-.0662-.0703-.1451-.0703-.2256.0001-.0804.0246-.1586.0703-.2246.0458-.0662.1103-.1172.1856-.1456l8.0566-3.0244c.0531-.0189.1018-.049.1416-.0888.0399-.0399.07-.0885.0889-.1416l3.0215-8.0528c.0283-.0752.0785-.1407.1445-.1865.0662-.0458.1451-.0703.2256-.0703z"
                className={styles.mild}
            />
            <path
                d="m51.1055 15.4229c.4359-.0079.8698.074 1.2724.2412.4027.1673.7673.417 1.0694.7314.3019.3145.5356.6887.6865 1.0977l3.3564 9.1103-19.4746 6.6846-.0019-.0049-14.9766 5.1387-1.5166-4.1172c-.2901-.7876-.2557-1.6587.0957-2.4209.3515-.7623.9919-1.3538 1.7793-1.6445l11.8838-4.3799-.082-.2217c-.2742-.7494-.2555-1.5754.0527-2.3115.3082-.7359.8832-1.3287 1.6094-1.6592l12.9834-5.957c.3963-.1818.8267-.2792 1.2627-.2871z"
                className={styles.mild}
            />
            <path
                d="m17.1162 12.4141c.0296.0794.0758.1519.1358.2119.0601.0601.1332.107.2128.1367l6.6495 2.498-6.6563 2.502c-.0796.0297-.1518.0766-.2119.1367-.06.0601-.1071.1324-.1367.2119l-2.4951 6.6494-2.501-6.6591c-.0297-.0797-.0767-.1519-.1367-.212-.0601-.0601-.1323-.107-.212-.1367l-6.65034-2.4922 6.65624-2.5009c.0797-.0297.1528-.0766.2129-.1368.0599-.06.1061-.1324.1357-.2119l2.4952-6.65036z"
                className={styles.mild}
            />
            <g className={styles.strong}>
                <path d="m33.75 48.0986c.0153.0011.0306.0026.0459.0039.0157.0015.0313.0032.0469.0049.0496.0057.0988.0132.1474.0225.021.004.0418.009.0625.0137.0428.0096.0851.0208.127.0332.019.0056.0379.0114.0566.0175.0419.0137.0833.0286.1241.045.0183.0073.0366.0145.0546.0224.0468.0204.0927.0425.1377.0664.0093.005.0182.0106.0274.0157.103.0567.2013.1221.2929.1962.0099.008.0196.0163.0293.0245.0391.0328.0767.0676.1133.1035.0041.004.0087.0076.0127.0117l.0147.0146c.0346.0353.0675.0724.0996.1104.0097.0115.0189.0235.0283.0351.0303.0376.0593.0763.0869.1163.0075.0107.0152.0213.0225.0322.0289.0436.0573.0885.083.1347l15.833 28.5c.5361.9655.188 2.1834-.7774 2.7198-.9653.5362-2.1831.1887-2.7197-.7764l-12.0849-21.7539v20.7822c-.0003 1.1044-.8956 2-2 2-1.1044-.0001-1.9998-.8957-2-2v-20.7822l-12.085 21.7539c-.5366.9649-1.7545 1.3124-2.7197.7764-.9651-.5365-1.3125-1.7544-.7764-2.7198l15.8154-28.4697c.039-.073.0835-.1427.1309-.2099.0249-.0355.0511-.07.0781-.1036.0064-.0079.0121-.0166.0186-.0244.0351-.0423.072-.083.1103-.122.0123-.0125.0256-.024.0381-.0362.0305-.0295.0615-.0585.0938-.0859.0107-.0091.0213-.0185.0322-.0274.0391-.0319.0797-.062.1211-.0908.018-.0126.0362-.0251.0547-.0371.0879-.057.1805-.1068.2763-.1494.0194-.0086.0389-.0174.0586-.0254.05-.0203.1007-.0394.1524-.0557.0066-.002.0138-.0028.0205-.0048.0466-.0142.0939-.0264.1416-.0371.0172-.0039.0344-.0084.0517-.0118.0528-.0104.1063-.0182.1602-.0244.0168-.0019.0338-.0034.0508-.0049.0581-.0051.1168-.0078.1758-.0078z" />
                <path
                    d="m66.1953 46.3945c.3625.0277.7146.1376 1.0283.3213l.1534.0977.1455.1094c.2824.2291.5096.5198.663.8496l.0704.1679.0009.001 2.7637 7.3701 7.374 2.7686c.399.1501.7514.404 1.0186.7334l.1094.1455.0976.1533c.2144.3662.3281.7845.3281 1.2109-.0001.4871-.1485.9628-.4257 1.3633-.2774.4006-.671.7074-1.127.8789l-.0019.001-7.3731 2.7607-2.7637 7.376-.0009.002c-.1716.4561-.4785.8496-.8789 1.1269-.4004.2772-.8762.4256-1.3633.4258-.4873 0-.9638-.1486-1.3643-.4258-.4005-.2773-.7073-.6709-.8789-1.1269v-.002l-2.7656-7.376-7.3721-2.7607-.0019-.001c-.456-.1715-.8496-.4783-1.127-.8789-.2426-.3505-.3867-.7587-.4189-1.1816l-.0069-.1817.0069-.1826c.0322-.4229.1762-.8311.4189-1.1816l.1094-.1455c.2673-.3295.6185-.5833 1.0176-.7334l7.374-2.7686 2.7656-7.3701c.1716-.4561.4783-.8506.8789-1.1279l.1533-.0977c.3662-.2143.7845-.3281 1.211-.3281zm-1.7012 11.0049-.0019-.001c-.1191.3204-.304.6126-.5459.8545-.2417.2417-.5335.4268-.8535.5459l.001.002-4.0508 1.5205 4.0488 1.5166c.2397.0894.4625.2164.6611.376l.1934.1728.1728.1934c.1618.2013.2904.4284.3799.6719l1.5137 4.04 1.5195-4.0518.001-.0019c.1201-.3192.3066-.6093.5479-.8506l.1904-.1709c.1981-.1598.4209-.2879.6601-.3779l.003-.001 4.0478-1.5166-4.0498-1.5205c-.3196-.1197-.6101-.3075-.8515-.5488-.242-.242-.4292-.5331-.5489-.8536l-1.5195-4.0478z"
                    clipRule="evenodd"
                    fillRule="evenodd"
                />
                <path
                    d="m51.0576 13.4238c.7115-.0128 1.418.1215 2.0752.3946.6572.273 1.2512.6791 1.7442 1.1923.4927.5131.8748 1.1227 1.121 1.7901l6.4092 17.3535c.2461.6679.3524 1.3812.3106 2.0918-.042.7106-.2308 1.4057-.5537 2.04-.3231.6344-.7742 1.1965-1.3243 1.6485-.5499.4517-1.1882.7836-1.873.9775l.001.001-13.75 3.8984c-1.2519.3549-2.5922.2247-3.752-.3652-.6192-.315-1.1618-.7499-1.6025-1.2715l-10.2988 3.7959c-1.2857.4737-2.7071.4178-3.9512-.1562-.6144-.2835-1.1607-.6816-1.6133-1.167l-7.4141 2.7343c-1.2856.4739-2.707.4167-3.9511-.1572-1.2442-.5741-2.2097-1.6186-2.68363-2.9043l-1.0957-2.9717c-.47359-1.2855-.41674-2.7071.15723-3.9511.57412-1.2438 1.6189-2.2089 2.9043-2.6827l7.4141-2.7334c.0295-.6634.1871-1.3218.4707-1.9365.5741-1.2439 1.6187-2.2098 2.9043-2.6836l10.2978-3.7968c-.0025-.6811.1287-1.3621.3945-2.002.499-1.2007 1.4328-2.169 2.6143-2.7119l.001-.001 12.9834-5.956c.6469-.2967 1.3489-.4569 2.0605-.4698zm-37.7568 26.044c-.2901.1069-.5266.3248-.6563.6054-.1296.2809-.142.6024-.0351.8926l1.0947 2.9707c.107.2903.3255.5266.6064.6563.2809.1294.6015.142.8916.0351l7.0372-2.5937-1.9024-5.1602zm10.789-7.3535c-.2901.1069-.5265.3257-.6562.6064-.1291.2801-.1421.6001-.0361.8897.0005.0013.0014.0025.0019.0039l3.1553 8.5605.1289.3516c.107.2902.3246.5266.6055.6562s.6022.1422.8925.0352l10.0049-3.6895-4.0927-11.1015zm27.0401-14.6914c-.1607.0029-.3198.0394-.4658.1064l-12.9825 5.9561-.0009-.001c-.2661.1227-.4764.3427-.5889.6133-.1121.2703-.1191.5738-.0186.8486l.0577.1553c.0088.0216.0202.0424.0283.0644l5.4756 14.8565c.0051.0139.0079.029.0127.0429l.0664.1787c.1017.2755.3037.5036.5654.6368.2618.1331.565.1621.8477.082l13.75-3.8975c.1545-.0437.2996-.1197.4238-.2216.124-.102.2259-.2291.2988-.3721.0729-.1432.1155-.3006.125-.461.0094-.1602-.0149-.321-.0703-.4716l-6.4092-17.3526c-.0556-.1505-.1418-.2895-.2529-.4053-.1113-.1158-.2453-.2078-.3936-.2695-.1483-.0616-.3081-.0917-.4687-.0888z"
                    clipRule="evenodd"
                    fillRule="evenodd"
                />
                <path
                    d="m14.6133 3.76074c.8331-.00028 1.5796.51623 1.873 1.2959l2.2774 6.05666 6.0537 2.2754c.7798.293 1.2967 1.039 1.2969 1.872 0 .833-.5163 1.5789-1.2959 1.8721l-6.0606 2.2774-2.2715 6.0537c-.2929.7799-1.0388 1.2967-1.872 1.2968-.8332-.0001-1.5791-.5169-1.8721-1.2968l-2.2783-6.0625-6.05179-2.2676c-.7802-.2926-1.29745-1.0388-1.29785-1.8721-.00025-.8333.51682-1.5798 1.29687-1.873l6.05857-2.2783 2.2725-6.05278.0596-.14258c.3261-.69942 1.0305-1.15382 1.8115-1.1543zm-.6211 9.34666.001.001c-.1299.3487-.3336.6656-.5967.9287-.263.263-.5792.4669-.9277.5967l-.001-.001-1.6621.625 1.6601.6221c.2604.0973.5022.2356.7178.4092l.207.1855.1856.207c.1743.2164.3137.4602.4111.7217l.625 1.668.625-1.6631c.1299-.3467.3329-.662.5947-.9238l.2071-.1856c.2163-.1742.4593-.3138.7207-.4111l1.6679-.627-1.667-.6259c-.3462-.13-.6613-.3332-.9228-.5948-.2629-.263-.4669-.5801-.5967-.9287l-.626-1.664z"
                    clipRule="evenodd"
                    fillRule="evenodd"
                />
            </g>
        </g>
    </svg>
);
