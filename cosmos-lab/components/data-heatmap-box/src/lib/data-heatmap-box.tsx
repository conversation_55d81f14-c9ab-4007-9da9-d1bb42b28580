import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { HEATMAP_BOX_BACKGROUND_COLORS } from './constants/levels.constants';
import { TESTID } from './constants/testid.constants';
import { StyledButton } from './styles/data-heatmap-box.styles';
import type { DataHeatmapBoxProps } from './types/data-heatmap-box.types';

/**
 * The DataHeatmapBox component represents an individual cell within a heatmap visualization with color-coded levels, interactive capabilities, and tooltip support.
 *
 * [DataHeatmapBox in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=30621-1116578&t=QHg4TovE4tsvKYCl-4).
 */
export const DataHeatmapBox = ({
    label = '',
    level,
    'data-id': dataId = TESTID,
    onClick,
    tooltip,
    cosmosUseWithCaution_isDisabled = false,
}: DataHeatmapBoxProps): React.JSX.Element => {
    const textColor = Number(level) > 7 ? 'inverted' : 'neutral';
    const bgColor = HEATMAP_BOX_BACKGROUND_COLORS[level];

    if (tooltip) {
        return (
            <Tooltip isInteractive text={tooltip}>
                <StyledButton
                    disabled={cosmosUseWithCaution_isDisabled}
                    $bgColor={bgColor}
                    data-id={dataId}
                    onClick={onClick}
                >
                    <Text
                        as="div"
                        shouldWrap={false}
                        type="headline"
                        size="400"
                        colorScheme={textColor}
                    >
                        {/* THIS IS A TEMPORARY SOLUTION, this is a known issue from the snapdom package and it should be fixed in the future. */}
                        {/* Julio Carozo approved this temporary solution */}
                        {/* https://github.com/zumerlab/snapdom/issues/42 */}
                        <span
                            style={{
                                visibility: 'hidden',
                                color: 'transparent',
                            }}
                        >
                            --
                        </span>
                        {label}
                        <span
                            style={{
                                visibility: 'hidden',
                                color: 'transparent',
                            }}
                        >
                            --
                        </span>
                    </Text>
                </StyledButton>
            </Tooltip>
        );
    }

    return (
        <StyledButton
            $bgColor={bgColor}
            data-id={dataId}
            data-testid="DataHeatmapBox"
            onClick={onClick}
        >
            <Text
                as="div"
                shouldWrap={false}
                type="headline"
                size="400"
                colorScheme={textColor}
            >
                {label}
            </Text>
        </StyledButton>
    );
};
