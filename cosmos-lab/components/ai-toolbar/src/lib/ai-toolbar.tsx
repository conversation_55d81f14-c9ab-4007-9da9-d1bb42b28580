import { type ReactNode, useRef, useState } from 'react';
import { But<PERSON> } from '@cosmos/components/button';
import { Icon } from '@cosmos/components/icon';
import { Popover } from '@cosmos/components/popover';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { TESTID } from './constants/testid.constants';

interface AIToolbarProps {
    /**
     * Text to display next to the AI icon. Defaults to "AI".
     */
    label?: string;

    /**
     * Callback fired when the thumbs up button is clicked.
     */
    onThumbsUp?: () => void;

    /**
     * Callback fired when the thumbs down button is clicked.
     */
    onThumbsDown?: () => void;

    /**
     * Callback fired when the copy button is clicked.
     */
    onCopy?: () => void;

    /**
     * Content to display in the view details popover. If not provided, the view details button will not be rendered.
     */
    viewDetailsContent?: ReactNode;

    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
}

/**
 * The AIToolbar component provides action buttons and controls for AI-generated content.
 * It includes feedback buttons (thumbs up/down), copy functionality, and a view details button.
 *
 * This component is designed to be used at the bottom of AI cards and content areas.
 *
 * [AIToolbar in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=48213-4733&t=QHg4TovE4tsvKYCl-4).
 */
export const AIToolbar = ({
    label = 'AI',
    onThumbsUp,
    onThumbsDown,
    onCopy,
    viewDetailsContent,
    'data-id': dataId = TESTID,
}: AIToolbarProps): React.JSX.Element => {
    const [isPopoverOpen, setIsPopoverOpen] = useState(false);
    const viewDetailsButtonRef = useRef<HTMLButtonElement>(null);

    const handleViewDetailsClick = () => {
        setIsPopoverOpen(!isPopoverOpen);
    };

    const handlePopoverDismiss = () => {
        setIsPopoverOpen(false);
    };

    return (
        <Stack
            data-id={dataId}
            data-testid="AIToolbar"
            align="center"
            gap="md"
            width="100%"
        >
            {/* AI Label */}
            <Stack align="center" gap="sm">
                <Icon name="AI" size="100" colorScheme="ai" />
                <Text size="100" colorScheme="ai" shouldWrap={false}>
                    {label}
                </Text>
            </Stack>

            {/* Action Buttons */}
            <Stack align="center" gap="sm">
                <Button
                    isIconOnly
                    size="sm"
                    level="tertiary"
                    colorScheme="neutral"
                    startIconName="ThumbsUp"
                    label={t`Thumbs up`}
                    data-id={`${dataId}-thumbs-up`}
                    onClick={onThumbsUp}
                />
                <Button
                    isIconOnly
                    size="sm"
                    level="tertiary"
                    colorScheme="neutral"
                    startIconName="ThumbsDown"
                    label={t`Thumbs down`}
                    data-id={`${dataId}-thumbs-down`}
                    onClick={onThumbsDown}
                />
                {onCopy && (
                    <Button
                        isIconOnly
                        size="sm"
                        level="tertiary"
                        colorScheme="neutral"
                        startIconName="Copy"
                        label={t`Copy`}
                        data-id={`${dataId}-copy`}
                        onClick={onCopy}
                    />
                )}
            </Stack>

            {viewDetailsContent ? (
                <>
                    <Button
                        ref={viewDetailsButtonRef}
                        label={t`View details`}
                        data-id={`${dataId}-view-details`}
                        size="sm"
                        level="tertiary"
                        onClick={handleViewDetailsClick}
                    />
                    <Popover
                        anchor={viewDetailsButtonRef.current}
                        isOpen={isPopoverOpen}
                        content={viewDetailsContent}
                        data-id={`${dataId}-popover`}
                        placement="bottom-end"
                        padding="xl"
                        onDismiss={handlePopoverDismiss}
                    />
                </>
            ) : null}
        </Stack>
    );
};
