import { z } from 'zod';
import type { RiskSettingsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { RISK_CALCULATION_CONSTANTS } from '@helpers/risk-score';
import type { FormSchema } from '@ui/forms';

export function createLevelDefinitionFields(
    riskSettings: RiskSettingsResponseDto,
): FormSchema {
    const fields: FormSchema = {};

    for (
        let level = 1;
        level <= RISK_CALCULATION_CONSTANTS.MAX_RISK_LEVELS;
        level = level + 1
    ) {
        const existingDefinition = riskSettings.levelDefinitions.find(
            (def) => def.level === level && def.type === 'IMPACT',
        );

        fields[`impactDefinition${level}`] = {
            type: 'textarea' as const,
            label: t`Impact level ${level}`,
            initialValue: existingDefinition?.description || '',
            validator: z.string().optional(),
            rows: 1,
        };
    }

    for (
        let level = 1;
        level <= RISK_CALCULATION_CONSTANTS.MAX_RISK_LEVELS;
        level = level + 1
    ) {
        const existingDefinition = riskSettings.levelDefinitions.find(
            (def) => def.level === level && def.type === 'LIKELIHOOD',
        );

        fields[`likelihoodDefinition${level}`] = {
            type: 'textarea' as const,
            label: t`Likelihood level ${level}`,
            initialValue: existingDefinition?.description || '',
            validator: z.string().optional(),
            rows: 1,
        };
    }

    return fields;
}

export function createThresholdFields(
    thresholds: RiskSettingsResponseDto['thresholds'],
): FormSchema {
    const fields: FormSchema = {};

    thresholds.forEach((threshold, index) => {
        const fieldIndex = index + 1;

        fields[`threshold${fieldIndex}Name`] = {
            type: 'text' as const,
            label: t`Name`,
            initialValue: threshold.name || '',
            validator: z.string().min(1, 'Name is required'),
        };
        fields[`threshold${fieldIndex}Description`] = {
            type: 'textarea' as const,
            label: t`Description`,
            initialValue: threshold.description || '',
            validator: z.string().optional(),
            rows: 1,
            isOptional: true,
        };
    });

    return fields;
}
