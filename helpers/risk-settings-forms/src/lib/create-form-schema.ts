import type { ReactNode } from 'react';
import type { RiskSettingsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import type { CustomFieldRenderProps, FormSchema } from '@ui/forms';
import {
    createLevelDefinitionFields,
    createThresholdFields,
} from './form-schema';

export function createRiskSettingsFormSchema(
    riskSettings: RiskSettingsResponseDto | null,
    formFieldComponents: {
        ImpactLikelihoodFormField: (
            fieldProps: CustomFieldRenderProps,
        ) => ReactNode;
        RiskLevelDefinitionsFormField: (
            fieldProps: CustomFieldRenderProps,
        ) => ReactNode;
        ThresholdsFormField: (fieldProps: CustomFieldRenderProps) => ReactNode;
    },
    createNumericOptions: (maxValue: number | undefined) => {
        id: string;
        label: string;
        value?: string;
    }[],
): FormSchema {
    if (!riskSettings) {
        return {};
    }

    const {
        ImpactLikelihoodFormField,
        RiskLevelDefinitionsFormField,
        ThresholdsFormField,
    } = formFieldComponents;

    return {
        impactLikelihoodDisplay: {
            type: 'custom' as const,
            customType: 'object' as const,
            label: 'Impact and Likelihood',
            render: ImpactLikelihoodFormField,
            initialValue: {
                impact: riskSettings.impact,
                likelihood: riskSettings.likelihood,
            },
            fields: {
                impact: {
                    type: 'select' as const,
                    label: t`Impact`,
                    options: createNumericOptions(10),
                    initialValue: {
                        id: String(riskSettings.impact),
                        label: String(riskSettings.impact),
                        value: String(riskSettings.impact),
                    },
                },
                likelihood: {
                    type: 'select' as const,
                    label: t`Likelihood`,
                    options: createNumericOptions(10),
                    initialValue: {
                        id: String(riskSettings.likelihood),
                        label: String(riskSettings.likelihood),
                        value: String(riskSettings.likelihood),
                    },
                },
            },
        },
        riskLevelDefinitions: {
            type: 'custom' as const,
            customType: 'object' as const,
            label: t`Risk Level Definitions`,
            render: RiskLevelDefinitionsFormField,
            initialValue: {},
            fields: createLevelDefinitionFields(riskSettings),
        },
        thresholds: {
            type: 'custom' as const,
            customType: 'object' as const,
            label: t`Thresholds`,
            render: ThresholdsFormField,
            initialValue: {},
            fields: createThresholdFields(riskSettings.thresholds),
        },
    };
}
