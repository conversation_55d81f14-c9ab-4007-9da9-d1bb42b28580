export interface DefaultThresholdData {
    name: string;
    description: string;
    color: string;
}

//TODO: manage threshold integration  https://drata.atlassian.net/browse/ENG-73827
export const DEFAULT_THRESHOLD_CONFIGURATIONS: DefaultThresholdData[] = [
    {
        name: 'Low',
        description: 'Less than or equal to 4.0',
        color: '#4fd838',
    },
    {
        name: 'Medium',
        description: 'Greater than 4.0 but less than or equal to 9.0',
        color: '#FFD910',
    },
    {
        name: 'High',
        description: 'Greater than 9.0 but less than or equal to 16.0',
        color: '#FFA621',
    },
    {
        name: 'Critical',
        description: 'Greater than 16.0',
        color: '#FF5C01',
    },
    {
        name: 'Extreme',
        description: 'Extreme risk level',
        color: '#B90410',
    },
];
export const DEFAULT_RISK_SETTINGS = {
    IMPACT: 5,
    LIKELIHOOD: 5,
    THRESHOLD_COUNT: 4,
} as const;
export function generateDefaultThresholdPositions(
    intervalsSize: number,
    maxLimit: number,
): number[] {
    const amountBetweenIntervals = Math.floor(maxLimit / intervalsSize);
    const remainder = maxLimit % intervalsSize;

    const handlesPositions: number[] = Array(intervalsSize - 1);

    for (let i = 0; i < handlesPositions.length; i = i + 1) {
        if (i === 0) {
            handlesPositions[i] = amountBetweenIntervals + remainder;
            continue;
        }
        handlesPositions[i] = handlesPositions[i - 1] + amountBetweenIntervals;
    }

    return handlesPositions;
}
export function getDefaultThresholdData(index: number): DefaultThresholdData {
    return (
        DEFAULT_THRESHOLD_CONFIGURATIONS[index] || {
            name: `Threshold ${index + 1}`,
            description: `Threshold level ${index + 1}`,
            color: '#B90410',
        }
    );
}
export function calculateThresholdRanges(
    sliderValues: number[],
    maxThresholdValue: number,
): { minThreshold: number; maxThreshold: number }[] {
    const minThresholdValues = [
        1,
        ...sliderValues.map((value) => Math.floor(value) + 1),
    ];

    const maxThresholdValues = [
        ...sliderValues.map((value) => Math.floor(value)),
        maxThresholdValue,
    ];

    const ranges: { minThreshold: number; maxThreshold: number }[] = [];

    for (const [i, minThresholdValue] of minThresholdValues.entries()) {
        ranges.push({
            minThreshold: minThresholdValue,
            maxThreshold: maxThresholdValues[i],
        });
    }

    return ranges;
}
