import { isEmpty, isString } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';

export const copyAISummaryToClipboard = async (
    summary: object | string | null,
): Promise<void> => {
    if (!summary || isEmpty(summary)) {
        snackbarController.addSnackbar({
            id: 'ai-summary-copy-error',
            hasTimeout: false,
            props: {
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
                title: t`No summary available to copy`,
            },
        });

        return;
    }

    const summaryString = isString(summary)
        ? summary
        : JSON.stringify(summary, null, 2);

    try {
        await navigator.clipboard.writeText(summaryString);

        snackbarController.addSnackbar({
            id: 'ai-summary-copy-success',
            props: {
                severity: 'success',
                closeButtonAriaLabel: t`Close`,
                title: t`Summary copied to clipboard`,
            },
        });
    } catch {
        snackbarController.addSnackbar({
            id: 'ai-summary-copy-error',
            props: {
                severity: 'critical',
                title: t`Failed to copy summary. Please try again.`,
                closeButtonAriaLabel: t`Close`,
            },
        });
    }
};
