import {
    AiFeedbackModal,
    type AiFeedbackStatus,
} from '@components/ai-feedback';
import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import type { FormValues } from '@ui/forms';

export const AI_FEEDBACK_MODAL_ID = 'ai-summary-feedback-modal';

export interface OpenAiFeedbackModalProps {
    onSubmit: (values: FormValues) => void;
    isLoading?: boolean;
    initialFeedbackStatus?: AiFeedbackStatus;
}

/**
 * Opens the AI feedback modal for collecting user feedback on AI-generated content.
 * This is a reusable helper that can be used across the application.
 */
export const openAiFeedbackModal = action(
    ({
        onSubmit,
        isLoading = false,
        initialFeedbackStatus = 'USEFUL',
    }: OpenAiFeedbackModalProps): void => {
        modalController.openModal({
            id: AI_FEEDBACK_MODAL_ID,
            content: () => (
                <AiFeedbackModal
                    isLoading={isLoading}
                    initialFeedbackStatus={initialFeedbackStatus}
                    data-id="jQxRYUGF"
                    onSubmit={onSubmit}
                />
            ),
            centered: true,
            disableClickOutsideToClose: false,
            size: 'md',
        });
    },
);

/**
 * Closes the AI feedback modal.
 */
export const closeAiFeedbackModal = action((): void => {
    modalController.closeModal(AI_FEEDBACK_MODAL_ID);
});
