import type { FrameworkWithStringId } from '@controllers/audit-creation-wizard';
import type { FrameworkForNewAuditResponseDto } from '@globals/api-sdk/types';

/**
 * Sorts frameworks alphabetically by label.
 */
export const sortFrameworksAlphabetically = (
    frameworkList: FrameworkForNewAuditResponseDto[],
): FrameworkForNewAuditResponseDto[] => {
    return frameworkList.sort((a, b) => a.label.localeCompare(b.label));
};

/**
 * Filters frameworks by removing ignored ones and sorts them alphabetically.
 */
export const filterAndSortFrameworks = (
    frameworks: FrameworkForNewAuditResponseDto[],
    ignoredFrameworks: string[],
): FrameworkForNewAuditResponseDto[] => {
    const filtered = frameworks.filter((framework) => {
        // Filter out frameworks that are in the ignored list
        return !ignoredFrameworks.includes(framework.tag);
    });

    return sortFrameworksAlphabetically(filtered);
};

/**
 * Converts framework IDs from number to string for form compatibility.
 */
export const convertFrameworksToStringIds = (
    frameworks: FrameworkForNewAuditResponseDto[],
): FrameworkWithStringId[] => {
    return frameworks.map((framework) => ({
        ...framework,
        id: String(framework.id),
    }));
};
