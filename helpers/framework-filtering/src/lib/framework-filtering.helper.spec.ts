import { describe, expect, test } from 'vitest';
import type { FrameworkForNewAuditResponseDto } from '@globals/api-sdk/types';
import {
    convertFrameworksToStringIds,
    filterAndSortFrameworks,
    sortFrameworksAlphabetically,
} from './framework-filtering.helper';

const createMockFramework = (
    id: number,
    label: string,
    tag: string,
): FrameworkForNewAuditResponseDto =>
    ({
        id,
        label,
        tag,
        type: 'SOC_2_TYPE_2',
        hasControls: true,
        pill: label,
        logo: '',
        color: '#000000',
        bgColor: '#ffffff',
        dateType: 'RANGE',
        description: 'Test framework',
        name: label,
        frameworkId: id,
        customFrameworkId: '',
    }) as FrameworkForNewAuditResponseDto;

describe('framework filtering helper', () => {
    describe('sortFrameworksAlphabetically', () => {
        test('sorts frameworks by label alphabetically', () => {
            const frameworks = [
                createMockFramework(3, 'Zebra Framework', 'ZEBRA'),
                createMockFramework(1, 'Alpha Framework', 'ALPHA'),
                createMockFramework(2, 'Beta Framework', 'BETA'),
            ];

            const sorted = sortFrameworksAlphabetically(frameworks);

            expect(sorted[0].label).toBe('Alpha Framework');
            expect(sorted[1].label).toBe('Beta Framework');
            expect(sorted[2].label).toBe('Zebra Framework');
        });

        test('handles case-insensitive sorting', () => {
            const frameworks = [
                createMockFramework(1, 'zebra framework', 'ZEBRA'),
                createMockFramework(2, 'Alpha Framework', 'ALPHA'),
                createMockFramework(3, 'beta framework', 'BETA'),
            ];

            const sorted = sortFrameworksAlphabetically(frameworks);

            expect(sorted[0].label).toBe('Alpha Framework');
            expect(sorted[1].label).toBe('beta framework');
            expect(sorted[2].label).toBe('zebra framework');
        });

        test('handles empty array', () => {
            const sorted = sortFrameworksAlphabetically([]);

            expect(sorted).toStrictEqual([]);
        });

        test('handles single framework', () => {
            const frameworks = [
                createMockFramework(1, 'Single Framework', 'SINGLE'),
            ];
            const sorted = sortFrameworksAlphabetically(frameworks);

            expect(sorted).toStrictEqual(frameworks);
        });

        test('preserves original array immutability', () => {
            const frameworks = [
                createMockFramework(2, 'Beta', 'BETA'),
                createMockFramework(1, 'Alpha', 'ALPHA'),
            ];

            sortFrameworksAlphabetically(frameworks);

            // Original array should be modified (sort is in-place)
            expect(frameworks[0].label).toBe('Alpha');

            expect(frameworks[1].label).toBe('Beta');
        });
    });

    describe('filterAndSortFrameworks', () => {
        test('filters out ignored frameworks and sorts remaining', () => {
            const frameworks = [
                createMockFramework(1, 'SOC 2', 'SOC_2'),
                createMockFramework(2, 'FedRAMP', 'FEDRAMP'),
                createMockFramework(3, 'ISO 27001', 'ISO27001'),
                createMockFramework(4, 'HIPAA', 'HIPAA'),
            ];

            const ignored: string[] = ['FEDRAMP'];
            const result = filterAndSortFrameworks(frameworks, ignored);

            expect(result).toHaveLength(3);

            expect(result.find((f) => f.tag === 'FEDRAMP')).toBeUndefined();

            expect(result[0].label).toBe('HIPAA'); // Alphabetically first

            expect(result[1].label).toBe('ISO 27001');

            expect(result[2].label).toBe('SOC 2');
        });

        test('includes frameworks with invalid enum tags', () => {
            const frameworks = [
                createMockFramework(1, 'Valid Framework', 'SOC_2'),
                createMockFramework(2, 'Invalid Framework', 'INVALID_TAG'),
                createMockFramework(3, 'Another Valid', 'ISO27001'),
            ];

            const ignored: string[] = ['SOC_2'];
            const result = filterAndSortFrameworks(frameworks, ignored);

            expect(result).toHaveLength(2);
            expect(
                result.find((f) => f.label === 'Invalid Framework'),
            ).toBeDefined();
            expect(
                result.find((f) => f.label === 'Another Valid'),
            ).toBeDefined();
            expect(
                result.find((f) => f.label === 'Valid Framework'),
            ).toBeUndefined();
        });

        test('handles empty frameworks array', () => {
            const result = filterAndSortFrameworks([], ['FEDRAMP']);

            expect(result).toStrictEqual([]);
        });

        test('handles empty ignored frameworks array', () => {
            const frameworks = [
                createMockFramework(1, 'Framework A', 'SOC_2'),
                createMockFramework(2, 'Framework B', 'ISO27001'),
            ];

            const result = filterAndSortFrameworks(frameworks, []);

            expect(result).toHaveLength(2);
            expect(result[0].label).toBe('Framework A');
            expect(result[1].label).toBe('Framework B');
        });

        test('handles all frameworks being ignored', () => {
            const frameworks = [
                createMockFramework(1, 'FedRAMP', 'FEDRAMP'),
                createMockFramework(2, 'SOC 2', 'SOC_2'),
            ];

            const ignored: string[] = ['FEDRAMP', 'SOC_2'];
            const result = filterAndSortFrameworks(frameworks, ignored);

            expect(result).toStrictEqual([]);
        });
    });

    describe('convertFrameworksToStringIds', () => {
        test('converts numeric IDs to string IDs', () => {
            const frameworks = [
                createMockFramework(123, 'Framework A', 'TAG_A'),
                createMockFramework(456, 'Framework B', 'TAG_B'),
            ];

            const result = convertFrameworksToStringIds(frameworks);

            expect(result[0].id).toBe('123');
            expect(result[1].id).toBe('456');
            expect(typeof result[0].id).toBe('string');
            expect(typeof result[1].id).toBe('string');
        });

        test('preserves all other framework properties', () => {
            const frameworks = [
                createMockFramework(789, 'Test Framework', 'TEST_TAG'),
            ];

            const result = convertFrameworksToStringIds(frameworks);

            expect(result[0].label).toBe('Test Framework');
            expect(result[0].tag).toBe('TEST_TAG');
            expect(result[0].type).toBe('SOC_2_TYPE_2');
            expect(result[0].hasControls).toBeTruthy();
            expect(result[0].pill).toBe('Test Framework');
        });

        test('handles empty array', () => {
            const result = convertFrameworksToStringIds([]);

            expect(result).toStrictEqual([]);
        });

        test('handles zero ID', () => {
            const frameworks = [
                createMockFramework(0, 'Zero ID Framework', 'ZERO'),
            ];
            const result = convertFrameworksToStringIds(frameworks);

            expect(result[0].id).toBe('0');
            expect(typeof result[0].id).toBe('string');
        });

        test('handles negative ID', () => {
            const frameworks = [
                createMockFramework(-1, 'Negative ID Framework', 'NEG'),
            ];
            const result = convertFrameworksToStringIds(frameworks);

            expect(result[0].id).toBe('-1');
            expect(typeof result[0].id).toBe('string');
        });
    });
});
