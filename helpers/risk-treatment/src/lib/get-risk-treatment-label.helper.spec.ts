import { describe, expect, test } from 'vitest';
import type { RiskResponseDto } from '@globals/api-sdk/types';
import { getRiskTreatmentLabel } from './get-risk-treatment-label.helper';

describe('risk label mapper helper', () => {
    describe('getRiskTreatmentLabel', () => {
        test.each([
            ['UNTREATED', 'Needs treatment'],
            ['ACCEPT', 'Accepted'],
            ['TRANSFER', 'Transferred'],
            ['AVOID', 'Avoided'],
            ['MITIGATE', 'Mitigated'],
            ['UNKNOWN' as RiskResponseDto['treatmentPlan'], '—'],
        ])('should return "%s" for treatment plan "%s"', (input, expected) => {
            expect(
                getRiskTreatmentLabel(
                    input as RiskResponseDto['treatmentPlan'],
                ),
            ).toBe(expected);
        });
    });
});
