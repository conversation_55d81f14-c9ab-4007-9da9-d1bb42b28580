import type { RiskResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export function getRiskTreatmentLabel(
    status: RiskResponseDto['treatmentPlan'],
): string {
    switch (status) {
        case 'UNTREATED': {
            return t`Needs treatment`;
        }
        case 'ACCEPT': {
            return t`Accepted`;
        }
        case 'TRANSFER': {
            return t`Transferred`;
        }
        case 'AVOID': {
            return t`Avoided`;
        }
        case 'MITIGATE': {
            return t`Mitigated`;
        }
        default: {
            return '—';
        }
    }
}

export const getTreatmentIcon = (
    treatmentType: string,
    value: number,
): string => {
    if (treatmentType === 'UNTREATED') {
        return value > 0 ? 'WarningTriangle' : 'CheckCircle';
    }

    return 'CheckCircle';
};

export const getTreatmentColor = (
    treatmentType: string,
    value: number,
): string => {
    if (treatmentType === 'UNTREATED') {
        return value > 0 ? 'warning' : 'success';
    }

    return 'success';
};
