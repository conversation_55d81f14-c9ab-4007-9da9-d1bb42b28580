import type { AllowedBackgroundToken } from '@cosmos/components/box';
import type { ValidThresholdRange } from '../types/risk-score-helper.types';

export const INTENSITIES_BY_THRESHOLDS = {
    2: ['low', 'critical'],
    3: ['low', 'moderate', 'high'],
    4: ['low', 'moderate', 'high', 'critical'],
    5: ['low', 'moderate', 'high', 'high', 'critical'],
} as const satisfies ValidThresholdRange;

export const RISK_CALCULATION_CONSTANTS = {
    MAX_RISK_LEVELS: 10,
    MIN_RISK_LEVELS: 1,
    DEFAULT_IMPACT_LEVEL: 5,
    DEFAULT_LIKELIHOOD_LEVEL: 5,
} as const;

export type RiskCalculationConstants = typeof RISK_CALCULATION_CONSTANTS;

export const RISK_LEVEL_COLOR_MAPPINGS: Record<string, AllowedBackgroundToken> =
    {
        '#4fd838': 'dataDiverge1Strong',
        '#ffd910': 'dataDiverge3Strong',
        '#ffa621': 'dataDiverge6Strong',
        '#ff5c01': 'dataDiverge8Strong',
        '#b90410': 'dataDiverge10Strong',
    } as const;

export const DEFAULT_RISK_LEVEL_COLOR: AllowedBackgroundToken =
    'dataDiverge1Strong';

export type RiskLevelColorMappings = typeof RISK_LEVEL_COLOR_MAPPINGS;
