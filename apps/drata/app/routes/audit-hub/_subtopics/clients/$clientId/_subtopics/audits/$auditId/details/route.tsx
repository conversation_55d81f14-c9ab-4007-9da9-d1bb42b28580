import type { Client<PERSON>oader } from '@app/types';
import { sharedAuditHubController } from '@controllers/audit-hub';
import {
    sharedAuditEvidenceQueryController,
    sharedAuditHubAuditorClientEvidenceStatusController,
    sharedCompanyArchiveStatusQueryController,
    sharedCompanyStatsQueryController,
    sharedCompanySummaryQueryController,
    sharedConnectionInfoQueryController,
    sharedTotalEvidenceQueryController,
} from '@controllers/audit-hub-auditor-client-actions';
import { sharedAuditHubAuditorClientAuditController } from '@controllers/audit-hub-auditor-client-audit';
import { sharedAuditHubAuditorValidatePersonnelController } from '@controllers/audit-hub-auditor-validate-personnel';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { t } from '@globals/i18n/macro';
import { action, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AuditorClientAuditModel } from '@models/auditor-client-audit';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AuditorClientAuditDetailsView } from '@views/auditor-client-audit-details';

export const meta: MetaFunction = () => [
    { title: t`Audit Hub - Audit Details` },
];

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { clientId, auditId } = params;

        if (clientId && auditId) {
            sharedAuditHubController.loadAuditById(auditId);

            sharedCustomerRequestsController.auditId = auditId;
            sharedAuditEvidenceQueryController.loadLatestAuditorFrameworkEvidencePingQuery(
                auditId,
            );
            sharedAuditHubAuditorClientEvidenceStatusController.updateEvidenceExpiredStatus();
            sharedCompanyArchiveStatusQueryController.loadLatestCompanyArchiveStatus(
                'PRE_AUDIT',
            );
            sharedAuditHubAuditorClientEvidenceStatusController.updateEvidenceExpiredStatusByArchiveStatus();
            sharedConnectionInfoQueryController.loadConnectionInfo();
            sharedCompanySummaryQueryController.loadCompanySummary();

            sharedAuditHubAuditorClientAuditController.loadAuditDates(auditId);
            sharedAuditHubAuditorClientAuditController.loadAuditors(auditId);
            sharedAuditorController.auditSummaryByIdQuery.load({
                path: { auditorFrameworkId: auditId },
            });

            sharedCustomerRequestsController.getCustomerRequestList();
            sharedCompanyStatsQueryController.loadStats();
            sharedTotalEvidenceQueryController.handleGetTotalEvidenceByFrameworkId(
                auditId,
            );
            sharedAuditHubAuditorValidatePersonnelController.validatePersonnelStartDateEndDate(
                auditId,
            );

            // Set the current workspace based on the audit data. This affects only on Auditor portal views.
            when(
                () =>
                    Boolean(sharedAuditHubController.auditByIdData?.framework),
                () => {
                    const { auditByIdData } = sharedAuditHubController;

                    if (auditByIdData?.framework) {
                        sharedWorkspacesController.setCurrentWorkspaceById(
                            auditByIdData.framework.productId,
                        );
                    }
                },
            );

            return {
                pageHeader: new AuditorClientAuditModel(),
                utilities: {
                    utilitiesList: [],
                },
            };
        }

        return null;
    },
);

const AuditorClientAuditDetails = (): React.JSX.Element => {
    return (
        <AuditorClientAuditDetailsView
            data-testid="AuditorClientAuditDetails"
            data-id="auditClientDetails"
        />
    );
};

export default AuditorClientAuditDetails;
