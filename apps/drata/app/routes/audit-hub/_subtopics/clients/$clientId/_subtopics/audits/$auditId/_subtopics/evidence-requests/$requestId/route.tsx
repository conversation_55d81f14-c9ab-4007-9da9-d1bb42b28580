import type { ClientLoader } from '@app/types';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { sharedCustomerRequestUtilitiesNotesController } from '@controllers/utilities';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { RequestDetailsPageHeaderModel } from '@models/request-details-page-header';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { requestId, auditId, clientId } = params;

        if (!Number(requestId) || !clientId) {
            throw new Error('Invalid requestId');
        }

        sharedCustomerRequestDetailsController.requestId = Number(requestId);
        sharedCustomerRequestDetailsController.clientId = clientId;

        sharedAuditHubController.loadRequestOwnersForAudit(auditId as string);

        sharedCustomerRequestDetailsController.load();

        sharedCustomerRequestUtilitiesNotesController.loadMessages(
            Number(requestId),
            clientId,
        );

        return {
            pageHeader: new RequestDetailsPageHeaderModel(),
            tabs: [
                {
                    id: 'overview',
                    label: t`Overview`,
                    topicPath: `clients/${clientId}/audits/${auditId}/evidence-requests/${requestId}/overview`,
                },
                {
                    id: 'evidence',
                    label: t`Evidence`,
                    topicPath: `clients/${clientId}/audits/${auditId}/evidence-requests/${requestId}/evidence`,
                },
                {
                    id: 'controls',
                    label: t`Controls`,
                    topicPath: `clients/${clientId}/audits/${auditId}/evidence-requests/${requestId}/controls`,
                },
            ],
            utilities: {
                utilitiesList: ['notes_for_customer_requests'],
            },
            layout: {
                centered: false,
            },
        };
    },
);

const AuditHubRequest = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AuditHubRequest"
            data-id="CY5rwNFa"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default AuditHubRequest;
