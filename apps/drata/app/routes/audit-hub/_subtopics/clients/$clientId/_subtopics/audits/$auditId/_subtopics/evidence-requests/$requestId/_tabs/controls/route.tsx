import { sharedAuditHubControlsController } from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { sharedLinkControlsController } from '@controllers/evidence-library';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { EvidenceRequestDetailsControlsView } from '@views/audit-hub-evidence-request-details-controls';

export const clientLoader: ClientLoaderFunction = action((): null => {
    const { getRequestId: requestId, customerRequestEvidenceQuery } =
        sharedCustomerRequestDetailsController;

    sharedAuditHubControlsController.loadControlsPage();

    customerRequestEvidenceQuery.load({
        path: { customerRequestId: Number(requestId) },
    });

    if (sharedWorkspacesController.currentWorkspaceId) {
        sharedLinkControlsController.loadControlsWithWorkspace(
            sharedWorkspacesController.currentWorkspaceId,
        );
    }

    return null;
});

export const meta: MetaFunction = () => {
    return [{ title: t`Audit hub - Controls` }];
};

const RequestDetailsControls = (): React.JSX.Element => {
    return (
        <EvidenceRequestDetailsControlsView
            data-id="vkjXpHrX"
            data-testid="RequestDetailsControls"
        />
    );
};

export default RequestDetailsControls;
