import { action } from '@globals/mobx';
import { type ClientLoaderFunction, redirect } from '@remix-run/react';

export const clientLoader: ClientLoaderFunction = action(({ request }) => {
    const url = new URL(request.url);
    const redirectUrl = `${url.pathname}/active${url.search}${url.hash}`;

    return redirect(redirectUrl, 302);
});

const PoliciesIndex = (): React.JSX.Element => {
    return <div data-testid="PoliciesIndex" data-id="zb_L36pq"></div>;
};

export default PoliciesIndex;
