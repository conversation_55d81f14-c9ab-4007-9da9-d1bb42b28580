import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { action } from '@globals/mobx';
import { PolicyControlsView } from '@views/policy-controls';

export const clientLoader = action((): null => {
    sharedPolicyBuilderController.loadControlsAssociatedData();

    return null;
});

const PolicyControlsRoute = (): React.JSX.Element => {
    return (
        <PolicyControlsView
            data-id="policy-controls-route"
            data-testid="PolicyControlsRoute"
        />
    );
};

export default PolicyControlsRoute;
