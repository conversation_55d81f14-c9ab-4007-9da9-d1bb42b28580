import { action } from '@globals/mobx';
import {
    PolicyWorkflowsView,
    policyWorkflowsViewController,
} from '@views/policy-workflows';

export const clientLoader = action((): null => {
    policyWorkflowsViewController.initializeViewData();

    return null;
});

const PolicyBuilderWorkflows = (): React.JSX.Element => {
    return (
        <PolicyWorkflowsView
            data-testid="PolicyBuilderWorkflows"
            data-id="policy-builder-workflows"
        />
    );
};

export default PolicyBuilderWorkflows;
