import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { action } from '@globals/mobx';
import { PoliciesBuilderOverviewView } from '@views/policies-builder-overview';

export const clientLoader = action((): null => {
    sharedPolicyBuilderController.loadFrameworksAssociatedData();

    return null;
});

const PolicyBuilderOverview = (): React.JSX.Element => {
    return (
        <PoliciesBuilderOverviewView
            data-testid="PolicyBuilderOverview"
            data-id="policy-builder-overview"
        />
    );
};

export default PolicyBuilderOverview;
