import type { <PERSON>lientLoader } from '@app/types';
import { sharedPoliciesController } from '@controllers/policies';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { action } from '@globals/mobx';
import { PoliciesArchiveView } from '@views/policies';

export const clientLoader = action((): ClientLoader => {
    sharedPoliciesController.loadArchivedPolicies({
        globalFilter: { search: '', filters: {} },
        pagination: {
            pageIndex: 0,
            pageSize: DEFAULT_PAGE_SIZE,
            pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
        },
        sorting: [],
    });

    return null;
});

const PoliciesArchive = (): React.JSX.Element => {
    return (
        <PoliciesArchiveView data-testid="PoliciesArchive" data-id="_0PX-cyl" />
    );
};

export default PoliciesArchive;
