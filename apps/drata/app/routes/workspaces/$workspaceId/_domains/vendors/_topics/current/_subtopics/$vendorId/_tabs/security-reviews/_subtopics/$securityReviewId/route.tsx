import type { ClientLoader } from '@app/types';
import { sharedFeatureAnnouncementDismissalsController } from '@controllers/feature-announcement-dismissals';
import { sharedUtilitiesObservationsController } from '@controllers/utilities';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
    sharedVendorsSecurityReviewObservationsController,
    sharedVendorsTypeformQuestionnairesController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import { VendorsSecurityReviewPageHeaderModel } from '@models/vendors-profile';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import {
    sharedVendorsSecurityReviewAiGetStartedController,
    sharedVendorsSecurityReviewQuestionnairesController,
} from '@views/vendors-profile-security-review';

export const meta: MetaFunction = () => [
    { title: 'Vendors Profile Security Reviews' },
];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { securityReviewId, vendorId } = params;

        if (!securityReviewId || isNaN(Number(securityReviewId))) {
            throw new Error('Invalid securityReviewId');
        }

        sharedVendorsDetailsController.loadVendorDetails(Number(vendorId));
        sharedVendorsSecurityReviewQuestionnairesController.setVendorId(
            Number(vendorId),
        );

        sharedVendorsSecurityReviewDetailsController.loadSecurityReviewDetails({
            path: { id: Number(securityReviewId) },
        });
        sharedVendorsSecurityReviewDocumentsController.loadSecurityReviewDocuments(
            {
                path: { id: Number(securityReviewId) },
            },
        );
        sharedVendorsSecurityReviewObservationsController.loadSecurityReviewObservations(
            {
                path: { id: Number(securityReviewId) },
            },
        );

        sharedVendorsTypeformQuestionnairesController.allVendorsQuestionnairesQuery.load(
            {
                query: {
                    page: 1,
                    limit: 50,
                    prioritizeCategory: false,
                },
            },
        );

        sharedVendorsSecurityReviewAiGetStartedController.loadAiDismissals();

        sharedFeatureAnnouncementDismissalsController.loadFeatureDismissal({
            query: { type: 'SECURITY_REVIEW_OBSERVATION_CARD' },
        });

        const pageHeaderModel = new VendorsSecurityReviewPageHeaderModel();

        pageHeaderModel.setVendorType('current');

        sharedUtilitiesObservationsController.openUtility();

        return {
            pageHeader: pageHeaderModel,
            utilities: {
                utilitiesList: ['observations', 'vrm_agent_assessment'],
            },
            contentNav: {
                tabs: [],
            },
        };
    },
);

const VendorsCurrentSecurityReview = (): React.JSX.Element => {
    // TODO: depending on the review status, return proper view https://drata.atlassian.net/browse/ENG-66372
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsCurrentSecurityReview"
            data-id="Bwg9WWyA"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsCurrentSecurityReview;
