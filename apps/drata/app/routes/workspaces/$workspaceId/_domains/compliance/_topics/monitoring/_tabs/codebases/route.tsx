import type { <PERSON>lientLoader } from '@app/types';
import {
    BannerLocation,
    BannerPersistenceType,
    createBannerMessage,
} from '@controllers/banner-service';
import { sharedConnectionsController } from '@controllers/connections';
import {
    sharedMonitoringCodeController,
    sharedMonitoringCodeFiltersController,
    sharedMonitoringStatsController,
} from '@controllers/monitoring';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { ClientLoaderFunction } from '@remix-run/react';
import { MonitoringCodeView } from '@views/monitoring-code';

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    sharedMonitoringCodeController.monitoringCodeListLoad();
    sharedMonitoringStatsController.loadMonitoringCodeStats();
    sharedMonitoringCodeFiltersController.loadFilters();
    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return {
        pageHeader: {
            pageId: 'monitoring-codebase-page',
            title: t`Codebase Monitoring`,
        },
        banners: () => {
            const { currentWorkspace } = sharedWorkspacesController;
            const { allConfiguredConnections, allConfiguredConnectionsQuery } =
                sharedConnectionsController;
            const { roles } = sharedCurrentUserController;

            // Don't show banners while workspace or connections are still loading
            if (!currentWorkspace || allConfiguredConnectionsQuery.isLoading) {
                return [];
            }

            const hasCodebaseConnection = allConfiguredConnections.some(
                (connection) =>
                    connection.providerTypes.some(
                        (p) => p.value === 'CODEBASE',
                    ) &&
                    connection.workspaces.some(
                        (workspace) => workspace.id === currentWorkspace.id,
                    ),
            );

            const hasJiraConnection = allConfiguredConnections.some(
                (connection) =>
                    connection.clientType === 'JIRA' &&
                    connection.workspaces.some(
                        (workspace) => workspace.id === currentWorkspace.id,
                    ),
            );

            const hasAutomatedTicketingManagementRole = roles.some(
                (role) => role === 'ADMIN' || role === 'ACT_AS_READ_ONLY',
            );

            if (hasCodebaseConnection && hasJiraConnection) {
                const body = hasAutomatedTicketingManagementRole
                    ? t`Since you turned on Compliance as Code, you can now extend ticket rules to include codebase tests. Go to Ticket Automation to edit your rules.`
                    : t`Since Compliance as Code is enabled, you can now extend ticket rules to include codebase tests. Go to Ticket Automation to edit your rules.`;

                return [
                    createBannerMessage({
                        id: 'codebase-jira-automation-banner',
                        title: t`A new customization is available for ticket automation rules`,
                        severity: 'education',
                        location: BannerLocation.PAGE_HEADER,
                        persistenceType: BannerPersistenceType.ROUTE_SCOPED,
                        body,
                    }),
                ];
            }

            return [];
        },
    };
});

const MonitoringCodebasesTab = (): React.JSX.Element => {
    return (
        <MonitoringCodeView
            data-testid="MonitoringCodebasesTab"
            data-id="2bZnz37w"
        />
    );
};

export default MonitoringCodebasesTab;
