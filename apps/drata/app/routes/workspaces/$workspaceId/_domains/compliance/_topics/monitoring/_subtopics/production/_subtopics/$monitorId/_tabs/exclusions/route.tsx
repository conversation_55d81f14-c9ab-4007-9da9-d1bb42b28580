import { sharedMonitoringDetailsExclusionsController } from '@controllers/monitoring-details';
import { action } from '@globals/mobx';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { MonitoringDetailsExclusionsView } from '@views/monitoring-details-exclusions';

export const clientLoader = action(({ params }: ClientLoaderFunctionArgs) => {
    const { monitorId } = params;

    if (monitorId) {
        sharedMonitoringDetailsExclusionsController.setTestId(
            Number(monitorId),
        );
        sharedMonitoringDetailsExclusionsController.initializeFilterData();
    }

    return null;
});

const MonitoringDetailsExclusions = (): React.JSX.Element => {
    return (
        <MonitoringDetailsExclusionsView
            data-testid="MonitoringDetailsExclusions"
            data-id="n0NYgmjo"
        />
    );
};

export default MonitoringDetailsExclusions;
