import type { ClientLoader } from '@app/types';
import { sharedConnectionsController } from '@controllers/connections';
import {
    sharedMonitoringController,
    sharedMonitoringFiltersController,
    sharedMonitoringStatsController,
} from '@controllers/monitoring';
import { action } from '@globals/mobx';
import type { ClientLoaderFunction } from '@remix-run/react';
import { MonitoringView } from '@views/monitoring';

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    sharedMonitoringController.monitoringListLoad();
    sharedMonitoringStatsController.loadMonitoringProductionStats();
    sharedMonitoringFiltersController.loadFilters();
    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return null;
});

const MonitoringMonitorsTab = (): React.JSX.Element => {
    return (
        <MonitoringView
            data-testid="MonitoringMonitorsTab"
            data-id="mgJGmJAl"
        />
    );
};

export default MonitoringMonitorsTab;
