import { isNil } from 'lodash-es';
import type { ClientLoader } from '@app/types';
import {
    activeMonitoringController,
    activeMonitoringDetailsController,
    activeMonitoringNonV2Controller,
    activeTrackCardController,
    sharedFindingsController,
    sharedFindingsFiltersController,
    sharedMonitoringDetailsExclusionsController,
    sharedMonitoringDetailsNotesController,
    sharedMonitoringDetailsTestRunSyncController,
    sharedMonitoringHistoryController,
    sharedMonitoringPersonnelExclusionsController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { DEFAULT_PARAMS } from '@cosmos/components/datatable';
import { action, when } from '@globals/mobx';
import { MonitoringDetailsPageHeaderModel } from '@models/monitoring-details';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

const MONITOR_TABS = [
    { path: 'overview', label: 'Overview' },
    { path: 'findings', label: 'Findings' },
    { path: 'exclusions', label: 'Exclusions' },
    { path: 'history', label: 'History' },
    { path: 'controls', label: 'Controls' },
] as const;

/**
 * Validates if the provided monitor ID is valid.
 */
const validateMonitorId = (monitorId: string | undefined): number => {
    if (isNil(monitorId) || monitorId.trim() === '') {
        throw new Error('Monitor ID is required');
    }

    const testId = Number(monitorId);

    if (isNaN(testId) || testId <= 0) {
        throw new Error('Invalid monitor ID format');
    }

    return testId;
};

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { monitorId } = params;

        const testId = validateMonitorId(monitorId);

        // Load monitor data from multiple endpoints in parallel
        activeMonitoringController.loadMonitor(testId);
        activeMonitoringDetailsController.loadMonitorDetails(testId);
        activeMonitoringNonV2Controller.loadMonitor(testId);

        activeTrackCardController.loadTrackData(testId);

        // Load test details for page header (required for all tabs)
        sharedMonitoringTestDetailsController.loadTest(testId);

        // Load findings data
        sharedFindingsController.load({
            testId,
            ...DEFAULT_PARAMS,
        });
        sharedFindingsFiltersController.load({ testId });

        // Load history data
        sharedMonitoringHistoryController.setMonitorId(String(testId));
        sharedMonitoringHistoryController.loadMonitoringHistory({
            ...DEFAULT_PARAMS,
        });

        sharedMonitoringDetailsNotesController.loadNotes(testId);

        sharedMonitoringDetailsTestRunSyncController.init();

        // Wait for the non-v2 controller to load data and then conditionally load exclusions
        when(
            () => Boolean(activeMonitoringNonV2Controller.monitorDetailsData),
            () => {
                const { canManageExclusions, source } =
                    activeMonitoringNonV2Controller;

                const shouldUseMonitorExclusions =
                    canManageExclusions && source !== 'ACORN';

                if (shouldUseMonitorExclusions) {
                    sharedMonitoringDetailsExclusionsController.setTestId(
                        testId,
                    );
                    sharedMonitoringDetailsExclusionsController.load(
                        DEFAULT_PARAMS,
                    );
                } else {
                    sharedMonitoringPersonnelExclusionsController.setTestId(
                        testId,
                    );
                    sharedMonitoringPersonnelExclusionsController.loadPersonnelExclusions(
                        DEFAULT_PARAMS,
                    );
                }
            },
        );

        return {
            pageHeader: new MonitoringDetailsPageHeaderModel(),
            tabs: MONITOR_TABS.map((tab) => ({
                topicPath: `compliance/monitoring/production/${testId}/${tab.path}`,
                label: tab.label,
            })),
            utilities: {
                utilitiesList: ['notes_for_monitoring_tests'],
            },
        };
    },
);

const MonitoringMonitorDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="MonitoringMonitorDetails"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default MonitoringMonitorDetails;
