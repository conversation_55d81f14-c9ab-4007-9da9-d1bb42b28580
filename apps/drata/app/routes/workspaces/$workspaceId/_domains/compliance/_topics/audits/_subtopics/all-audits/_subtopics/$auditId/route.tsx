import type { ClientLoader } from '@app/types';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { action } from '@globals/mobx';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { auditId } = params;

        if (!auditId) {
            throw new Error('Invalid auditId');
        }

        sharedCustomerRequestDetailsController.auditId = auditId;

        return {};
    },
);

const AuditLayout = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AuditLayout"
            data-id="audit-layout"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default AuditLayout;
