import { sharedAuditHubEvidenceController } from '@controllers/audit-hub';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { EvidenceRequestDetailsEvidenceView } from '@views/audit-hub-evidence-request-details-evidence';

export const meta: MetaFunction = () => {
    return [{ title: t`Audit Request - Evidence` }];
};

export const clientLoader: ClientLoaderFunction = action((): null => {
    sharedAuditHubEvidenceController.loadEvidencesPage();

    return null;
});

const AuditRequestsEvidence = (): React.JSX.Element => {
    return (
        <EvidenceRequestDetailsEvidenceView
            data-id="vkjXpHrX"
            data-testid="AuditRequestsEvidence"
            isFromAuditHub={false}
        />
    );
};

export default AuditRequestsEvidence;
