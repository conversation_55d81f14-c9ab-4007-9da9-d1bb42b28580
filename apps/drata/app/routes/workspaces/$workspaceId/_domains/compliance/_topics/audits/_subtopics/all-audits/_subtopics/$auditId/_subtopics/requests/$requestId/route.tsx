import type { <PERSON>lientLoader } from '@app/types';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { auditId, requestId } = params;

        if (!Number(requestId)) {
            throw new Error('Invalid requestId');
        }

        sharedCustomerRequestDetailsController.requestId = Number(requestId);
        sharedAuditHubController.loadRequestOwnersForAudit(auditId as string);
        sharedCustomerRequestDetailsController.load();

        return {
            pageHeader: {
                title: 'Request details', // TODO: https://drata.atlassian.net/browse/ENG-73385
            },
            tabs: [
                {
                    id: 'overview',
                    label: t`Overview`,
                    topicPath: `compliance/audits/all-audits/${auditId}/requests/${requestId}/overview`,
                },
                {
                    id: 'evidence',
                    label: t`Evidence`,
                    topicPath: `compliance/audits/all-audits/${auditId}/requests/${requestId}/evidence`,
                },
                {
                    id: 'controls',
                    label: t`Controls`,
                    topicPath: `compliance/audits/all-audits/${auditId}/requests/${requestId}/controls`,
                },
            ],
        };
    },
);

const AuditRequests = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AuditRequests"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default AuditRequests;
