import type { ClientLoader } from '@app/types';
import { ComplianceAuditsContentHeaderModel } from '@components/audits';
import { sharedAuditsControllers } from '@controllers/audits';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: 'Audits' }];

export const clientLoader = action((): ClientLoader => {
    // Load audits with default active filter
    sharedAuditsControllers.loadInitialPage();

    return {
        pageHeader: new ComplianceAuditsContentHeaderModel(),
        tabs: [
            {
                topicPath: 'compliance/audits/all-audits',
                label: 'All audits',
            },
            {
                topicPath: 'compliance/audits/auditors',
                label: 'Auditors',
            },
        ],
    };
});

const Audits = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Audits" data-id="jPwZmxJb">
            <Outlet />
        </RouteLandmark>
    );
};

export default Audits;
