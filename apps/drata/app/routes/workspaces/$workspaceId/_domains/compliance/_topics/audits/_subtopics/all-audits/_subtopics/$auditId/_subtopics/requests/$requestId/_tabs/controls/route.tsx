import { isNil } from 'lodash-es';
import {
    sharedAuditHubController,
    sharedAuditHubControlsController,
} from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { sharedLinkControlsController } from '@controllers/evidence-library';
import { t } from '@globals/i18n/macro';
import { action, when } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { EvidenceRequestDetailsControlsView } from '@views/audit-hub-evidence-request-details-controls';

export const clientLoader: ClientLoaderFunction = action((): null => {
    const { getRequestId: requestId, customerRequestEvidenceQuery } =
        sharedCustomerRequestDetailsController;

    sharedAuditHubControlsController.loadControlsPage();

    customerRequestEvidenceQuery.load({
        path: { customerRequestId: Number(requestId) },
    });

    when(
        () => !isNil(sharedAuditHubController.auditByIdData?.framework),
        () => {
            const { auditByIdData } = sharedAuditHubController;

            if (auditByIdData?.framework) {
                sharedLinkControlsController.loadControlsWithWorkspace(
                    auditByIdData.framework.productId,
                );
            }
        },
    );

    return null;
});

export const meta: MetaFunction = () => {
    return [{ title: t`Audit hub - Controls` }];
};

const RequestDetailsControls = (): React.JSX.Element => {
    return (
        <EvidenceRequestDetailsControlsView
            data-id="vkjXpHrX"
            data-testid="RequestDetailsControls"
        />
    );
};

export default RequestDetailsControls;
