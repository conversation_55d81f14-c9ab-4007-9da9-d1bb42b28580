import type { <PERSON>lientLoader } from '@app/types';
import { sharedConnectionsController } from '@controllers/connections';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { MonitoringBuilderView } from '@views/monitoring-builder';

export const meta: MetaFunction = () => [{ title: 'Monitoring builder' }];

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return {
        pageHeader: {
            title: t`Create test`,
            pageId: 'monitoring-builder',
        },
        tabs: [],
        utilities: {
            utilitiesList: ['resource_guide'],
        },
    };
});

const MonitoringBuilder = (): React.JSX.Element => {
    return (
        <MonitoringBuilderView
            data-testid="MonitoringBuilder"
            data-id="4oYwg9Du"
        />
    );
};

export default MonitoringBuilder;
