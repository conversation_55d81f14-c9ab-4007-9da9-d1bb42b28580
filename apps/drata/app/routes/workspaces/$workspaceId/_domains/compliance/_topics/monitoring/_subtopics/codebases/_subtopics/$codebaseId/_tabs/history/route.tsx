import { sharedMonitoringHistoryController } from '@controllers/monitoring-details';
import { DEFAULT_PARAMS } from '@cosmos/components/datatable';
import { action } from '@globals/mobx';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { MonitoringDetailsHistoryView } from '@views/monitoring-details-history';

export const clientLoader = action(({ params }: ClientLoaderFunctionArgs) => {
    const { codebaseId } = params;

    if (codebaseId) {
        sharedMonitoringHistoryController.setMonitorId(codebaseId);
        sharedMonitoringHistoryController.loadMonitoringHistory(DEFAULT_PARAMS);
    }

    return null;
});

const MonitoringCodeDetailsHistory = (): React.JSX.Element => {
    return (
        <MonitoringDetailsHistoryView
            data-testid="MonitoringCodeDetailsHistory"
            data-id="kR7mN9pQ"
        />
    );
};

export default MonitoringCodeDetailsHistory;
