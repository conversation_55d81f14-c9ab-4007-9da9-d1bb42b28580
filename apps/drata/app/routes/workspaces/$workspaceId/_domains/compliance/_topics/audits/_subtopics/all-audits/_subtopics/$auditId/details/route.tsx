import type { ClientLoader } from '@app/types';
import {
    AUDIT_HUB_DEFAULT_PARAMS,
    sharedAuditHubController,
} from '@controllers/audit-hub';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { action, observer } from '@globals/mobx';
import { AuditDetailsPageHeaderModel } from '@models/audits-details';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AuditsDetailsPageView } from '@views/audits-details-page';

export const meta: MetaFunction = () => [{ title: 'Audit details' }];

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { auditId } = params;

        if (!auditId) {
            throw new Error('Audit ID is required');
        }

        sharedAuditHubController.loadAuditById(auditId);
        sharedAuditHubController.loadAuditorFrameworkPersonnel(auditId);
        sharedAuditHubController.loadRequestOwnersForAudit(auditId);

        sharedAuditorController.auditSummaryByIdQuery.load({
            path: { auditorFrameworkId: auditId },
        });

        sharedCustomerRequestsController.auditId = auditId;
        sharedCustomerRequestsController.customerRequestListQuery.load({
            query: {
                auditId,
                page: AUDIT_HUB_DEFAULT_PARAMS.pagination.page,
                limit: AUDIT_HUB_DEFAULT_PARAMS.pagination.pageSize,
            },
        });

        return {
            pageHeader: new AuditDetailsPageHeaderModel(),
            contentNav: {
                tabs: [],
            },
        };
    },
);

const AuditDetailsPage = observer((): React.JSX.Element => {
    return (
        <AuditsDetailsPageView
            data-testid="AuditDetailsPage"
            data-id="nkWc3xQp"
        />
    );
});

export default AuditDetailsPage;
