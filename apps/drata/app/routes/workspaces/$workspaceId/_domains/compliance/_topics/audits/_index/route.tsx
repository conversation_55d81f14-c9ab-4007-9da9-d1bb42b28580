import { type ClientLoaderFunction, redirect } from '@remix-run/react';

export const clientLoader: ClientLoaderFunction = ({ request }) => {
    const url = new URL(request.url);
    const redirectUrl = `${url.pathname}/all-audits${url.search}${url.hash}`;

    return redirect(redirectUrl, 302);
};

const AuditsIndex = (): React.JSX.Element => {
    return <div data-testid="AuditsIndex" data-id="zb_L36pq"></div>;
};

export default AuditsIndex;
