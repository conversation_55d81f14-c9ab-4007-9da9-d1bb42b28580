import type { ClientLoader } from '@app/types';
import {
    sharedEventsDetailsController,
    sharedEventsNotesController,
} from '@controllers/events-details';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { EventDetailsPageHeaderModel } from '@models/event-details-page-header';
import type { ClientLoaderFunction, MetaFunction } from '@remix-run/react';
import { EventDetailsView } from '@views/event-details';

export const meta: MetaFunction = () => {
    return [{ title: t`Audit event` }];
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { eventId } = params;

        if (!eventId) {
            throw new Error('Event ID is required');
        }
        sharedEventsDetailsController.loadEventDetails(eventId);
        sharedEventsNotesController.loadNotesInfinite(eventId, 10);

        return {
            pageHeader: new EventDetailsPageHeaderModel(),
            utilities: {
                utilitiesList: ['notes_for_events'],
            },
        };
    },
);

const EventDetails = (): React.JSX.Element => {
    return <EventDetailsView data-testid="EventDetails" data-id="52lYYce4" />;
};

export default EventDetails;
