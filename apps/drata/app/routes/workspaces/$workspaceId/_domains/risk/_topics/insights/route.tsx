import type { <PERSON>lientLoader } from '@app/types';
import type { PageHeaderOverrides } from '@controllers/page-header';
import {
    sharedRiskInsightsController,
    sharedRiskInsightsDownloadController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable, observer, toJS } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { RiskInsightsView } from '@views/risk-insights';

export const meta: MetaFunction = () => {
    return [{ title: t`Risk Insights` }];
};

const RiskInsightsActionStack = observer((): React.JSX.Element => {
    const { downloadAll, isPreparingDownloadAll } =
        sharedRiskInsightsDownloadController;

    const { isEmptyRiskInsights } = sharedRiskInsightsController;

    return (
        <ActionStack
            data-id="vendors-questionnaires-add-page-action-stack"
            data-testid="RiskInsightsActionStack"
            stacks={[
                {
                    actions: [
                        {
                            actionType: 'button',
                            id: 'risk-insights-download-button',
                            typeProps: {
                                label: t`Download insights`,
                                isLoading: toJS(isPreparingDownloadAll),
                                startIconName: 'Download',
                                level: 'secondary',
                                onClick: downloadAll,
                                cosmosUseWithCaution_isDisabled:
                                    isEmptyRiskInsights,
                            },
                        },
                    ],
                    id: `risk-insights-actions-stack-0`,
                },
            ]}
        />
    );
});

export class RiskInsightsPageHeaderModel implements PageHeaderOverrides {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'risk-insights-page-header';
    isCentered = true;

    title = t`Risk Insights`;
}

const headerModel = new RiskInsightsPageHeaderModel();

export const clientLoader = action((): ClientLoader => {
    sharedRiskInsightsController.initialize();
    sharedRiskSettingsController.load();

    return {
        pageHeader: {
            title: headerModel.title,
            actionStack: <RiskInsightsActionStack />,
        },
    };
});

const RiskInsights = (): React.JSX.Element => {
    return <RiskInsightsView data-testid="RiskInsights" data-id="D61xoR_R" />;
};

export default RiskInsights;
