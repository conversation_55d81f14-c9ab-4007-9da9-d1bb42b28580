import { isString } from 'lodash-es';
import type { ClientLoader } from '@app/types';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Risk' }];
};

export const clientLoader = action((): ClientLoader => {
    const topics: Record<
        string,
        { id: string; topicPath: string; label: string }
    > = {
        'risk.assets': {
            id: 'risk.assets',
            topicPath: 'risk/assets',
            label: t`Assets`,
        },
        'risk.vulnerabilities': {
            id: 'risk.vulnerabilities',
            topicPath: 'risk/vulnerabilities',
            label: t`Vulnerabilities`,
        },
    };

    if (sharedFeatureAccessModel.hasRiskReadPermission) {
        topics['risk.management'] = {
            id: 'risk.management',
            topicPath: 'risk/management',
            label: t`Management`,
        };
    }

    if (sharedEntitlementFlagController.isRiskManagementEnabled) {
        topics['risk.settings'] = {
            id: 'risk.settings',
            topicPath: 'risk/settings/scoring',
            label: t`Settings`,
        };

        topics['risk.insights'] = {
            id: 'risk.library',
            topicPath: 'risk/insights',
            label: t`Insights`,
        };
    }

    const topicsOrder = [
        sharedEntitlementFlagController.isRiskManagementEnabled
            ? 'risk.insights'
            : null,
        'risk.assets',
        sharedFeatureAccessModel.hasRiskReadPermission
            ? 'risk.management'
            : null,
        'risk.vulnerabilities',
        sharedEntitlementFlagController.isRiskManagementEnabled
            ? 'risk.settings'
            : null,
    ].filter(isString);

    return {
        pageHeader: {
            title: t`Risk`,
        },
        topicsNav: {
            id: 'nav.risk',
            title: t`Risk`,
            domainsOrder: ['risk'],
            domains: {
                risk: {
                    label: t`Risk`,
                    hideLabel: true,
                    topicsOrder,
                    topics,
                },
            },
        },
    };
});

const Risk = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Risk" data-id="ISzzDb1x">
            <Outlet />
        </RouteLandmark>
    );
};

export default Risk;
