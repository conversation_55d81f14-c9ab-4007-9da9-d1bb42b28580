import { sharedRiskLibraryController } from '@controllers/risk';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { MultipleRiskRegisterHeaderModel } from '@models/multiple-risk-register-header';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, redirect } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import { MultipleRiskRegisterView } from '@views/multiple-risk-register';

export const meta: MetaFunction = () => {
    return [{ title: t`Risk Registers` }];
};

export const clientLoader: ClientLoaderFunction = action(({ request }) => {
    if (
        !sharedRiskLibraryController.isMultipleRegisters ||
        !sharedFeatureAccessModel.isMultipleRiskRegistersEnabled
    ) {
        const url = new URL(request.url);
        // TODO: Replace registerId with actual single register ID
        const redirectUrl = `${url.pathname}/1${url.search}${url.hash}`;

        return redirect(redirectUrl, 302);
    }

    return {
        pageHeader: new MultipleRiskRegisterHeaderModel(),
    };
});

const RiskRegistersIndex = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskRegistersIndex"
            data-id="risk-registers-index"
        >
            <MultipleRiskRegisterView />
        </RouteLandmark>
    );
};

export default RiskRegistersIndex;
