import type { ClientLoader } from '@app/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedOnboardingFlowsHeaderModel } from '@models/risk-management';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import { RiskRegisterOnboardingLearnView } from '@views/risk-register-onboarding-learn';

export const meta: MetaFunction = () => {
    return [{ title: t`Risk register onboarding` }];
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        return sharedOnboardingFlowsHeaderModel.getClientLoader(params);
    },
);

const RiskRegisterOnboardingLearn = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskRegisterOnboardingLearn"
            data-id="risk-register-onboarding-learn"
        >
            <RiskRegisterOnboardingLearnView />
        </RouteLandmark>
    );
};

export default RiskRegisterOnboardingLearn;
