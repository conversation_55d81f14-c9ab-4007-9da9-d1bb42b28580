# ENG-73651 – Conditional Route Banners (Plan & Status)

## Summary
- Removed all Dashboard route demo banners (no `banners` returned from Dashboard route)
- Kept tabs gated by `isWorkspacesEnabled`
- Banner Service controller updated to support route-based TIMER_BASED auto-dismiss and manual dismiss animation for route banners

## Files touched
- apps/drata/app/routes/workspaces/$workspaceId/_domains/dashboard/route.tsx
- controllers/banner-service/src/lib/banner-service.controller.ts

## Current behavior
- Dashboard route: returns only `pageHeader` (+ optional `tabs`) — no `banners`
- Route-based TIMER_BASED banners (if any) now auto-dismiss; ROUTE_SCOPED clears on navigation; SESSION_SCOPED clears on workspace change; PERSISTENT stays

## Verify quickly
- <PERSON>t changed files
  - pnpm run lint controllers/banner-service/src/lib/banner-service.controller.ts
  - pnpm run lint 'apps/drata/app/routes/workspaces/$workspaceId/_domains/dashboard/route.tsx'
- Navigate to Dashboard: expect no route-provided banners to render

## Open question
- Do we keep the route-based timer support in the controller, or revert it before PR? Current change is minimal and self-contained.

## Next steps
- If keeping timers, add a brief section in Banner Service README under "Route-provided banners" about auto-dismiss and manual dismiss support
- Open PR with summary: “Dashboard: remove route demo banners; add route-banner timer support”

