import { css, keyframes, type RuleSet } from 'styled-components';
import {
    aiGradientEnd,
    aiGradientStart,
    borderRadiusMd,
    borderWidthMd,
} from '@cosmos/constants/tokens';

/**
 * CSS keyframes for spinning gradient border animation.
 * Animates the gradient angle using CSS custom properties.
 * Matches StandardSpinner rotation speed (1 second per rotation).
 */
export const spinGradientBorderKeyframes = keyframes`
    from {
        --gradient-angle: 315deg;
    }
    to {
        --gradient-angle: 675deg;
    }
`;

/**
 * Shared pseudo-element styles for gradient borders.
 * This can be used by any component that needs gradient border pseudo-element styling.
 */
export const gradientBorderPseudoElement = (
    gradientStart: string,
    gradientEnd: string,
    borderRadius: string,
    isAnimated = false,
): RuleSet => css`
  &::before {
    content: "";
    position: absolute;
    top: calc(-1 * ${borderWidthMd});
    right: calc(-1 * ${borderWidthMd});
    bottom: calc(-1 * ${borderWidthMd});
    left: calc(-1 * ${borderWidthMd});
    border-radius: ${borderRadius};
    background: conic-gradient(
      from var(--gradient-angle),
      ${gradientStart},
      ${gradientEnd},
      ${gradientStart}
    );
    z-index: -2;
    pointer-events: none;
    --gradient-angle: 315deg;

    ${
        isAnimated &&
        css`
      animation: ${spinGradientBorderKeyframes} 1s linear infinite;
    `
    }
  }
`;

/**
 * Core gradient border CSS properties that can be reused across components.
 * This provides the base gradient border styling without padding compensation.
 *
 * TODO: Watch for Chrome and Firefox to support background-clip: border-area; because the day will come when we can replace all of this hackery. Https://webkit.org/blog/16214/background-clip-border-area/ .
 */
export const gradientBorderCore = (
    gradientStart: string,
    gradientEnd: string,
    borderRadius: string,
    backgroundColor: string,
    isAnimated = false,
): RuleSet => css`
  position: relative;
  z-index: 0;
  border-radius: ${borderRadius};
  background: ${backgroundColor};
  border: ${borderWidthMd} solid transparent;
  background-clip: padding-box;

  /* CSS custom property for background color that can be updated on hover */
  --gradient-border-bg: ${backgroundColor};

  @property --gradient-angle {
    syntax: "<angle>";
    initial-value: 315deg;
    inherits: false;
  }

  ${gradientBorderPseudoElement(
      gradientStart,
      gradientEnd,
      borderRadius,
      isAnimated,
  )}

  &::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-radius: calc(${borderRadius} - ${borderWidthMd});
    background: var(--gradient-border-bg);
    z-index: -1;
    pointer-events: none;
  }
`;

/**
 * Creates a gradient border effect using a conic gradient and pseudo-element approach.
 * This approach doesn't expand beyond component boundaries and supports both static and animated states.
 *
 * @param gradientStart - The starting color of the gradient.
 * @param gradientEnd - The ending color of the gradient.
 * @param borderRadius - The border radius to match the element (default: '4px').
 * @param contentPadding - The original content padding to compensate for border space.
 * @param backgroundColor - The background color for the content area.
 * @param isAnimated - Whether the gradient should animate (spin). Defaults to false.
 * @param includePaddingCompensation - Whether to include padding compensation. Defaults to true.
 * @returns CSS for gradient border effect.
 */
export const gradientBorderMixin = (
    gradientStart: string,
    gradientEnd: string,
    borderRadius = borderRadiusMd,
    contentPadding?: string,
    backgroundColor?: string,
    isAnimated = false,
    includePaddingCompensation = true,
): RuleSet => css`
  ${
      includePaddingCompensation &&
      css`
    /* Adjust padding: reduce top/bottom for border, add triple border width to left/right */
    padding: ${
        contentPadding
            ? `calc(${contentPadding} - ${borderWidthMd}) calc(${contentPadding} + 3 * ${borderWidthMd})`
            : `0 calc(4 * ${borderWidthMd})`
    };
  `
  }

  /* Apply core gradient border styling */
    ${gradientBorderCore(
        gradientStart,
        gradientEnd,
        borderRadius,
        backgroundColor || 'var(--component-bg-color)',
        isAnimated,
    )}
`;

/**
 * Simple AI gradient border mixin for components.
 * Applies a 2px gradient border using AI design tokens with 315deg angle.
 * This is a simplified version that doesn't require hooks or complex configuration.
 */
export const aiGradientBorder = css`
    position: relative;
    z-index: 0;
    border: ${borderWidthMd} solid transparent;
    background-clip: padding-box;

    /* CSS custom property for background color that can be updated on hover */
    --gradient-border-bg: var(--component-bg-color, transparent);

    @property --gradient-angle {
        syntax: "<angle>";
        initial-value: 315deg;
        inherits: false;
    }

    &::before {
        content: "";
        position: absolute;
        top: calc(-1 * ${borderWidthMd});
        right: calc(-1 * ${borderWidthMd});
        bottom: calc(-1 * ${borderWidthMd});
        left: calc(-1 * ${borderWidthMd});
        border-radius: inherit;
        background: conic-gradient(
            from var(--gradient-angle),
            ${aiGradientStart},
            ${aiGradientEnd},
            ${aiGradientStart}
        );
        z-index: -2;
        pointer-events: none;
        /* Default to no animation unless explicitly enabled via CSS var */
        animation: ${spinGradientBorderKeyframes} var(--ai-gradient-animation-duration, 0s) linear infinite;
    }

    &::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        border-radius: calc(var(--border-radius, ${borderRadiusMd}) - ${borderWidthMd});
        background: var(--gradient-border-bg);
        z-index: -1;
        pointer-events: none;
    }
`;
