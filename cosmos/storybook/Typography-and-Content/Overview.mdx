import { Meta } from '@storybook/addon-docs/blocks';
import LinkTo from '@storybook/addon-links/react';

<Meta title="Typography & Content/Overview" />

# Typography & Content Components

Typography and content components provide consistent, accessible ways to display text and content across Drata applications.

## Purpose

These components handle:
- **Text styling** and typography hierarchy
- **Content formatting** and presentation
- **Reading experience** optimization
- **Accessibility** for text content

## Available Components

### Text
Flexible text component for all typography needs
- **Types**: headline, subheadline, title, body, code
- **Element rendering** via `as` (h1–h6, p, div, span)
- **Size scale** 100–600 and weight options
- **Color schemes** using design tokens

### Caption (using Text)
Use Text for caption-style content
- **Small size**: `size="100"`
- **Muted color**: `colorScheme="faded"`
- **Semantics**: render with `as="p"` or `as="span"` as appropriate

### List (Lab)
Structured content display for ordered and unordered lists
- **Bullet and numbered** list variants via `type="unordered" | "ordered" | "none"`
- **API**: `items` array of ReactNode for list items
- **Size and color** inherit Text tokens (`size`, `colorScheme`)
- Note: Currently in Cosmos Lab

### Highlight (Lab)
Text emphasis and search result highlighting
- **Visual emphasis** for important text
- **Search highlighting** for query matches
- **Color options**: `yellow`, `green`, `red`
- Note: Currently in Cosmos Lab

### Wordmark (Lab)
Brand and logo wordmark display
- **Types**: `customer` (Drata words), `audithub`
- **Height** uses dimension tokens
- **Color scheme** uses Icon color schemes
- Note: Currently in Cosmos Lab

### ShowMore (Lab)
Progressive disclosure for long content
- **Expandable content** with show/hide functionality
- **Props**: `content` ReactNode, `isOpen`
- Note: Currently in Cosmos Lab

### Truncation (Lab)
Text overflow handling for constrained spaces
- **Modes**: `start`, `middle`, `end`
- **Options**: `maxLength`, `lineClamp` (CSS multi-line), `maxWidthPx`
- **Callback**: `onTruncate`
- Note: Currently in Cosmos Lab

## Design Principles

### Hierarchy
- **Clear visual hierarchy** through size and weight
- **Consistent spacing** between text elements
- **Logical content flow** for reading comprehension
- **Semantic HTML** structure for accessibility

### Readability
- **Optimal line length** for comfortable reading
- **Appropriate line height** for text density
- **Sufficient contrast** for accessibility
- **Responsive sizing** for different devices

### Consistency
- **Unified typography** scale across components
- **Consistent color** usage for text types
- **Standardized spacing** between text elements
- **Predictable behavior** across contexts

## Usage Guidelines

### Typography Hierarchy

```tsx
// Page structure with proper hierarchy
<Stack direction="column" gap="md">
  <Text type="headline" size="600" as="h1">Page Title</Text>

  <Text type="body" size="300" as="p">
    Introduction paragraph with larger text for emphasis.
  </Text>

  <Text type="title" size="500" as="h2">Section Heading</Text>
  <Text type="body" size="200" as="p">
    Regular body text for main content.
  </Text>

  <List
    type="unordered"
    size="200"
    items={[
      'First list item',
      'Second list item',
    ]}
  />

  <Text type="body" size="100" colorScheme="faded" as="p">
    Additional metadata or supplementary information.
  </Text>
</Stack>
```

### Content Patterns
- **Headings** - Use semantic heading variants (h1-h4) for structure
- **Body text** - Use body variant for main content
- **Captions** - Use for metadata, timestamps, and secondary information
- **Lists** - Use for structured content and navigation

### Responsive Typography
- Use the `size` scale (100–600) consistently
- Use the `as` prop for semantic headings for better navigation
- Leverage layout containers (e.g., Stack/Box) for responsive spacing, not Text itself
- Ensure readable line length and line height using tokens

## Accessibility Features

### Semantic HTML
- **Proper heading** hierarchy (h1, h2, h3, h4)
- **List markup** with ul, ol, and li elements
- **Paragraph structure** for body content
- **Emphasis elements**: use `Text` formatting

### Screen Reader Support
- **Meaningful text** that works out of context
- **Descriptive labels** for interactive text elements
- **Alternative text** for text-based images
- **Proper markup** for content relationships

### Visual Accessibility
- **High contrast** text colors
- **Scalable fonts** that work with browser zoom
- **Readable fonts** optimized for screen display
- **Sufficient spacing** for easy scanning

## Best Practices

### Do's
- ✅ Use semantic heading hierarchy (don't skip levels)
- ✅ Choose appropriate text variants for content type
- ✅ Maintain consistent spacing between text elements
- ✅ Test readability at different screen sizes

### Don'ts
- ❌ Use heading variants purely for visual styling
- ❌ Create walls of text without proper breaks
- ❌ Use color alone to convey text meaning
- ❌ Make text too small for comfortable reading

### Content Guidelines
- **Write clear, concise** text appropriate for the audience
- **Use consistent terminology** throughout the interface
- **Provide context** for technical terms and concepts
- **Structure content** with headings and lists for scannability

## Performance Considerations

### Font Loading
- **Optimized font** loading for better performance
- **Fallback fonts** for loading states
- **Subset fonts** to reduce file size
- **Preload critical** fonts for faster rendering

### Text Rendering
- **Efficient text** rendering for large content
- **Lazy loading** for long text content
- **Optimized truncation** for performance
- **Smooth animations** for text state changes

## Resources

- <LinkTo kind="Typography & Content/Text" story="Docs">Text — component docs</LinkTo>
- <LinkTo kind="Typography & Content/List" story="Docs">List — Lab component docs</LinkTo>
- <LinkTo kind="Typography & Content/Highlight" story="Docs">Highlight — Lab component docs</LinkTo>
- <LinkTo kind="Typography & Content/Wordmark" story="Docs">Wordmark — Lab component docs</LinkTo>
- <LinkTo kind="Typography & Content/ShowMore" story="Docs">ShowMore — Lab component docs</LinkTo>
- <LinkTo kind="Typography & Content/Truncation" story="Docs">Truncation — Lab component docs</LinkTo>

---

> **Note:** This content was AI-generated and is awaiting review.
