import {
    Canvas,
    Meta,
} from '@storybook/addon-docs/blocks';
import LinkTo from "@storybook/addon-links/react";
import { Banner } from '@cosmos/components/banner';
import * as TypographyTableStories from './components/TypographyTable.stories';
import {
    fontFamilyDocsObject,
    fontSizeDocsObject,
    fontWeightDocsObject,
    lineHeightDocsObject,
} from '@cosmos/constants/tokens';


<Meta title="Tokens/Typography" />

# Typography

The typography tokens define a set group of properties used to style text elements.

## Import

```jsx
import { fontWeightRegular } from '@cosmos/constants/tokens';
```

## How to use

<Banner
    severity="education"
    title="Use Text Component"
    body={
        <>
            In most cases, you should be able to use the{' '}
            <LinkTo kind="Typography/Content/Text" story="Docs" style={{textDecoration: 'underline'}}>Cosmos Text Component</LinkTo>{' '}
            instead of creating a styled component with the typography
            tokens.
        </>
    }
/>

---

### CSS

```css
.selector {
    font-family: var(---font-family-default);
    font-size: var(---font-size-100);
    font-weight: var(---font-weight-regular);
    line-height: var(---line-height-100);
}
```

### Styled Components (Deprecated)

```jsx
import {
    fontFamilyDefault,
    fontSize100,
    fontWeightRegular,
    lineHeight100,
} from '@cosmos/constants/tokens';

export const StyledBodyText100Span = styled.span`
    font-family: ${fontFamilyDefault};
    font-size: ${fontSize100};
    font-weight: ${fontWeightRegular};
    line-height: ${lineHeight100};
`;
```

## Tokens

<Canvas of={TypographyTableStories.Default} sourceState="none" />
