/* List container styles for ul element */
.listContainer {
    list-style: none;
    margin: 0;
    padding: 0;
    padding-left: 0;
    padding-inline-start: 0;
}

/* List item styles for li element */
.listItem {
    display: flex;
    flex-direction: row;
    gap: var(--space-2);
    padding: var(--dimension-sm) 0;
    list-style: none;
    margin: 0;
    width: 100%;

    /* Align items */
    align-items: flex-start;
}

/* Selected state */
.listItem[aria-selected="true"] {
    background-color: var(--primary-background-mild);
    color: var(--primary-text-initial);
}

/* Disabled state */
.listItem[aria-disabled="true"] {
    cursor: default;
    color: var(--neutral-text-disabled);
}

/* Focus styles for keyboard navigation */
.listItem:focus-visible {
    border-color: var(--primary-border-focus);
    outline-offset: var(--dimension-xs);
}

/* Prevent focus border after mouse drag operations */
.listItem:focus:not(:focus-visible) {
    border-color: transparent;
}

/* Hover styles similar to dropdown items */
.listItem:hover:not(:focus-visible):not([aria-disabled="true"]) {
    background-color: var(--neutral-background-surface-hover);
}

/* Allow hover styles on items with non-visible focus (after mouse drag) */
.listItem:hover:focus:not(:focus-visible):not([aria-disabled="true"]) {
    background-color: var(--neutral-background-surface-hover);
}
