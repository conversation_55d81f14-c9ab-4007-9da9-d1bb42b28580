import { isEmpty } from 'lodash-es';
import { useCallback, useMemo, useState } from 'react';
import { DragDropContext, type DropResult } from 'react-beautiful-dnd';
import { Box } from '@cosmos/components/box';
import { Checkbox } from '@cosmos/components/checkbox';
import { DroppableContainerComponent } from '@cosmos/components/droppable';
import { FieldLabel } from '@cosmos/components/field-label';
import { Search } from '@cosmos/components/search';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { ReorderableItem } from './components';
import { DEFAULT_DATA_ID } from './constants';
import styles from './reorderable-layout.module.css';
import type { ReorderableProps } from './types';

const BaseReorderableLayout = ({
    'data-id': dataId = DEFAULT_DATA_ID,
    items,
    padding = 'md',
    searchPlaceholder = 'Search',
    onItemsUpdate,
}: ReorderableProps): React.JSX.Element => {
    const [searchQuery, setSearchQuery] = useState('');

    // Filter items based on search query
    const filteredItems = useMemo(() => {
        if (!searchQuery.trim()) {
            return items;
        }

        return items.filter((item) =>
            item.label.toLowerCase().includes(searchQuery.toLowerCase()),
        );
    }, [items, searchQuery]);

    // Determine checkbox state (excluding disabled items)
    const checkboxState = useMemo(() => {
        const reorderableItems = filteredItems.filter((item) => !item.disabled);

        if (isEmpty(reorderableItems)) {
            return false;
        }

        const selectedCount = reorderableItems.filter(
            (item) => item.selected,
        ).length;
        const totalCount = reorderableItems.length;

        if (selectedCount === 0) {
            return false;
        }
        if (selectedCount === totalCount) {
            return true;
        }

        return 'indeterminate';
    }, [filteredItems]);

    const isAllSelected = checkboxState === true;

    const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const query = event.target.value;

        setSearchQuery(query);
    };

    const handleSearchClear = () => {
        setSearchQuery('');
    };

    const handleItemSelect = useCallback(
        (itemId: string, selected: boolean) => {
            const updatedItems = items.map((item) =>
                item.id === itemId ? { ...item, selected } : item,
            );

            onItemsUpdate?.(updatedItems);
        },
        [items, onItemsUpdate],
    );

    const handleSelectAll = useCallback(
        (checked?: boolean) => {
            const updatedItems = items.map((item) =>
                item.disabled
                    ? item
                    : { ...item, selected: checked ?? !isAllSelected },
            );

            onItemsUpdate?.(updatedItems);
        },
        [items, isAllSelected, onItemsUpdate],
    );

    const handleDragEnd = (result: DropResult) => {
        if (!result.destination) {
            return;
        }

        const { source, destination } = result;

        if (source.index === destination.index) {
            return;
        }

        const reorderedItems = [...items];
        const [removed] = reorderedItems.splice(source.index, 1);

        reorderedItems.splice(destination.index, 0, removed);

        onItemsUpdate?.(reorderedItems);
    };

    return (
        <Stack
            direction="column"
            gap="sm"
            p={padding}
            data-id={`${dataId}-content`}
            data-testid="BaseReorderableLayout"
        >
            <Stack direction="row" gap="xl">
                <Search
                    id={`${dataId}-search-input`}
                    name={`${dataId}-search`}
                    placeholder={searchPlaceholder}
                    value={searchQuery}
                    clearSearchButtonLabel="Clear search"
                    data-id={`${dataId}-search`}
                    onChange={handleSearchChange}
                    onClear={handleSearchClear}
                />
            </Stack>

            <Stack direction="row" align="center" gap="sm" py="md" pl="6x">
                <Box width="05x" flexShrink="0" aria-hidden="true" />
                <Box width="6x" flexShrink="0">
                    <Checkbox
                        id={`${dataId}-select-all-checkbox`}
                        name={`${dataId}-select-all`}
                        checked={checkboxState}
                        aria-label={`Select all ${filteredItems.length} items`}
                        data-id={`${dataId}-checkbox-select-all`}
                        aria-labelledby={`${dataId}-select-all-label`}
                        onChange={handleSelectAll}
                    />
                </Box>
                <FieldLabel
                    htmlFor={`${dataId}-select-all-checkbox`}
                    labelId={`${dataId}-select-all-label`}
                    data-id={`${dataId}-field-label-select-all`}
                    label={isAllSelected ? 'Deselect all' : 'Select all'}
                />
            </Stack>
            <Divider />

            <DragDropContext onDragEnd={handleDragEnd}>
                <DroppableContainerComponent
                    as="ul"
                    droppableId={`${dataId}-droppable`}
                    type="reorderable-item"
                    className={styles.listContainer}
                >
                    {filteredItems.map((item, index) => (
                        <ReorderableItem
                            key={item.id}
                            id={item.id}
                            label={item.label}
                            selected={item.selected}
                            disabled={item.disabled}
                            data-id={dataId}
                            index={index}
                            onSelect={handleItemSelect}
                        />
                    ))}
                </DroppableContainerComponent>
            </DragDropContext>
        </Stack>
    );
};

/**
 * The ReorderableLayout component provides a layout with search functionality,
 * reorderable items with checkboxes, and optional drag-and-drop reordering capabilities.
 *
 * Features:
 * - Search input to filter items
 * - "Select all" functionality
 * - Checkboxes for item selection
 * - Optional drag handles for reordering with enhanced visual feedback
 * - Customizable header and footer content.
 */
export const ReorderableLayout = BaseReorderableLayout;
