import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest';
import { cleanup, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReorderableLayout } from './reorderable-layout';
import type { ReorderableItem } from './types';

// Mock react-beautiful-dnd
vi.mock('react-beautiful-dnd', () => ({
    DragDropContext: ({
        children,
        onDragEnd,
    }: {
        children: React.ReactNode;
        onDragEnd: (result: unknown) => void;
    }) => {
        // Store onDragEnd for testing
        (global as Record<string, unknown>).mockOnDragEnd = onDragEnd;

        return (
            <div data-testid="drag-drop-context" data-id="ZkPZaHps">
                {children}
            </div>
        );
    },
    Droppable: ({
        children,
    }: {
        children: (provided: unknown, snapshot: unknown) => React.ReactNode;
    }) => children({ droppableProps: {}, innerRef: vi.fn() }, {}),
    Draggable: ({
        children,
    }: {
        children: (provided: unknown, snapshot: unknown) => React.ReactNode;
    }) =>
        children(
            { draggableProps: {}, dragHandleProps: {}, innerRef: vi.fn() },
            { isDragging: false },
        ),
}));

// Mock the droppable components to render children properly
vi.mock('@cosmos/components/droppable', () => ({
    DroppableContainerComponent: ({
        children,
        ...props
    }: { children: React.ReactNode } & Record<string, unknown>) => (
        <div {...props} data-id="LpdMjqGz">
            {children}
        </div>
    ),
    // eslint-disable-next-line @eslint-react/hooks-extra/ensure-custom-hooks-using-other-hooks -- Mock function for testing
    useDraggableInPortal: () => {
        /**
         * Return a function that just passes through the render function.
         */
        return (
            renderFn: (
                provided: unknown,
                snapshot: unknown,
            ) => React.JSX.Element,
        ) => renderFn;
    },
}));

const mockItems: ReorderableItem[] = [
    { id: 'item-1', label: 'Item 1', selected: true },
    { id: 'item-2', label: 'Item 2', selected: false },
    { id: 'item-3', label: 'Item 3', selected: true },
    { id: 'item-4', label: 'Item 4', selected: false, disabled: true },
];

describe('reorderableLayout', () => {
    // Helper function to generate unique test IDs
    let getUniqueTestId: () => string;

    beforeEach(() => {
        let testIdCounter = 0;

        getUniqueTestId = (): string => {
            testIdCounter = testIdCounter + 1;

            return `test-popover-${testIdCounter}`;
        };
    });

    afterEach(() => {
        cleanup();
        vi.clearAllMocks();
    });

    describe('basic Rendering', () => {
        test('accepts all props without errors', () => {
            const testId = getUniqueTestId();
            const onItemsUpdate = vi.fn();

            expect(() => {
                render(
                    <ReorderableLayout
                        items={mockItems}
                        data-id={testId}
                        searchPlaceholder="Search items"
                        onItemsUpdate={onItemsUpdate}
                    />,
                );
            }).not.toThrow();
        });
    });

    describe('search Functionality', () => {
        test('renders search input with correct placeholder', () => {
            const testId = getUniqueTestId();

            render(
                <ReorderableLayout
                    items={mockItems}
                    searchPlaceholder="Custom search"
                    data-id={testId}
                    onItemsUpdate={vi.fn()}
                />,
            );

            const searchInput = screen.getByDisplayValue('');

            expect(searchInput.getAttribute('placeholder')).toBe(
                'Custom search',
            );
        });

        test('uses default search placeholder when none provided', () => {
            const testId = getUniqueTestId();

            render(<ReorderableLayout items={mockItems} data-id={testId} />);

            const searchInput = screen.getByDisplayValue('');

            expect(searchInput.getAttribute('placeholder')).toBe('Search');
        });

        test('filters items based on search query', async () => {
            const testId = getUniqueTestId();

            render(<ReorderableLayout items={mockItems} data-id={testId} />);

            const searchInput = screen.getByDisplayValue('');

            await userEvent.type(searchInput, 'Item 1');

            // Check if filtering works by looking for the specific item
            await waitFor(() => {
                expect(screen.getByText('Item 1')).toBeDefined();
                expect(screen.queryByText('Item 2')).toBeNull();
            });
        });

        test('clears search when clear button is clicked', async () => {
            const testId = getUniqueTestId();

            render(<ReorderableLayout items={mockItems} data-id={testId} />);

            const searchInput = screen.getByDisplayValue('');

            // Type in search
            await userEvent.type(searchInput, 'Item 1');

            // Find and click clear button
            const clearButton = screen.getByRole('button', {
                name: /clear search/i,
            });

            await userEvent.click(clearButton);

            // Verify search is cleared and all items are visible
            expect((searchInput as HTMLInputElement).value).toBe('');
            await waitFor(() => {
                expect(screen.getByText('Item 1')).toBeDefined();
                expect(screen.getByText('Item 2')).toBeDefined();
            });
        });

        test('search is case insensitive', async () => {
            const testId = getUniqueTestId();

            render(<ReorderableLayout items={mockItems} data-id={testId} />);

            const searchInput = screen.getByDisplayValue('');

            await userEvent.type(searchInput, 'item 1');

            await waitFor(() => {
                expect(screen.getByText('Item 1')).toBeDefined();
                expect(screen.queryByText('Item 2')).toBeNull();
            });
        });
    });

    describe('items Rendering', () => {
        test('handles empty items array', () => {
            const testId = getUniqueTestId();

            render(<ReorderableLayout items={[]} data-id={testId} />);

            // Check that the component renders by looking for the search input
            const searchInput = screen.getByRole('textbox');

            expect(searchInput).toBeDefined();
        });

        test('renders with items data', () => {
            const testId = getUniqueTestId();

            render(<ReorderableLayout items={mockItems} data-id={testId} />);

            // Check that the component renders by looking for the search input
            const searchInput = screen.getByRole('textbox');

            expect(searchInput).toBeDefined();

            // Check that items are rendered
            expect(screen.getByText('Item 1')).toBeDefined();
            expect(screen.getByText('Item 2')).toBeDefined();
            expect(screen.getByText('Item 3')).toBeDefined();
            expect(screen.getByText('Item 4')).toBeDefined();
        });

        test('renders container with proper structure', () => {
            const testId = getUniqueTestId();

            render(<ReorderableLayout items={mockItems} data-id={testId} />);

            // Check that the component renders with proper structure
            const searchInput = screen.getByRole('textbox');

            expect(searchInput).toBeDefined();

            // Check that the search input has the correct data-id
            expect(searchInput.getAttribute('data-id')).toBe(
                `${testId}-search-input`,
            );
        });
    });

    describe('component functionality', () => {
        test('renders without errors with onItemsUpdate callback', () => {
            const testId = getUniqueTestId();
            const onItemsUpdate = vi.fn();

            render(
                <ReorderableLayout
                    items={mockItems}
                    data-id={testId}
                    onItemsUpdate={onItemsUpdate}
                />,
            );

            // Check that the component renders by looking for the search input
            const searchInput = screen.getByRole('textbox');

            expect(searchInput).toBeDefined();
        });
    });

    describe('drag and drop functionality', () => {
        test('handles successful drag and drop reordering', () => {
            const testId = getUniqueTestId();
            const onItemsUpdate = vi.fn();
            const reorderableItems = [
                { id: '1', label: 'Item 1', selected: false },
                { id: '2', label: 'Item 2', selected: false },
                { id: '3', label: 'Item 3', selected: false },
            ];

            render(
                <ReorderableLayout
                    items={reorderableItems}
                    data-id={testId}
                    onItemsUpdate={onItemsUpdate}
                />,
            );

            // Simulate drag and drop using the mocked onDragEnd
            const mockDragResult = {
                source: { index: 0 },
                destination: { index: 2 },
                draggableId: '1',
            };

            // Call the mocked onDragEnd function
            const mockOnDragEnd = (global as Record<string, unknown>)
                .mockOnDragEnd as (result: unknown) => void;

            mockOnDragEnd(mockDragResult);

            // Should be called with reordered items
            expect(onItemsUpdate).toHaveBeenCalled();
        });

        test('handles drag with no destination', () => {
            const testId = getUniqueTestId();
            const onItemsUpdate = vi.fn();

            render(
                <ReorderableLayout
                    items={mockItems}
                    data-id={testId}
                    onItemsUpdate={onItemsUpdate}
                />,
            );

            // Clear initial calls
            onItemsUpdate.mockClear();

            // Simulate drag with no destination
            const mockDragResult = {
                source: { index: 0 },
                destination: null,
                draggableId: 'item-1',
            };

            const mockOnDragEnd = (global as Record<string, unknown>)
                .mockOnDragEnd as (result: unknown) => void;

            mockOnDragEnd(mockDragResult);

            // Should not call onItemsUpdate
            expect(onItemsUpdate).not.toHaveBeenCalled();
        });

        test('handles drag to same position', () => {
            const testId = getUniqueTestId();
            const onItemsUpdate = vi.fn();

            render(
                <ReorderableLayout
                    items={mockItems}
                    data-id={testId}
                    onItemsUpdate={onItemsUpdate}
                />,
            );

            // Clear initial calls
            onItemsUpdate.mockClear();

            // Simulate drag to same position
            const mockDragResult = {
                source: { index: 1 },
                destination: { index: 1 },
                draggableId: 'item-2',
            };

            const mockOnDragEnd = (global as Record<string, unknown>)
                .mockOnDragEnd as (result: unknown) => void;

            mockOnDragEnd(mockDragResult);

            // Should not call onItemsUpdate
            expect(onItemsUpdate).not.toHaveBeenCalled();
        });

        test('prevents reordering of disabled items', () => {
            const testId = getUniqueTestId();
            const mockOnItemsUpdate = vi.fn();
            const itemsWithDisabled = [
                { id: '1', label: 'Item 1', selected: false, disabled: false },
                { id: '2', label: 'Item 2', selected: false, disabled: true },
                { id: '3', label: 'Item 3', selected: false, disabled: false },
            ];

            render(
                <ReorderableLayout
                    items={itemsWithDisabled}
                    data-id={testId}
                    onItemsUpdate={mockOnItemsUpdate}
                />,
            );

            // Verify that disabled items don't have drag handles
            const dragHandles = screen.queryAllByTestId(/drag-handle/);

            // Should have fewer drag handles than total items due to disabled item
            expect(dragHandles.length).toBeLessThan(itemsWithDisabled.length);
        });
    });

    describe('accessibility', () => {
        test('has proper semantic structure', () => {
            const testId = getUniqueTestId();

            render(<ReorderableLayout items={mockItems} data-id={testId} />);

            // Check that the component renders
            const searchInput = screen.getByRole('textbox');

            expect(searchInput).toBeDefined();

            // Check that list items are properly structured
            const listItems = screen.getAllByRole('listitem');

            expect(listItems).toHaveLength(4);
        });

        test('has proper keyboard accessibility', () => {
            const testId = getUniqueTestId();

            render(<ReorderableLayout items={mockItems} data-id={testId} />);

            // Check that the component renders
            const searchInput = screen.getByRole('textbox');

            expect(searchInput).toBeDefined();

            // Check that interactive elements are accessible
            const checkboxes = screen.getAllByRole('checkbox');

            expect(checkboxes.length).toBeGreaterThan(0);
        });
    });

    describe('props and configuration', () => {
        test('applies custom padding', () => {
            const testId = getUniqueTestId();

            render(
                <ReorderableLayout
                    items={mockItems}
                    data-id={testId}
                    padding="lg"
                />,
            );

            // Check that the component renders
            const searchInput = screen.getByRole('textbox');

            expect(searchInput).toBeDefined();
        });

        test('uses default data-id when none provided', () => {
            render(<ReorderableLayout items={mockItems} />);

            // Check that the component renders
            const searchInput = screen.getByRole('textbox');

            expect(searchInput).toBeDefined();

            // Check that the search input has the default data-id
            expect(searchInput.getAttribute('data-id')).toBe(
                'reorderable-layout-search-input',
            );
        });
    });
});
