import { type JSX, memo } from 'react';
import { Draggable } from 'react-beautiful-dnd';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Checkbox } from '@cosmos/components/checkbox';
import { useDraggableInPortal } from '@cosmos/components/droppable';
import { FieldLabel } from '@cosmos/components/field-label';
import { Stack } from '@cosmos/components/stack';
import styles from '../reorderable-layout.module.css';

interface ReorderableItemProps {
    id: string;
    index: number;
    label: string;
    selected: boolean | 'indeterminate';
    disabled?: boolean;
    'data-id': string;
    onSelect: (itemId: string, selected: boolean) => void;
}

const ReorderableItemComponent = ({
    id,
    index,
    label,
    selected,
    disabled = false,
    'data-id': dataId,
    onSelect,
}: ReorderableItemProps): JSX.Element => {
    const renderDraggable = useDraggableInPortal();
    const checkboxId = `${dataId}-checkbox-${id}`;
    const labelId = `${dataId}-label-${id}`;

    const handleSelect = () => {
        onSelect(id, !selected);
    };

    return (
        <Draggable
            disableInteractiveElementBlocking
            key={id}
            draggableId={id}
            index={index}
            isDragDisabled={disabled}
            data-id={`${dataId}-draggable-${id}`}
            data-testid="ReorderableItemComponent"
        >
            {renderDraggable((provided, snapshot) => {
                const { innerRef, draggableProps, dragHandleProps } = provided;

                return (
                    <li
                        ref={innerRef}
                        className={styles.listItem}
                        data-id={`${dataId}-item-${id}`}
                        data-testid="ReorderableItem"
                        {...draggableProps}
                    >
                        <Stack
                            width="100%"
                            direction="row"
                            align="center"
                            gap="sm"
                            pl={disabled ? '2xl' : 'none'}
                        >
                            {disabled ? (
                                /* Spacer to align with drag handle - matches Icon size="200" */
                                <Box
                                    width="05x"
                                    flexShrink="0"
                                    aria-hidden="true"
                                />
                            ) : (
                                <Button
                                    isIconOnly
                                    label={`Drag to reorder ${label}`}
                                    startIconName="ReorderDotsVertical"
                                    level="tertiary"
                                    size="sm"
                                    data-id={`${dataId}-drag-handle-${id}`}
                                    {...dragHandleProps}
                                    colorScheme={
                                        snapshot.isDragging
                                            ? 'primary'
                                            : 'neutral'
                                    }
                                />
                            )}
                            <Box width="6x" flexShrink="0">
                                <Checkbox
                                    id={checkboxId}
                                    name={checkboxId}
                                    checked={selected}
                                    disabled={disabled}
                                    aria-label={label}
                                    data-id={`${dataId}-checkbox-${id}`}
                                    aria-labelledby={labelId}
                                    onChange={handleSelect}
                                />
                            </Box>
                            <Box flexGrow="1">
                                <FieldLabel
                                    htmlFor={checkboxId}
                                    labelId={labelId}
                                    label={label}
                                    data-id={`${dataId}-field-label-${id}`}
                                />
                            </Box>
                        </Stack>
                    </li>
                );
            })}
        </Draggable>
    );
};

export const ReorderableItem = memo(ReorderableItemComponent);
