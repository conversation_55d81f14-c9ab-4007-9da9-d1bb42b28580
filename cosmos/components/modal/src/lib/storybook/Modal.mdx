import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ModalStories from './Modal.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={ModalStories} />

<Title />

<Banner
    severity="critical"
    title="Important"
    body="This package only provides the internal pieces of a modal. You must put them together yourself and use them with the `useModal` hook from `@cosmos/components/modal`. See examples at the bottom of the page."
/>

<Description />

<Primary />

<Banner
    severity="primary"
    title="Note"
    body="The following controls are combined from all sub-components for demonstration purposes. See individual sub-component sections for relevant props."
/>

<Controls of={ModalStories.Playground} sort="requiredFirst" />

## Import

```tsx
import { Modal } from '@cosmos/components/modal';

```

## Components

### ModalHeader

The top section of a modal, containing a title and description text to provide context or information about the content within the modal.

<Canvas of={ModalStories.ModalHeader} />

<Controls of={ModalStories.ModalHeader} sort="requiredFirst" />

### ModalBody

The central area of a modal where the main content or information is displayed, such as text, forms, or multimedia elements.

<Canvas of={ModalStories.ModalBody} />

<Controls of={ModalStories.ModalBody} sort="requiredFirst" />

### ModalFooter

The bottom section of a modal, reserved for buttons allow users to take specific actions related to the modal content, like confirming or canceling an operation.

<Canvas of={ModalStories.ModalFooter} />

<Controls of={ModalStories.ModalFooter} sort="requiredFirst" />

## Examples

### Generic

```js
import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';

...

...

const handleOpen = () => {
    modalController.openModal({
        id: 'modal-id',
        content: () => <YourModalComponent ... />,
        centered: true,
        disableClickOutsideToClose: false,
        size: 'md',
    });
}
```

```tsx
import { Modal } from '@cosmos/components/modal';

...

function YourModalComponent({ onClose, onConfirm }) {
    return (
        <>
            <Modal.Header
                title="REPLACE_ME"
                description="REPLACE_ME"
                closeButtonAriaLabel="REPLACE_ME"
                onClose={onClose}
            />
            <Modal.Body>{/* Your modal content goes here */}</Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: 'REPLACE_ME',
                        level: 'tertiary',
                        onClick: onClose,
                    },
                    {
                        label: 'REPLACE_ME',
                        level: 'primary',
                        onClick: onConfirm,
                    },
                ]}
            />
        </>
    );
}
```

### Form

```tsx
import { Modal } from '@cosmos/components/modal';
...

const StyledForm = styled(Form)`
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
`;

...

function YourModalFormComponent({ onClose, onSubmit }) {
    const handleSubmit = async () => {
        try {
            // post data to API and wait for response
            // trigger onSubmit when response is OK
            onSubmit();
        } catch (error) {
            // form should be re-enabled on error so user can try again
        }
    };

    return (
        <Formik onSubmit={handleSubmit}>
            {(formikProps) => {
                return (
                    <StyledForm noValidate>
                        <Modal.Header
                            title="REPLACE_ME"
                            description="REPLACE_ME"
                            closeButtonAriaLabel="REPLACE_ME"
                            onClose={onClose}
                        />
                        <Modal.Body>
                            {/* Your form components go here */}
                        </Modal.Body>
                        <Modal.Footer
                            rightActionStack={[
                                {
                                    label: 'REPLACE_ME',
                                    level: 'tertiary',
                                    type: 'button',
                                    onClick: onClose,
                                },
                                {
                                    label: 'REPLACE_ME',
                                    level: 'primary',
                                    type: 'submit',
                                    isLoading: formikProps.isSubmitting,
                                },
                            ]}
                        />
                    </StyledForm>
                );
            }}
        </Formik>
    );
}
```

## 🟢 When to use the component

- Use modal dialogs for important warnings, as a way to prevent or correct critical errors. Whenever there is a chance that users’ work may be lost or that an action may have destructive, irreversible consequences, interrupt the users to avoid a problem.(Consider using the ConfirmationModal component)
- Use modal dialogs to request the user to enter information critical to continuing the current process. When missing information prevents the system from continuing a user-initiated process, a modal dialog can prompt the user for that information.
- Provide explanatory content seamlessly within a process using modals, avoiding disruption to the user's task. These modals present optional information, enhancing user understanding—for instance, displaying a privacy policy during account creation or offering in-depth explanations while filling out a form. Usually, no additional user action is needed beyond closing these modals.
- **Focused decision making** - When you need to isolate user attention on a specific choice or action that requires immediate resolution
- **Nimble work surfaces** - When users need to perform focused tasks or make quick edits without losing their current context or navigating away from their primary workflow

## ❌ When not to use the component

- Avoid modal dialogs for complex decision making that requires additional sources of information unavailable in the modal. If a modal requires the user to do complex research or consult additional sources of information (potentially blocked by the modal), then it’s not the right UI element for that interaction.
- Don’t use modals for error messaging. Display form-related error, success, or warning messages in the context of the page, like showing an error next to a missing field. For page-level messages, such as successful form submission, use a Banner at the top of the next page.
- Don’t use modals if a separate, designated URL is desired.
- Avoid building complex flows in modals that divert users from the original page. Instead, use an individual page for multi-step processes to guide users and manage flow complexities more effectively.
- **Automatic displays** - Don't automatically display modals without user action, as this creates surprising and disruptive experiences

## 🛠️ How it works

The Modal component provides a compound structure with ModalHeader, ModalBody, and ModalFooter components that work together to create focused dialog experiences. A modal is a temporary dialog box that supersedes a page or a regular user flow, and requires the user to interact with it before continuing with their main task. Modal interactions typically focus the user’s attention on a specific task or decision and prevent interaction with the rest of the interface until the modal is dismissed.

**Component structure:**
- **ModalHeader** - Contains title, optional description, and close button with proper ARIA labeling
- **ModalBody** - Scrollable content area that adapts to different modal sizes with appropriate padding
- **ModalFooter** - Action area with left and right action stacks for organizing buttons by importance

**Size variants:**
- **Small (`sm`)** - 386px width, fixed height (264px), for simple confirmations and brief interactions
- **Medium (`md`)** - 548px width, max height 400px, default size for most modal content
- **Large (`lg`)** - 711px width, max height 600px, for complex forms and detailed content
- **Extra Large (`xl`)** - 1440px width, max height 100%, for full-featured interfaces

**Technical behavior:**
- **Focus management** - Automatically traps focus within modal and returns focus to triggering element on close
- **Dismissal options** - Supports Escape key, click outside (configurable), and explicit close button interactions
- **Overlay handling** - Prevents interaction with underlying page content and manages backdrop styling. Click-outside-to-close can be disabled via `disableClickOutsideToClose` prop
- **Context integration** - Uses React Context to share size and close handlers between child components
- **Controller integration** - Works seamlessly with the `modalController` for programmatic modal management and state handling

### Usability

- Users should trigger modals. Modals should appear as a result of an action made by the user or (less commonly) inactivity. A modal should not surprise the user, so don’t automatically display them.
- Choose the modal size that fits your content. There are three sizes to choose from: small, medium, and large.
- Avoid scrollable content within a modal if possible.
- Limit the number of actions in a modal. A primary and secondary action should be used for modals. The rarely used tertiary actions are often destructive, such as “Delete”.
- Don’t reload the page or display new content behind a modal when the modal is triggered.
- Don’t use a modal on top of another modal. This can cause accessibility problems and confusion.
- Don't put buttons that close the Modal, or confirm and submit content, in the Modal body.

### Content

**Header guidelines:**
- **Descriptive titles** - Use clear, specific titles that explain the modal's purpose (e.g., "Delete Account" not "Confirm Action")
- **Optional descriptions** - Include brief explanatory text when the title alone doesn't provide sufficient context
- **Consistent labeling** - Use aria-labelledby with the header title for proper screen reader announcement

**Body content:**
- **Focused messaging** - Keep content directly relevant to the modal's purpose without unnecessary information
- **Scannable format** - Use clear headings, bullet points, and white space for easy reading
- **Form organization** - Structure form fields logically with appropriate validation and error handling
- **Progressive disclosure** - For complex content, consider breaking information into digestible sections

**Action buttons:**
- **Clear labels** - Use specific, action-oriented button text (e.g., "Save Changes" instead of "OK")
- **Logical ordering** - Place primary actions on the right, secondary actions on the left in the footer
- **Destructive actions** - Use appropriate color schemes (danger) and confirmation patterns for irreversible actions
- **Loading states** - Show loading indicators on buttons during processing to provide feedback

### Accessibility

**What the design system provides:**
- Focus management that automatically traps focus within the modal and returns focus to the triggering element when closed
- Keyboard navigation with full keyboard accessibility and Escape key support for dismissal
- ARIA attributes with proper aria-labelledby and aria-describedby associations with modal content
- Screen reader support that announces modal opening and provides context through proper semantic structure
- Backdrop behavior that prevents interaction with underlying content and manages focus appropriately

**Development responsibilities:**
- Label the modal with its heading. Use aria-labelledby=”[id]” on the modal to read out the modal title when opening the modal. The [id] should match the value of the id attribute on the heading element.
- Share more context with aria-describedby. Optionally, you may also use aria-describedby=”[id]” on the modal to associate descriptive text to the modal window so that it’s read when opening the modal. The id should belong to a paragraph or a brief piece of content.
- Modals should always be focusable with a keyboard. (If an element eg. button triggers the dialog, that element must also be keyboard focusable.)
- Inform user that an alert dialog has opened
- Constrain focus to dialog
- Once a Modal is closed, return focus to element that had focus before the dialog was invoked.
- Modals must be possible to close and have a clear indication of how to close. Examples of ways to close are pressing Esc and a close button.
- Underlying page content must not look actionable.
- Prevent user searching in the underlying page while Modal is active.
- Prevent scrolling of the underlying page wile Modal is active.
- Modals should always be visible - regardless of scrolling, screen size or orientation changes.

**Design responsibilities:**
- Visual focus indicators with clear focus states for all interactive elements within modals
- Sufficient contrast to ensure text and interactive elements meet WCAG contrast requirements against modal backgrounds
- Clear close affordances with obvious visual indicators for how to dismiss the modal
- Backdrop styling that creates appropriate visual separation between modal content and underlying page
- Responsive behavior with modals that work effectively across different screen sizes and orientations

