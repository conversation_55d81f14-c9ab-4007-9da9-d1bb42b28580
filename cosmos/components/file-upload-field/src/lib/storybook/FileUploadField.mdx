import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as FileUploadFieldStories from './FileUploadField.stories';

<Meta of={FileUploadFieldStories} />

<Title />

<Banner
    severity="education"
    title="Use Web Wrappers for Form Fields"
    body={
        <>
            Avoid using form field components directly from Cosmos. Instead, use
            our web wrappers for optimized functionality and consistent
            integration.{' '}
            <Link
                href="/?path=/docs/form-components-about--docs#web-wrappers"
                label="Click here for more details"
                isExternal
            />
            .
        </>
    }
/>

<br />

<Banner
    severity="warning"
    title="Unstable component"
    body={
        <>
            This component is considered <strong>Unstable</strong> because it
            has incomplete test coverage.
        </>
    }
/>
<br />

<Description />

<Primary />

<Controls of={FileUploadFieldStories.Playground} />

## Import

```jsx
import { FileUploadField } from '@drata/cosmos-file-upload-field';
```

## 🟢 When to use the component

- **Document uploads** - When users need to upload one or more files like PDFs, Word documents, or spreadsheets
- **Drag-and-drop uploads** - When users benefit from drag-and-drop functionality alongside traditional file selection
- **File validation needs** - When uploads require format restrictions, size limits, or custom validation rules

## ❌ When not to use the component

- **Image-specific uploads** - Use ImageUploadField for image uploads with preview functionality
- **Read-only file display** - Use appropriate display components when files are only being shown, not uploaded

## 🛠️ How it works

The FileUploadField component provides comprehensive file upload functionality with form field integration, drag-and-drop support, validation, and file management capabilities.

**Core functionality:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration
- **FileUpload core** - Handles drag-and-drop, file selection, validation, and file list management
- **Multiple formats** - Accepts array of `acceptedFormats` using supported MIME types
- **Size validation** - Configurable `maxFileSizeInBytes` with default 25MB limit
- **Multi-file support** - `isMulti` prop enables multiple file selection and management

**File handling:**
- **Single file mode** - `oneFileOnly` prop restricts to exactly one file when enabled
- **Initial files** - Supports `initialFiles` and `initialFilesWithErrors` for pre-populated states
- **Drop zone display** - `showDropzone` prop controls visibility of drag-and-drop area
- **File list management** - `showFileList` prop controls display of uploaded files
- **Custom error messages** - `errorCodeMessages` object for file-invalid-type, file-too-large, file-too-small, too-many-files

**Event handling:**
- **Unified callback** - `onUpdate` callback provides comprehensive file state updates
- **Action tracking** - Callback includes action type (DROP_ACCEPTED, DROP_REJECTED, REMOVE_FILE)
- **Complete state** - Callback receives both valid files and files with errors

### Usability

To upload a file, the user can either:

- use the ‘Choose file’ button
- ‘drag and drop’ a file into the file upload input area

If the requirement only allows a single file to be uploaded, allow customers to only choose one file from the file explorer. Use content to clearly set that expectation.

### Content

- Use descriptive labels that explain what files are being collected (e.g., "Supporting Documents", "Compliance Evidence")
- Inner label should provide helpful context ("Or drop files here")
- Use help text to explain file requirements, size limits, or format restrictions
- If only one file is allowed to be uploaded, set expectations clearly with content
- Button text should clearly indicate the upload action ("Upload files", "Select documents")
- Error messages should clearly explain what's wrong and how to fix it

**List input requirements** 

Use help text to highlight any restrictions. These include file size, file types etc so you can set the user up for success. Don’t allow customers to select incompatible file types. If users are shown an error, be specific on what caused the error and how to fix it.

Use positive framing to clearly communicate limitations to the user

| **Requirements** | **Recommended phrasing** |
| --- | --- |
| File type | You can upload [x], [x] and [x] file formats. |
| File size | Files can be up to [file limit]. |
| Number of files | You can upload up to [max #] files. |

[File extension formats](https://www.notion.so/drata/File-extension-formats-9ac1e98c52e04eeb87b29da8215e33a1?source=copy_link) on how to properly display file formats.

**Set the context with heading and label**

Upload a file is the generic heading and label. Change it to fit your context e.g. Upload evidence.

**Truncate file names when needed**

Use an ellipsis (…) if the filename extends beyond the width of its parent element. A tooltip should appear on hover to disclose the full length of the filename.
#### Error messages

Note: Not all these messages are built into the component. You may need to use the right ones based on your context.

| Scenario | Message |
| --- | --- |
| If no file has been selected | Say ‘Select a [whatever they need to select]’. For example, ‘Select a report’. |
| If the file is the wrong file type | Say ‘The selected file must be a [list of file types]’. For example, ‘The selected file must be a CSV or ODS’ or ‘The selected file must be a JPG, BMP, PNG, TIF or PDF’. |
| If the file is too big | Say ‘The selected file must be smaller than [largest file size]’. For example, ‘The selected file must be smaller than 2MB’. |
| If the file is empty | Say ‘The selected file is empty’. |
| If the file is password protected | Say ‘The selected file is password protected’. |
| If there was a problem and the file was not uploaded | Say ‘The selected file could not be uploaded – try again’. |
| If there is a limit on how many files the user can select | Say ‘You can only select up to [highest number] files at the same time’. |
| If the file is not in a template that must be used or the template has been changed | Say ‘The selected file must use the template’. |
| Generic and backend errors | Something went wrong. Try uploading your files again. |

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships, file input labeling, and drag-and-drop accessibility
- Full keyboard navigation support including Tab navigation, Space/Enter activation, and arrow key file management
- Screen reader announcements for file selection, validation errors, upload progress, and file removal actions
- High contrast support that works with system accessibility preferences and meets WCAG guidelines for all upload states
- Focus management with visible focus indicators and logical tab order through upload interface and file list
- Touch target sizing that meets accessibility guidelines with proper spacing for mobile file selection and management

**Development responsibilities:**
- The File Uploader should always use ’FileUploaderLabel’ to ensure it has an associated label.
- Use meaningful help text that explains file requirements, size limits, format restrictions, and business context
- Implement comprehensive error handling with clear, actionable messages for all validation scenarios
- Ensure `errorCodeMessages` provide specific, helpful guidance for each type of file validation error
- Handle loading and processing states appropriately with proper ARIA live region announcements
- Coordinate with form validation systems to provide consistent error handling and user feedback
- The only tab stop in the File Uploader is the visually hidden input within the dropzone. The “Enter” and “Space” keys open the file selector.
- Do not use ‘drag and drop’ as the only way to upload files. You must provide another method, such as the ‘Choose file’ button. This is to comply with [WCAG 2.2 success criterion 2.5.7 Dragging Movements](https://www.w3.org/WAI/WCAG22/Understanding/dragging-movements.html).

**Design responsibilities:**
- Provide sufficient color contrast for all upload states, file list items, and validation feedback across different themes
- Design clear visual hierarchy that shows the relationship between upload area, file list, and validation messages
- Ensure focus indicators are clearly visible and meet contrast requirements for all interactive elements
- Create consistent visual patterns for file upload fields across the application
- Design appropriate spacing and sizing for drag-and-drop areas, file list items, and touch targets across screen sizes
- Ensure drag-and-drop visual feedback, file validation states, and error messaging provide clear visual cues that complement screen reader announcements

