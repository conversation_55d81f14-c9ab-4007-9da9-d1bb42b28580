import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as SearchStories from './Search.stories';

<Meta of={SearchStories} />

<Title />

<Description />

<Primary />

<Controls of={SearchStories.Playground} />

## Import

```jsx
import { Search } from '@drata/cosmos-search';
```

## 🟢 When to use the component

- **Large Data Sets:** When dealing with large amounts of data, a search component helps users find specific items quickly. For example, Search is useful for a Risks table.
- **Customized Search Experiences:** When users need to search within a customized or specific context, such as searching within a particular project or dataset.

## ❌ When not to use the component

- **Minimal Content or Data:** If your use case has a limited number of items, a search component might be unnecessary.
- **Primary Task Not Search-Oriented:** If the primary task does not involve finding specific items or information, a search component might distract from the main user tasks.

## 🛠️ How it works

### Usability

- Use a placeholder in the field to indicate a possible search criteria or a prompt to search.
- Add auto-suggestion when possible.

### Content

- Placeholder should be clear enough for the user to indicate the possible search criteria, such as **“Search Control by DCN number.”**

### Accessibility

- **Labeling:** Provide a clear and descriptive label for the search input field using the `aria-label` or `aria-labelledby` attributes.
- **Keyboard Navigation:** Ensure users can navigate the search input and button using the keyboard. Keypress should trigger the search action.

