import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as PanelStories from './Panel.stories';
import { Banner } from '@cosmos/components/banner';
import modalPanels from './assets/modal-panels.png';

<Meta of={PanelStories} />

<Title />

<Banner
    severity="critical"
    title="Important"
    body="This package only provides the internal pieces of a panel. You must put them together yourself and use them with the TBD_REPLACE_ME_PANEL_SERVICE_IN_WEB. See examples at the bottom of the page."
/>

<Description />

<Primary />

<Controls of={PanelStories.Playground} />

## Import

```jsx
import { Panel } from '@drata/cosmos-panel';
```

## Components

### PanelControls

The panel controls component contains a close button to exit the panel and action controls like pagination, enabling users to navigate through and load the next item into the panel context.

<Canvas of={PanelStories.PanelControls} />

<Controls of={PanelStories.PanelControls} sort="requiredFirst" />

### PanelHeader

The header is docked to the top of its panel. When the content of the panel body becomes overflowed, the body will provide scrolling while the header remains visible in place.

The header has:

- a top row of controls including a close action and optional secondary actions.
- a title, and subtitle with left-aligned text.
- optional tabs

<Canvas of={PanelStories.PanelHeader} />

<Controls of={PanelStories.PanelHeader} sort="requiredFirst" />

### Body

The panel body can contain a single column of content. Panel’s should favor summarized content, or minimally scoped tasks.

<Canvas of={PanelStories.PanelBody} />

<Controls of={PanelStories.PanelBody} sort="requiredFirst" />

### Footer

The optional panel footer is docked to the bottom of the panel and offers optional primary and secondary actions as well as a left-aligned destructive action.

<Canvas of={PanelStories.PanelFooter} />

<Controls of={PanelStories.PanelFooter} sort="requiredFirst" />

## Variants and prop considerations

### Non-modal and modal
<img src={modalPanels} alt="add modal panels picture" />

Panels can be modal, and block interaction with the rest of the page, or non-modal and allow interaction with the main content area and the panel content at the same time.

Non-modal panels reduct the main viewport by cropping it, while the modal panel overlaps the main viewport, temporarily taking ownership of it.

### Tabs

No use cases exist for tabs in a panel yet. We expect we may need this in the future though. For now design will need to make a strong case for using tabs and bring it to Cosmos for review.


## 🟢 When to use the component

- When a user triggers a task or needs to review or take other actions without losing their current context.
- The main content extends beyond the boundaries of the viewport, while panning and zooming is supported.
- It is not important to see canvas content while completing the task in a Panel.
- The content can be presented in summary with minimal depth.
- The task performed in a Panel is momentary.

## 🔴 When not to use the component

- When your task or content requires focus, has multiple steps, or could benefit from more space.
- If your content is a message from the system.

## 🛠️ How it works

The Panel system consists of individual components that must be assembled together and used with a panel service. The components work together to create a consistent panel experience.

**Component structure:**
- **PanelControls** - Sticky header with close button and optional pagination for navigating between items
- **PanelHeader** - Contains title, optional slot content, related page links, and action buttons
- **PanelBody** - Flexible content area that scrolls independently while header and footer remain fixed
- **PanelFooter** - Sticky footer with left and right action stacks for primary and secondary actions

**Layout behavior:**
- **Full height** - Panel should take up 100% of the height of its parent container (typically viewport)
- **Right-docked** - Panel is positioned on the right side of the viewport and affects document flow
- **Sticky elements** - Controls and footer remain visible during body content scrolling
- **Responsive design** - Components adapt to different screen sizes while maintaining usability
- The main content extends beyond the boundaries of the viewport, while panning and zooming is supported.

**Interaction modes:**
- **Modal panels** - Block interaction with main content using backdrop overlay
- **Non-modal panels** - Allow simultaneous interaction with both panel and main content
- **Context switching** - Support pagination between related objects without closing the panel

**Technical integration:**
- **Panel service required** - Must be used with a panel management service (not included in this package)
- **Manual assembly** - Components must be composed together by the implementing application
- **Controller integration** - Works with `panelController` for programmatic panel management
- **Focus management** - Proper focus handling for accessibility when opening/closing panels

### Usability

There are different use cases for using the panel and key mode decisions as well.

**Detail panel**

The panel can be used to serve an overview of a Drata object and core actions to the user. Consider this a “drive-thru” form of a larger, more robust detail page that offers all possible actions and information about a Drata object.

Panels can be modal, and block interaction with the rest of the page, or non-modal and allow interaction with the main content area and the panel content at the same time.

If a user opens an object detail panel from a table or gallery of the same object type, you may offer the panel in a non-modal mode. Alternatively, if the user is opening an object detail from anywhere else, it should be a modal panel with a light box that blocks interaction with the rest of the viewport.

If a user takes a secondary task from a detail panel—E.g. from a control detail panel the user selects “Edit control info”—the scoped task should open in a modal panel or modal dialog.

**Scoped tasks in panels**

A panel can also offer access to a scoped task without taking the user completely out of their current context. Ideally these tasks would be limited in depth without multiple steps. As with detail panels, a scoped task panel can be served modal, and block interaction with the rest of the page, or non-modal and allow interaction with the main content area and the panel content at the same time.

### Content

**Header content:**
- **Clear titles** - Use descriptive titles that identify the object or task (supports line clamping for long titles)
- **Contextual slots** - Include relevant metadata, status indicators, or key identifiers in the header slot
- **Related links** - Provide links to related resources or full detail pages
- **Action placement** - Use either `openPageLink` for navigation or `action` button for primary actions (not both)

**Body content:**
- **Focused information** - Present only the most relevant details for the user's current task
- **Scannable layout** - Organize content for quick comprehension and decision-making
- **Minimal depth** - Avoid complex nested information that would be better suited for full pages

**Footer actions:**
- **Left stack** - Typically used for destructive actions like "Delete" or "Remove"
- **Right stack** - Primary and secondary actions like "Save", "Cancel", or "Submit"
- **Action hierarchy** - Use appropriate button levels to communicate action importance

**Pagination content:**
- **Context awareness** - Show current position and total items when navigating between related objects
- **Navigation labels** - Use clear "Previous" and "Next" labels for pagination controls

### Accessibility

When managing the visibility of the panel, there are a few essential accessibility elements to make sure are in place:

- When the panel is not visible, set its `aria-hidden` attribute to `true`, and when visible, set `aria-hidden` to `false`.
- If the panel's visibility is triggered by an element, the triggering element must be focusable with `aria-controls` set to the unique ID of the panel and `aria-expanded` set to either `true` or `false` depending on the visibility of the panel.
- When toggling a panel open, place the user's focus inside the first focusable element within the panel that isn't the close button. If the close button is the only focusable element, place focus there. Return focus to the triggering element when the panel closes.

**What the design system provides:**
- Keyboard navigation with full keyboard support for all interactive elements including close, pagination, and action buttons
- Focus management with proper focus trapping and restoration when panels open and close
- Screen reader support through semantic structure with appropriate headings and landmark navigation
- Sticky positioning that keeps controls and footer accessible during content scrolling
- Touch targets with adequate sizing for touch interactions on mobile devices

**Development responsibilities:**
- Panel service integration to implement proper panel management service for opening, closing, and state management
- Focus handling to ensure focus moves appropriately when panels open and returns to trigger element when closed
- Escape key support for implementing Escape key handling for closing panels
- Loading states that handle loading appropriately while maintaining semantic structure
- Dynamic content updates for panel content and pagination when navigating between items
- Visibility management with proper `aria-hidden` attributes (`true` when not visible, `false` when visible)
- Trigger element setup with `aria-controls` set to the panel's unique ID and `aria-expanded` reflecting panel visibility state
- Focus placement to move focus to the first focusable element within the panel (excluding close button) when opening, or to close button if it's the only focusable element. Return focus to the triggering element when the panel closes.

**Design responsibilities:**
- Visual hierarchy with clear relationships between header, body, and footer content
- Modal indicators that provide clear visual feedback for modal vs non-modal panel states
- Content overflow handling with appropriate scrolling and truncation for long content
- Responsive behavior that ensures panels work effectively across different screen sizes
- State communication that clearly shows loading, error, and success states within panel content
