import { describe, expect, test } from 'vitest';
import type { Action } from '@cosmos/components/action-stack';
import type {
    ButtonAction,
    DropdownAction,
    TextAction,
    TooltipButtonAction,
} from '../../../../action-stack/src/lib/types/action.type';
import { DEFAULT_ACTIONS_SIZE } from '../constants/default-actions-size.constant';
import { normalizeBulkActionsSize } from './normalize-bulk-actions-size.helper';

describe('normalizeBulkActionsSize', () => {
    test('should return empty array for empty input', () => {
        const result = normalizeBulkActionsSize([]);

        expect(result).toStrictEqual([]);
    });

    test('should normalize tooltipButton action size', () => {
        const actions: Action[] = [
            {
                id: 'test-tooltip',
                actionType: 'tooltipButton',
                typeProps: {
                    button: {
                        label: 'Test',
                        size: 'md',
                    },
                    tooltip: { text: 'Test tooltip' },
                },
            },
        ];

        const result: Action[] = normalizeBulkActionsSize(actions);

        expect(result).toHaveLength(1);

        const { typeProps } = result[0] as TooltipButtonAction;

        expect(typeProps.button.size).toBe(DEFAULT_ACTIONS_SIZE);
        expect(typeProps.tooltip.text).toBe('Test tooltip');
    });

    test('should normalize dropdown action size', () => {
        const actions: Action[] = [
            {
                id: 'test-dropdown',
                actionType: 'dropdown',
                typeProps: {
                    label: 'Drop down',
                    items: [],
                },
            },
        ];

        const result = normalizeBulkActionsSize(actions);

        expect(result).toHaveLength(1);

        const { typeProps } = result[0] as DropdownAction;

        expect(typeProps.size).toBe(DEFAULT_ACTIONS_SIZE);
        expect(typeProps.items).toStrictEqual([]);
    });

    test('should normalize button action size', () => {
        const actions: Action[] = [
            {
                id: 'test-button',
                actionType: 'button',
                typeProps: {
                    size: 'md',
                    label: 'Test Button',
                },
            },
        ];

        const result = normalizeBulkActionsSize(actions);

        expect(result).toHaveLength(1);

        const { typeProps } = result[0] as ButtonAction;

        expect(typeProps.size).toBe(DEFAULT_ACTIONS_SIZE);
        expect(typeProps.label).toBe('Test Button');
    });

    test('should set text action size to "100"', () => {
        const actions: Action[] = [
            {
                id: 'test-text',
                actionType: 'text',
                typeProps: {
                    size: '200',
                },
            },
        ];

        const result = normalizeBulkActionsSize(actions);

        expect(result).toHaveLength(1);

        const { typeProps } = result[0] as TextAction;

        expect(typeProps.size).toBe('100');
    });
});
