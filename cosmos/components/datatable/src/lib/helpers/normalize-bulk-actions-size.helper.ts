import type { Action } from '@cosmos/components/action-stack';
import { DEFAULT_ACTIONS_SIZE } from '../constants/default-actions-size.constant';

export function normalizeBulkActionsSize(actions: Action[]): Action[] {
    return actions.map((action) => {
        const { typeProps, actionType } = action;

        switch (actionType) {
            case 'tooltipButton': {
                const { button } = typeProps;

                return {
                    ...action,
                    typeProps: {
                        ...typeProps,
                        button: {
                            ...button,
                            size: DEFAULT_ACTIONS_SIZE,
                        },
                    },
                };
            }
            case 'dropdown':
            case 'button': {
                return {
                    ...action,
                    typeProps: {
                        ...typeProps,
                        size: DEFAULT_ACTIONS_SIZE,
                    },
                };
            }
            case 'text': {
                return {
                    ...action,
                    typeProps: {
                        ...typeProps,
                        size: '100',
                    },
                };
            }
            default: {
                return action;
            }
        }
    }) as Action[];
}
