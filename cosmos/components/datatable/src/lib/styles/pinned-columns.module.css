.pinnedHeader {
    position: sticky !important; /* Override the default position: relative from StyledTableHeaderDiv */
    left: var(--pinned-left-offset, 0px);
    /* Higher z-index to ensure pinned headers stay above pinned cells and regular content */
    z-index: 3;
}

.pinnedCell {
    position: sticky !important; /* Ensure sticky positioning works correctly */
    left: var(--pinned-left-offset, 0px);
    /* Medium z-index to ensure pinned cells stay above regular content but below headers */
    z-index: 2;
}

.lastPinnedShadow {
    box-shadow: var(--dimension-05x) var(--dimension-0x) var(--dimension-1x)
        rgba(41, 42, 51, 0.12);
}
