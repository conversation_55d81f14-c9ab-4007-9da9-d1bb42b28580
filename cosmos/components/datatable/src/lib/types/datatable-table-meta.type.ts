import type { EmptyStateProps } from '@cosmos/components/empty-state';
import type { DatatableProps } from './datatable-props.type';
import type { DensityOption } from './density-option.type';
import type { ViewModeType } from './enabled-views.type';
import type { FilterProps } from './filter-props.type';
import type { FilterViewModeProps } from './filter-view-mode-props.type';
import type { RowActionsProps } from './row-action.types';
import type { TableSearchProps } from './table-search-props.type';
import type { TableSettingsTriggerProps } from './table-settings-trigger-props.type';

export interface DatatableTableMeta<TData> {
    'data-id': DatatableProps<TData>['data-id'];
    bulkActionDropdownItems?: DatatableProps<TData>['bulkActionDropdownItems'];
    cantDetermineTotalCount?: boolean;
    emptyStateProps?: EmptyStateProps;
    filterProps?: FilterProps;
    filterViewModeProps?: FilterViewModeProps;
    isRowSelectionEnabled?: DatatableProps<TData>['isRowSelectionEnabled'];
    isMultiRowSelectionEnabled?: DatatableProps<TData>['isMultiRowSelectionEnabled'];
    isLoading: boolean;
    setIsAllRowsSelected: (isAllRowsSelected: boolean) => void;
    tableActions?: DatatableProps<TData>['tableActions'];
    rowActionsProps?: RowActionsProps<TData>;
    tableId: string;
    tableSearchProps: TableSearchProps;
    tableSettingsTriggerProps?: TableSettingsTriggerProps;
    onRowClick?: DatatableProps<TData>['onRowClick'];
    getSelectAllButtonText?: DatatableProps<TData>['getSelectAllButtonText'];
    disabledRowSelectionCheckboxTooltip?: DatatableProps<TData>['disabledRowSelectionCheckboxTooltip'];
    isSelectAllButtonHidden?: boolean;
    viewMode: ViewModeType;
    galleryCard?: React.ComponentType<{
        row: TData;
    }>;
    galleryCustomSkeletonCard?: React.ReactElement;
    hidePagination?: boolean;
    density?: DensityOption;
    isFullPageTable?: boolean;
    getPinnedColumnInfo?: (columnId: string) =>
        | {
              columnId: string;
              isPinned: boolean;
              isLastPinned: boolean;
              pinnedLeft: number;
              headerStyles: React.CSSProperties;
              cellStyles: React.CSSProperties;
          }
        | undefined;
}
