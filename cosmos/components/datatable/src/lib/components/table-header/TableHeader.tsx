import type { ReactNode } from 'react';
import { styled } from 'styled-components';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { neutralBackgroundMild } from '@cosmos/constants/tokens';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable components
import type { SortDirection } from '@tanstack/react-table';
import { tableColumnSizeMixin } from '../../styles';
import type { TableColumnSizeMixin } from '../../types/table-column-size-mixin.type';
import { ColumnOptionsMenu } from '../column-options-menu/ColumnOptionsMenu';

const StyledTableHeaderDiv = styled.div<
    TableColumnSizeMixin & {
        $cellPadding?: string;
        $rowMinHeight?: string;
        /**
         * NOTE: weird styled component bug I don't remember the fix for, aria-sort not used in styling but the types are complaining about it.
         */
        ['aria-sort']?: string;
    }
>`
    display: flex;
    flex-direction: column;
    justify-content: center;

    background-color: ${neutralBackgroundMild};
    position: relative;
    vertical-align: middle;
    padding: ${({ $cellPadding }) => $cellPadding};
    min-height: ${({ $rowMinHeight }) => $rowMinHeight};
    ${tableColumnSizeMixin}
`;

export interface TableHeaderProps {
    /**
     * Boolean that determines if the column is sortable.
     */
    canSort: boolean;
    /**
     * Boolean that determines if the column can be pinned.
     */
    canPin?: boolean;
    /**
     * Header content.
     */
    children: ReactNode;
    /**
     * CSS class name to apply to the header.
     */
    className?: string;
    /**
     * Inline styles to apply to the header (used for CSS custom properties).
     */
    style?: React.CSSProperties;
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * Unique identifier used to refer to this element.
     */
    headerId: string;
    /**
     * Size of the column as defined in either defaultColumnOptions or on the column itself.
     */
    size?: number | 'auto';
    /**
     * Max size of the column as defined in either defaultColumnOptions or on the column itself.
     */
    maxSize?: number | 'auto';
    /**
     * Min size of the column as defined in either defaultColumnOptions or on the column itself.
     */
    minSize?: number | 'auto';
    /**
     * Callback for sorting actions.
     */
    onSort?: (direction: 'asc' | 'desc') => void;
    /**
     * Clears sorting for this column (unsort) when the active option is toggled off.
     */
    onClearSort?: () => void;
    /**
     * Current sort direction of the column.
     */
    sortDirection?: false | SortDirection;
    /**
     * Callback for pinning the column.
     */
    onPin?: () => void;
    /**
     * Callback for unpinning the column.
     */
    onUnpin?: () => void;
    /**
     * Whether this column is currently pinned.
     */
    isPinned?: boolean;
    /**
     * Whether more columns can be pinned (for disabling pin option).
     */
    canPinMore?: boolean;
    /**
     * Padding for the cell.
     */
    cellPadding?: string;
    /**
     * Height for the row.
     */
    rowMinHeight?: string;
    /**
     * Column index for ARIA accessibility.
     */
    colIndex?: number;
    /**
     * Handle for column resizing.
     */
    resizeHandle?: React.JSX.Element;
}

export const TableHeader = ({
    canSort,
    children,
    canPin = false,
    className = undefined,
    style = undefined,
    'data-id': dataId = undefined,
    headerId,
    size = undefined,
    maxSize = undefined,
    minSize = undefined,
    onSort = undefined,
    sortDirection = false,
    onPin = undefined,
    onUnpin = undefined,
    onClearSort = undefined,
    isPinned = false,
    canPinMore = true,
    cellPadding,
    rowMinHeight,
    colIndex = undefined,
    resizeHandle = undefined,
}: TableHeaderProps): React.JSX.Element => {
    // Show options menu if we have sorting or pinning capabilities
    const showOptionsMenu = canSort || canPin;

    return (
        <StyledTableHeaderDiv
            role="columnheader"
            className={className}
            style={style}
            $cssVar={`--header-${headerId}-size`}
            $size={size}
            $maxSize={maxSize}
            $minSize={minSize}
            $cellPadding={cellPadding}
            $rowMinHeight={rowMinHeight}
            aria-colindex={colIndex}
            data-id={dataId}
            data-testid="TableHeader"
        >
            {resizeHandle}
            <Stack
                direction="row"
                align="center"
                justify="between"
                width="100%"
                data-id={`${dataId}-header-content-stack`}
            >
                <Stack
                    direction="row"
                    align="center"
                    style={{ flex: 1 }}
                    data-id={`${dataId}-text-stack`}
                >
                    <Text
                        data-id={`${dataId}-textWrapper`}
                        type="title"
                        size="100"
                    >
                        {children}
                    </Text>
                </Stack>
                {showOptionsMenu && (
                    <ColumnOptionsMenu
                        columnId={headerId}
                        data-id={`${dataId}-column-options`}
                        canSort={canSort}
                        canPin={canPin}
                        sortDirection={sortDirection}
                        isPinned={isPinned}
                        canPinMore={canPinMore}
                        onSort={onSort}
                        onClearSort={onClearSort}
                        onPin={onPin}
                        onUnpin={onUnpin}
                    />
                )}
            </Stack>
        </StyledTableHeaderDiv>
    );
};
