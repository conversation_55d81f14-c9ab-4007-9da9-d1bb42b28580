import { isEmpty } from 'lodash-es';
import {
    SchemaDropdown,
    type SchemaDropdownItemData,
} from '@cosmos/components/schema-dropdown';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type { Row, RowData, Table } from '@tanstack/react-table';
import type { DatatableTableMeta } from '../../types/datatable-table-meta.type';

interface RowActionsCellProps<TData extends RowData> {
    row: Row<TData>;
    table: Table<TData>;
}

export const RowActionsCellComponent = <TData extends RowData>({
    row,
    table,
}: RowActionsCellProps<TData>): React.JSX.Element | null => {
    const { rowActionsProps } = table.getMeta() as DatatableTableMeta<TData>;

    const rowActions = rowActionsProps?.getRowActions(row.original) ?? [];

    if (isEmpty(rowActions)) {
        return null;
    }

    const dropdownItems: SchemaDropdownItemData[] = rowActions;

    return (
        <SchemaDropdown
            isIconOnly
            colorScheme="neutral"
            data-id={`row-actions-${row.id}`}
            label="Row actions"
            startIconName="HorizontalMenu"
            level="tertiary"
            size="sm"
            a11yLabelOverride="Row actions"
            items={dropdownItems}
            data-testid="RowActionsCellComponent"
        />
    );
};
