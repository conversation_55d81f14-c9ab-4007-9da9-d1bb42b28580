import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNode } from 'react';
import { styled } from 'styled-components';
import { borderWidthSm } from '@cosmos/constants/tokens';
import { tableColumnSizeMixin } from '../../styles';
import type { TableColumnSizeMixin } from '../../types/table-column-size-mixin.type';

const StyledTableCellDiv = styled.div<
    TableColumnSizeMixin & { $cellPadding?: string }
>`
    border-top: solid ${borderWidthSm} var(--table-row-border-color);
    background-color: var(--table-row-background-color);
    vertical-align: top;
    padding: ${({ $cellPadding }) => $cellPadding};
    ${tableColumnSizeMixin}
`;

export interface TableCellProps {
    /**
     * Unique identifier used to refer to this element.
     */
    cellColumnId: string;
    children?: ReactNode;
    className?: string;
    /**
     * Inline styles to apply to the cell (used for CSS custom properties).
     */
    style?: React.CSSProperties;
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * Size of the column as defined in either defaultColumnOptions or on the column itself.
     */
    size?: number | 'auto';
    /**
     * Max size of the column as defined in either defaultColumnOptions or on the column itself.
     */
    maxSize?: number | 'auto';
    /**
     * Min size of the column as defined in either defaultColumnOptions or on the column itself.
     */
    minSize?: number | 'auto';
    /**
     * Will cause any clicks on the element to not trigger the row click callback.
     */
    onClick?: MouseEventHandler<HTMLDivElement>;
    /**
     * Padding for the cell.
     */
    cellPadding?: string;
    /**
     * Column index for ARIA accessibility.
     */
    colIndex?: number;
}

export const TableCell = ({
    cellColumnId,
    children = undefined,
    className = undefined,
    style = undefined,
    'data-id': dataId = undefined,
    size = undefined,
    maxSize = undefined,
    minSize = undefined,
    onClick = undefined,
    cellPadding = undefined,
    colIndex = undefined,
}: TableCellProps): React.JSX.Element => {
    return (
        <StyledTableCellDiv
            className={className}
            style={style}
            role="gridcell"
            $cssVar={`--col-${cellColumnId}-size`}
            $size={size}
            $maxSize={maxSize}
            $minSize={minSize}
            $cellPadding={cellPadding}
            aria-colindex={colIndex}
            data-id={dataId}
            data-testid="TableCell"
            onClick={onClick}
        >
            {children}
        </StyledTableCellDiv>
    );
};
