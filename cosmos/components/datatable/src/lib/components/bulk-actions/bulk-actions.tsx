import { useMemo } from 'react';
import { styled } from 'styled-components';
import {
    type Action,
    ActionStack,
    type Stack,
} from '@cosmos/components/action-stack';
import {
    borderRadiusLg,
    borderWidthMd,
    breakpointMd,
    dimensionXl,
    neutralBackgroundModerate,
    neutralBorderInitial,
    shadow100,
} from '@cosmos/constants/tokens';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type { RowData, Table } from '@tanstack/react-table';
import { getBulkActionsProps } from '../../helpers/get-bulk-actions-props.helper';
import { normalizeBulkActionsSize } from '../../helpers/normalize-bulk-actions-size.helper';
import type { DatatableTableMeta } from '../../types/datatable-table-meta.type';

const StyledDiv = styled.div`
    display: flex;
    width: fit-content;
    height: fit-content;
    max-width: ${breakpointMd};
    padding: ${dimensionXl};
    border-radius: ${borderRadiusLg};
    border: ${borderWidthMd} solid ${neutralBorderInitial};
    box-shadow: ${shadow100};
    background-color: ${neutralBackgroundModerate};
    overflow-x: auto;

    button > span {
        white-space: nowrap;
    }
`;

interface BulkActions<TData extends RowData> {
    table: Table<TData>;
}

export const BulkActions = <TData extends RowData>({
    table,
}: BulkActions<TData>): React.JSX.Element | null => {
    const {
        options: { meta },
    } = table;

    const { bulkActionDropdownItems = [], isSelectAllButtonHidden } =
        meta as DatatableTableMeta<TData>;

    const bulkActions = useMemo(() => {
        return normalizeBulkActionsSize(bulkActionDropdownItems);
    }, [bulkActionDropdownItems]);

    const {
        showBulkActions,
        bulkRowsSelectedCount,
        bulkActionsSelectAllButton,
    } = getBulkActionsProps(table);

    return showBulkActions ? (
        <StyledDiv>
            <ActionStack
                gap={dimensionXl}
                stacks={
                    [
                        {
                            id: 'bulk-actions-stack',
                            actions: [
                                bulkRowsSelectedCount,
                                !isSelectAllButtonHidden &&
                                    bulkActionsSelectAllButton,
                                ...bulkActions,
                            ].filter(Boolean) as Action[],
                            alignment: 'center',
                            shouldFlex: true,
                        },
                    ].filter(Boolean) as Stack[]
                }
            />
        </StyledDiv>
    ) : null;
};
