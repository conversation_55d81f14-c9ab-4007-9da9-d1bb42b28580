import { styled } from 'styled-components';
import { Stack } from '@cosmos/components/stack';
import { borderWidthSm, neutralBorderFaded } from '@cosmos/constants/tokens';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import { flexRender, type RowData, type Table } from '@tanstack/react-table';
import { getTableDensityStyles } from '../../helpers/get-table-density-styles.helper';
import type { DatatableTableMeta } from '../../types/datatable-table-meta.type';
import { ColumnResizeHandle } from '../column-resize-handle/ColumnResizeHandle';
import { TableHeader } from '../table-header/TableHeader';

const StyledTableHeadDiv = styled.div`
    outline: ${borderWidthSm} solid ${neutralBorderFaded};
    outline-offset: -1px;

    position: sticky;
    top: 0px;
    z-index: 3;
    display: flex;
    flex-direction: column;
`;

interface TableHeadProps<T extends RowData> {
    table: Table<T>;
}

export const TableHead = <T extends RowData>({
    table: {
        getHeaderGroups,
        options,
        isMaxPinnedColumnsReached,
        getPinnedColumnInfo,
    },
}: TableHeadProps<T>): React.JSX.Element => {
    const headerGroups = getHeaderGroups();
    const meta = options.meta as DatatableTableMeta<T>;

    const { cellPadding, rowMinHeight } = getTableDensityStyles(meta.density);

    return (
        <StyledTableHeadDiv
            role="rowgroup"
            data-testid="TableHead"
            data-id="KF_tu_ss"
        >
            {headerGroups.map((headerGroup, rowIndex) => {
                return (
                    <Stack
                        key={headerGroup.id}
                        role="row"
                        // ARIA row indices are 1-based: header row = 1, data rows = 2, 3, 4...
                        aria-rowindex={rowIndex + 1}
                        data-id="uQ6W5Jzn"
                        display="flex"
                        direction="row"
                    >
                        {headerGroup.headers.map((headerInstance, colIndex) => {
                            const {
                                id,
                                column: {
                                    columnDef: {
                                        header,
                                        size,
                                        maxSize,
                                        minSize,
                                    },
                                    getCanSort,
                                    getIsSorted,
                                    getIsPinned,
                                    getCanPin,
                                    toggleSorting,
                                    clearSorting,
                                    pin,
                                },
                                getContext,
                            } = headerInstance;
                            const isLastInRow =
                                colIndex === headerGroup.headers.length - 1;

                            const sortDirection = getIsSorted();
                            const isPinned = getIsPinned() === 'left';
                            const canPin = getCanPin();
                            const canPinMore =
                                !isPinned && !isMaxPinnedColumnsReached();

                            const pinnedInfo = getPinnedColumnInfo(id);
                            const pinnedClassName =
                                pinnedInfo?.headerClassName ?? '';
                            const pinnedStyles = pinnedInfo?.headerStyles;

                            return (
                                <TableHeader
                                    headerId={id}
                                    key={id}
                                    canSort={getCanSort()}
                                    data-id={`table-header-${id}`}
                                    size={isLastInRow ? 'auto' : size}
                                    maxSize={isLastInRow ? 'auto' : maxSize}
                                    minSize={minSize}
                                    cellPadding={cellPadding}
                                    sortDirection={sortDirection}
                                    isPinned={isPinned}
                                    canPinMore={canPinMore}
                                    className={pinnedClassName}
                                    style={pinnedStyles}
                                    rowMinHeight={rowMinHeight}
                                    colIndex={colIndex + 1}
                                    canPin={canPin}
                                    resizeHandle={
                                        <ColumnResizeHandle
                                            header={headerInstance}
                                            data-id={`${id}-resize-handle`}
                                        />
                                    }
                                    onSort={(direction) => {
                                        toggleSorting(direction === 'desc');
                                    }}
                                    onClearSort={() => {
                                        clearSorting();
                                    }}
                                    onPin={() => {
                                        pin('left');
                                    }}
                                    onUnpin={() => {
                                        pin(false);
                                    }}
                                >
                                    {flexRender(header, getContext())}
                                </TableHeader>
                            );
                        })}
                    </Stack>
                );
            })}
        </StyledTableHeadDiv>
    );
};
