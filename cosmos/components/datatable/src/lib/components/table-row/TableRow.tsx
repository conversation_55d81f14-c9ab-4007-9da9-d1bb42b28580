import { styled } from 'styled-components';
import { borderWidthSm } from '@cosmos/constants/tokens';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type { Row, RowData, Table } from '@tanstack/react-table';
import { getTableDensityStyles } from '../../helpers/get-table-density-styles.helper';
import { TableCell } from '../table-cell/TableCell';
import { TableCellRenderer } from '../table-cell-renderer/TableCellRenderer';
import { StyledTableRowDiv } from './styles/StyledTableRowTr.styles';

const StyledTableCell = styled(TableCell)<{ $isLastRowInPage?: boolean }>`
    border-top: solid ${borderWidthSm} var(--table-row-border-color);
    background-color: var(--table-row-background-color);
    ${({ $isLastRowInPage }) =>
        $isLastRowInPage &&
        `border-bottom: solid ${borderWidthSm} var(--table-row-border-color);`}
`;

interface TableRowProps<TData extends RowData> {
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    innerRef?: (rowElement: HTMLDivElement) => void;
    table: Table<TData>;
    row: Row<TData>;
    rowHeight: number;
    rowIndex?: number;
    /**
     * Whether this row is the last row in the current page.
     */
    isLastRowInPage?: boolean;
}

export const TableRow = <TData extends RowData>({
    'data-id': dataId = undefined,
    innerRef = undefined,
    table: { getState, getMeta, getPinnedColumnInfo },
    row,
    rowHeight,
    rowIndex = undefined,
    isLastRowInPage = false,
}: TableRowProps<TData>): React.JSX.Element => {
    const { isAllRowsSelected } = getState();
    const {
        id: rowId,
        getVisibleCells,
        getIsSelected,
        original,
        getCanSelect,
    } = row;
    const rowGroups = getVisibleCells();
    const canSelect = getCanSelect();
    const isSelected = canSelect && (isAllRowsSelected || getIsSelected());

    const { onRowClick, isLoading, density } = getMeta();
    const { cellPadding, rowMinHeight } = getTableDensityStyles(density);

    const isClickable = onRowClick !== undefined && !isLoading;

    const handleRowClick = () => {
        onRowClick?.({ row: original, _internal: row });
    };

    return (
        <StyledTableRowDiv
            role="row"
            ref={innerRef}
            data-id={dataId}
            key={rowId}
            aria-rowindex={rowIndex}
            aria-selected={isSelected}
            $isClickable={isClickable}
            $rowHeight={rowHeight}
            $rowMinHeight={rowMinHeight}
            $selected={isSelected}
            data-testid="TableRow"
        >
            {rowGroups.map(
                (
                    {
                        id,
                        column: {
                            id: cellColumnId,
                            columnDef: {
                                cell,
                                size,
                                maxSize,
                                minSize,
                                meta: { shouldIgnoreRowClick } = {},
                            },
                        },
                        getContext,
                    },
                    colIndex,
                ) => {
                    const allowOnClick = isClickable && !shouldIgnoreRowClick;
                    const isLastInRow = colIndex === rowGroups.length - 1;

                    const pinnedInfo = getPinnedColumnInfo(cellColumnId);
                    const pinnedClassName = pinnedInfo?.cellClassName ?? '';
                    const pinnedStyles = pinnedInfo?.cellStyles;

                    return (
                        <StyledTableCell
                            key={id}
                            data-id={`${dataId}-table-cell-${id}`}
                            cellColumnId={cellColumnId}
                            size={isLastInRow ? 'auto' : size}
                            maxSize={isLastInRow ? 'auto' : maxSize}
                            minSize={minSize}
                            $isLastRowInPage={isLastRowInPage}
                            cellPadding={cellPadding}
                            // ARIA column indices are 1-based for screen reader navigation
                            colIndex={colIndex + 1}
                            className={pinnedClassName}
                            style={pinnedStyles}
                            onClick={allowOnClick ? handleRowClick : undefined}
                        >
                            <TableCellRenderer
                                data-id={`${dataId}-table-cell-renderer-${id}`}
                                isLoading={isLoading}
                                cell={cell}
                                cellContext={getContext()}
                            />
                        </StyledTableCell>
                    );
                },
            )}
        </StyledTableRowDiv>
    );
};
