import { Button } from '@cosmos/components/button';
import { Dropdown } from '@cosmos/components/dropdown';
import { Icon } from '@cosmos/components/icon';
import { ListBoxItem } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable components
import type { SortDirection } from '@tanstack/react-table';
import { DEFAULT_MAX_PINNED_COLUMNS } from '../../constants/max-pinned-columns.constant';

export interface ColumnOptionsMenuProps {
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * Column ID for generating unique element IDs.
     */
    columnId: string;
    /**
     * Whether sorting is enabled for this column.
     */
    canSort?: boolean;
    /**
     * Whether pinning is enabled for this column.
     */
    canPin?: boolean;
    /**
     * Current sort direction of the column.
     */
    sortDirection?: false | SortDirection;
    /**
     * Callback for sorting actions.
     */
    onSort?: (direction: 'asc' | 'desc') => void;
    /**
     * Callback to clear sorting (unsort) when a selected sort option is toggled off.
     */
    onClearSort?: () => void;
    /**
     * Callback for pinning the column.
     */
    onPin?: () => void;
    /**
     * Callback for unpinning the column.
     */
    onUnpin?: () => void;
    /**
     * Whether this column is currently pinned.
     */
    isPinned?: boolean;
    /**
     * Whether more columns can be pinned (for disabling pin option).
     */
    canPinMore?: boolean;
    /**
     * Maximum number of columns that can be pinned (for messaging).
     */
    maxPinnedColumns?: number;
}

export const ColumnOptionsMenu = ({
    'data-id': dataId = undefined,
    columnId,
    canSort = false,
    canPin = false,
    sortDirection = false,
    onSort = undefined,
    onClearSort = undefined,
    onPin = undefined,
    onUnpin = undefined,
    isPinned = false,
    canPinMore = true,
    maxPinnedColumns = DEFAULT_MAX_PINNED_COLUMNS,
}: ColumnOptionsMenuProps): React.JSX.Element | null => {
    const isPinningDisabled = !isPinned && !canPinMore;

    // Don't render if no options are available
    if (!canSort && !canPin) {
        return null;
    }

    const handleSortAscending = () => {
        if (sortDirection === 'asc') {
            onClearSort?.();

            return;
        }
        onSort?.('asc');
    };

    const handleSortDescending = () => {
        if (sortDirection === 'desc') {
            onClearSort?.();

            return;
        }
        onSort?.('desc');
    };

    const handleTogglePin = () => {
        if (isPinned) {
            onUnpin?.();
        } else {
            onPin?.();
        }
    };

    return (
        <Dropdown
            data-id={`${dataId}-column-options-dropdown`}
            data-testid="ColumnOptionsMenu"
        >
            <Dropdown.Trigger data-id={`${dataId}-column-options-trigger`}>
                <Button
                    isIconOnly
                    level="tertiary"
                    size="sm"
                    colorScheme="neutral"
                    startIconName="HorizontalMenu"
                    label={t`Column options`}
                    data-id={`${dataId}-column-options-button`}
                />
            </Dropdown.Trigger>
            <Dropdown.Content data-id={`${dataId}-column-options-content`}>
                {canSort && (
                    <>
                        <Dropdown.Item
                            data-id={`${dataId}-sort-ascending-item`}
                        >
                            <ListBoxItem
                                id={`${columnId}-sort-asc-option`}
                                label={t`Sort ascending`}
                                data-id={`${dataId}-sort-ascending-list-item`}
                                startSlot={<Icon name="ArrowUp" size="200" />}
                                aria-selected={sortDirection === 'asc'}
                                onClick={handleSortAscending}
                            />
                        </Dropdown.Item>
                        <Dropdown.Item
                            data-id={`${dataId}-sort-descending-item`}
                        >
                            <ListBoxItem
                                id={`${columnId}-sort-desc-option`}
                                label={t`Sort descending`}
                                data-id={`${dataId}-sort-descending-list-item`}
                                startSlot={<Icon name="ArrowDown" size="200" />}
                                aria-selected={sortDirection === 'desc'}
                                onClick={handleSortDescending}
                            />
                        </Dropdown.Item>
                    </>
                )}
                {canPin && (
                    <Dropdown.Item data-id={`${dataId}-pinning-item`}>
                        <ListBoxItem
                            id={`${columnId}-pin-option`}
                            data-id={`${dataId}-pinning-list-item`}
                            label={isPinned ? t`Unpin column` : t`Pin column`}
                            description={
                                isPinned
                                    ? undefined
                                    : t`Only ${maxPinnedColumns} columns can be pinned at a time`
                            }
                            startSlot={
                                <Icon
                                    name="Pin"
                                    size="200"
                                    data-id={`${dataId}-pin-icon`}
                                    colorScheme={
                                        isPinningDisabled ? 'faded' : 'neutral'
                                    }
                                />
                            }
                            onClick={handleTogglePin}
                            {...(isPinningDisabled
                                ? {
                                      isReadOnly: true,
                                      readOnlyTooltip: t`Only ${maxPinnedColumns} columns can be pinned at a time`,
                                  }
                                : {
                                      isReadOnly: undefined,
                                  })}
                        />
                    </Dropdown.Item>
                )}
            </Dropdown.Content>
        </Dropdown>
    );
};
