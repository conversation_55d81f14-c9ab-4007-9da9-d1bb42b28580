import type { Meta } from '@storybook/react-vite';
import { Datatable } from '../datatable';
import { ARG_TYPES } from './constants';

const meta: Meta<typeof Datatable> = {
    tags: ['Stable'],
    title: 'Information & Data/Datatable',
    component: Datatable,
    argTypes: ARG_TYPES,
};

export default meta;

// Playground should be first and have all controls enabled so they are accessible on Docs page

export { Playground } from './stories';
export { ColumnPinning } from './stories';
export { Density } from './stories';
export { EmptyState } from './stories';
export { Loading } from './stories';
export { PinnedFilters } from './stories';
export { SetPage } from './stories';
export { SmallTable } from './stories';
export { TableCheckboxStatesUsability, UnpinnedFilters } from './stories';
export { VisibilityOptions } from './stories';
export { ColumnManagement } from './stories';
