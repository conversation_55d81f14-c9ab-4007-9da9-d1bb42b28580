import type { StoryObj } from '@storybook/react-vite';
import { DEFAULT_COLUMN_MAX_SIZE } from '../../constants/default-column-max-size.constant';
import { DEFAULT_COLUMN_MIN_SIZE } from '../../constants/default-column-min-size.constant';
import { Datatable } from '../../datatable';
import { SAMPLE_COLUMNS } from '../constants';
import { makePeople, type Person } from '../helpers/maker.helper';

export default {};
type Story = StoryObj<typeof Datatable<Person>>;

export const ColumnPinning: Story = {
    render: (args) => {
        const sampleData = makePeople(5);

        const columns = SAMPLE_COLUMNS;

        return (
            <Datatable
                {...args}
                data={sampleData}
                total={sampleData.length}
                data-id="column-pinning-example"
                columns={columns}
            />
        );
    },
    args: {
        'data-id': 'column-pinning-example-datatable',
        defaultColumnOptions: {
            minSize: DEFAULT_COLUMN_MIN_SIZE,
            maxSize: DEFAULT_COLUMN_MAX_SIZE,
        },
        getRowId: (row: Person) => row.id,
        hidePagination: false,
        tableSearchProps: {
            placeholder: 'Search people',
            hideSearch: true,
            debounceDelay: 300,
            defaultValue: '',
        },
        isSortable: true,
        isRowSelectionEnabled: true,
        rowActionsProps: {
            type: 'dropdown',
            getRowActions: () => [
                {
                    id: 'edit',
                    label: 'Edit',
                    onClick: () => {
                        // Handle edit action
                    },
                },
                {
                    id: 'delete',
                    label: 'Delete',
                    onClick: () => {
                        // Handle delete action
                    },
                },
            ],
        },
        tableId: 'column-pinning-example-table',
    },
    argTypes: {
        initialColumnPinning: {
            control: 'object',
            description: 'Initial column pinning state',
        },
    },
    parameters: {
        docs: {
            description: {
                story: `
Column pinning allows users to pin important columns to the left side of the table,
keeping them visible while scrolling horizontally through other columns.

## Features

- **Pin/Unpin Columns**: Click the [...] button in any column header to access pinning options
- **Maximum Limit**: Up to 3 columns can be pinned at once (configurable)
- **Sticky Positioning**: Pinned columns stay visible during horizontal scrolling
- **Visual Indicators**: Pinned columns have a subtle background color and accent border
- **Sorting Integration**: Pinned columns retain full sorting functionality

## Usage

Column pinning is enabled by default when columns have \`enablePinning !== false\`.
You can also set initial pinned columns using \`initialColumnPinning\`.
                `,
            },
        },
        controls: {
            include: ['initialColumnPinning'],
        },
    },
};
