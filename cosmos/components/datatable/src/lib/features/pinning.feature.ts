import { isNumber } from 'lodash-es';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable features
import type {
    Column,
    RowData,
    Table,
    TableFeature,
} from '@tanstack/react-table';
import { DEFAULT_MAX_PINNED_COLUMNS } from '../constants/max-pinned-columns.constant';
import styles from '../styles/pinned-columns.module.css';

export interface PinnedColumnInfo {
    columnId: string;
    isPinned: boolean;
    isLastPinned: boolean;
    pinnedLeft: number;
    headerClassName: string;
    cellClassName: string;
    headerStyles: React.CSSProperties;
    cellStyles: React.CSSProperties;
}

const getSystemColumns = <TData extends RowData>(table: Table<TData>) => {
    const allColumns = table.getAllColumns();

    const systemSelectionColumn = allColumns.find(
        (col) =>
            col.columnDef.meta?.isSystemColumn === true && col.id === 'select',
    );

    const systemActionsColumn = allColumns.find(
        (col) =>
            col.columnDef.meta?.isSystemColumn === true &&
            col.id === 'rowActions',
    );

    const systemColumnsCount = [
        systemSelectionColumn,
        systemActionsColumn,
    ].filter(Boolean).length;

    return { systemSelectionColumn, systemActionsColumn, systemColumnsCount };
};

export const PinningFeature: TableFeature = {
    createTable: <TData extends RowData>(table: Table<TData>) => {
        const { systemSelectionColumn, systemActionsColumn } =
            getSystemColumns(table);

        if (
            systemSelectionColumn &&
            systemSelectionColumn.getIsPinned() !== 'left'
        ) {
            systemSelectionColumn.pin('left');
        }

        if (
            systemActionsColumn &&
            systemActionsColumn.getIsPinned() !== 'left'
        ) {
            systemActionsColumn.pin('left');
        }

        table.getPinnedColumnInfo = (
            columnId: string,
        ): PinnedColumnInfo | undefined => {
            const column = table.getColumn(columnId);

            if (!column || column.getIsPinned() !== 'left') {
                return undefined;
            }

            const isPinned = true;

            const isSystemColumn =
                column.columnDef.meta?.isSystemColumn === true;

            const isLastPinned =
                !isSystemColumn && column.getIsLastColumn('left');

            const pinnedLeft = table.getPinnedLeftPosition(column);

            const headerClassName = isLastPinned
                ? `${styles.pinnedHeader} ${styles.lastPinnedShadow}`
                : styles.pinnedHeader;
            const cellClassName = isLastPinned
                ? `${styles.pinnedCell} ${styles.lastPinnedShadow}`
                : styles.pinnedCell;

            const pinnedStyles = {
                '--pinned-left-offset': `${pinnedLeft}px`,
            } as React.CSSProperties;

            return {
                columnId,
                isPinned,
                isLastPinned,
                pinnedLeft,
                headerClassName,
                cellClassName,
                headerStyles: pinnedStyles,
                cellStyles: pinnedStyles,
            };
        };

        table.getPinnedColumnsCount = (): number => {
            return table.getState().columnPinning.left?.length ?? 0;
        };

        table.isMaxPinnedColumnsReached = (): boolean => {
            return table.getPinnedColumnsCount() >= table.getMaxPinnedColumns();
        };

        table.getPinnedColumnIds = (): string[] => {
            return table.getState().columnPinning.left ?? [];
        };

        table.getMaxPinnedColumns = (): number => {
            const { systemColumnsCount } = getSystemColumns(table);

            return DEFAULT_MAX_PINNED_COLUMNS + systemColumnsCount;
        };

        table.getPinnedLeftPosition = (column: Column<TData>): number => {
            const startLeft = column.getStart('left');

            if (!Number.isNaN(startLeft)) {
                return startLeft;
            }

            const leftColumns = table.getLeftLeafColumns();
            const currentIndex = leftColumns.findIndex(
                (col) => col.id === column.id,
            );

            let totalWidth = 0;
            const columnsBeforeCurrent = leftColumns.slice(0, currentIndex);

            for (const col of columnsBeforeCurrent) {
                let size = col.getSize();

                if (Number.isNaN(size)) {
                    const definedSize = col.columnDef.size;

                    if (isNumber(definedSize) && Number.isFinite(definedSize)) {
                        size = definedSize;
                    } else {
                        size =
                            isNumber(col.columnDef.minSize) &&
                            Number.isFinite(col.columnDef.minSize)
                                ? col.columnDef.minSize
                                : 64;
                    }
                }

                totalWidth = totalWidth + size;
            }

            return totalWidth;
        };
    },
};
