/* eslint-disable import/no-duplicates  -- need this */
import type React from 'react';
import { type AriaAttributes, type ForwardedRef, forwardRef } from 'react';
import { Tooltip, type TooltipTriggerProps } from '@cosmos/components/tooltip';
// TODO Move types to their own package
// import type { DropdownProps } from '@cosmos/components/dropdown';
import type { StructuredListItemData } from '../StructuredListItem';
import { ListBoxItemContent } from './components/ListBoxItemContent';

type IsReadOnlyDiscriminatedUnion =
    | {
          /**
           * If true, the item will be non-interactive.
           */
          isReadOnly?: undefined;
          /**
           * Tooltip text to show when the item is isReadOnly, explaining why it's disabled.
           */
          readOnlyTooltip?: never;
      }
    | {
          /**
           * If true, the item will be non-interactive.
           */
          isReadOnly: boolean;
          /**
           * Tooltip text to show when the item is isReadOnly, explaining why it's disabled.
           */
          readOnlyTooltip: string;
      };

export type ListBoxItemProps = StructuredListItemData &
    TooltipTriggerProps & {
        /**
         * Unique testing ID for this element.
         */
        'data-id'?: string;
        /**
         * If `true`, a non-functional checkbox will be rendered and appear as checked when `aria-selected` is `true`.
         */
        showCheckbox?: boolean;
        /**
         * If `true`, a chevron icon will be rendered at the end of the item.
         */
        withCaret?: boolean;
        /* From downshift, explicitly redeclared for sake of dependency inversion  */
        // TODO: factor out into select-specific stuff when generalized
        /**
         * Automatically applied by downshift.
         */
        'aria-selected'?: AriaAttributes['aria-selected'];
        /* TODO: This prop should be required, but is not required all the way
         * through the component.  Needs refactoring and cleanup. */
        /**
         * Automatically applied by downshift.
         */
        id?: string;
        /**
         * Automatically applied by downshift.
         */
        onClick?: React.MouseEventHandler;
        /**
         * Automatically applied by downshift.
         */
        onMouseDown?: React.MouseEventHandler;
        /**
         * Automatically applied by downshift.
         */
        onMouseMove?: React.MouseEventHandler;
        /**
         * Automatically applied by downshift.
         */
        role?: 'option';
    } & IsReadOnlyDiscriminatedUnion;

/**
 * NOTE: Elements like this are a great opportunity to use a
 * wrapper with an `asChild` property, allowing this element to
 * only maintain presentational logic, but to wrap it in a more specific
 * element that's clickable and optimized specifically for a Select
 * component.  Could be a future improvement.
 */

/**
 * An individual option in an option list.  Presentation only.
 */
const BaseListBoxItem = (
    {
        isReadOnly = false,
        readOnlyTooltip = undefined,
        ...restProps
    }: ListBoxItemProps,
    ref: ForwardedRef<HTMLLIElement>,
) => {
    if (isReadOnly && readOnlyTooltip) {
        return (
            <Tooltip isInteractive text={readOnlyTooltip}>
                <ListBoxItemContent ref={ref} {...restProps} isReadOnly />
            </Tooltip>
        );
    }

    return (
        <ListBoxItemContent
            data-testid="BaseListBoxItem"
            data-id="PlVaZmUq"
            ref={ref}
            {...restProps}
            isReadOnly={isReadOnly}
        />
    );
};

export const ListBoxItem = forwardRef(BaseListBoxItem);
