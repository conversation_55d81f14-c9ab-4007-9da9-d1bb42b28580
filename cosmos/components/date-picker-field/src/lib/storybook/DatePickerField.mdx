import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as DatePickerFieldStories from './DatePickerField.stories';

<Meta of={DatePickerFieldStories} />

<Title />

<Description />

<Primary />

<Controls of={DatePickerFieldStories.Playground} />

## Import

```jsx
import { DatePickerField } from '@cosmos/components/date-picker-field';
```

## 🟢 When to use the component

- **Form date inputs** - Primary use for collecting date information in forms
- **Multi-date selection** - When users need to select multiple non-consecutive dates
- **Constrained date selection** - When certain dates should be unavailable
- Use date pickers when users need to know a date’s relationship to other days or when a date could be variable. This is often the case when the date is not memorable.

## ❌ When not to use the component

- **Date display only** - Use DateTime component for showing read-only dates
- **Date ranges** - Use DateRangeField for selecting start and end dates together

## 🛠️ How it works

The DatePickerField component provides a calendar interface for date selection with form integration.

### Usability

- Use locale and choose the date format that is appropriate for the region so users see it in a familiar format. e.g. UK follows dd/mm/yyyy.
- A Date Picker must have a label.
- Validate the input upon form submission and not instantaneously.

**Core functionality:**
- **Calendar popup** - Click input to open interactive calendar
- **Keyboard navigation** - Arrow keys and Enter/Space for date selection
- **Multiple selection modes** - Single date or multi-date selection
- **Date validation** - Built-in validation with optional unavailable date logic

### Content

**Labels and help text:**
- Use descriptive labels that explain the date's purpose (e.g., "Start Date", "Due Date")
- Use help text to provide instruction if needed. For example, If a user is setting a due date, use help text to tell them “An email reminder will be sent to you a week before this date.”
- For multi-date selection, clarify the interaction in labels or help text

**Date constraints:**
- Prevent errors by constraining the picker to only dates that are relevant to your context. For example, if a user is selecting a SOC report issue date, this has to be a date in the past. So disable any future dates.
- Use `getIsDateUnavailable` to disable inappropriate dates
- Provide clear `dateUnavailableText` explaining why dates are unavailable

**Form integration:**
- Use meaningful `name` and `formId` values for proper form submission
- Provide localized `monthSelectionFieldLabel` and `yearSelectionFieldLabel` for accessibility

### Accessibility

**What the design system provides:**
- Semantic form field structure with proper ARIA labeling
- Keyboard navigation support for calendar interactions
- Screen reader announcements for date selection
- Focus management between input field and calendar popup

**Development responsibilities:**
- Provide meaningful labels and help text
- Test keyboard navigation and screen reader compatibility
- Implement appropriate date constraints with clear messaging
- Allow multiple modes of data entry. Some users prefer entering date and time information by typing instead of selection, especially keyboard users.
- If the Date Picker has associated help text or error text, include the `aria-describedby` prop on the Date Picker. This should match the `id` of the help or error text.
    - Don't prevent form submission by disabling the submit button. A disabled button cannot be detected by assistive technologies. Use error messages to explain what information is missing or incorrect.

**Design responsibilities:**
- Ensure sufficient contrast for calendar elements
- Design clear visual indicators for unavailable dates
- Provide consistent calendar placement guidelines
