import type { CSSProperties } from 'react';
import { css, styled } from 'styled-components';
import { Stack } from '@cosmos/components/stack';
import {
    borderRadiusLg,
    borderWidthMd,
    borderWidthSm,
    dimension2xl,
    dimensionMd,
    dimensionXs,
    neutralBackgroundMild,
    neutralBackgroundSurfaceInitial,
    neutralBorderFaded,
    primaryBorderFocus,
    primaryBorderHover,
    shadow200,
} from '@cosmos/constants/tokens';
import { aiGradientBorder } from '@cosmos/helpers/styled-mixins';

// TODO: publish this as part of shared utility package
// eslint-disable-next-line react-refresh/only-export-components -- need this
export const wrapTextMixin = css`
    overflow-wrap: break-word;
    word-wrap: break-word;
    -ms-word-break: break-all;
    word-break: break-all; /* Firefox doesn't know what break-word is, it will use this instead */
    word-break: break-word;
`;

const wrapButtonControl = css`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
`;

export const StyledCardContent = styled(Stack)<{
    $isEditMode?: boolean;
}>`
    background-color: inherit;
    border-radius: ${borderRadiusLg};
    padding: 0 ${dimension2xl} ${dimension2xl};
`;

export const StyledCardContainerButton = styled.button<{
    $isRaised: boolean;
    $isEditMode: boolean;
    $cardHeight?: CSSProperties['height'];
    $isAI?: boolean;
    $isLoading?: boolean;
}>`
    all: unset;
    width: 100%;
    height: ${({ $cardHeight }) => $cardHeight || '100%'};
    display: flex;
    flex-direction: column;
    ${({ $isRaised }) => $isRaised && `box-shadow: ${shadow200};`};
    background-color: ${({ $isEditMode }) =>
        $isEditMode ? neutralBackgroundMild : neutralBackgroundSurfaceInitial};
    border-radius: ${borderRadiusLg};
    /* AI cards have no border (gradient border instead), others have default border */
    border: ${({ $isAI }) =>
        $isAI ? 'none' : `${borderWidthSm} solid ${neutralBorderFaded}`};

    &:hover {
        border-color: ${({ $isAI }) =>
            $isAI ? 'transparent' : primaryBorderHover};
        cursor: pointer;
    }

    &:focus-visible {
        outline: ${borderWidthMd} solid ${primaryBorderFocus};
        outline-offset: ${dimensionXs};
    }

    /* Apply AI gradient border when isAI is true */
    ${({ $isAI, $isEditMode, $isLoading }) =>
        $isAI &&
        css`
            --component-bg-color: ${$isEditMode ? neutralBackgroundMild : neutralBackgroundSurfaceInitial};
            --border-radius: ${borderRadiusLg};
            --ai-gradient-animation-duration: ${$isLoading ? '1s' : '0s'};
            ${aiGradientBorder}
        `}
`;

export const StyledCardContainerSection = styled.section<{
    $isRaised: boolean;
    $isEditMode: boolean;
    $cardHeight?: CSSProperties['height'];
    $isAI?: boolean;
    $isLoading?: boolean;
}>`
    height: ${({ $cardHeight }) => $cardHeight || '100%'};
    display: flex;
    flex-direction: column;
    ${({ $isRaised }) => $isRaised && `box-shadow: ${shadow200};`};
    background-color: ${({ $isEditMode }) =>
        $isEditMode ? neutralBackgroundMild : neutralBackgroundSurfaceInitial};
    border-radius: ${borderRadiusLg};
    /* AI cards have no border (gradient border instead), others have default border */
    border: ${({ $isAI }) =>
        $isAI ? 'none' : `${borderWidthSm} solid ${neutralBorderFaded}`};

    /* Apply AI gradient border when isAI is true */
    ${({ $isAI, $isEditMode, $isLoading }) =>
        $isAI &&
        css`
            --component-bg-color: ${$isEditMode ? neutralBackgroundMild : neutralBackgroundSurfaceInitial};
            --border-radius: ${borderRadiusLg};
            --ai-gradient-animation-duration: ${$isLoading ? '1s' : '0s'};
            ${aiGradientBorder}
        `}


`;

export const StyledCardTitleWrapperDiv = styled.div`
    padding: ${dimension2xl} ${dimension2xl};

    ${wrapButtonControl}
    ${wrapTextMixin};
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: start;
    gap: ${dimensionMd};
`;

export const StyledCardButtonActionDiv = styled.div<{
    $isEditMode?: boolean;
}>`
    ${wrapButtonControl};
    gap: ${dimensionMd};
    justify-content: flex-start;
    display: flex;
    position: sticky;
    bottom: 0;
    background-color: inherit;
    ${({ $isEditMode }) => $isEditMode && `padding-top: ${dimensionMd};`};
    margin-top: auto;
    & > button {
        flex-shrink: 0;
    }
`;
