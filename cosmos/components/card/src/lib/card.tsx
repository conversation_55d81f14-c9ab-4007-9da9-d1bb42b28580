import { isEmpty } from 'lodash-es';
import type { ReactNode } from 'react';
import type { CSSProperties } from 'styled-components';
import type { Action } from '@cosmos/components/action-stack';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { CardTitleWrapper } from './components';
import { CardActionsStack } from './components/card-actions-stack';
import { MAX_NUMBER_OF_BUTTONS, TESTID } from './constants';
import {
    StyledCardContainerButton,
    StyledCardContainerSection,
    StyledCardTitleWrapperDiv,
} from './styles';
import { StyledCardContent } from './styles/Card.styles';
import type { Size } from './types';

export interface CardProps {
    /**
     * The main title for the card, providing a concise description or label for the card content.
     */
    title: string;

    /**
     * Card body content.
     */
    body?: ReactNode;

    /**
     * Modifies size of elements within the card
     * when using actions prop it will resize button-like actions only.
     */
    size?: Size;

    /**
     * Internal use only, not documented in Storybook
     * Color scheme for the card. 'ai' applies gradient borders.
     */
    colorScheme?: 'ai';

    /**
     * Adds box shadow.
     */
    isRaised?: boolean;

    /**
     * Enables edit mode, modifying the card's appearance and behavior.
     * -Changes the background color to indicate that modifications are allowed.
     * - Displays action buttons inside the card body.
     * - Useful in design tools or configurations where users need to modify card content.
     */
    isEditMode?: boolean;

    /**
     * If `true`, a skeleton will render instead of body.
     */
    isLoading?: boolean;

    /**
     * Custom skeleton to be displayed when the card is in a loading state.
     */
    customSkeleton?: ReactNode;

    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;

    /**
     * Displays a tooltip when hovering over the card, if a tooltip text is provided.
     */
    tooltipText?: string;

    /**
     * Renders an additional component before the title.
     */
    slot?: ReactNode;

    /**
     * Callback on card click.
     */
    onClick?: () => void;

    /**
     * An array of `Action`s that will be displayed from left to right and top to bottom
     * in the order provided. If used with `buttonActionProps`, this prop will replace the button actions.
     */
    actions?: Action[];
    /**
     * Sets a custom height for the card container using CSS height property.
     * When provided as pixels (e.g., '300px'), it will be rendered as is.
     * You can also use any valid CSS height value (e.g., 'auto', '100vh', '50%').
     * If not provided, the card will default to 100% height of its parent container.
     */
    cardHeight?: CSSProperties['height'];
}

/**
 * The Card component is a useful tool to enclose related materials with a title.
 *
 * [Card in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=45945-70374&t=QHg4TovE4tsvKYCl-4).
 */
export const Card = ({
    title,
    body = null,
    slot,
    size = 'md',
    colorScheme,
    isRaised = false,
    isEditMode = false,
    isLoading = false,
    customSkeleton = undefined,
    'data-id': dataId = TESTID,
    tooltipText = '',
    onClick = undefined,
    actions = undefined,
    cardHeight = undefined,
}: CardProps): React.JSX.Element => {
    const isClickableCard = Boolean(onClick);

    // AI cards should never be raised
    const effectiveIsRaised = colorScheme === 'ai' ? false : isRaised;

    const isAI = colorScheme === 'ai';

    const handleClick = () => {
        if (onClick) {
            try {
                onClick();
            } catch (error) {
                // TODO: Use appCore.logger

                console.error('[Cosmos Card] handleClick(): onClick failed', {
                    title,
                    onClick,
                    error,
                });
            }
        } else {
            // This one should never happen, it is a super safety check added since the linter
            // requires checking onClick exisits because it is conditional
            // TODO: Use appCore.logger

            console.error('[Cosmos Card] handleClick(): onClick not defined', {
                title,
                onClick,
            });
        }
    };

    const cardActions = actions ?? [];

    const cardActionsToShow = cardActions.slice(0, MAX_NUMBER_OF_BUTTONS);

    if (isLoading && colorScheme !== 'ai') {
        return (
            <StyledCardContainerSection
                data-id={dataId}
                $isRaised={effectiveIsRaised}
                $isEditMode={isEditMode}
                $cardHeight={cardHeight}
                $isAI={isAI}
                $isLoading={isLoading}
            >
                {customSkeleton ? (
                    <>{customSkeleton}</>
                ) : (
                    <>
                        <StyledCardTitleWrapperDiv>
                            <Skeleton width="150px" />
                        </StyledCardTitleWrapperDiv>
                        <StyledCardContent
                            direction="column"
                            height="100%"
                            overflowX="hidden"
                            overflowY="auto"
                            position="relative"
                        >
                            <Stack direction="column" gap="3x">
                                <Skeleton width="100%" />
                                <Skeleton width="100%" />
                            </Stack>
                        </StyledCardContent>
                    </>
                )}
            </StyledCardContainerSection>
        );
    }

    return isClickableCard && !isEditMode ? (
        <StyledCardContainerButton
            data-id={dataId}
            $isRaised={effectiveIsRaised}
            $isEditMode={isEditMode}
            $cardHeight={cardHeight}
            $isAI={isAI}
            $isLoading={isLoading}
            onClick={handleClick}
        >
            <StyledCardTitleWrapperDiv>
                <CardTitleWrapper
                    slot={slot}
                    size={size}
                    title={title}
                    tooltipText={tooltipText}
                />
            </StyledCardTitleWrapperDiv>

            {body ? (
                <StyledCardContent
                    direction="column"
                    height="100%"
                    overflowX="hidden"
                    overflowY="auto"
                    position="relative"
                >
                    <Stack direction="column" gap="3x">
                        {body}
                    </Stack>
                </StyledCardContent>
            ) : null}
        </StyledCardContainerButton>
    ) : (
        <StyledCardContainerSection
            data-id={dataId}
            $isRaised={effectiveIsRaised}
            $isEditMode={isEditMode}
            $cardHeight={cardHeight}
            $isAI={isAI}
            $isLoading={isLoading}
        >
            <StyledCardTitleWrapperDiv>
                <CardTitleWrapper
                    slot={slot}
                    size={size}
                    title={title}
                    tooltipText={tooltipText}
                />

                {!isEditMode && !isEmpty(cardActionsToShow) && (
                    <CardActionsStack
                        actions={cardActionsToShow}
                        size={size}
                        isEditMode={isEditMode}
                    />
                )}
            </StyledCardTitleWrapperDiv>

            {Boolean(body) || (isEditMode && !isEmpty(cardActionsToShow)) ? (
                <StyledCardContent
                    $isEditMode={isEditMode}
                    direction="column"
                    height="100%"
                    overflowX="hidden"
                    overflowY="auto"
                    position="relative"
                >
                    {body ? (
                        <Stack direction="column" gap="6x">
                            {body}
                        </Stack>
                    ) : null}

                    {isEditMode && !isEmpty(cardActionsToShow) && (
                        <CardActionsStack
                            actions={cardActionsToShow}
                            size={size}
                            isEditMode={isEditMode}
                        />
                    )}
                </StyledCardContent>
            ) : null}
        </StyledCardContainerSection>
    );
};
