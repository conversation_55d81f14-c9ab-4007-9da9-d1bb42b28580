import { filter, flatMap, isEmpty, isEqual, isNil, uniqWith } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedMonitoringController } from '@controllers/monitoring';
import {
    activeTrackCardController,
    mapStatusToTrackStatus,
    type MonitorTrackStatus,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import type { ColorScheme } from '@cosmos/components/text';
import { eventsControllerListEventsOptions } from '@globals/api-sdk/queries';
import type { MonitorV2TrackResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    buildTrackTitle,
    formatTrackSinceFromDate,
    getConnectionIdsFromControlTest,
    getIsAnyConnectionMisconfigurated,
} from './track-card.helpers';

export class TrackCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    monitorEvents = new ObservedQuery(eventsControllerListEventsOptions);

    get isMonitorNotTestedStatus(): boolean {
        const { testDetails } = sharedMonitoringTestDetailsController;

        return !(
            testDetails?.checkResultStatus === 'READY' ||
            testDetails?.checkResultStatus === 'PREAUDIT'
        );
    }

    get hasPermissionIssues(): boolean {
        const regex = /.*is not authorized to perform*/;

        this.fetchMonitorEvents();

        if (isNil(this.monitorEvents.data?.data)) {
            return false;
        }

        const permissionErroredEvents = this.monitorEvents.data.data.filter(
            (event) =>
                !isEmpty(event.issues) &&
                event.issues.some(({ message }) =>
                    regex.test(message as string),
                ),
        );

        return !isEmpty(permissionErroredEvents);
    }

    get isDrataOrCustomTest(): boolean {
        const { testDetails } = sharedMonitoringTestDetailsController;

        return (
            testDetails?.source === 'DRATA' || testDetails?.source === 'CUSTOM'
        );
    }

    get hasConnectionError(): boolean {
        const { testDetails } = sharedMonitoringTestDetailsController;
        const { allConfiguredConnections } = sharedConnectionsController;

        const connectionIds = getConnectionIdsFromControlTest(testDetails);
        const connectionsForControlTest = uniqWith(
            filter(
                flatMap(allConfiguredConnections),
                (connection) =>
                    connection.id !== undefined &&
                    connectionIds.includes(connection.id),
            ),
            isEqual,
        );

        return getIsAnyConnectionMisconfigurated(connectionsForControlTest);
    }

    get errorMessage(): string {
        const { testDetails } = sharedMonitoringTestDetailsController;

        const { allConfiguredConnections } = sharedConnectionsController;

        if (this.hasPermissionIssues && this.isDrataOrCustomTest) {
            return 'Fix permission error';
        }

        const connectionIds = getConnectionIdsFromControlTest(testDetails);
        const connectionsForControlTest = uniqWith(
            filter(
                flatMap(allConfiguredConnections),
                (connection) =>
                    connection.id !== undefined &&
                    connectionIds.includes(connection.id),
            ),
            isEqual,
        );

        const hasConnectionError = getIsAnyConnectionMisconfigurated(
            connectionsForControlTest,
        );

        if (hasConnectionError) {
            return 'Fix connection error';
        }

        return 'Fix test error';
    }

    get modeledTrackData(): MonitorV2TrackResponseDto {
        if (this.isMonitorNotTestedStatus) {
            const { trackData } = activeTrackCardController;

            return {
                status: mapStatusToTrackStatus(
                    trackData?.status as MonitorTrackStatus,
                ) as MonitorV2TrackResponseDto['status'],
                consecutiveDays: trackData?.consecutiveDays || 0,
                startDateWithStatus:
                    trackData?.startDateWithStatus?.toString() || null,
            };
        }

        return {
            status: mapStatusToTrackStatus(
                'READY',
            ) as MonitorV2TrackResponseDto['status'],
            consecutiveDays: 0,
            startDateWithStatus: null,
        };
    }

    get title(): string {
        const { ticketingConnectionWithWriteAccess } =
            sharedConnectionsController;
        const { testDetails } = sharedMonitoringTestDetailsController;
        const checkResultStatus = testDetails?.checkResultStatus;

        return buildTrackTitle({
            hasTicketingConnection: Boolean(ticketingConnectionWithWriteAccess),
            trackStatus: checkResultStatus,
            errorTitle: this.errorMessage,
        });
    }

    get trackCardStatusMessage(): string {
        const { checkResultStatus } = sharedMonitoringTestDetailsController;

        switch (checkResultStatus) {
            case 'PASSED': {
                return 'Days passed';
            }
            case 'FAILED': {
                return 'Days failed';
            }
            case 'ERROR': {
                return 'Days erroring';
            }
            case 'READY':
            case 'PREAUDIT': {
                return 'No test results';
            }
            default: {
                return 'No test results';
            }
        }
    }

    get trackCardColorScheme(): ColorScheme {
        const { checkResultStatus } = sharedMonitoringTestDetailsController;

        switch (checkResultStatus) {
            case 'PASSED': {
                return 'success';
            }
            case 'FAILED': {
                return 'critical';
            }
            case 'ERROR': {
                return 'warning';
            }
            case 'READY':
            case 'PREAUDIT': {
                return 'neutral';
            }
            default: {
                return 'neutral';
            }
        }
    }

    get trackCardIcon(): string {
        const { checkResultStatus } = sharedMonitoringTestDetailsController;

        switch (checkResultStatus) {
            case 'ERROR': {
                return 'WarningTriangle';
            }
            case 'PASSED':
            case 'FAILED':
            case 'READY':
            case 'PREAUDIT':
            default: {
                return 'Calendar';
            }
        }
    }

    get sinceDateText(): string {
        return formatTrackSinceFromDate(
            this.modeledTrackData.startDateWithStatus,
        );
    }

    get trackCardDays(): number | string {
        const { isLoading } = activeTrackCardController;

        if (isLoading) {
            return '-';
        }

        const { consecutiveDays } = this.modeledTrackData;

        if (consecutiveDays === 0) {
            return '-';
        }

        return consecutiveDays;
    }

    get isReadyStatus(): boolean {
        return !this.isMonitorNotTestedStatus;
    }

    handleTestNow = (): void => {
        const { testDetails } = sharedMonitoringTestDetailsController;

        if (testDetails?.testId) {
            sharedMonitoringController.handleTestNow(testDetails.testId);
        }
    };

    fetchMonitorEvents(): void {
        const { testDetails } = sharedMonitoringTestDetailsController;

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                this.monitorEvents.load({
                    query: {
                        sortDir: 'DESC',
                        workspaceId: currentWorkspace?.id ?? 1,
                        source: 'AUTOPILOT',
                        testId: testDetails?.testId,
                        category: 'AUTOPILOT',
                        mostRecent: true,
                        paginated: true,
                    },
                });
            },
        );
    }
}

export const sharedTrackCardModel = new TrackCardModel();
