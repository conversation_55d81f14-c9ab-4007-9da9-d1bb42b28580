import { sharedMonitoringController } from '@controllers/monitoring';
import { activeMonitoringDetailsController } from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { snackbarController } from '@controllers/snackbar';
import { ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Metadata, type MetadataProps } from '@cosmos/components/metadata';
import { dimensionSm } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getTestResultData } from '@views/monitoring-code';
import { buildCategoryLabel } from './config/monitoring-code-header.config';

const getMonitorCodeHeaderSlot = ({
    isNew,
    isDraft,
}: {
    isNew: boolean;
    isDraft: boolean;
}) => {
    const metadataConfig: {
        condition: boolean;
        config: Pick<MetadataProps, 'colorScheme' | 'label'>;
    }[] = [
        {
            condition: isNew,
            config: {
                colorScheme: 'primary',
                label: t`New`,
            },
        },
        {
            condition: isDraft,
            config: {
                colorScheme: 'neutral',
                label: t`Draft`,
            },
        },
    ];

    return metadataConfig.find(({ condition }) => condition);
};

const handleTestNow = action(() => {
    const { testDetails } = sharedMonitoringTestDetailsController;

    if (testDetails?.testId) {
        sharedMonitoringController.handleTestNow(testDetails.testId);
    } else {
        snackbarController.addSnackbar({
            id: 'test-now-error',
            props: {
                title: t`Failed to start test`,
                description: t`An error occurred while starting the test. Please try again.`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    }
});

export class MonitoringCodePageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'monitoring-code-overview-page';

    get actionStack(): React.JSX.Element {
        return (
            <ActionStack
                data-id="page-header-action-stack"
                gap={dimensionSm}
                data-testid="MonitoringBuilderHeaderActionStack"
                actions={[
                    {
                        id: 'secondary-action-button',
                        actionType: 'button',
                        typeProps: {
                            label: 'Test now',
                            level: 'secondary',
                            onClick: handleTestNow,
                            isLoading:
                                sharedMonitoringTestDetailsController
                                    .testDetails?.checkStatus === 'TESTING' ||
                                sharedMonitoringController.isSingleTesting,
                        },
                    },
                ]}
            />
        );
    }

    get title(): string {
        return sharedMonitoringTestDetailsController.testName ?? 'LOADING...';
    }

    get slot(): React.ReactNode {
        const { monitorDetailsData } = activeMonitoringDetailsController;
        const { testDetails } = sharedMonitoringTestDetailsController;

        if (!monitorDetailsData) {
            return undefined;
        }

        if (!testDetails) {
            return undefined;
        }

        const slotDetails = getMonitorCodeHeaderSlot({
            isNew:
                (testDetails.isNew ?? false) ||
                (monitorDetailsData.isNew ?? false),
            isDraft: monitorDetailsData.isDraft ?? false,
        });

        if (!slotDetails) {
            return undefined;
        }

        return (
            <Metadata
                data-testid="monitoring-code-header-slot-metadata"
                type="tag"
                colorScheme={slotDetails.config.colorScheme}
                label={slotDetails.config.label}
            />
        );
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { checkResultStatus, testDetails, category, source } =
            sharedMonitoringTestDetailsController;

        return [
            {
                id: 'monitoring-header-latest-test-result',
                'data-id': 'monitoring-header-status',
                label: 'Latest test result',
                value: getTestResultData(checkResultStatus ?? ''),
                type: 'TAG',
            },
            {
                id: 'monitoring-header-creation-date',
                'data-id': 'monitoring-header-creation-date',
                label: 'Latest test run',
                value: formatDate(
                    'field_time',
                    testDetails?.lastCheck ?? undefined,
                ),
                type: 'TEXT',
            },
            {
                id: 'monitoring-header-test-lifecycle',
                'data-id': 'monitoring-header-test-lifecycle',
                label: 'Test lifecycle',
                value: source === 'ACORN' ? 'Codebase' : '-',
                type: 'TEXT',
            },
            {
                id: 'monitoring-header-category',
                'data-id': 'monitoring-header-category',
                label: 'Category',
                value: buildCategoryLabel(category),
                type: 'TEXT',
            },
        ];
    }
}
