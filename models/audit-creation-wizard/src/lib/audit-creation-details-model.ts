import { isNil } from 'lodash-es';
import { z } from 'zod';
import {
    sharedAuditCreationFrameworksController,
    sharedAuditCreationWizardController,
} from '@controllers/audit-creation-wizard';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';

class AuditCreationDetailsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get formSchema(): FormSchema {
        const { auditDetails, isSoc2Type1 } =
            sharedAuditCreationWizardController;

        const { filteredFrameworks, handleFrameworkChange } =
            sharedAuditCreationFrameworksController;

        const formSchema = {
            framework: {
                type: 'select',
                label: t`Framework`,
                options: filteredFrameworks,
                placeholder: t`Framework`,
                required: true,
                loaderLabel: t`Loading`,
                initialValue: auditDetails?.framework,
                onChange: handleFrameworkChange,
                validator: z.object(
                    {
                        id: z.string({
                            required_error: t`Framework is required`,
                        }),
                    },
                    {
                        required_error: t`Framework is required`,
                    },
                ),
            },
            ...(isSoc2Type1
                ? {
                      date: {
                          type: 'date',
                          label: t`Date`,
                          required: true,
                          locale: 'en-US',
                          loaderLabel: t`Loading`,
                          initialValue: auditDetails?.date
                              ?.startingDate as TDateISODate,
                          validator: z
                              .string({
                                  message: t`Please select a date`,
                              })
                              .date(t`Please select a date`),
                      },
                  }
                : {
                      dateRange: {
                          type: 'dateRange',
                          label: t`Dates`,
                          required: true,
                          locale: 'en-US',
                          loaderLabel: t`Loading`,
                          initialValue: {
                              start: auditDetails?.date?.startingDate || '',
                              end:
                                  (!isNil(auditDetails?.date?.endingDate) &&
                                      auditDetails.date.endingDate) ||
                                  '',
                          },
                          validator: z.object({
                              start: z
                                  .string({
                                      message: t`Please select a valid start date`,
                                  })
                                  .date(t`Please select a valid start date`),
                              end: z
                                  .string({
                                      message: t`Please select a valid end date`,
                                  })
                                  .date(t`Please select a valid end date`),
                          }),
                      },
                  }),
        };

        return formSchema as FormSchema;
    }
}

export const sharedAuditCreationDetailsModel = new AuditCreationDetailsModel();
