import { isNil } from 'lodash-es';
import { z } from 'zod';
import {
    sharedAuditCreationFrameworksController,
    sharedAuditCreationWizardController,
} from '@controllers/audit-creation-wizard';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import { sharedDirectLocaleController } from '@globals/i18n';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';

class AuditCreationDetailsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get formSchema(): FormSchema {
        const { auditDetails } = sharedAuditCreationWizardController;

        const { filteredFrameworks } = sharedAuditCreationFrameworksController;

        const formSchema = {
            framework: {
                type: 'select',
                label: t`Framework`,
                options: filteredFrameworks,
                placeholder: t`Framework`,
                required: true,
                loaderLabel: t`Loading`,
                initialValue: auditDetails?.framework ?? null,
                validator: z.object(
                    {
                        id: z.string({
                            required_error: t`Framework is required`,
                        }),
                    },
                    {
                        message: t`Framework is required`,
                    },
                ),
            },
            date: {
                type: 'date',
                label: t`Audit period`,
                required: true,
                locale: sharedDirectLocaleController.getLocale(),
                loaderLabel: t`Loading`,
                initialValue: auditDetails?.date?.startingDate as TDateISODate,
                shownIf: {
                    fieldName: 'framework.type',
                    operator: 'equals',
                    value: 'SOC_2_TYPE_1',
                },
                validator: z
                    .string({
                        message: t`Please select a date`,
                    })
                    .date(t`Please select a date`),
            },
            dateRange: {
                type: 'dateRange',
                label: t`Audit period`,
                required: true,
                locale: sharedDirectLocaleController.getLocale(),
                loaderLabel: t`Loading`,
                initialValue: {
                    start: auditDetails?.date?.startingDate || '',
                    end:
                        (!isNil(auditDetails?.date?.endingDate) &&
                            auditDetails.date.endingDate) ||
                        '',
                },
                shownIf: {
                    fieldName: 'framework.type',
                    operator: 'notEquals',
                    value: 'SOC_2_TYPE_1',
                },
                validator: z
                    .object(
                        {
                            start: z.string({
                                message: t`Please select a valid start date`,
                            }),
                            end: z.string({
                                message: t`Please select a valid end date`,
                            }),
                        },
                        {
                            message: t`Please select a valid range date`,
                        },
                    )
                    .refine(
                        (data) => {
                            if (!data.start || !data.end) {
                                return true;
                            }

                            return data.start !== data.end;
                        },
                        {
                            message: t`Start and end date must be different`,
                        },
                    ),
            },
        };

        return formSchema as FormSchema;
    }
}

export const sharedAuditCreationDetailsModel = new AuditCreationDetailsModel();
