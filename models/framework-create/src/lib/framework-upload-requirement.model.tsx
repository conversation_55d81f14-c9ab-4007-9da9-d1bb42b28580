import { sharedFrameworkRequirementsUploadController } from '@controllers/frameworks';
import { makeAutoObservable } from '@globals/mobx';

class FrameworkUploadRequirementModel {
    constructor() {
        makeAutoObservable(this);
    }

    get isLoadingWizard(): boolean {
        const { isPendingValidateCsv, isPendingUpdateRequirement } =
            sharedFrameworkRequirementsUploadController;

        return isPendingValidateCsv || isPendingUpdateRequirement;
    }
}

export const sharedFrameworkUploadRequirementModel =
    new FrameworkUploadRequirementModel();
