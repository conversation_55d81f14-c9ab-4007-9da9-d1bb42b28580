import type { Action } from '@cosmos/components/action-stack';
import {
    ToggleGroup,
    type ToggleGroupOption,
} from '@cosmos/components/toggle-group';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import {
    type RiskScoreType,
    sharedRiskInsightsScoreTypeModel,
} from '@models/risk-insights';

class RiskScoresCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    handleToggleChange = (value?: string) => {
        if (!value) {
            return;
        }

        if (value === 'INHERENT' || value === 'RESIDUAL') {
            sharedRiskInsightsScoreTypeModel.scoreType = value as RiskScoreType;
        }
    };

    get toggleOptions(): ToggleGroupOption[] {
        return [
            {
                label: t`Inherent`,
                value: 'INHERENT',
            },
            {
                label: t`Residual`,
                value: 'RESIDUAL',
            },
        ];
    }

    get actions(): Action[] {
        return [
            {
                id: 'explanatory-text',
                actionType: 'text',
                typeProps: {
                    children: t`Inherent risk is displayed if no residual score is available`,
                    size: '100',
                    colorScheme: 'faded',
                },
            },
            {
                id: 'score-type-toggle',
                actionType: 'cosmosUseWithCaution_customComponent',
                cosmosUseWithCaution_customComponent: (
                    <ToggleGroup
                        options={this.toggleOptions}
                        orientation="horizontal"
                        selectedOption={
                            sharedRiskInsightsScoreTypeModel.scoreType
                        }
                        onChange={this.handleToggleChange}
                    />
                ),
            } as Action,
        ];
    }
}

export const sharedRiskScoresCardModel = new RiskScoresCardModel();
