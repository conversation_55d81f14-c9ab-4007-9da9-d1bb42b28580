import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

export class MultipleRiskRegisterHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'multiple-risk-register-page';

    get title(): string {
        return t`All Registers`;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        // TODO: Get actual values from API ENG-72820
        const currentRegistersInUse = 2;
        const maxActiveRegisters = 10;

        return [
            {
                id: 'all-registers-total-registers',
                'data-id': 'all-registers-total-registers',
                label: t`Usage`,
                value: `${currentRegistersInUse} of ${maxActiveRegisters} registers in use`,
                type: 'TEXT',
            },
        ];
    }
}
