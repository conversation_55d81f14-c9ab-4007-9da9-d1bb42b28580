import { head, isEmpty } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import {
    type PolicyVersionStatus,
    sharedPolicyBuilderController,
    sharedPolicyCkEditorController,
} from '@controllers/policy-builder';
import type { FeedbackProps } from '@cosmos/components/feedback';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type {
    ClientTypeEnum,
    PolicyApprovalResponseDto,
    PolicyApprovalReviewGroupResponseDto,
    PolicyVersionResponseDto,
    PolicyWithReplaceResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, when } from '@globals/mobx';
import { formatDate, isDateWithinNextMonths } from '@helpers/date-time';
import { policiesBuilderHistoryDatatableController } from '@views/policies-builder-history';
import { policyApprovalWorkflowsController } from '@views/policy-workflows';
import { formatAssignedToText } from './helpers/format-assigned-to-text.helper';

class PolicyBuilderModel {
    constructor() {
        makeAutoObservable(this);
    }

    /**
     * ===== PRIVATE PROPERTIES =====.
     * Internal properties used only within this model.
     */

    isPolicyBuilderFirstLoading = true;

    private get policy(): PolicyWithReplaceResponseDto | null {
        return sharedPolicyBuilderController.policy;
    }

    get currentVersion() {
        return sharedPolicyBuilderController.currentVersion;
    }

    get renewalDate(): string | null {
        return this.currentVersion?.renewalDate ?? null;
    }

    get requiresAcknowledgment(): boolean | null {
        return this.currentVersion?.requiresAcknowledgment ?? null;
    }

    get requiresApproval(): boolean {
        return this.currentVersion?.policyVersionStatus === 'NEEDS_APPROVAL';
    }

    get hasExpiredRenewalDate(): boolean {
        return this.currentVersion?.hasExpiredRenewalDate ?? false;
    }

    get policyApprovals() {
        const { approvalData } = sharedPolicyBuilderController;

        return approvalData?.approvals ?? [];
    }

    get needsApprovalConfiguration(): boolean {
        return isEmpty(this.policyApprovals);
    }

    get currentApproval(): PolicyApprovalResponseDto | null {
        return head(this.policyApprovals) ?? null;
    }

    get hasMultipleTiers(): boolean {
        return (this.currentApproval?.reviewGroups.length ?? 0) > 1;
    }

    get firstReviewGroup(): PolicyApprovalReviewGroupResponseDto | null {
        return head(this.currentApproval?.reviewGroups) ?? null;
    }

    private get assignedTo(): string {
        return this.policy?.assignedTo ?? '';
    }

    private get groups() {
        return this.policy?.groups ?? [];
    }

    get policyGracePeriodSLAs() {
        return this.policy?.policyGracePeriodSLAs ?? [];
    }

    get policyWeekTimeFrameSLAs() {
        return this.policy?.policyWeekTimeFrameSLAs ?? [];
    }

    get policyP3MatrixSLAs() {
        return this.policy?.policyP3MatrixSLAs ?? [];
    }

    get hasWeekTimeFrameSLAs() {
        return !isEmpty(this.policyWeekTimeFrameSLAs);
    }

    get hasGracePeriodSLAs() {
        return !isEmpty(this.policyGracePeriodSLAs);
    }

    get hasP3MatrixSLAs() {
        return !isEmpty(this.policyP3MatrixSLAs);
    }

    private get linkedFrameworks() {
        return sharedPolicyBuilderController.policyFrameworksAssociated;
    }

    private get hasLinkedFrameworks(): boolean {
        return !isEmpty(this.linkedFrameworks);
    }

    private get replacedPolicies() {
        return this.policy?.replacedPolicies ?? [];
    }

    replacedPoliciesNames() {
        return this.replacedPolicies.map((policy) => policy.name);
    }

    /**
     * Initializes a one-time watcher for the initial loading state of the Policy Builder.
     * Call this right after triggering data loads (e.g., in the route clientLoader).
     */
    startInitialLoadingWatcher() {
        this.isPolicyBuilderFirstLoading = true;
        when(
            () => {
                return (
                    !sharedPolicyBuilderController.isPolicyLoading &&
                    !sharedPolicyBuilderController.isVersionLoading &&
                    !sharedPolicyBuilderController.isFrameworksLoading &&
                    !sharedPolicyBuilderController.isControlsLoading &&
                    !policiesBuilderHistoryDatatableController.isPolicyVersionHistoryQueryLoading &&
                    !policyApprovalWorkflowsController.isPolicyApprovalConfigurationLoading
                );
            },
            () => {
                this.isPolicyBuilderFirstLoading = false;
            },
        );
    }

    /**
     * ===== OVERVIEW VIEW =====.
     * Properties and logic specific to the policy overview view,
     * we use only frameworks loading because the other data is fetched in the root client loader for the policy header.
     */
    get isPolicyOverviewViewLoading(): boolean {
        return (
            this.isPolicyBuilderFirstLoading ||
            (sharedPolicyBuilderController.isFrameworksLoading &&
                isEmpty(
                    sharedPolicyBuilderController.policyFrameworksAssociated,
                ))
        );
    }

    /**
     * ===== PUBLIC PROPERTIES =====.
     * Properties exposed to external components.
     */

    get policyName(): string {
        return this.policy?.name ?? '';
    }

    get policyDescription(): string {
        return this.policy?.currentDescription ?? '';
    }

    get assignedToText(): string {
        return formatAssignedToText(this.assignedTo, this.groups);
    }

    get shouldDisplaySLA(): boolean {
        return (
            !isEmpty(this.policyGracePeriodSLAs) ||
            !isEmpty(this.policyWeekTimeFrameSLAs) ||
            !isEmpty(this.policyP3MatrixSLAs)
        );
    }

    /**
     * ===== POLICY TYPE AND STATE PROPERTIES =====.
     * Business logic properties for determining policy state and type.
     */

    get isNewDrataTemplatePolicy(): boolean {
        return (
            this.policy?.templateId !== null &&
            this.policy?.currentPublishedPolicyVersion === null
        );
    }

    get isCustomPolicy(): boolean {
        return this.policy?.templateId === null;
    }

    get isAuthoredPolicy(): boolean {
        return this.currentVersion?.type === 'BUILDER';
    }

    get isUploadedPolicy(): boolean {
        return this.currentVersion?.type === 'UPLOADED';
    }

    get hasNotionOrConfluenceConnection(): boolean {
        const externalPolicyConnections =
            sharedConnectionsController.loadConfiguredConnectionsByProviderType(
                'EXTERNAL_POLICY',
            );

        return externalPolicyConnections.some(
            (connection) =>
                (connection.clientType === 'CONFLUENCE' ||
                    connection.clientType === 'NOTION') &&
                connection.state === 'ACTIVE',
        );
    }

    get externalPolicyProvider(): ClientTypeEnum | null {
        if (this.hasExternalConnection) {
            return (
                // We only support one external policy connection at a time
                head(
                    sharedConnectionsController.loadConfiguredConnectionsByProviderType(
                        'EXTERNAL_POLICY',
                    ),
                )?.clientType ?? null
            );
        }

        return null;
    }

    get renewalDateDisplay(): {
        value: KeyValuePairProps['value'];
        feedbackProps?: FeedbackProps;
    } {
        if (!this.renewalDate) {
            return {
                value: '-',
            };
        }
        const shouldShowFeedback = isDateWithinNextMonths(2, this.renewalDate);

        if (shouldShowFeedback) {
            const title = formatDate('sentence', this.renewalDate);

            let severity: FeedbackProps['severity'];
            let description: string;

            if (this.hasExpiredRenewalDate) {
                severity = 'critical';
                description = t`This policy is past its renewal date. Update the renewal date and if necessary renew the policy to send for approval and/or acknowledgement by personnel.`;
            } else {
                severity = 'warning';
                description = t`The renewal date for this policy is coming up soon. Update the renewal date and if necessary renew the policy to send for approval and/or acknowledgement by personnel.`;
            }

            return {
                value: null,
                feedbackProps: {
                    severity,
                    title,
                    description,
                },
            };
        }

        return {
            value: formatDate('sentence', this.renewalDate),
        };
    }

    get frameworksDisplay(): {
        type: KeyValuePairProps['type'];
        value: KeyValuePairProps['value'];
    } {
        if (this.hasLinkedFrameworks) {
            return {
                type: 'TAG',
                value: this.linkedFrameworks.map((framework) => ({
                    label: framework.pill,
                    colorScheme: 'primary' as const,
                })),
            };
        }

        return {
            type: 'TEXT',
            value: '-',
        };
    }

    get disclaimer(): string {
        return this.policy?.disclaimer ?? '';
    }

    get replacedPoliciesCount(): string {
        return this.replacedPolicies.length.toString();
    }

    get isDraft(): boolean {
        return this.currentVersion?.policyVersionStatus === 'DRAFT';
    }

    get isApproved(): boolean {
        return this.currentVersion?.policyVersionStatus === 'APPROVED';
    }

    get isPublished(): boolean {
        return this.currentVersion?.policyVersionStatus === 'PUBLISHED';
    }

    get isApprovedOrPublished(): boolean {
        return this.isApproved || this.isPublished;
    }

    get clientType(): PolicyVersionResponseDto['clientType'] {
        return this.currentVersion?.clientType;
    }

    get approvedAt(): PolicyVersionResponseDto['approvedAt'] {
        return this.currentVersion?.approvedAt ?? null;
    }

    get isBambooHRProvider(): boolean {
        return (
            this.clientType === 'BAMBOO_HR' ||
            this.clientType === 'MERGEDEV_BAMBOO_HR'
        );
    }

    get hasPublishedVersion(): boolean {
        return Boolean(this.publishedVersionId);
    }

    get hasDraftVersion(): boolean {
        return this.draftVersionId !== this.publishedVersionId;
    }

    get publishedVersionId(): number | null {
        return this.policy?.currentPublishedPolicyVersion?.id ?? null;
    }

    get draftVersionId(): number | null {
        return this.policy?.latestPolicyVersion?.id ?? null;
    }

    get latestPolicyVersion() {
        return this.policy?.latestPolicyVersion ?? null;
    }

    get policyId(): number {
        return this.policy?.id ?? 0;
    }

    get currentStatus(): PolicyVersionStatus | undefined {
        return this.currentVersion?.policyVersionStatus;
    }

    get currentVersionId(): number | null {
        return this.currentVersion?.id ?? null;
    }

    get hasTemplate(): boolean {
        return Boolean(this.policy?.templateId);
    }

    get currentMajorVersion(): number {
        return this.policy?.currentPublishedPolicyVersion?.version ?? 1;
    }

    get currentMinorVersion(): number {
        return this.policy?.currentPublishedPolicyVersion?.subVersion ?? 0;
    }

    get nextMajorVersion(): number {
        return this.currentMajorVersion + 1;
    }

    get nextMinorVersion(): number {
        return this.currentMinorVersion + 1;
    }

    get currentVersionHtml(): string {
        return sharedPolicyBuilderController.currentVersion?.html ?? '';
    }

    get htmlContent(): string {
        return sharedPolicyCkEditorController.htmlContent;
    }

    get hasHtmlContent(): boolean {
        return !isEmpty(this.htmlContent);
    }

    get currentExplanationOfChanges(): string {
        return this.currentVersion?.changesExplanation ?? '';
    }

    get isPolicyCkEditorInEditMode(): boolean {
        return sharedPolicyCkEditorController.isCkEditorInEditMode;
    }

    get hasPolicyCkEditorUnsavedChanges(): boolean {
        return sharedPolicyCkEditorController.hasUnsavedChanges;
    }

    get canEnterPolicyCkEditorEditMode(): boolean {
        return sharedPolicyCkEditorController.canEnterEditMode;
    }

    /**
     * ===== APPROVAL DATA =====.
     * Approval-related properties.
     */

    get latestApprovalId(): number | null {
        return sharedPolicyBuilderController.latestApprovalId;
    }

    get currentUserReviewId(): number | null {
        return sharedPolicyBuilderController.currentUserReviewId;
    }

    /**
     * Check if there are changes requested in the approval process.
     */
    get hasChangesRequested(): boolean {
        return sharedPolicyBuilderController.hasChangesRequested;
    }

    get shouldShowChangesRequestedBanner(): boolean {
        return this.hasChangesRequested && this.requiresApproval;
    }

    /**
     * Get the name of the user who requested changes.
     */
    get changesRequestedBy(): string | null {
        return sharedPolicyBuilderController.changesRequestedBy;
    }

    get isCurrentUserApprover(): boolean {
        return sharedPolicyBuilderController.isCurrentUserApprover;
    }

    /**
     * ===== PUBLISHED STATUS BUSINESS LOGIC =====.
     * Logic for published status actions.
     */
    get isTheLastPolicyVersionInDraftStatus(): boolean {
        // Check if current version is published and there's a newer draft version
        const isPublished = this.currentStatus === 'PUBLISHED';
        const hasNewerVersion =
            this.policy?.latestPolicyVersion?.id !== this.currentVersion?.id;

        return Boolean(isPublished && hasNewerVersion);
    }

    get showPublishButtonActions(): boolean {
        return this.isTheLastPolicyVersionInDraftStatus;
    }

    get latestVersionId(): number | null {
        return this.policy?.latestPolicyVersion?.id ?? null;
    }

    get hasExternalConnection(): boolean {
        return !isEmpty(
            sharedConnectionsController.loadConfiguredConnectionsByProviderType(
                'EXTERNAL_POLICY',
            ),
        );
    }

    get latestIsDraft(): boolean {
        return (
            this.policy?.latestPolicyVersion?.policyVersionStatus === 'DRAFT'
        );
    }

    get isExternalPolicy(): boolean {
        // Check if this is an external policy type
        return this.currentVersion?.type === 'EXTERNAL' || false;
    }

    get isPolicyVersionOwner(): boolean {
        // Check if current user is the policy version owner
        const { currentVersion } = sharedPolicyBuilderController;

        if (!currentVersion?.owner) {
            return false;
        }

        const currentUserId = sharedCurrentUserController.entryId;

        return currentVersion.owner.entryId === currentUserId;
    }
}

export const sharedPolicyBuilderModel = new PolicyBuilderModel();
