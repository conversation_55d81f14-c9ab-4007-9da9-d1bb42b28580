import { getPolicyStatusLabel } from '@components/policies';
import {
    sharedPolicyBuilderController,
    sharedPolicyHeaderActionsController,
} from '@controllers/policy-builder';
import { Banner, type BannerProps } from '@cosmos/components/banner';
import { Button } from '@cosmos/components/button';
import type {
    ContentType,
    KeyValuePairProps,
} from '@cosmos/components/key-value-pair';
import { Metadata, type MetadataProps } from '@cosmos/components/metadata';
import type { UserResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { getUserInitials } from '@helpers/user';
import { sharedPolicyBuilderModel } from '@models/policy-builder';

export class PolicyBuilderHeaderModel {
    readonly pageId = 'policy-builder';

    constructor() {
        makeAutoObservable(this);
    }

    private get policy() {
        return sharedPolicyBuilderController.policy;
    }

    private get shouldShowVersionBanner(): boolean {
        const { isDraft, isPublished, hasPublishedVersion, hasDraftVersion } =
            sharedPolicyBuilderModel;

        return (
            (isDraft && hasPublishedVersion) || (isPublished && hasDraftVersion)
        );
    }

    get isLoading(): boolean {
        return sharedPolicyBuilderModel.isPolicyBuilderFirstLoading;
    }

    get title(): string {
        return sharedPolicyBuilderModel.policyName;
    }

    get actionStack(): React.JSX.Element {
        return this.policyHeaderActionsController.actionStack;
    }

    private get policyHeaderActionsController() {
        return sharedPolicyHeaderActionsController;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const pairs = [];
        const { currentVersion } = sharedPolicyBuilderController;

        if (currentVersion?.policyVersionStatus) {
            const { label, colorScheme } = getPolicyStatusLabel(
                currentVersion.policyVersionStatus,
            );

            pairs.push(
                this.buildBadgeKeyValue(
                    t`Status`,
                    'policy-status',
                    label,
                    colorScheme,
                ),
            );
        }

        if (currentVersion?.createdAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Created on`,
                    'policy-creation-date',
                    currentVersion.createdAt,
                ),
            );
        }
        if (currentVersion?.approvedAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Approved on`,
                    'policy-approved-date',
                    currentVersion.approvedAt,
                ),
            );
        }
        if (currentVersion?.publishedAt) {
            pairs.push(
                this.buildKeyValue(
                    t`Published on`,
                    'policy-published-date',
                    currentVersion.publishedAt,
                ),
            );
        }

        if (this.policy?.currentOwner) {
            pairs.push(
                this.buildUserKeyValue(
                    t`Owner`,
                    'policy-owner',
                    this.policy.currentOwner,
                ),
            );
        }

        return pairs;
    }

    get slot(): React.JSX.Element {
        return (
            <Metadata
                key="version"
                label={this.versionLabel}
                type="tag"
                colorScheme={
                    sharedPolicyBuilderModel.isDraft ? 'warning' : 'neutral'
                }
            />
        );
    }

    get banner(): React.JSX.Element | undefined {
        const {
            isDraft,
            publishedVersionId,
            draftVersionId,
            changesRequestedBy,
            shouldShowChangesRequestedBanner,
            hasExpiredRenewalDate,
            currentVersion,
        } = sharedPolicyBuilderModel;
        const { switchToPolicyVersion } = sharedPolicyBuilderController;

        const handleGoToPublishedVersion = (): void => {
            if (publishedVersionId) {
                switchToPolicyVersion(publishedVersionId);
            }
        };

        const handleGoToDraftVersion = (): void => {
            if (draftVersionId) {
                switchToPolicyVersion(draftVersionId);
            }
        };

        const conditions: {
            when: unknown;
            data: BannerProps;
        }[] = [
            {
                when: shouldShowChangesRequestedBanner,
                data: {
                    severity: 'warning',
                    title: t`Changes requested`,
                    body: changesRequestedBy
                        ? t`${changesRequestedBy} requested changes to this version. See review and approval section for details.`
                        : t`Changes were requested for this version. See review and approval section for details.`,
                    'data-id': 'policy-builder-changes-requested-banner',
                },
            },
            {
                when: this.shouldShowVersionBanner && isDraft,
                data: {
                    severity: 'primary',
                    title: t`This is a draft version`,
                    'data-id': 'policy-builder-draft-banner',
                    action: (
                        <Button
                            label={t`View latest published version`}
                            size="sm"
                            level="secondary"
                            data-id="draft-banner-published-button"
                            onClick={handleGoToPublishedVersion}
                        />
                    ),
                },
            },
            {
                when: this.shouldShowVersionBanner && !isDraft,
                data: {
                    severity: 'primary',
                    title: t`This is a published version`,
                    body: t`You have an unpublished draft version`,
                    'data-id': 'policy-builder-published-banner',
                    action: (
                        <Button
                            label={t`View draft version`}
                            size="sm"
                            level="secondary"
                            data-id="published-banner-draft-button"
                            onClick={handleGoToDraftVersion}
                        />
                    ),
                },
            },
            {
                when: !this.policy?.currentOwner,
                data: {
                    severity: 'critical',
                    title: t`Owner is missing`,
                    body: t`Drata requires an owner for all policies.`,
                },
            },
            {
                when:
                    hasExpiredRenewalDate &&
                    ['DRAFT', 'NEEDS_APPROVAL', 'APPROVED'].includes(
                        currentVersion?.policyVersionStatus ?? '',
                    ),
                data: {
                    severity: 'critical',
                    title: t`Update renewal date`,
                    body: t`Before finalizing, make sure your renewal date is updated.`,
                },
            },
            {
                when: currentVersion?.externalProvider,
                data: {
                    severity: 'warning',
                    title: t`External policy sync`,
                    body: t`This policy is synced with an external source.`,
                },
            },
            {
                when: isDraft && currentVersion?.approvedAt,
                data: {
                    severity: 'warning',
                    title: t`Changes requested`,
                    body: t`Changes have been requested for this policy version.`,
                },
            },
        ];

        const matchedCondition = conditions.find((c) => c.when);

        if (!matchedCondition) {
            return undefined;
        }

        const {
            severity,
            title,
            body,
            action,
            'data-id': dataId,
        } = matchedCondition.data;

        return (
            <Banner
                severity={severity}
                title={title}
                body={body}
                action={action}
                data-id={dataId}
                displayMode="section"
            />
        );
    }

    private get versionLabel() {
        const { currentVersion, isDraft } = sharedPolicyBuilderModel;

        if (isDraft) {
            return t`Draft`;
        }
        if (currentVersion?.composedVersion) {
            const version = currentVersion.composedVersion;

            return t`V${version}`;
        }

        return t`V1.0`;
    }

    private buildKeyValue(
        label: string,
        id: string,
        date: string,
    ): KeyValuePairProps {
        return {
            id,
            label,
            value: new Date(date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
            }),
            type: 'TEXT' as ContentType,
        };
    }

    private buildUserKeyValue(
        label: string,
        id: string,
        user: UserResponseDto,
    ): KeyValuePairProps {
        const fullName = getFullName(user.firstName, user.lastName);
        const initials = getUserInitials(user);

        return {
            id,
            label,
            value: {
                username: fullName,
                avatarProps: {
                    fallbackText: initials,
                    imgSrc: user.avatarUrl ?? '',
                    imgAlt: fullName,
                },
            },
            type: 'USER' as ContentType,
        };
    }

    private buildBadgeKeyValue(
        label: string,
        id: string,
        badgeLabel: string,
        colorScheme: MetadataProps['colorScheme'],
    ): KeyValuePairProps {
        return {
            id,
            label,
            value: {
                label: badgeLabel,
                colorScheme,
            },
            type: 'BADGE' as ContentType,
        };
    }
}
