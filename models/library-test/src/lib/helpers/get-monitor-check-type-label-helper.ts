import type { FailsByCategoryResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getMonitorCheckTypeLabel = (
    category: FailsByCategoryResponseDto['category'],
): string => {
    switch (category) {
        case 'POLICY': {
            return t`Policy`;
        }
        case 'IN_DRATA': {
            return t`In Drata`;
        }
        case 'AGENT': {
            return t`Device`;
        }
        case 'INFRASTRUCTURE': {
            return t`Infrastructure`;
        }
        case 'CUSTOM': {
            return t`Custom`;
        }
        case 'VERSION_CONTROL': {
            return t`Version Control`;
        }
        case 'IDENTITY': {
            return t`Identity Provider`;
        }
        case 'TICKETING': {
            return t`Ticketing`;
        }
        case 'OBSERVABILITY': {
            return t`Observability`;
        }
        case 'HRIS': {
            return t`HRIS`;
        }
        default: {
            return t`Unknown`;
        }
    }
};
