import type { <PERSON>lientLoader } from '@app/types';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { Params } from '@remix-run/react';
import { AppLink } from '@ui/app-link';

class OnboardingFlowsHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    getClientLoader(params: Params): ClientLoader {
        const registerId = params.registerId ?? 1;

        return {
            pageHeader: {
                title: t`Get started with risk management`,
                pageId: 'risk-register-onboarding-learn',
                backLink: (
                    <AppLink
                        href={`risk/management/registers/${registerId}/register-risks`}
                        size="sm"
                    >{t`Back to risk register`}</AppLink>
                ),
                isCentered: true,
                actionStack: (
                    <ActionStack
                        actions={[
                            {
                                actionType: 'button',
                                id: 'risk-register-onboarding-learn-action-stack',
                                typeProps: {
                                    label: t`Exit onboarding`,
                                    level: 'tertiary',
                                    colorScheme: 'neutral',
                                    onClick: () => {
                                        sharedProgrammaticNavigationController.navigateTo(
                                            `${routeController.userPartOfUrl}/risk/management/registers/${registerId}/register-risks`,
                                        );
                                    },
                                },
                            },
                        ]}
                    />
                ),
            },
            contentNav: {
                tabs: [],
            },
        };
    }
}

export const sharedOnboardingFlowsHeaderModel =
    new OnboardingFlowsHeaderModel();
