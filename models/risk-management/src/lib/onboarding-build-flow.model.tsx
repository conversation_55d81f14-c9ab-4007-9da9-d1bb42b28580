import { sharedOnboardingBuildFlowController } from '@controllers/risk';
import { routeController } from '@controllers/route';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';
import {
    getRiskGroupValue,
    type RiskGroup,
} from '@views/risk-register-onboarding-build';

export class OnboardingBuildFlowModel {
    constructor() {
        makeAutoObservable(this);
    }

    get riskRegisterToRedirectLink(): string {
        return `${routeController.userPartOfUrl}/risk/management/registers/${routeController.currentParams.registerId}/register-risks`;
    }

    getInitialValue = (riskGroup: RiskGroup): string | string[] => {
        const { riskGroups } = sharedOnboardingBuildFlowController;

        if (riskGroup === 'AI_DEVELOPMENT' || riskGroup === 'AI_USE') {
            const initialValue = [];

            if (riskGroups.has('AI_USE')) {
                initialValue.push('AI_USE');
            }
            if (riskGroups.has('AI_DEVELOPMENT')) {
                initialValue.push('AI_DEVELOPMENT');
            }

            return initialValue;
        }

        return riskGroups.has(riskGroup) ? riskGroup : '';
    };

    getStepSchema = (riskGroup: RiskGroup): FormSchema => {
        switch (riskGroup) {
            case 'AI_DEVELOPMENT':
            case 'AI_USE': {
                return {
                    riskGroups: {
                        type: 'checkboxGroup',
                        label: t`Artificial intelligence options`,
                        isOptional: true,
                        options: [
                            {
                                label: t`My organization develops AI systems`,
                                value: getRiskGroupValue('AI_DEVELOPMENT'),
                            },
                            {
                                label: t`My organization utilizes AI systems`,
                                value: getRiskGroupValue('AI_USE'),
                            },
                        ],
                        initialValue: this.getInitialValue(
                            'AI_DEVELOPMENT',
                        ) as RiskGroup[],
                        shouldHideLabel: true,
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                    },
                };
            }
            case 'PHYSICAL_SITE': {
                return {
                    riskGroups: {
                        type: 'radioGroup',
                        label: t`My organization owns or operates a physical site`,
                        options: [
                            {
                                label: t`Yes`,
                                value: getRiskGroupValue('PHYSICAL_SITE'),
                            },
                            { label: t`No`, value: '' },
                        ],
                        initialValue: this.getInitialValue(
                            'PHYSICAL_SITE',
                        ) as RiskGroup,
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                    },
                };
            }
            case 'CLOUD_ENVIRONMENT': {
                return {
                    riskGroups: {
                        type: 'radioGroup',
                        label: t`My organization uses cloud environments (e.g., AWS, Azure, GCP)`,
                        options: [
                            {
                                label: t`Yes`,
                                value: getRiskGroupValue('CLOUD_ENVIRONMENT'),
                            },
                            { label: t`No`, value: '' },
                        ],
                        initialValue: this.getInitialValue(
                            'CLOUD_ENVIRONMENT',
                        ) as RiskGroup,
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                    },
                };
            }
            case 'UNSECURE_DEVICES': {
                return {
                    riskGroups: {
                        type: 'radioGroup',
                        label: t`Personnel can use company-issued devices in non-secure settings (e.g. co-working spaces or coffee shops)`,
                        options: [
                            {
                                label: t`Yes`,
                                value: getRiskGroupValue('UNSECURE_DEVICES'),
                            },
                            { label: t`No`, value: '' },
                        ],
                        initialValue: this.getInitialValue(
                            'UNSECURE_DEVICES',
                        ) as RiskGroup,
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                    },
                };
            }
            case 'DEVICE_DELIVERY': {
                return {
                    riskGroups: {
                        type: 'radioGroup',
                        label: t`My organization physically ships devices to its personnel`,
                        options: [
                            {
                                label: t`Yes`,
                                value: getRiskGroupValue('DEVICE_DELIVERY'),
                            },
                            { label: t`No`, value: '' },
                        ],
                        initialValue: this.getInitialValue(
                            'DEVICE_DELIVERY',
                        ) as RiskGroup,
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                    },
                };
            }
            case 'SOFTWARE_DEVELOPMENT': {
                return {
                    riskGroups: {
                        type: 'radioGroup',
                        label: t`My organization develops software in-house`,
                        options: [
                            {
                                label: t`Yes`,
                                value: getRiskGroupValue(
                                    'SOFTWARE_DEVELOPMENT',
                                ),
                            },
                            { label: t`No`, value: '' },
                        ],
                        initialValue: this.getInitialValue(
                            'SOFTWARE_DEVELOPMENT',
                        ) as RiskGroup,
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                    },
                };
            }
            case 'REGULATORY_REQUIREMENTS': {
                return {
                    riskGroups: {
                        type: 'radioGroup',
                        label: t`My organization is bound by information security or privacy regulatory requirements`,
                        options: [
                            {
                                label: t`Yes`,
                                value: getRiskGroupValue(
                                    'REGULATORY_REQUIREMENTS',
                                ),
                            },
                            { label: t`No`, value: '' },
                        ],
                        initialValue: this.getInitialValue(
                            'REGULATORY_REQUIREMENTS',
                        ) as RiskGroup,
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                    },
                };
            }
            default: {
                throw new Error('Unknown risk group');
            }
        }
    };

    get isPending(): boolean {
        const { onboardingResponseIsPending } =
            sharedOnboardingBuildFlowController;

        return onboardingResponseIsPending;
    }
}

export const sharedOnboardingBuildFlowModel = new OnboardingBuildFlowModel();
