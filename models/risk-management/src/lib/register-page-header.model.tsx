import {
    FlatfileEntityEnum,
    sharedFlatfileController,
} from '@controllers/flatfile';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { sharedRiskLibraryController } from '@controllers/risk';
import { routeController } from '@controllers/route';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { dimension3x } from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, runInAction } from '@globals/mobx';

export class RegisterPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    get title(): string {
        // TODO: Replace with actual register details when controller is built
        return sharedRiskLibraryController.isMultipleRegisters
            ? t`Placeholder register name`
            : t`Risk management`;
    }

    get description(): string {
        // TODO: Replace with actual register details when controller is built
        return t`Placeholder register description`;
    }

    get groupActions(): {
        id: string;
        label: string;
        description?: string;
        type: string;
        startIconName: string;
        onSelect: () => void;
    }[] {
        const actions = [
            {
                id: 'bulk-import',
                label: t`Create/update risks in bulk`,
                type: 'item',
                startIconName: 'Settings',
                onSelect: () => {
                    runInAction(() => {
                        sharedFlatfileController.createSpace({
                            entityType: FlatfileEntityEnum.RISK,
                        });
                    });
                },
            },
        ];

        // Only include create register action if multiple risk registers feature is enabled
        if (
            !sharedRiskLibraryController.isMultipleRegisters &&
            sharedFeatureAccessModel.isMultipleRiskRegistersEnabled
        ) {
            actions.unshift({
                id: 'create-register',
                label: t`Create register`,
                type: 'item',
                startIconName: 'Plus',
                onSelect: () => {
                    sharedProgrammaticNavigationController.navigateTo(
                        `${routeController.userPartOfUrl}/risk/management/create-register`,
                    );
                },
            });
        }

        return actions;
    }

    get actions(): Action[] {
        if (sharedFeatureAccessModel.isReleaseBulkImportRiskEnabled) {
            return [
                {
                    actionType: 'dropdown',
                    id: 'risk-management-actions-dropdown',
                    typeProps: {
                        label: t`Create risk`,
                        level: 'primary',
                        endIconName: 'ChevronDown',
                        align: 'end',
                        items: [
                            {
                                id: 'create-risk-group',
                                type: 'group',
                                items: [
                                    {
                                        id: 'create-risk',
                                        label: t`Create a single risk`,
                                        type: 'item',
                                        startIconName: 'Plus',
                                        onSelect: () => {
                                            runInAction(() => {
                                                sharedProgrammaticNavigationController.navigateTo(
                                                    `${routeController.userPartOfUrl}/risk/management/registers/1/create-risk`,
                                                );
                                            });
                                        },
                                    },
                                    ...this.groupActions,
                                ],
                            },
                        ],
                    },
                },
            ];
        }

        return [
            {
                actionType: 'button',
                id: 'create-risk-button',
                typeProps: {
                    label: t`Create risk`,
                    level: 'primary',
                    onClick: () => {
                        sharedProgrammaticNavigationController.navigateTo(
                            `${routeController.userPartOfUrl}/risk/management/registers/1/create-risk`,
                        );
                    },
                },
            },
        ];
    }

    get actionStack(): React.JSX.Element {
        return (
            <ActionStack
                data-id="risk-register-details-action-stack"
                gap={dimension3x}
                stacks={[
                    {
                        actions: this.actions,
                        id: 'risk-register-details-actions-stack',
                    },
                ]}
            />
        );
    }

    get keyValuePairs(): KeyValuePairProps[] {
        if (!sharedRiskLibraryController.isMultipleRegisters) {
            return [];
        }

        return [
            {
                id: 'description',
                label: t`Description`,
                value: this.description,
                type: 'TEXT',
            },
        ];
    }

    get breadcrumbs(): Breadcrumb[] {
        if (
            !sharedRiskLibraryController.isMultipleRegisters ||
            !sharedFeatureAccessModel.isMultipleRiskRegistersEnabled
        ) {
            return [];
        }

        return [
            {
                label: t`All registers`,
                pathname: `${routeController.userPartOfUrl}/risk/management/registers`,
            },
        ];
    }
}
