import { getExternalPolicyProviderLabel } from '@components/policies';
import {
    BannerLocation,
    type BannerMessage,
    BannerPersistenceType,
    createBannerMessage,
} from '@controllers/banner-service';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedPoliciesController } from '@controllers/policies';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable } from '@globals/mobx';
import { storage } from '@globals/storage';

const STORAGE_KEYS = {
    RENEWAL_DATE_BANNER: 'policies-renewal-date-banner',
    EXTERNAL_POLICY_BANNER: 'policies-external-policy-banner',
} as const;

const BANNER_IDS = {
    RENEWAL_DATE: 'policies-renewal-date-banner',
    EXTERNAL_POLICY: 'policies-external-policy-banner',
    DELETED: 'policies-deleted-banner',
    OUTDATED: 'policies-outdated-banner',
} as const;

class PoliciesBannersModel {
    constructor() {
        makeAutoObservable(this);
    }

    get externalPolicyBanner(): BannerMessage | null {
        const {
            showExternalPolicyBanner,
            externalPolicyConnection,
            hasBambooHrConnection,
        } = sharedPoliciesController;
        const isConnectionsLoading =
            sharedConnectionsController.allConfiguredConnectionsQuery.isLoading;

        if (
            isConnectionsLoading ||
            !showExternalPolicyBanner ||
            !externalPolicyConnection ||
            this.getBannerHiddenState(STORAGE_KEYS.EXTERNAL_POLICY_BANNER)
        ) {
            return null;
        }

        const providerLabel = getExternalPolicyProviderLabel(
            externalPolicyConnection.clientType,
        );
        const title = hasBambooHrConnection
            ? t`You're using ${providerLabel} for policy content and acknowledgment`
            : t`You're using ${providerLabel} for policy content`;

        return createBannerMessage({
            id: BANNER_IDS.EXTERNAL_POLICY,
            title,
            severity: 'primary',
            location: BannerLocation.PAGE_HEADER,
            persistenceType: BannerPersistenceType.ROUTE_SCOPED,
            onClose: () => {
                this.setBannerHiddenState(
                    STORAGE_KEYS.EXTERNAL_POLICY_BANNER,
                    true,
                );
            },
        });
    }

    get deletedBanner(): BannerMessage | null {
        const {
            hasPoliciesDeleted,
            showExternalPolicyBanner,
            externalPolicyConnection,
        } = sharedPoliciesController;

        if (
            !hasPoliciesDeleted ||
            !showExternalPolicyBanner ||
            !externalPolicyConnection
        ) {
            return null;
        }

        const providerLabel = getExternalPolicyProviderLabel(
            externalPolicyConnection.clientType,
        );

        return createBannerMessage({
            id: BANNER_IDS.DELETED,
            title: t`A policy was deleted in ${providerLabel}`,
            body: t`Edit any policies marked in red. Import and associate a new file to these policies.`,
            severity: 'critical',
            location: BannerLocation.PAGE_HEADER,
            persistenceType: BannerPersistenceType.ROUTE_SCOPED,
        });
    }

    get outdatedBanner(): BannerMessage | null {
        const {
            hasPoliciesOutdated,
            showExternalPolicyBanner,
            externalPolicyConnection,
        } = sharedPoliciesController;

        if (
            !hasPoliciesOutdated ||
            !showExternalPolicyBanner ||
            !externalPolicyConnection
        ) {
            return null;
        }

        const providerLabel = getExternalPolicyProviderLabel(
            externalPolicyConnection.clientType,
        );

        return createBannerMessage({
            id: BANNER_IDS.OUTDATED,
            title: t`A policy was updated in ${providerLabel}`,
            body: t`Edit any policies marked with yellow to review the changes.`,
            severity: 'warning',
            location: BannerLocation.PAGE_HEADER,
            persistenceType: BannerPersistenceType.ROUTE_SCOPED,
        });
    }

    get renewalDateBanner(): BannerMessage | null {
        const isBannerHidden = this.getBannerHiddenState(
            STORAGE_KEYS.RENEWAL_DATE_BANNER,
        );

        if (isBannerHidden) {
            return null;
        }

        return createBannerMessage({
            id: BANNER_IDS.RENEWAL_DATE,
            title: t`Policies have renewal dates to make it easier to prepare for compliance goals`,
            body: t`It's a best practice to have personnel acknowledge policies at least once a year and many frameworks require it. Tasks and tests are automated based on renewal dates to help you stay on top of your goals. Default renewal dates are 1 year from the last approval or creation date, but you can change them as needed.`,
            severity: 'primary',
            location: BannerLocation.PAGE_HEADER,
            persistenceType: BannerPersistenceType.ROUTE_SCOPED,
            onClose: () => {
                this.setBannerHiddenState(
                    STORAGE_KEYS.RENEWAL_DATE_BANNER,
                    true,
                );
            },
        });
    }

    getPolicyBanners = (): BannerMessage[] => {
        const banners: BannerMessage[] = [];

        const {
            renewalDateBanner,
            externalPolicyBanner,
            deletedBanner,
            outdatedBanner,
        } = this;

        if (renewalDateBanner) {
            banners.push(renewalDateBanner);
        }
        if (externalPolicyBanner) {
            banners.push(externalPolicyBanner);
        }
        if (outdatedBanner) {
            banners.push(outdatedBanner);
        }
        if (deletedBanner) {
            banners.push(deletedBanner);
        }

        return banners;
    };

    private getBannerHiddenState(key: string): boolean {
        try {
            return storage.local.getItem(key) === 'true';
        } catch (error) {
            logger.warn({
                message: 'Failed to read banner state from localStorage',
                additionalInfo: { key, error },
            });

            return false;
        }
    }

    private setBannerHiddenState(key: string, isHidden: boolean): void {
        try {
            if (isHidden) {
                storage.local.setItem(key, 'true');
            } else {
                storage.local.removeItem(key);
            }
        } catch (error) {
            logger.warn({
                message: 'Failed to save banner state to localStorage',
                additionalInfo: { key, isHidden, error },
            });
        }
    }
}

export const sharedPoliciesBannersModel = new PoliciesBannersModel();
