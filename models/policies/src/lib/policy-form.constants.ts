import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import type {
    RenewalScheduleOption,
    RenewalScheduleType,
} from './policy-form.types';

/**
 * Links.
 */
export const GROUP_BASED_POLICY_HELP_LINK =
    'https://help.drata.com/en/articles/5812756-group-based-policy#h_aff1c5d04f';

/**
 * Owner field translations.
 */
export const getOwnerPlaceholder = (): string => t`Search by name or email...`;
export const getLoadingUsersLabel = (): string => t`Loading users...`;
export const getNoUsersFoundLabel = (): string => t`No users found`;
export const getLoadingGroupsLabel = (): string => t`Loading groups...`;

/**
 * Policy Source translations.
 */
export const getPolicySourceLabel = (): string =>
    t`What is the source of the policy content?`;
export const getUploadExistingPolicyLabel = (): string =>
    t`Upload existing policy file`;
export const getUploadExistingPolicyHelpText = (): string =>
    t`Upload a document (PDF, Word, etc.) that contains your policy content`;
export const getAuthorInDrataLabel = (): string => t`Author policy in Drata`;
export const getAuthorInDrataHelpText = (): string =>
    t`Create your policy content using Drata's built-in editor`;
export const getSelectSourceTypeError = (): string =>
    t`Please select a source type`;
export const getUploadPolicyFileLabel = (): string => t`Policy file`;
export const getUploadPolicyFileHelpText = (): string =>
    t`Upload your policy source file`;
export const getUploadPolicyFileError = (): string =>
    t`Please upload a policy file`;

/**
 * Details translations.
 */
export const getPolicyNameLabel = (): string => t`Name`;
export const getPolicyNameHelpText = (): string =>
    t`Applies to all versions of this policy`;
export const getPolicyNameRequiredError = (): string =>
    t`Policy name is required`;
export const getPolicyNameMaxLengthError = (): string =>
    t`Policy name cannot exceed 192 characters`;
export const getPolicyDescriptionLabel = (): string => t`Description`;
export const getPolicyDescriptionRequiredError = (): string =>
    t`Policy description is required`;
export const getPolicyDescriptionMaxLengthError = (): string =>
    t`Policy description cannot exceed 30,000 characters`;
export const getPolicyDisclaimerMaxLengthError = (): string =>
    t`Policy disclaimer cannot exceed 30,000 characters`;
export const getRenewalDateLabel = (): string => t`Renewal Date`;
export const getOwnerLabel = (): string => t`Owner`;
export const getOwnerHelpText = (): string =>
    t`The person responsible for this policy`;
export const getOwnerRequiredError = (): string => t`Please select an owner`;
export const getDisclaimerLabel = (): string => t`Disclaimer`;
export const getDisclaimerHelpText = (): string =>
    t`Disclaimers will appear in the policy viewer in My Drata for employees to review prior to acknowledgement.`;
export const getDisclaimerMaxLengthError = (): string =>
    t`Disclaimer cannot exceed 1000 characters`;

/**
 * Personnel Groups translations.
 */
export const getApplicablePersonnelLabel = (): string =>
    t`Applicable personnel`;
export const getApplicablePersonnelHelpText = (): string =>
    t`This reflects the assigned personnel for all versions of this policy. Updating this may impact control readiness.`;
export const getAllEmployeesLabel = (): string => t`All personnel`;
export const getSpecificGroupsLabel = (): string => t`Specific groups`;
export const getNotApplicableLabel = (): string =>
    t`Policy doesn't apply to personnel`;
export const getSelectPersonnelError = (): string =>
    t`Please select who this policy applies to`;
export const getNotifyNewMembersLabel = (): string =>
    t`Notify new members when they join selected groups`;
export const getSpecificGroupsFieldLabel = (): string => t`Specific groups`;
export const getSpecificGroupsHelpText = (): string =>
    t`Select the groups this policy applies to`;
export const getSearchGroupsPlaceholder = (): string => t`Search for groups...`;
export const getAtLeastOneGroupError = (): string =>
    t`At least one group must be selected`;
export const getNoPersonnelGroupsFoundLabel = (): string =>
    t`No personnel groups found`;
export const getRemoveAllSelectedPersonnelGroupsLabel = (): string =>
    t`Remove all selected personnel groups`;
export const getAriaLabelForLearnMoreLink = (): string =>
    t`Learn more about group-based policies (opens in new tab)`;

/**
 * Replace Policies translations.
 */
export const getShouldReplacePoliciesLabel = (): string =>
    t`Should this policy replace any Drata default policies?`;
export const getShouldReplacePoliciesHelpText = (): string =>
    t`Replacing a default policy will transfer control mappings, SLAs, and archive the default policy`;
export const getYesReplaceLabel = (): string =>
    t`Yes, replace default policies`;
export const getNoKeepSeparateLabel = (): string =>
    t`No, keep as separate policy`;
export const getSelectOptionError = (): string => t`Please select an option`;
export const getSelectPoliciesToReplaceLabel = (): string =>
    t`Select policies to replace`;
export const getSelectPoliciesToReplaceHelpText = (): string =>
    t`Choose which Drata default policies this policy should replace`;
export const getAtLeastOnePolicyError = (): string =>
    t`At least one policy must be selected`;
export const getSelectRenewalDateError = (): string =>
    t`Please select a renewal date`;
export const getRenewalIntervalLabel = (): string => t`Renewal Interval`;
export const getSelectRenewalIntervalError = (): string =>
    t`Please select a renewal interval`;

/**
 * External Source translations.
 */
export const getSelectFileLabel = (): string => t`Select File`;
export const getSelectFileToImportError = (): string =>
    t`There was an error importing the file. Please try again.`;
export const getImportFileRequiredError = (): string =>
    t`Please select a file to import`;
export const getLearnMoreLabel = (): string =>
    t`Learn about Group-based policies`;

/**
 * Renewal schedule helper.
 */
export const getRenewalScheduleTypeMonthValue = (
    renewalScheduleType: RenewalScheduleType,
): number => {
    switch (renewalScheduleType) {
        case 'ONE_MONTH': {
            return 1;
        }
        case 'TWO_MONTHS': {
            return 2;
        }
        case 'THREE_MONTHS': {
            return 3;
        }
        case 'SIX_MONTHS': {
            return 6;
        }
        case 'ONE_YEAR': {
            return 12;
        }
        case 'CUSTOM': {
            return 0;
        }
    }
};

export const getRenewalScheduleOptions = (): RenewalScheduleOption[] => [
    {
        id: 'renewal-interval-1-month',
        label: t`1 Month`,
        value: 'ONE_MONTH',
    },
    {
        id: 'renewal-interval-2-months',
        label: t`2 Months`,
        value: 'TWO_MONTHS',
    },
    {
        id: 'renewal-interval-3-months',
        label: t`3 Months`,
        value: 'THREE_MONTHS',
    },
    {
        id: 'renewal-interval-6-months',
        label: t`6 Months`,
        value: 'SIX_MONTHS',
    },
    {
        id: 'renewal-interval-1-year',
        label: t`1 Year`,
        value: 'ONE_YEAR',
    },
    {
        id: 'renewal-interval-custom',
        label: t`Custom - One Time`,
        value: 'CUSTOM',
    },
];

export const gracePeriodSLAOptions = (): ListBoxItemData[] => [
    {
        id: 'grace-period-1-day',
        label: t`1 Day`,
        value: 'ONE_DAY',
    },
    {
        id: 'grace-period-7-days',
        label: t`7 Days`,
        value: 'SEVEN_DAYS',
    },
    {
        id: 'grace-period-14-days',
        label: t`14 Days`,
        value: 'FOURTEEN_DAYS',
    },
    {
        id: 'grace-period-21-days',
        label: t`21 Days`,
        value: 'TWENTY_ONE_DAYS',
    },
    {
        id: 'grace-period-30-days',
        label: t`30 Days`,
        value: 'THIRTY_DAYS',
    },
];

export const weekTimeFrameSLAOptions = (): ListBoxItemData[] => [
    {
        id: 'week-time-frame-1-day',
        label: t`24 Hours`,
        value: 'ONE_DAY',
    },
    {
        id: 'week-time-frame-2-days',
        label: t`2 Days`,
        value: 'TWO_DAYS',
    },
    {
        id: 'week-time-frame-3-days',
        label: t`3 Days`,
        value: 'THREE_DAYS',
    },
    {
        id: 'week-time-frame-4-days',
        label: t`4 Days`,
        value: 'FOUR_DAYS',
    },
    {
        id: 'week-time-frame-5-days',
        label: t`5 Days`,
        value: 'FIVE_DAYS',
    },
    {
        id: 'week-time-frame-1-week',
        label: t`1 Week`,
        value: 'ONE_WEEK',
    },
];

export const p3MatrixSLAOptions = (): ListBoxItemData[] => [
    {
        id: 'p3-matrix-sla-1-day',
        label: t`1 Day`,
        value: 'ONE_DAY',
    },
    {
        id: 'p3-matrix-sla-3-days',
        label: t`3 Days`,
        value: 'THREE_DAYS',
    },
    {
        id: 'p3-matrix-sla-7-days',
        label: t`7 Days`,
        value: 'SEVEN_DAYS',
    },
    {
        id: 'p3-matrix-sla-14-days',
        label: t`14 Days`,
        value: 'FOURTEEN_DAYS',
    },
    {
        id: 'p3-matrix-sla-30-days',
        label: t`30 Days`,
        value: 'THIRTY_DAYS',
    },
    {
        id: 'p3-matrix-sla-60-days',
        label: t`60 Days`,
        value: 'SIXTY_DAYS',
    },
    {
        id: 'p3-matrix-sla-90-days',
        label: t`90 Days`,
        value: 'NINETY_DAYS',
    },
    {
        id: 'p3-matrix-sla-best-effort',
        label: t`Best Effort`,
        value: 'BEST_EFFORT',
    },
];
