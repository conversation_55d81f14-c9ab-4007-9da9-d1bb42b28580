import { forwardRef } from 'react';
import { Box } from '@cosmos/components/box';
import { But<PERSON> } from '@cosmos/components/button';
import type { FormFieldProps } from '@cosmos/components/form-field';
import { FormWrapper, type FormWrapperProps } from './form-wrapper';
import { UniversalRenderFields } from './universal-render-fields';

export interface FormProps extends Omit<FormWrapperProps, 'children'> {
    /** If `true`, then the form will not render a submit button. */
    hasExternalSubmitButton?: boolean;

    /**
     * Optional style overrides for field labels throughout the form.
     * Allows customizing label appearance consistently across all form fields.
     */
    labelStyleOverrides?: FormFieldProps['labelStyleOverrides'];

    /**
     * Optional label for the form submission button.
     */
    submitButtonLabel?: string;

    /**
     * Optional width for the form submission button.
     */
    submitButtonWidth?: 'auto' | 'full-width';
}

/**
 * The Form component provides an easy way to quickly render forms using Cosmos components. The structure of the form is defined by a schema, a JSON-like representation of the form.
 *
 * 🚧 Currently no Figma link.
 */
export const Form = forwardRef(
    (
        {
            'data-id': dataId,
            formId,
            hasExternalSubmitButton = false,
            labelStyleOverrides,
            onSubmit,
            schema,
            isReadOnly = false,
            submitButtonLabel = 'Submit',
            submitButtonWidth = 'full-width',
            isLoading = false,
        }: FormProps,
        ref: React.ForwardedRef<HTMLFormElement>,
    ): React.JSX.Element => {
        return (
            <FormWrapper
                data-id={dataId}
                formId={formId}
                schema={schema}
                ref={ref}
                isReadOnly={isReadOnly}
                onSubmit={onSubmit}
            >
                <UniversalRenderFields
                    fields={schema}
                    formId={formId}
                    data-id={`${dataId}-fields`}
                    labelStyleOverrides={labelStyleOverrides}
                />

                {hasExternalSubmitButton ? (
                    <button
                        hidden
                        type="submit"
                        aria-hidden="true"
                        tabIndex={-1}
                    />
                ) : (
                    <Box
                        width={
                            submitButtonWidth === 'auto'
                                ? 'fit-content'
                                : undefined
                        }
                    >
                        <Button
                            type="submit"
                            isLoading={isLoading}
                            a11yLoadingLabel="Submitting..."
                            label={submitButtonLabel}
                            width={submitButtonWidth}
                            data-id={`${dataId}-submit`}
                        />
                    </Box>
                )}
            </FormWrapper>
        );
    },
);

Form.displayName = 'Form';
