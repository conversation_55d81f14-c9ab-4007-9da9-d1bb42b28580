import { isFunction } from 'lodash-es';
import { memo, useMemo } from 'react';
import {
    CheckboxField,
    type CheckboxFieldProps,
} from '@cosmos/components/checkbox-field';
import {
    CheckboxFieldGroup,
    type CheckboxFieldGroupProps,
} from '@cosmos/components/checkbox-field-group';
import {
    ComboboxField,
    type ComboboxFieldProps,
} from '@cosmos/components/combobox-field';
import { DatePickerField } from '@cosmos/components/date-picker-field';
import {
    RadioFieldGroup,
    type RadioFieldGroupProps,
} from '@cosmos/components/radio-field-group';
import {
    SelectField,
    type SelectFieldProps,
} from '@cosmos/components/select-field';
import {
    SliderField,
    type SliderFieldProps,
} from '@cosmos/components/slider-field';
import { Stack } from '@cosmos/components/stack';
import { TextField, type TextFieldProps } from '@cosmos/components/text-field';
import {
    TextareaField,
    type TextareaFieldProps,
} from '@cosmos/components/textarea-field';
import {
    To<PERSON><PERSON>ield,
    type ToggleFieldProps,
} from '@cosmos/components/toggle-field';
import {
    ChoiceCard,
    type ChoiceCardProps,
} from '@cosmos-lab/components/choice-card';
import type { ChoiceCardGroupProps } from '@cosmos-lab/components/choice-card-group';
import { DateRangeField } from '@cosmos-lab/components/date-range-field';
import { Divider } from '@cosmos-lab/components/divider';
import { t } from '@globals/i18n/macro';
import { BuilderChoiceCardGroup } from './components/builder-choice-card-group';
import { BuilderFileUploadField } from './components/builder-file-upload-field';
import { BuilderImageUploadField } from './components/builder-image-upload-field';
import type { BaseField } from './types/base-field.type';
import type { FieldSchema } from './types/field-schema.type';
import type { LimitedDatePickerFieldProps } from './types/limited-date-picker-field-props.type';
import type { LimitedDateRangeFieldProps } from './types/limited-date-range-field-props.type';
import type { LimitedFileUploadFieldProps } from './types/limited-file-upload-field-props.type';
import type { LimitedImageUploadFieldProps } from './types/limited-image-upload-field-props.type';
import type { SharedProps } from './types/shared-props.type';
import type { UniversalFieldController } from './types/universal-field-controller.type';
import type { UniversalFormFieldProps } from './types/universal-form-field-props.type';
import { useUniversalFieldController } from './use-universal-field-controller';

/**
 * Maps field types to their corresponding component implementations.
 * Used to dynamically select the appropriate component based on the field schema type.
 */

const renderMap = {
    text: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <TextField
            {...(fieldSchema as BaseField<TextFieldProps>)}
            {...sharedProps}
            {...props}
            value={fieldProps.value}
            onBlur={fieldProps.onBlur}
            onChange={fieldProps.onChange}
        />
    ),
    textarea: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <TextareaField
            {...(fieldSchema as BaseField<TextareaFieldProps>)}
            {...sharedProps}
            {...props}
            value={fieldProps.value}
            onBlur={fieldProps.onBlur}
            onChange={fieldProps.onChange}
        />
    ),
    checkbox: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <CheckboxField
            {...(fieldSchema as BaseField<CheckboxFieldProps>)}
            {...sharedProps}
            {...props}
            value={fieldProps.value}
            checked={fieldProps.value}
            onChange={fieldProps.onChange}
        />
    ),
    checkboxGroup: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <CheckboxFieldGroup
            {...(fieldSchema as BaseField<CheckboxFieldGroupProps>)}
            {...sharedProps}
            {...props}
            value={fieldProps.value}
            onChange={fieldProps.onChange}
        />
    ),
    choiceCard: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <ChoiceCard
            {...(fieldSchema as BaseField<ChoiceCardProps>)}
            {...sharedProps}
            {...props}
            checked={fieldProps.value}
            value={fieldProps.value}
            onChange={fieldProps.onChange}
        />
    ),
    choiceCardGroup: ({ sharedProps, props, fieldProps, fieldSchema }) => {
        return (
            // eslint-disable-next-line custom/enforce-data-id -- passed in props
            <BuilderChoiceCardGroup
                sharedProps={sharedProps}
                props={props}
                fieldProps={fieldProps}
                fieldSchema={fieldSchema as BaseField<ChoiceCardGroupProps>}
            />
        );
    },
    combobox: ({ sharedProps, props, fieldProps, fieldSchema }) => {
        const valueProp = {
            [(fieldSchema as BaseField<ComboboxFieldProps>).isMultiSelect
                ? 'defaultSelectedOptions'
                : 'defaultValue']: fieldProps.value,
        };

        return (
            // eslint-disable-next-line custom/enforce-data-id -- passed in props
            <ComboboxField
                {...(fieldSchema as BaseField<ComboboxFieldProps>)}
                {...sharedProps}
                {...props}
                {...valueProp}
                name={fieldProps.name}
                onChange={fieldProps.onChange}
                onBlur={fieldProps.onBlur}
            />
        );
    },
    date: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <DatePickerField
            {...(fieldSchema as BaseField<LimitedDatePickerFieldProps>)}
            {...sharedProps}
            {...props}
            getIsDateUnavailable={() => false}
            dateUnavailableText={t`This date is unavailable`}
            monthSelectionFieldLabel={t`Select Month`}
            yearSelectionFieldLabel={t`Select Year`}
            value={fieldProps.value}
            locale={
                (fieldSchema as BaseField<LimitedDatePickerFieldProps>)
                    .locale || 'en-US'
            }
            onChange={fieldProps.onChange}
        />
    ),
    dateRange: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <DateRangeField
            {...(fieldSchema as BaseField<LimitedDateRangeFieldProps>)}
            {...sharedProps}
            {...props}
            value={fieldProps.value}
            yearSelectionFieldLabel={t`Select Year`}
            monthSelectionFieldLabel={t`Select Month`}
            a11yHiddenLabel={{
                start: t`Start date`,
                end: t`End date`,
            }}
            dateUnavailableText={{
                start: t`This start date is unavailable`,
                end: t`This end date is unavailable`,
            }}
            locale={
                (fieldSchema as BaseField<LimitedDateRangeFieldProps>).locale ||
                'en-US'
            }
            onChange={fieldProps.onChange}
        />
    ),
    file: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <BuilderFileUploadField
            sharedProps={sharedProps}
            props={props}
            fieldProps={fieldProps}
            fieldSchema={fieldSchema as BaseField<LimitedFileUploadFieldProps>}
        />
    ),
    image: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <BuilderImageUploadField
            sharedProps={sharedProps}
            fieldSchema={fieldSchema as BaseField<LimitedImageUploadFieldProps>}
            props={props}
            fieldProps={fieldProps}
        />
    ),
    radioGroup: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <RadioFieldGroup
            {...(fieldSchema as BaseField<RadioFieldGroupProps>)}
            {...sharedProps}
            {...props}
            value={fieldProps.value}
            onChange={fieldProps.onChange}
        />
    ),
    select: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <SelectField
            {...(fieldSchema as BaseField<SelectFieldProps>)}
            {...sharedProps}
            {...props}
            value={fieldProps.value}
            onChange={fieldProps.onChange}
        />
    ),
    slider: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <SliderField
            {...(fieldSchema as BaseField<SliderFieldProps>)}
            {...sharedProps}
            {...props}
            value={fieldProps.value}
            onCommit={fieldProps.onChange}
            onInputChange={fieldProps.onChange}
        />
    ),
    toggle: ({ sharedProps, props, fieldProps, fieldSchema }) => (
        // eslint-disable-next-line custom/enforce-data-id -- passed in props
        <ToggleField
            {...(fieldSchema as BaseField<ToggleFieldProps>)}
            {...sharedProps}
            {...props}
            checked={fieldProps.value}
            onChange={fieldProps.onChange}
        />
    ),
    custom: ({ fieldProps, props, sharedProps, fieldSchema }) => {
        // Pass label from props if explicitly provided, otherwise use schema.label
        const schema = fieldSchema as Extract<FieldSchema, { type: 'custom' }>;

        if (isFunction(schema.render)) {
            return schema.render({
                ...fieldProps,
                ...sharedProps,
                formId: props.formId,
                label: props.label ?? schema.label,
                recursiveFieldName: schema.recursiveFieldName ?? 'children',
                'data-id': props['data-id'],
            }) as React.JSX.Element;
        }

        return null;
    },
} as const as Record<
    FieldSchema['type'],
    ({
        sharedProps,
        props,
        fieldProps,
        fieldSchema,
    }: {
        sharedProps: SharedProps;
        props: UniversalFormFieldProps;
        fieldProps: UniversalFieldController;
        fieldSchema: FieldSchema;
    }) => React.JSX.Element
>;

export const UniversalFormField = memo(
    (props: UniversalFormFieldProps): React.JSX.Element | null => {
        const { name, showDivider, __fromCustomRender } = props;
        const [field] = useUniversalFieldController(name);

        const Renderer = useMemo(() => {
            if (
                field.type === 'custom' &&
                field.fieldSchemaProps.type === 'custom' &&
                field.fieldSchemaProps.validateWithDefault &&
                __fromCustomRender
            ) {
                return renderMap[field.fieldSchemaProps.validateWithDefault];
            }

            return renderMap[field.type as keyof typeof renderMap];
        }, [__fromCustomRender, field.fieldSchemaProps, field.type]);

        const sharedProps: SharedProps = useMemo(
            () => ({
                required: field.required,
                feedback: field.feedback,
                optionalText: field.optionalText,
            }),
            [field.feedback, field.optionalText, field.required],
        );

        return field.isShown ? (
            <Stack gap="6x" width="100%" direction="column">
                <Renderer
                    sharedProps={sharedProps}
                    fieldProps={field}
                    fieldSchema={field.fieldSchemaProps}
                    props={props}
                />

                {showDivider && <Divider />}
            </Stack>
        ) : null;
    },
);

UniversalFormField.displayName = 'UniversalFormField';
