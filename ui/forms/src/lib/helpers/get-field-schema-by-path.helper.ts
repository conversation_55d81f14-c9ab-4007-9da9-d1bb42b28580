import { isEmpty } from 'lodash-es';
import type { FieldSchemaWithGroup } from '../types/field-schema-with-group.type';
import type { FormSchema } from '../types/form-schema.type';

const normalizePath = (path: string): string => path.replaceAll(/\[\d+\]/g, '');

/**
 * Retrieves a field schema by its path in the form schema.
 * Supports nested paths using dot notation (e.g., 'group.field').
 *
 * @param schema - The form schema to search in.
 * @param rawPath - The path to the field, using dot notation for nested fields.
 * @returns The field schema if found, undefined otherwise.
 */
export const getFieldSchemaByPath = (
    schema: FormSchema,
    rawPath: string,
): FieldSchemaWithGroup | undefined => {
    const path = normalizePath(rawPath);
    const [firstKey, ...restKeys] = path.split('.');

    const current = schema[firstKey];

    if (isEmpty(restKeys)) {
        return current;
    }

    if (current.type === 'custom' && current.customType) {
        if (
            current.customType === 'arrayOfObjects' &&
            current.recursiveFieldName
        ) {
            const { recursiveFieldName, fields, label, render } = current;

            const fieldsCopy = { ...fields };

            fieldsCopy[recursiveFieldName] = {
                type: 'custom',
                customType: 'arrayOfObjects',
                fields,
                recursiveFieldName,
                initialValue: [],
                label,
                render,
            };

            return getFieldSchemaByPath(fieldsCopy, restKeys.join('.'));
        }

        return getFieldSchemaByPath(current.fields, restKeys.join('.'));
    }

    if (current.type === 'group') {
        return getFieldSchemaByPath(current.fields, restKeys.join('.'));
    }

    return undefined;
};
