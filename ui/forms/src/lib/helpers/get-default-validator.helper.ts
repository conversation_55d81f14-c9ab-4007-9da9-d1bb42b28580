import { z } from 'zod';
import { t } from '@globals/i18n/macro';
import type { FieldSchema } from '../types/field-schema.type';
import type { Validator } from '../types/validator.type';

/**
 * Object mapping field types to their validator functions.
 * Each function takes a field schema and returns a Zod validator.
 */
const FIELD_VALIDATORS: Partial<
    Record<
        FieldSchema['type'],
        (schema: FieldSchema, errorMap: z.ZodErrorMap) => Validator | null
    >
> = {
    checkbox: (_, errorMap) => z.boolean({ errorMap }),

    checkboxGroup: (_, errorMap) =>
        z.array(z.string(), { errorMap }).nonempty(),

    choiceCard: (_, errorMap) => z.boolean({ errorMap }),

    choiceCardGroup: (schema, errorMap) => {
        const isCheckbox =
            (schema as { choiceCardInputType?: 'checkbox' | 'radio' })
                .choiceCardInputType === 'checkbox';

        if (isCheckbox) {
            return z.string({ errorMap }).array().min(1);
        }

        // For radio type - needs to have at least 1 character (not empty)
        return z.string({ errorMap }).min(1);
    },
    combobox: (schema, errorMap) => {
        if ((schema as { isMultiSelect?: boolean }).isMultiSelect) {
            const arrayValidator = z
                .array(
                    z.object({
                        id: z.string(),
                        label: z.string(),
                        value: z.string().optional(),
                    }),
                    { errorMap },
                )
                .nonempty();

            return (schema as { isOptional?: boolean }).isOptional
                ? z.union([
                      z.undefined(),
                      arrayValidator,
                      z.array(z.any()).length(0),
                  ])
                : arrayValidator;
        }

        const objectValidator = z.object(
            {
                id: z.string(),
                label: z.string(),
                value: z.string().optional(),
            },
            { errorMap },
        );

        return (schema as { isOptional?: boolean }).isOptional
            ? objectValidator.optional()
            : objectValidator;
    },

    date: (_, errorMap) =>
        z.union([
            z.string({ errorMap }).date(),
            z
                .array(z.string().date())
                .min(1, { message: t`At least one date is required` }),
        ]),

    dateRange: (schema) =>
        z
            .object({
                start: z.string().date().nullable(),
                end: z.string().date().nullable(),
            })
            .superRefine((data, ctx) => {
                if (schema.isOptional) {
                    return;
                }

                if (!data.start) {
                    ctx.addIssue({
                        code: z.ZodIssueCode.custom,
                        message: t`Start date is required`,
                        path: [],
                    });
                }
                if (!data.end) {
                    ctx.addIssue({
                        code: z.ZodIssueCode.custom,
                        message: t`End date is required`,
                        path: [],
                    });
                }
            }),

    radioGroup: (_, errorMap) => z.string({ errorMap }),

    select: (_, errorMap) =>
        z.object(
            {
                id: z.string(),
                label: z.string(),
                value: z.string().optional(),
            },
            { errorMap },
        ),

    slider: (_, errorMap) => z.number({ errorMap }).array(),

    text: (schema, errorMap) => {
        const validator = z.string({ errorMap });

        return schema.isOptional ? validator.optional() : validator.nonempty();
    },

    textarea: (schema, errorMap) => {
        if ('maxCharacters' in schema && schema.maxCharacters) {
            const maxCharacters = Number(schema.maxCharacters);

            const validator = z
                .string({ errorMap })
                .max(Number(schema.maxCharacters), {
                    message:
                        // TODO: replace with something from Product/Design
                        t`Maximum ${maxCharacters} characters allowed`,
                });

            return schema.isOptional
                ? validator.optional()
                : validator.nonempty();
        }

        return schema.isOptional
            ? z.string({ errorMap }).optional()
            : z.string({ errorMap });
    },

    file: (_, errorMap) => z.array(z.any(), { errorMap }).nonempty(),

    image: (_, errorMap) => z.array(z.any(), { errorMap }).nonempty(),
};

/**
 * Returns a default validator based on the field type.
 * Uses an object-based lookup for better performance and maintainability.
 */
export const getDefaultValidator = (
    fieldSchema: FieldSchema,
): Validator | null => {
    // Temporary type assertion to get around DateRangeField
    const fieldLabel = (fieldSchema as { label?: string }).label ?? 'Field';
    const defaultErrorMap = {
        required_error: t`${fieldLabel} is required`,
    };

    /**
     * Look up the validator function for this field type.
     */
    const validatorFn =
        fieldSchema.type in FIELD_VALIDATORS
            ? FIELD_VALIDATORS[fieldSchema.type]
            : null;

    if (!validatorFn) {
        return z.any();
    }

    // If a validator function exists for this field type, call it with the schema and error map
    return validatorFn(fieldSchema, () => ({
        message: defaultErrorMap.required_error,
    }));
};
