import type { CustomFieldRenderProps } from './custom-field-render-props.type';
import type { FieldPropsByType } from './field-props-by-type.type';
import type { BaseFieldFor } from './field-schema-for.type';
import type { FieldType } from './field-type.type';
import type { FormSchema } from './form-schema.type';
import type { InitialValueByFieldType } from './initial-value-by-field-type.type';

/**
 * Base properties for all custom field types.
 * This interface defines the common properties that all custom fields must have.
 */
interface CustomFieldBaseProps {
    /** Label displayed for the field. */
    label: string;
    /**
     * Optional custom render function that gives full control over the field's appearance.
     * When using this with validateWithDefault, make sure to pass __fromCustomRender to UniversalFormField.
     */
    render?: (fieldProps: CustomFieldRenderProps) => React.ReactNode;
    /** Initial value for the field. */
    initialValue?: unknown;
}

/**
 * Simple custom field without validation or nested structure.
 * Used when you need a completely custom field with your own render function and validation.
 */
type CustomFieldWithoutValidator<TFormSchema> = BaseFieldFor<
    CustomFieldBaseProps,
    TFormSchema
> & {
    /** Cannot be used with this field type. */
    validateWithDefault?: never;
    /** Cannot be used with this field type. */
    customType?: never;
    /** Cannot be used with this field type. */
    fields?: never;
    /** Cannot be used with this field type. */
    recursiveFieldName?: never;
};

/**
 * Custom field type for object values.
 * Used when you need a field that contains a nested object with its own schema.
 */
type CustomFieldWithObjectValue<TFormSchema> = Omit<
    CustomFieldBaseProps,
    'initialValue'
> & {
    /** Specifies this is an object-type custom field. */
    customType: 'object';
    /** Cannot be used with customType. */
    validateWithDefault?: never;
    /** Initial value for the object. */
    initialValue?: Record<string, unknown>;
    /** Required schema defining the structure of the object. */
    fields: FormSchema;
    /** Cannot be used with customType. */
    recursiveFieldName?: never;
} & BaseFieldFor<Omit<CustomFieldBaseProps, 'initialValue'>, TFormSchema>;

/**
 * Custom field type for array of objects values.
 * Used when you need a field that contains a list of objects, each with the same schema.
 * Can also handle recursive structures when recursiveFieldName is provided.
 */
type CustomFieldWithArrayValue<TFormSchema> = Omit<
    CustomFieldBaseProps,
    'initialValue'
> & {
    /** Specifies this is an array-of-objects-type custom field. */
    customType: 'arrayOfObjects';
    /** Cannot be used with customType. */
    validateWithDefault?: never;
    /** Initial value for the array. */
    initialValue?: unknown[];
    /** Required schema defining the structure of each object in the array. */
    fields: FormSchema;
    /**
     * Optional: Field name that contains recursive children of the same type.
     * When provided, enables recursive/nested functionality.
     * Example: 'subConditions', 'children', 'subItems'.
     */
    recursiveFieldName?: string;
} & BaseFieldFor<Omit<CustomFieldBaseProps, 'initialValue'>, TFormSchema>;

/**
 * Custom field that uses a standard field type for validation.
 * This allows you to create a custom field that behaves like a standard field type
 * but with your own rendering logic.
 */
type CustomFieldWithValidator<
    TType extends FieldType,
    TFormSchema,
> = CustomFieldBaseProps & {
    /** Specifies which standard field type to use for validation. */
    validateWithDefault: TType;
    /** Initial value matching the specified field type. */
    initialValue?: InitialValueByFieldType[TType];
    /** Cannot be used with validateWithDefault. */
    customType?: never;
    /** Cannot be used with validateWithDefault. */
    fields?: never;
    /** Cannot be used with validateWithDefault. */
    recursiveFieldName?: never;
} & BaseFieldFor<FieldPropsByType[TType], TFormSchema>;

/**
 * Union of all possible validator types.
 * This creates a union type of all possible field types that can be used with validateWithDefault.
 */
type CustomFieldWithValidatorUnion<TFormSchema> = {
    [K in FieldType]: CustomFieldWithValidator<K, TFormSchema>;
}[FieldType];

/**
 * Represents all possible custom field configurations.
 * This is the main type used when defining a custom field in a form schema.
 *
 * There are four main variants:
 * 1. Simple custom field (CustomFieldWithoutValidator)
 * 2. Object custom field (CustomFieldWithObjectValue)
 * 3. Array of objects custom field (CustomFieldWithArrayValue) - supports recursive structures via recursiveFieldName
 * 4. Custom field with standard validation (CustomFieldWithValidatorUnion).
 */
export type CustomFieldProps<TFormSchema> =
    | CustomFieldWithoutValidator<TFormSchema>
    | CustomFieldWithObjectValue<TFormSchema>
    | CustomFieldWithArrayValue<TFormSchema>
    | CustomFieldWithValidatorUnion<TFormSchema>;
