import type { FormFieldProps } from '@cosmos/components/form-field';

export interface UniversalFormFieldProps {
    /**
     * Optional unique identifier for testing and analytics tracking.
     * Used as a data attribute on the rendered field component.
     */
    'data-id': string;

    /**
     * Unique identifier for the form this field belongs to.
     * Used to associate the field with its parent form.
     */
    formId: string;

    /**
     * Optional style overrides for the field label.
     * Allows customizing the appearance of the field label.
     */
    labelStyleOverrides?: FormFieldProps['labelStyleOverrides'];

    /**
     * The name of the field in the form.
     * Used as the field identifier in form data and for validation.
     */
    name: string;

    /** If `true`, a divider will be rendered below the field. */
    showDivider?: boolean;

    /**
     * Internal flag used when a field is rendered from a custom render function.
     * Enables proper validation when using validateWithDefault in custom fields.
     */
    __fromCustomRender?: boolean;

    /**
     * Label text informs users of the expected information in a form field.
     */
    label?: string;
}
